package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ApiListExport implements Serializable {

    private static final long serialVersionUID = -5836782370561810170L;

    @Column(value = "接口名称", order = 1)
    private String urlName;

    @Column(value = "接口地址", order = 2)
    private String url;

    @Column(value = "请求次数", order = 3)
    private Long requestNum;

    @Column(value = "成功次数", order = 4)
    private Long successNum;

    @Column(value = "失败次数", order = 5)
    private Long failNum;

    @Column(value = "异常次数", order = 6)
    private Long abnormalNum;

    @Column(value = "异常率", order = 7)
    private BigDecimal anomalyRate;

    @Column(value = "平均访问时长", order = 8)
    private BigDecimal avgDurationTime;

    public ApiListExport(String urlName, String url, Long requestNum, Long successNum, Long failNum, Long abnormalNum, BigDecimal anomalyRate, BigDecimal avgDurationTime) {
        this.urlName = urlName;
        this.url = url;
        this.requestNum = requestNum;
        this.successNum = successNum;
        this.failNum = failNum;
        this.abnormalNum = abnormalNum;
        this.anomalyRate = anomalyRate;
        this.avgDurationTime = avgDurationTime;
    }
}
