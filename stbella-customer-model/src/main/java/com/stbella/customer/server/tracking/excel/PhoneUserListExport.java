package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import java.io.Serializable;
import lombok.Data;

/**
 * 毛客资导出
 */
@Data
public class PhoneUserListExport implements Serializable {

    private static final long serialVersionUID = 6503644303714649946L;

    @Column(value = "客户姓名", order = 1)
    private String clientName;

    @Column(value = "客户手机号", order = 2)
    private String phone;

    @Column(value = "品牌", order = 3)
    private String brandName;

    @Column(value = "注册时间", order = 4)
    private String registerTime;

    @Column(value = "用户状态", order = 5)
    private String statusName;

    // scrm状态
    private String scrmStatus;
}
