package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value = "PlatformStatActiveOldOrNewUserVo",description = "平台统计-新老活跃用户趋势vo")
public class PlatformStatActiveOldOrNewUserVo implements Serializable {

    private static final long serialVersionUID = 1283301882492666180L;


    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty("新用户活跃数")
    private Long newUserView;

    @ApiModelProperty("老用户用户活跃数")
    private Long oldUserView;


}
