package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "StoreAppointmentSaleRequest", description = "客户门店预约销售request")
public class StoreAppointmentSaleRequest extends BasicReq {

    private static final long serialVersionUID = 2335477580836878236L;

    @ApiModelProperty(value = "销售手机号")
    private String salePhone;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "品牌")
    private Integer brandType;
}
