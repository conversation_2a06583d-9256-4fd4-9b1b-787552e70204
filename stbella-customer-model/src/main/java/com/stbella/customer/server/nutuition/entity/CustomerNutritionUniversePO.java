package com.stbella.customer.server.nutuition.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import com.stbella.customer.server.customer.handler.ListConfigHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_nutrition_universe")
@ApiModel(value = "CustomerNutritionUniversePO对象", description = "")
public class CustomerNutritionUniversePO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户手机号（唯一）")
    private String phone;

    @ApiModelProperty(value = "渠道来源：1：淘宝；2-天猫；3-京东；4-门店；5-分销；6-微商城")
    @TableField(typeHandler = ListConfigHandler.class, jdbcType = JdbcType.VARCHAR, javaType = true)
    private List<Integer> source;

    @ApiModelProperty(value = "全域订单金额")
    private BigDecimal allAmount;

    @ApiModelProperty(value = "全域结算金额")
    private BigDecimal allRealityAmount;

    @ApiModelProperty(value = "最近下单时间")
    private Date lastOrderTime;


}
