package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value="CustomerVisitorListRequest", description="")
public class CustomerCheckOutSummaryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户basicId")
    private Long basicId;

    @ApiModelProperty(value = "品牌类型，0:圣贝拉 1:小贝拉")
    private Integer storeType;

}
