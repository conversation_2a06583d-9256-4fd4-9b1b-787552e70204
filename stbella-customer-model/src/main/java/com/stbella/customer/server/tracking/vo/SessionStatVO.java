package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "SessionStatVO",description = "Session统计VO")
public class SessionStatVO implements Serializable {

    private static final long serialVersionUID = -8198379081861893190L;

    @ApiModelProperty(value = "日期，yyyy-MM-dd")
    private String statDate;

    @ApiModelProperty(value = "用户名")
    private String clientName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "sessionId")
    private String sessionId;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "退出时间")
    private Date endTime;

    @ApiModelProperty(value = "访问时长（min）")
    private BigDecimal durationTime;

    @ApiModelProperty(value = "访问渠道")
    private String source;

    @ApiModelProperty(value = "访问场景")
    private String scene;

    @ApiModelProperty(value = "首次进入页面")
    private String firstName;

    @ApiModelProperty(value = "退出页面")
    private String lastName;

    @ApiModelProperty(value = "交互深度")
    private Long pageNumber;

}
