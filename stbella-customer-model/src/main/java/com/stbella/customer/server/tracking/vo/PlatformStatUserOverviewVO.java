package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "PlatformStatUserOverviewVO",description = "平台统计-用户概括vo")
public class PlatformStatUserOverviewVO implements Serializable {

    private static final long serialVersionUID = 1260515228380372817L;

    @ApiModelProperty("活跃用户数")
    private Long activeUserView;

    @ApiModelProperty("新增用户数")
    private Long newUserView;

}
