package com.stbella.customer.server.tracking.request;

import com.stbella.customer.server.tracking.annotations.NullOrAlphanumeric;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value="CustomerDataTrackingEventRequest", description="点击事件上报请求类")
public class CustomerDataTrackingEventRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的会话")
    private String sessionid;

    @ApiModelProperty(value = "事件id")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的事件")
    private String eventId;

    @ApiModelProperty(value = "事件类型")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件内容")
    private String eventContent;

    @ApiModelProperty(value = "事件query")
    private String eventQuery;

    @ApiModelProperty(value = "页面地址")
    @NullOrAlphanumeric(message = "错误的地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

}
