package com.stbella.customer.server.tracking;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * 埋点平台用户状态枚举
 */
public enum UserStatusEnum {
    NORMAL(0, "正常"),
    WHITE(1, "白名单"),
    BLACK(2, "黑名单"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    UserStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        } else {
            UserStatusEnum[] var1 = values();

            for (UserStatusEnum userStatusEnum : var1) {
                if(userStatusEnum.code.equals(code)){
                    return userStatusEnum.desc;
                }
            }
            return "";
        }
    }
}
