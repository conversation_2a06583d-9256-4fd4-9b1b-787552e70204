package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户C端跳转事件埋点
 * </p>
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_api")
@ApiModel(value = "CustomerDataTrackingApiPO对象", description = "")
public class CustomerDataTrackingApiPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    private String sessionid;

    @ApiModelProperty(value = "来源渠道")
    private String source;

    @ApiModelProperty(value = "参数")
    private String param;

    @ApiModelProperty(value = "接口")
    private String url;

    @ApiModelProperty(value = "请求方式")
    private String method;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "请求耗时（毫秒）")
    private Long durationTime;

    @ApiModelProperty(value = "品牌类型 0圣贝拉 1小贝拉")
    private Integer brandType;


    @ApiModelProperty(value = "接口状态 0失败 1正常")
    private Integer status;

    @ApiModelProperty(value = "接口code")
    private String code;


}
