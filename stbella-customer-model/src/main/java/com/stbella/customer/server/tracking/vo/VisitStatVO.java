package com.stbella.customer.server.tracking.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "PageVisitStatisticsVO", description = "页面统计")
public class VisitStatVO implements Serializable {


    private static final long serialVersionUID = 5158450022432053919L;

    @ApiModelProperty(value = "页面id")
    private Long id;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问人数")
    private Long userView;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "会话数")
    private Long sessionCount;

    @ApiModelProperty(value = "访问时长")
    private BigDecimal durationTime;

    @ApiModelProperty(value = "平均访问时长")
    private BigDecimal avgDurationTime;

    @ApiModelProperty(value = "日期")
    private String statDate;

    @ApiModelProperty(value = "是否启用, 0-是 1-否")
    private Integer enable;

    @ApiModelProperty(value = "是否支持投放，0-支持 1-不支持")
    private Integer advert;
}
