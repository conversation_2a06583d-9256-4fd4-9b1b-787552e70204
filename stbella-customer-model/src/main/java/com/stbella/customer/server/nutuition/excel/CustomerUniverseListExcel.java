package com.stbella.customer.server.nutuition.excel;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomerUniverseListExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(value = "客户ID", order = 0)
    private String customerUniverseId;

    @Column(value = "手机号", order = 1)
    private String customerPhone;

    @Column(value = "来源渠道", order = 2)
    private String customerSourceStrs;

    @Column(value = "全域结算金额", order = 3)
    private BigDecimal allRealityAmount;

    @Column(value = "累积消费笔数", order = 4)
    private String orderTotal;

    @Column(value = "全额退款笔数", order = 5)
    private String allRefundNum;

    @Column(value = "客单价", order = 6)
    private String perCustomerTransaction;

    @Column(value = "首次下单时间", order = 7)
    private String firstOrderTime;

    @Column(value = "最近下单时间", order = 8)
    private String lastOrderTime;

    @Column(value = "创建时间", order = 9)
    private String createTime;

}
