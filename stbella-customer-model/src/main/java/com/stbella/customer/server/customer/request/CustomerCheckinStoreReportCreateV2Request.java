package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "CustomerCheckinStoreReportCreateV2Request", description = "新增探店打卡报告request-v2版本")
public class CustomerCheckinStoreReportCreateV2Request implements Serializable {

    private static final long serialVersionUID = -8655927995722082231L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "basicUid")
    private Long basicUid;

    @ApiModelProperty(value = "clientUid")
    private Integer clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "手机号区域,  0-中国大陆 1-中国香港 2-中国澳门 3-中国台湾 4, 新加坡5, 韩国6, 美国,7,  英国,8,澳大利亚,9泰国")
    private Integer phoneZone;

    @ApiModelProperty(value = "客户手机号")
    private String clientPhone;

    @ApiModelProperty(value = "性别，1:男 2:女")
    private Integer clientGender;

    @ApiModelProperty(value = "关注的问题")
    private List<ConcernedIssueRequest> concernedIssueList;

    @ApiModelProperty(value = "特别需求")
    private String specialRequest;

    @ApiModelProperty(value = "门店套餐")
    private String storeGoods;

    @ApiModelProperty(value = "参观的套餐")
    private List<String> visitGoods;

    @ApiModelProperty(value = "参观日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitDate;

    @ApiModelProperty(value = "意向报价列表")
    private List<QuotationItem> intentionalQuotation;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店品牌，0:圣贝拉 1:小贝拉")
    private Integer storeType;

    @ApiModelProperty(value = "销售id")
    private Integer saleId;

    @ApiModelProperty(value = "销售姓名")
    private String saleName;

    @ApiModelProperty(value = "销售手机号")
    private String salePhone;

    @ApiModelProperty(value = "版本号: 1:第一个版本(默认), 2:第二个版本")
    @NotNull(message = "版本号不能为空")
    private Integer version;

    @ApiModelProperty(value = "状态，0:草稿箱 1:已发布")
    @NotNull(message = "状态不能为空，0:草稿箱 1:已发布")
    private Integer status;

    @ApiModelProperty(value = "品牌利益活动是否展示 0:不展示 1:展示")
    private Integer brandActivityShow;

    @ApiModelProperty(value = "品牌利益活动内容")
    private String brandActivityContent;

/*    @ApiModelProperty(value = "销售企微二维码是否展示 0:不展示 1:展示")
    private Integer saleQrCodeShow;

    @ApiModelProperty(value = "销售企微二维码地址")
    private String saleQrCodeUrl;*/

    @Data
    @ApiModel(value = "ConcernedIssueRequest", description = "客户关注问题")
    public static class ConcernedIssueRequest implements Serializable {

        private static final long serialVersionUID = 3578934666873103168L;

        @ApiModelProperty(value = "关注问题的标题")
        private String title;

        @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
        private Integer isSelect;

        @ApiModelProperty(value = "组件类型")
        private String type;

        @ApiModelProperty(value = "是否有更多")
        private Integer showMore;

        @ApiModelProperty(value = "id")
        private Integer id;

        @ApiModelProperty(value = "选项列表")
        private List<CustomerCheckinStoreReportCreateRequest.ConcernedIssueRequest.ConcernedIssueOptionRequest> optionList;

        @Data
        @ApiModel(value = "ConcernedIssueOptionRequest", description = "客户关注问题")
        public static class ConcernedIssueOptionRequest implements Serializable {

            private static final long serialVersionUID = -1797534031974401546L;

            @ApiModelProperty(value = "配图")
            private String url;

            @ApiModelProperty(value = "选项名称")
            private String optionName;

            @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
            private Integer isSelect;

            @ApiModelProperty(value = "内容类型，0:无内容 1:字符串类型 2:数组类型")
            private Integer contentType;

            @ApiModelProperty(value = "选项内容描述(String类型)")
            private String content;

            @ApiModelProperty(value = "最大长度")
            private Integer maxlength;

            @ApiModelProperty(value = "placeholder")
            private String placeholder;

            @ApiModelProperty(value = "组件类型")
            private String type;

            @ApiModelProperty(value = "输入内容")
            private String value;

            @ApiModelProperty(value = "尾部文字")
            private String inputNext;

            @ApiModelProperty(value = "选项内容(List类型)")
            private List<CustomerCheckinStoreReportCreateRequest.ConcernedIssueRequest.ConcernedIssueOptionRequest.ConcernedIssueOptionContentRequest> list;

            @Data
            @ApiModel(value = "ConcernedIssueOptionContentRequest", description = "子选项内容")
            public static class ConcernedIssueOptionContentRequest implements Serializable {

                private static final long serialVersionUID = 4359925294866985045L;

                @ApiModelProperty(value = "子选项内容名称")
                private String label;

                @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
                private Integer isSelect;

                @ApiModelProperty(value = "子选项内容描述")
                private String content;
            }
        }
    }

    @Data
    @ApiModel(value = "QuotationItem", description = "报价单")
    public static class QuotationItem implements Serializable {

        private static final long serialVersionUID = 3983748254646214776L;

        @ApiModelProperty(value = "购物车id")
        private Integer cartId;

        @ApiModelProperty(value = "报价单订单id")
        private Integer orderId;

        @ApiModelProperty(value = "物品清单是否展示, 0-不展示 1-展示")
        private Integer goodsListShowFlag;
    }
}
