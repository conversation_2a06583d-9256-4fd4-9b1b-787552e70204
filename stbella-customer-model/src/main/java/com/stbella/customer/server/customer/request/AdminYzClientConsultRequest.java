package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "AdminYzClientConsultRequest", description = "新增occRequest")
public class AdminYzClientConsultRequest implements Serializable {

    private static final long serialVersionUID = -7131693048034951817L;

    @ApiModelProperty(value = "添加人的ID admin id")
    private Long sellerId;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "分娩医院")
    private String hospital;

    @ApiModelProperty(value = "预产期")
    private Date predictBornTime;

    @ApiModelProperty(value = "孕几周")
    private Integer gestationWeekNow;

    @ApiModelProperty(value = "客户备注")
    private String clientRemark;

    @ApiModelProperty(value = "标签")
    private String tagIds;

    @ApiModelProperty(value = "到店时间")
    private Date arrivalTime;

    @ApiModelProperty(value = "咨询时间")
    private Date consultingTime;

    @ApiModelProperty(value = "来源")
    private Integer fromType;

    @ApiModelProperty(value = "拓展属性id")
    private Integer fromTypeExtendId;

    @ApiModelProperty(value = "预约日期")
    private Date skuExtendDay;

    @ApiModelProperty(value = "预约小时区间ID")
    private Long skuExtendId;

    @ApiModelProperty(value = "预约人数")
    private Integer peopleAppointNum;

    @ApiModelProperty(value = "预约备注")
    private String appointRemark;

    @ApiModelProperty(value = "省")
    private Integer province;

    @ApiModelProperty(value = "市")
    private Integer city;

    @ApiModelProperty(value = "区")
    private Integer region;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "关注")
    private Integer attention;

    @ApiModelProperty(value = "预算")
    private Integer budget;

    @ApiModelProperty(value = "电话类型")
    private Integer phoneType;

    @ApiModelProperty(value = "注册来源")
    private Integer source;

    @ApiModelProperty(value = "尘锋scrmId")
    private Long chenfengId;

    @ApiModelProperty(value = "胎次")
    private Integer predictBornNum;

    @ApiModelProperty(value = "家庭组")
    private String familyTag;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "")
    private Long upId;

    @ApiModelProperty(value = "注册来源")
    private Integer registerFrom;

    @ApiModelProperty(value = "在钉钉群里报客资,0报，1不报")
    private Integer dingDing;

    @ApiModelProperty(value = "?")
    private Integer pi;

    @ApiModelProperty(value = "")
    private Integer projectId;

}
