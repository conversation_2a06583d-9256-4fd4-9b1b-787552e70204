package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.ChannelSourceEnum;
import com.stbella.customer.server.customer.enums.CustomerAttentionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerArchiveEditBackRequest", description="后台修改客户基本信息")
public class CustomerArchiveEditBackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "customerId",required = true)
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    @ApiModelProperty(value = "姓名",required = true)
    @Size(max = 20,message = "姓名不能大于20个字符")
    @NotBlank(message = "姓名不能为空")
    private String customerName;

    @ApiModelProperty(value = "手机号-同主表的手机号码",required = true)
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "1\\d{10}", message = "手机号码必须为1开头11位数字")
    private String phoneNumber;

    @ApiModelProperty(value = "身份证号码")
    @Size(max = 30,message = "姓名不能大于30个字符")
    private String cardNo;

    @ApiModelProperty(value = "生日  yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty(value = "年龄")
    @Min(value = 1,message = "年龄最小1")
    @Max(value = 200,message ="年龄最大200" )
    private Integer age;

    @ApiModelProperty(value = "星座 0-白羊座、1-金牛座、2-双子座、3-巨蟹座、4-狮子座、5-处女座、6-天秤座、7-天蝎座、8-射手座、9-摩羯座、10-水瓶座、11-双鱼座")
    @Min(0)
    @Max(11)
    private Integer constellation;

    @ApiModelProperty(value = "身高 整数 单位cm")
    @DecimalMin(value = "1",message = "身高不能小于1")
    @DecimalMax(value = "300",message = "身高不能大于300")
    private BigDecimal height;

    @ApiModelProperty(value = "体重 整数 单位kg")
    @DecimalMin(value = "1",message = "体重不能小于1")
    @DecimalMax(value = "300",message = "体重不能大于300")
    private BigDecimal weight;

    @ApiModelProperty(value = "饮食禁忌")
    @Size(max = 50,message = "饮食禁忌字数不能大于50")
    private String foodProhibition;

    @ApiModelProperty(value = "所在区域 省-id")
    private Integer provinceId;

    @ApiModelProperty(value = "所在区域 市-id")
    private Integer cityId;

    @ApiModelProperty(value = "所在区域 区-id")
    private Integer districtId;

    @ApiModelProperty(value = "所在区域 省-name")
    private String provinceName;

    @ApiModelProperty(value = "所在区域 市-name")
    private String cityName;

    @ApiModelProperty(value = "所在区域 区-name")
    private String districtName;

    @ApiModelProperty(value = "家庭地址")
    @Size(max = 100,message = "家庭地址字数不能大于100")
    private String address;

    @ApiModelProperty(value = "客户关注")
    @EnumValue(enumClass = CustomerAttentionEnum.class, enumMethod = "isValidCode")
    private Integer attention;

    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "咨询日期")
    private Date consultDate;

    @ApiModelProperty(value = "家属电话")
    @Size(max = 25,message = "家属电话字数不能大于25")
    private String familyPhone;

    @ApiModelProperty(value = "紧急联系人")
    @Size(max = 30,message = "紧急联系人字数不能大于30")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人电话")
    @Size(max = 25,message = "紧急联系人电话字数不能大于25")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "基本信息备注")
    @Size(max = 200,message = "基本信息备注字数不能大于200")
    private String remark;

}
