package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
@ApiModel(value = "AllUserNumTotalVO", description = "活跃用户数vo")
public class AllUserNumTotalVO implements Serializable {
    private static final long serialVersionUID = -5331830584117969325L;


    @ApiModelProperty(value = "天 yyyy-mm-dd")
    private String date;

    @ApiModelProperty(value = "月 yyyy-mm")
    private String dateMonth;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "整数，数量")
    private Long num;

}
