package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "DataStatDateRequest", description = "数据统计日期查询请求")
public class DataStatDateRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 9116779088048610550L;

    @ApiModelProperty(value = "是否走缓存，0-不走 1-走")
    private Integer cache;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    private Date dateEnd;

    @ApiModelProperty(value = "分页offset值", hidden = true)
    private Integer offset;

    @ApiModelProperty(value = "每页记录数", hidden = true)
    private Integer limit;
}
