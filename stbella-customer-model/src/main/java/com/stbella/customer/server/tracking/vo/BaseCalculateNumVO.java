package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "BaseCalculateNumVO",description = "基本数据列表计算")
public class BaseCalculateNumVO implements Serializable {

    private static final long serialVersionUID = -4157928929812864661L;

    @ApiModelProperty(value = "合计")
    private Long totalValue;

    @ApiModelProperty(value = "均值")
    private BigDecimal avgValue;

    @ApiModelProperty(value = "数据列表")
    private List<BaseDateNumVO> dataList;
}
