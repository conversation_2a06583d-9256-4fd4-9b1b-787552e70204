package com.stbella.customer.server.nutuition.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 营养用户表
 *
 * </p>
 *
 * <AUTHOR> @since 2022-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_info_nutrition")
@ApiModel(value = "CustomerNutritionPO对象", description = "营养用户表")
public class CustomerNutritionPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户主表编号")
    private Long infoId;

    @ApiModelProperty(value = "头像")
    private String headPortrait;

    @ApiModelProperty(value = "年龄")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer age;

    @ApiModelProperty(value = "地址-省")
    private String addressProvince;

    @ApiModelProperty(value = "地址-市")
    private String addressCity;

    @ApiModelProperty(value = "地址-区")
    private String addressArea;

    @ApiModelProperty(value = "地址-详情")
    private String addressDetails;

    @ApiModelProperty(value = "预产期")
    private Date anticipateDate;

    @ApiModelProperty(value = "分娩方式  0-自然分娩；1-剖腹产；2-其他")
    private Integer deliveryMode;

    @ApiModelProperty(value = "生日")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date birthday;

    @ApiModelProperty(value = "邮箱")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String email;

    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "社会关系：0-配偶；1-父母；2-其他")
    private Integer socialRelations;

    @ApiModelProperty(value = "客户来源：0：自行开发；")
    private Integer source;

    @ApiModelProperty(value = "所属销售")
    private Long sellId;

    @ApiModelProperty(value = "所属销售名字")
    private String sellName;

    @ApiModelProperty(value = "标签编号：0-低意向；1-中意向；2-高意向")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer lebelId;

    @ApiModelProperty(value = "签约状态；0-未签约；1-已签约")
    private Integer contractStatus;

    @ApiModelProperty(value = "下单数量")
    private Integer orderNum;

    private Long createBy;

    private String createByName;

    private Long updateBy;

    private String updateByName;


}
