package com.stbella.customer.server.nursingboard.enums;


import com.stbella.customer.server.customer.enums.BaseEnum;

import java.math.BigDecimal;

/**
 * 折线图 枚举 上下限
 */
public enum BaseLineChartEnum implements BaseEnum<Integer, String> {


    //宝宝体重折线图上限 单位 g
    BABY_WEIGHT_UPPER_LIMIT(0, "2"),
    //宝宝体重折线图下限 单位 g
    BABY_WEIGHT_LOWER_LIMIT(1, "2"),

    //妈妈体重折线图上限 单位 KG
    MOM_WEIGHT_UPPER_LIMIT(2, "2"),
    //妈妈体重折线图下限 单位 KG
    MOM_WEIGHT_LOWER_LIMIT(3, "2"),

    //妈妈腰围折线图上限 单位 cm
    MOM_WAISTLINE_UPPER_LIMIT(4, "2"),
    //妈妈腰围折线图下限 单位 cm
    MOM_WAISTLINE_LOWER_LIMIT(5, "2"),

    //妈妈臀围折线图上限 单位 cm
    MOM_HIP_LINE_UPPER_LIMIT(6, "2"),
    //妈妈臀围折线图下限 单位 cm
    MOM_HIP_LINE_LOWER_LIMIT(7, "2"),

    //宝宝黄疸折线图上限 单位 mg/dl
    JAUNDICE_UPPER_LIMIT(8, "40.0"),

    //宝宝黄疸折线图下限 单位 mg/dl
    JAUNDICE_LOWER_LIMIT(9, "0.0"),

    //体温折线图下限 单位 ℃
    BODY_TEMPERATURE_LOWER_LIMIT(10, "35.0"),
    //体温折线图上限 单位 ℃
    BODY_TEMPERATURE_UPPER_LIMIT(11, "42.0")
    ;


    private final Integer code;

    private final String value;

    BaseLineChartEnum(Integer code, String value) {
        this.code = code;
        this.value = value;

    }


    @Override
    public Integer code() {
        return code;
    }

    @Override
    public String desc() {
        return value;
    }
}
