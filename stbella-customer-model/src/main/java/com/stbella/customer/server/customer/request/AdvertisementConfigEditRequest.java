package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value="AdvertisementConfigEditRequest", description="C端小程序编辑VO")
public class AdvertisementConfigEditRequest implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "0-首页 1-个人中心 2-会员中心")
    @NotNull(message = "pageType不能为空")
    private Integer pageType;

    @ApiModelProperty(value = "页面选首页：选项为 0-首页 ,  页面选个人中心：选项为 0-顶部广告位、1-腰部广告位")
    @NotNull(message = "module不能为空")
    private Integer module;

    @ApiModelProperty(value = "图片")
    @NotNull(message = "图片不能为空")
    private String pictureUrl;

    @ApiModelProperty(value = "跳转链接")
    private String link;

    @ApiModelProperty(value = "0- H5页面：链接; 1- 公众号文章：链接; 2- 视频号：链接; 3- 其他小程序：其他小程序APP ID; 4- 小程序页面(tab)链接; 5- 小程序页面(非tab)链接")
    private Integer linkType;

    @ApiModelProperty(value = "其他小程序appid")
    @Size(max = 500, message = "其他小程序appid最多500个字符")
    private String otherAppId;

    @ApiModelProperty(value = "权重")
    @NotNull(message = "权重不能为空")
    @Min(value = 1,message = "权重要大于0")
    @Max(value = 9999999,message = "权重最大为9999999")
    private Integer weight;

    @ApiModelProperty(value = "有效期开始")
    @NotNull(message = "有效期开始不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validStartDate;

    @ApiModelProperty(value = "有效期结束")
    @NotNull(message = "有效期结束不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndDate;

    @ApiModelProperty(value = "是否展示 0-不展示 1-展示")
    private Integer showFlag;

    @ApiModelProperty(value = "门店品牌 0-圣贝拉 1-小贝拉")
    private Integer storeBrand;

    @ApiModelProperty(value = "是否需要授权 0-不需要授权 1-需要授权")
    private Integer authFlag;

    @JsonIgnore
    private Long operator;
}
