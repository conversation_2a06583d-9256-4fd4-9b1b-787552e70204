package com.stbella.customer.server.nursingboard.vo;

import com.stbella.care.server.care.enmu.YesOrNoEnum;
import com.stbella.care.server.care.vo.api.CareBoardBaseVO.WriteMap;
import com.stbella.care.server.care.vo.carestation.check.form.customerbaby.CareCheckFormSpecialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "NurseBoardMomDataVO",description = "护理看板-妈妈护理数据")
public class NurseBoardMomDataVO implements Serializable {

    private static final long serialVersionUID = -4427576700631649459L;

    @ApiModelProperty(value = "胸围图表数据")
    private BaseLineChartVO bustData;

    @ApiModelProperty(value = "腰围图表数据")
    private BaseLineChartVO waistlineData;

    @ApiModelProperty(value = "臀围图表数据")
    private BaseLineChartVO hipLineData;

    @ApiModelProperty(value = "体重图表数据")
    private BaseLineChartVO weightData;

    @ApiModelProperty(value = "乳房状态")
    private BaseIndexVO breast;

    @ApiModelProperty(value = "挤奶量")
    private BaseIndexVO milkingAmount;

    //新增版本判断
    @ApiModelProperty(value = "特殊逻辑")
    private CareCheckFormSpecialVO specialLogicVO;

    // 新增乳房状态字段 挤奶量
    @ApiModelProperty(value = "乳房状态-R")
    private BaseIndexVO breastR;

    @ApiModelProperty(value = "乳房状态-L")
    private BaseIndexVO breastL;

    @ApiModelProperty(value = "宫底")
    private BaseIndexVO uterineBottom;

    @ApiModelProperty(value = "恶露")
    private BaseIndexVO lochia;

    @ApiModelProperty(value = "体温图表数据")
    private BaseLineChartVO bodyTemperatureData;

    @ApiModelProperty(value = "舒张压(mmHg)")
    private BaseIndexVO diastolicBloodPressure;

    @ApiModelProperty(value = "收缩压(mmHg)")
    private BaseIndexVO systolicBloodPressure;

    //新增枚举 异常 com.stbella.care.server.care.enmu.CustomerCheckEnum.Papilla com.stbella.care.server.care.enmu.CheckEnum.Nipple
    @ApiModelProperty(value = "乳头")
    private BaseIndexVO nipple;

    @ApiModelProperty(value = "宫体")
    private BaseIndexVO uterineBody;

    @ApiModelProperty(value = "伤口")
    private BaseIndexVO hurt;

    @ApiModelProperty(value = "本次入住护理记录日期")
    private List<WriteMap> writeMap;

    @ApiModelProperty(value = "是否存在异常, 0-不存在, 1-存在")
    private Integer exception = 0;

    //新增字段
    @ApiModelProperty(value = "是否小月子，1是0否（走订单逻辑）")
    private Integer isSmallMonth = YesOrNoEnum.NO.getCode();

    private Integer gestationWeek;

    private Integer gestationDay;

    @ApiModelProperty(value = "脉搏")
    private BaseIndexVO pulse;

    @ApiModelProperty(value = "吸奶数据")
    private MomBreastFeedDetail breastFeedDetail;

    @Data
    @ApiModel(value = "MomBreastFeedDetail", description = "吸奶数据")
    public static class MomBreastFeedDetail implements Serializable {

        private static final long serialVersionUID = -6295326447539484683L;

        @ApiModelProperty(value = "吸奶总次数")
        private BaseIndexVO milkingTotalTimes;

        @ApiModelProperty(value = "吸奶总量")
        private BaseIndexVO milkingTotalAmount;

        @ApiModelProperty(value = "吸奶量-右")
        private BaseIndexVO milkingAmountR;

        @ApiModelProperty(value = "吸奶量-左")
        private BaseIndexVO milkingAmountL;
    }

}
