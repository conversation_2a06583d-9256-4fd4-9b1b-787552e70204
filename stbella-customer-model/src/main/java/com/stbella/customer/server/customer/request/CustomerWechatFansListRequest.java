package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description: 根据unionId获取粉丝数据
 * @author: Seven
 * @time: 2022/12/12 14:09
 */
@Data
public class CustomerWechatFansListRequest implements Serializable {

    @ApiModelProperty("unionId")
    @NotNull(message = "unionId不能为空")
    private String unionId;

    @ApiModelProperty("来源 1）小贝拉 2）圣贝拉")
    private Integer source;
}
