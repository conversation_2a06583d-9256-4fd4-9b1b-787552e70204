package com.stbella.customer.server.customer.constant;

/**
 * 发送企微通知模板
 */
public class MessageConstant {

    // 美团新客资预约通知400消息模版
    public static final String MEITUAN_NEWLEADS_APPOINTMENT_400_MESSAGE = "大众点评预约客户来啦\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\n"
        + "手机号：%s\n"
        + "渠道：%s\n"
        + "门店：%s\n"
        + "留资来源：%s\n"
        + "对应门店是否已有客资：%s\n"
        + "%s";

    // 抖音新客资预约通知400模版消息
    public static final String DOUYIN_NEWLEADS_APPOINTMENT_400_MESSAGE = "抖音预约客户来啦\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\n"
        + "手机号：%s\n"
        + "微信号：%s\n"
        + "渠道：%s\n"
        + "门店：%s\n"
        + "客户省份：%s\n"
        + "客户城市：%s\n"
        + "对应门店是否已有客资：%s\n"
        + "%s";

    // AI客服新客资通知400模版消息
    public static final String AI_NEW_LEAD_400_MESSAGE = "客户留资\n"
        + "联系方式：%s\n"
        + "品牌：%s\n"
        + "渠道：%s\n"
        + "预产期：%s\n"
        + "意向服务：%s\n"
        + "所在城市：%s\n"
        + "意向门店：%s\n"
        + "意向套餐：%s\n"
        + "预算：%s\n"
        + "scrm是否已有客资：%s\n"
        + "备注：%s\n"
        + "%s";
}
