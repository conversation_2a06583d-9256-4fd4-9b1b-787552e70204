package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "ApiDetailListRequest", description = "接口分析-接口详情列表请求类")
public class ApiDetailListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 2194745279289988373L;

    @ApiModelProperty(value = "接口路径")
    private String url;

    @ApiModelProperty(value = "品牌类型 0圣贝拉 1小贝拉")
    private Integer brandType;

    @ApiModelProperty(value = "手机或名称")
    private String clientNameOrPhone;

    @ApiModelProperty(value = "请求结果 0失败 1成功 2异常")
    private Integer status;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    private Date dateEnd;

}
