package com.stbella.customer.server.customer.request;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.care.server.care.enmu.YesOrNoEnum;
import com.stbella.core.base.BasicReq;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerPostpartumSaveRequest extends BasicReq {

    private static final long serialVersionUID = 5999036053816510676L;

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @ApiModelProperty(value = "记录日期")
    @NotNull(message = "记录日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordDate;

    @ApiModelProperty(value = "体重")
    @Min(value = 30,message = "体重最小为30")
    @Max(value = 150,message = "体重最大为150")
    private BigDecimal weight;

    @ApiModelProperty(value = "胸围")
    @Min(value = 0,message = "胸围最小为0")
    @Max(value = 200,message = "胸围最大为200")
    private BigDecimal breastCircumference;

    @ApiModelProperty(value = "臀围")
    @Min(value = 0,message = "臀围最小为0")
    @Max(value = 200,message = "臀围最大为200")
    private BigDecimal hipCircumference;

    @ApiModelProperty(value = "腹围")
    @Min(value = 0,message = "腹围最小为0")
    @Max(value = 200,message = "腹围最大为200")
    private BigDecimal abdomenCircumference;

    @ApiModelProperty(value = "大腿围左")
    @Min(value = 0,message = "大腿围左最小为0")
    @Max(value = 200,message = "大腿围左最大为200")
    private BigDecimal thighCircumferenceLeft;

    @ApiModelProperty(value = "大腿围右")
    @Min(value = 0,message = "大腿围右最小为0")
    @Max(value = 200,message = "大腿围右最大为200")
    private BigDecimal thighCircumferenceRight;

    @ApiModelProperty(value = "腋围")
    @Min(value = 0,message = "腋围最小为0")
    @Max(value = 200,message = "腋围最大为200")
    private BigDecimal armpitCircumference;

    @ApiModelProperty(value = "腰围")
    @Min(value = 0,message = "腰围最小为0")
    @Max(value = 200,message = "腰围最大为200")
    private BigDecimal waistCircumference;

    @ApiModelProperty(value = "意向项目是否选择")
    private Integer intentionProjectFlag;

    @ApiModelProperty(value = "意向项目选项 0-私密 1-美胸 2-美体 3-减肥 4-医美 5-面部 6-情绪疏导")
    private List<Integer> intentionProject;

    @ApiModelProperty(value = "其他备注")
    @Size(max = 50,message = "其他备注最大长度为50")
    private String remark;

    @ApiModelProperty(value = "任务节点 0-客户入馆第七天 1-宫体已入盆 2-离馆前3天")
    private Integer taskNode;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    private Long operateId;

    private String operateName;

    public static Boolean verifyNullData(CustomerPostpartumSaveRequest  request){
        if (Objects.isNull(request.getWeight())
                && Objects.isNull(request.getBreastCircumference())
                && Objects.isNull(request.getHipCircumference())
                && Objects.isNull(request.getAbdomenCircumference())
                && Objects.isNull(request.getThighCircumferenceLeft())
                && Objects.isNull(request.getThighCircumferenceRight())
                && Objects.isNull(request.getArmpitCircumference())
                && Objects.isNull(request.getWaistCircumference())) {
            return false;
        }
        return true;
    }

    public void verifyIntentionParam(){
        if (Objects.nonNull(this.intentionProjectFlag)
                && Objects.equals(YesOrNoEnum.YES.getCode(),this.intentionProjectFlag)){
            if (CollectionUtil.isEmpty(this.intentionProject)){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "意向项目选项不能为空");
            }
        }
        if (Objects.nonNull(this.taskNode)){
            if (Objects.isNull(this.orderNo)){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "订单号不能为空");
            }
        }
    }

}
