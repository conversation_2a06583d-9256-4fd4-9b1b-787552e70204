package com.stbella.customer.server.nutuition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomerSearchDTO {

    @ApiModelProperty(value = "营养客户表主键id")
    private Long customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "标签编号：0-低意向；1-中意向；2-高意向")
    private Integer lebelId;

    @ApiModelProperty(value = "签约状态；0-未签约；1-已签约")
    private Integer contractStatus;

    @ApiModelProperty(value = "销售id")
    private Long saleId;
}
