package com.stbella.customer.server.nutuition.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "营养客户表主键id")
    private Long customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "标签编号：0-低意向；1-中意向；2-高意向")
    private Integer lebelId;

    @ApiModelProperty(value = "签约状态；0-未签约；1-已签约")
    private Integer contractStatus;

    @ApiModelProperty(value = "套餐名称 本期还为做订单和套餐，所以先全部写死套餐A")
    private String packName;

    @ApiModelProperty(value = "是否展示编辑按钮")
    private Boolean ifDisplayButton;
}
