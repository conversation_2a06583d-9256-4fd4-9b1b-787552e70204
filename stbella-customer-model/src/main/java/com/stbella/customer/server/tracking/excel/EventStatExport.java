package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EventStatExport implements Serializable {


    private static final long serialVersionUID = 8156941536558431039L;

    @Column(value = "事件id", order = 1)
    private String eventId;

    @Column(value = "事件名称", order = 2)
    private String eventName;

    @Column(value = "触发次数", order = 3)
    private Long pageView;

    @Column(value = "触发人数", order = 4)
    private Long userView;

    public EventStatExport(String eventId, String eventName, Long pageView, Long userView) {
        this.eventId = eventId;
        this.eventName = eventName;
        this.pageView = pageView;
        this.userView = userView;
    }
}
