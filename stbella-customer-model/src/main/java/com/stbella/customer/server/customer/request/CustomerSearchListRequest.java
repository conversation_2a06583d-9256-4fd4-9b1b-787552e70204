package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerSearchListRequest", description="")
public class CustomerSearchListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

    @ApiModelProperty(value = "手机号码/姓名")
    private String keyword;

    @ApiModelProperty(value = "签单门店Id")
    private Long signStoreId;

    @ApiModelProperty(value = "客户状态不在入馆和离馆标识 0校验  1不校验")
    private Integer entryLeaveFlag;


}
