package com.stbella.customer.server.nutuition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;


/**
 * 社会关系
 */
public enum CustomerNutritionSocialRelationsEnum {

    MATE(0, "配偶"),
    PARENT(1, "父母"),
    OTHER(2, "其他");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CustomerNutritionSocialRelationsEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CustomerNutritionSocialRelationsEnum getEnumByCode(Integer code) {
        for (CustomerNutritionSocialRelationsEnum enums : CustomerNutritionSocialRelationsEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的渠道：" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (CustomerNutritionSocialRelationsEnum customerStatusEnum : values()) {
            if (Objects.equals(customerStatusEnum.code, code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CustomerNutritionSocialRelationsEnum enums : CustomerNutritionSocialRelationsEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的渠道：" + code);
    }
}