package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "ApiDetailListVO", description = "接口分析-接口详情")
public class ApiDetailListVO implements Serializable {

    private static final long serialVersionUID = -1107484529192619826L;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "接口")
    private String url;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;


    @ApiModelProperty(value = "请求耗时（毫秒）")
    private Long durationTime;

    @ApiModelProperty(value = "请求结果 0失败 1成功 2异常")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;
}
