package com.stbella.customer.server.nutuition.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerManagerVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "营养客户表主键id")
    private Long customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "头像")
    private String headPortrait;

    @ApiModelProperty(value = "客户来源：0：自行开发；")
    private Integer source;

    @ApiModelProperty(value = "所属销售")
    private Long saleId;

    @ApiModelProperty(value = "所属销售名字")
    private String saleName;

    @ApiModelProperty(value = "该客户的月子餐订单数量")
    private Integer orderNumber;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "是否展示编辑删除按钮 true展示 false不展示")
    private Boolean ifDisplayButton;

}
