package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "CreateStbellaDoVisitRequest", description = "创建预约门店request")
public class CreateStbellaDoVisitRequest implements Serializable {

    private static final long serialVersionUID = 8111233090590775851L;

    @ApiModelProperty(value = "用户全局id")
    private Integer basicUid;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "是否备孕 0否 1是")
    private Integer isPreparePregnancy;
    @ApiModelProperty(value = "门店id")
    private Integer storeId;
    @ApiModelProperty(value = "预产期")
    private Integer wantIn;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "客户类型")
    private Integer clientType;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;





}
