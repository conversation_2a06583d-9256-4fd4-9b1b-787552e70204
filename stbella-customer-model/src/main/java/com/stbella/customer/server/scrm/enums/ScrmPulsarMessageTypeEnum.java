package com.stbella.customer.server.scrm.enums;

import com.stbella.core.exception.BusinessException;
import com.stbella.customer.server.customer.enums.ErrorCodeEnum;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import lombok.Getter;

/**
 * scrm消息队列中消息类型
 */
public enum ScrmPulsarMessageTypeEnum {
    ACCOUNT_INFO_UPDATE(10001, "客户信息变更"),
    FOLLOW_UP_RECORD_UPDATE(10002, "跟进记录变更"),
    CHECKIN_STORE_CHANGE(10003, "打卡记录变更"),
    CONTACT_INFO_UPDATE(10004, "联系人信息变更"),
    OPPORTUNITY_ADD(10005, "增加商机"),
    OPPORTUNITY_DEL(10006, "删除商机"),
    OPPORTUNITY_UPDATE(10007, "更新商机"),
    DISTRIBUTION_CUSTOMER_TEAM_MEMBER(10008, "分配客户团队成员"),
    SALE_STORE_CONFIG_UPDATE(10009, "销售与门店关联关系变更"),
    USER_INFO_UPDATE(10010, "员工信息变更"),
    CUSTOMER_ADD_OPPORTUNITY(10011, "400新增商机"),
    CREATE_CUSTOMER_TO_SCRM(10012, "创建客户到scrm"),
    OCC_CLOCK_STORE_SIGNIN(10013, "occ同步探店打卡"),
    OPPORTUNITY_RECORD_TEMP_UPDATE(10014, "商机中间表变更"),
    MEMBER_BIRTHDAY_PRODUCTION_GIFT(10015, "会员生日礼赠产康"),
    SYNC_CUSTOMER_CHILDBIRTH_ROOM(10016, "分娩喜报同步"),
    ACTIVITY_SIGNUP_EMAIL(10017, "活动报名列表邮件发送"),
    CUSTOMER_OPPORTUNITY_STATUS_CHANGE(10018, "客户商机状态变更"),
    OPPORTUNITY_400_UPDATE(10019, "400商机变更"),
    OPPORTUNITY_TEAM_MEMBER_UPDATE(10020, "商机团队成员变更"),
    OPPORTUNITY_400_WIN(10021, "400商机赢单"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    ScrmPulsarMessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ScrmPulsarMessageTypeEnum of(Integer code) {
        Optional<ScrmPulsarMessageTypeEnum> first = Arrays.stream(values())
            .filter(o -> Objects.equals(o.code, code))
            .findFirst();

        if (first.isPresent()) {
            return first.get();
        } else {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_ENUM_VALUE.code().toString(), "scrm消息类型枚举未匹配");
        }
    }
}
