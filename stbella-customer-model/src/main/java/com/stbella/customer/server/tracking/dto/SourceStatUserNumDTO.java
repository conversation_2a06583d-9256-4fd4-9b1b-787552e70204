package com.stbella.customer.server.tracking.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 渠道统计用户的PV、UV
 */
@Data
@ApiModel(value = "SourceStatUserNumDTO", description = "渠道统计用户的PV、UV")
public class SourceStatUserNumDTO implements Serializable {

    private static final long serialVersionUID = -6242438824574769459L;

    @ApiModelProperty(value = "渠道key")
    private String sourceKey;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "访问人数")
    private Long userView;
}
