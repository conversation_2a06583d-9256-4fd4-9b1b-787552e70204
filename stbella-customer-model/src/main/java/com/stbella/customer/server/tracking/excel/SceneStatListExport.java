package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SceneStatListExport implements Serializable {

    private static final long serialVersionUID = -6651527220377169828L;

    @Column(value = "场景Key", order = 1)
    private String sourceKey;

    @Column(value = "场景名称", order = 2)
    private String sourceName;

    @Column(value = "场景备注", order = 3)
    private String sourceContent;

    @Column(value = "访问人数", order = 4)
    private Long userView;

    @Column(value = "访问次数", order = 5)
    private Long pageView;

    @Column(value = "新增用户数", order = 6)
    private Long newUser;
}
