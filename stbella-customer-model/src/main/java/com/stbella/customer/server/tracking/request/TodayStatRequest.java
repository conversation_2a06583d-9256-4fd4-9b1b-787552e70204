package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "ToDayStatRequest", description = "今日统计请求")
public class TodayStatRequest implements Serializable {

    private static final long serialVersionUID = -9061585221856985977L;

    @ApiModelProperty(value = "时间, 毫秒时间戳")
    private Date date;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "是否读缓存，0-不读缓存 1-读缓存")
    private Integer cache;

}
