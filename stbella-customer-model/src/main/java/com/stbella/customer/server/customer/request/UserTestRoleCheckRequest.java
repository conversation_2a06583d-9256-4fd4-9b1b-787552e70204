package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value="UserTestRoleCheckRequest", description="用户是否拥有品牌用户中心演示权限请求")
public class UserTestRoleCheckRequest implements Serializable {

    private static final long serialVersionUID = 1027618702924695607L;

    @ApiModelProperty(value = "用户basicId")
    @NotNull(message = "basicId不能为空")
    private Long basicId;

    @ApiModelProperty(value = "品牌类型 ，0-圣贝拉 1-小贝拉")
    @NotNull(message = "storeBrand不能为空，品牌类型0-圣贝拉 1-小贝拉")
    private Integer storeBrand;

    @ApiModelProperty(value = "品牌类型 ，0-圣贝拉 1-小贝拉")
    @NotNull(message = "brandType不能为空，品牌类型0-圣贝拉 1-小贝拉")
    private Integer brandType;
}
