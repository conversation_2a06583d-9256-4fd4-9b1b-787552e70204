package com.stbella.customer.server.tracking.request;

import com.stbella.customer.server.tracking.annotations.NullOrAlphanumeric;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value="CustomerDataTrackingSessionRequest", description="会话上报请求类")
public class CustomerDataTrackingSessionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的session")
    private String sessionid;

    @ApiModelProperty(value = "微信渠道来源")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的来源")
    private String source;

    @ApiModelProperty(value = "微信渠道来源场景（自定义）")
    @NullOrAlphanumeric(message = "错误的场景")
    private String scene;

    @ApiModelProperty(value = "微信渠道来源场景key值")
    private Integer sceneKey;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "停留时长")
    private Long durationTime;

    @ApiModelProperty(value = "页面地址")
    @NullOrAlphanumeric(message = "错误的页面")
    private String pagePath;

}
