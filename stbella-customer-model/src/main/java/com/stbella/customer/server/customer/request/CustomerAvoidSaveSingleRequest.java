package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasicReq;
import com.stbella.customer.server.customer.enums.CustomerAvoidEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerAvoidSaveSingleRequest extends BasicReq implements Serializable {

    private static final long serialVersionUID = -9054839250054533874L;

    @ApiModelProperty(value = "客户basicId")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "订单号(非必须,只作数据来源记录)")
    private String orderNo;

    /**
     * @see CustomerAvoidEnum
     */
    @NotNull(message = "禁忌类型不能为空")
    @ApiModelProperty(value = "禁忌类型 0:餐食禁忌 1:过敏食物 2:客户喜好")
    private Integer avoidType;

    @Valid
    @ApiModelProperty(value = "客户饮食禁忌")
    private List<FoodAvoid> foodAvoids;

    @Data
    public static class FoodAvoid implements Serializable {

        private static final long serialVersionUID = 3982523690678134134L;

        @ApiModelProperty(value = "食物id stbella-care.food_repo")
        private Long foodId;

        @ApiModelProperty(value = "标签分类 0:默认 1:其他 2:无")
        private Integer tagCls;

        @ApiModelProperty(value = "客户喜好avoidType=2/其他标签数据")
        private String content;
    }

    public Long operatorId() {
        try {
            if (Objects.nonNull(this.getOperator())) {
                return Long.valueOf(this.getOperator().getOperatorGuid());
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public String operatorName() {
        try {
            if (Objects.nonNull(this.getOperator())) {
                return this.getOperator().getOperatorName();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
