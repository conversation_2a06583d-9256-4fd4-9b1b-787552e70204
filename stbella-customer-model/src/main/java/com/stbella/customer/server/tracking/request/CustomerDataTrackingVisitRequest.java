package com.stbella.customer.server.tracking.request;

import com.stbella.customer.server.tracking.annotations.NullOrAlphanumeric;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value="CustomerDataTrackingVisitRequest", description="页面访问上报请求类")
public class CustomerDataTrackingVisitRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的session")
    private String sessionid;

    @ApiModelProperty(value = "页面地址")
    @Pattern(regexp = "^[a-zA-Z0-9-_?&/]+$", message = "错误的页面")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "停留时长")
    private Long durationTime;

    @ApiModelProperty(value = "下一页面地址")
    @NullOrAlphanumeric(message = "错误的页面")
    private String pagePathNext;

    @ApiModelProperty(value = "上一页面地址")
    @NullOrAlphanumeric(message = "错误的页面")
    private String pagePathPre;

    @ApiModelProperty(value = "事件id")
    @NullOrAlphanumeric(message = "错误的事件")
    private String eventId;

    @ApiModelProperty(value = "事件name")
    private String eventName;

    @ApiModelProperty(value = "事件备注")
    private String eventContent;

    @ApiModelProperty(value = "事件查询条件")
    private String eventQuery;

    @ApiModelProperty(value = "事件类型")
    @NullOrAlphanumeric(message = "错误的类型")
    private String eventType;

}
