package com.stbella.customer.server.scrm.request;

import com.stbella.customer.server.scrm.ScrmRequestParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "AccountInfoRequest", description = "scrm端客户信息请求")
public class AccountInfoRequest implements Serializable {

    private static final long serialVersionUID = 6572774039343568751L;

    @ApiModelProperty(value = "客户在scrm中的id")
    private Long id;

    /*
    根据业务方需求，我们客户的业务类型有两种，一种为默认，一种为企业客户
    当客户没有手机号时，默认创建为企业客户，其他为默认类型
     */
    @ApiModelProperty(value = "客户类型")
    private Long entityType;

    @ApiModelProperty(value = "客户所有人")
    private Long ownerId;

    @ApiModelProperty(value = "客户所有人手机号")
    private String ownerPhone;

    @ApiModelProperty(value = "客户名称")
    private String accountName;

    @ApiModelProperty(value = "客户级别")
    private Integer level;

    @ApiModelProperty(value = "上级客户")
    private Long parentAccountId;

    @ApiModelProperty(value = "行业")
    private Integer industryId;

    @ApiModelProperty(value = "省份")
    @ScrmRequestParam(value = "account")
    private Integer fState;

    @ApiModelProperty(value = "市")
    @ScrmRequestParam(value = "account")
    private Integer fCity;

    @ApiModelProperty(value = "区")
    @ScrmRequestParam(value = "account")
    private Integer fDistrict;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "wxUnionID")
    private String wxUnionID;

    @ApiModelProperty(value = "好友类型")
    private Integer wxUserType;

    @ApiModelProperty(value = "是否为流失好友")
    private Integer loss;

    @ApiModelProperty(value = "外部联系人ID")
    private String externalUserId;

    @ApiModelProperty(value = "共享标签")
    private String sharedTags;

    @ApiModelProperty(value = "企业ID")
    private String corpId;

    @ApiModelProperty(value = "邮箱")
    private String accountEmail;

    @ApiModelProperty(value = "职位")
    private String wxPosition;

    @ApiModelProperty(value = "好友昵称")
    private String wxUserName;

    @ApiModelProperty(value = "来源方式")
    private Integer accountChannel;

    @ApiModelProperty(value = "好友头像")
    private String wxAvatarUrl;

    @ApiModelProperty(value = "最新跟进记录时间")
    private Long recentActivityRecordTime;

    @ApiModelProperty(value = "最新跟进人")
    private Long recentActivityCreatedBy;

    @ApiModelProperty(value = "企业简称")
    private String accountCorpName;

    @ApiModelProperty(value = "企业名称")
    private String accountCorpFullName;

    @ApiModelProperty(value = "创建日期")
    private Long createdAt;

    @ApiModelProperty(value = "客户来源")
    private Integer[] highSeaAccountSource;

    @ApiModelProperty(value = "创建人")
    private Long createdBy;

    @ApiModelProperty(value = "最新修改日")
    private Long updatedAt;

    @ApiModelProperty(value = "最新修改人")
    private Long updatedBy;

    @ApiModelProperty(value = "审批提交人")
    private Long applicantId;

    @ApiModelProperty(value = "区域")
    private Long territoryId;

    @ApiModelProperty(value = "所属部门")
    private Long dimDepart;

    @ApiModelProperty(value = "审批状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "锁定状态")
    private Integer lockStatus;

    @ApiModelProperty(value = "工商注册")
    private Integer srcFlg;

    @ApiModelProperty(value = "关联企微联系人")
    private Long qwContactId;

    @ApiModelProperty(value = "免打扰")
    private Boolean doNotDisturb;

    @ApiModelProperty(value = "外部部门")
    private Long outterDepartId;

    @ApiModelProperty(value = "签约数量")
    private Double totalWonOpportunities;

    @ApiModelProperty(value = "实际收款金额")
    private Double paidAmount;

    @ApiModelProperty(value = "应收余额（欠款）")
    private Double invoiceBalance;

    @ApiModelProperty(value = "签约总金额")
    private Double totalWonOpportunityAmount;

    @ApiModelProperty(value = "未收款金额")
    private Double unpaidAmount;

    @ApiModelProperty(value = "未出应收账金额")
    private Double amountUnbilled;

    @ApiModelProperty(value = "实际应收账金额")
    private Double actualInvoicedAmount;

    @ApiModelProperty(value = "生效订单数")
    private Double totalActiveOrders;

    @ApiModelProperty(value = "订单总金额")
    private Double totalOrderAmount;

    @ApiModelProperty(value = "是否签约;是/否")
    private String isCustomer;

    @ApiModelProperty(value = "销售线索")
    private Long leadId;

    @ApiModelProperty(value = "计划内拜访数")
    private Double visitInplanCount;

    @ApiModelProperty(value = "拜访总数")
    private Double visitTotalCount;

    @ApiModelProperty(value = "最近拜访时间")
    private Long visitLatestTime;

    @ApiModelProperty(value = "未拜访天数")
    private Long visitUnvisitDay;

    @ApiModelProperty(value = "客户得分")
    private Double accountScore;

    @ApiModelProperty(value = "咨询日期")
    private Long customItem180__c;

    @ApiModelProperty(value = "到店日期")
    private Long customItem181__c;

    @ApiModelProperty(value = "是否关注公众号")
    private Integer customItem183__c;

    @ApiModelProperty(value = "是否注册小程序")
    private Integer customItem184__c;

    @ApiModelProperty(value = "孕周")
    private Double customItem188__c;

    @ApiModelProperty(value = "孕期状态")
    private String customItem189__c;

    @ApiModelProperty(value = "紧急联系人姓名")
    private String customItem190__c;

    @ApiModelProperty(value = "紧急联系人电话")
    private String customItem191__c;

    @ApiModelProperty(value = "与委托人关系")
    @ScrmRequestParam(value = "account")
    private Integer relationWithClient__c;

    @ApiModelProperty(value = "证件类型")
    @ScrmRequestParam(value = "account")
    private Integer certType__c;

    @ApiModelProperty(value = "证件号")
    private String idCard__c;

    @ApiModelProperty(value = "出生日期")
    private Long birthdate__c;

    @ApiModelProperty(value = "职业")
    private String profession__c;

    @ApiModelProperty(value = "血型")
    @ScrmRequestParam(value = "account")
    private Integer bloodType__c;

    @ApiModelProperty(value = "名族")
    private String nation__c;

    @ApiModelProperty(value = "星座")
    @ScrmRequestParam(value = "account")
    private Integer constellationType__c;

    @ApiModelProperty(value = "客户阶段")
    @ScrmRequestParam(value = "account")
    private Integer customerStage__c;

    @ApiModelProperty(value = "预产期")
    private Long predictBornDate__c;

    @ApiModelProperty(value = "入住日期")
    private Long checkInDate__c;

    @ApiModelProperty(value = "离馆日期")
    private Long checkOutDate__c;

    @ApiModelProperty(value = "实际入住天数")
    private Long inDays__c;

    @ApiModelProperty(value = "分娩日期")
    private Long bornTime__c;

    @ApiModelProperty(value = "分娩医院")
    private String hospital__c;

    @ApiModelProperty(value = "分娩方式")
    @ScrmRequestParam(value = "account")
    private Integer productionMode__c;

    @ApiModelProperty(value = "胎次")
    private Integer bornNum__c;

    @ApiModelProperty(value = "胎数")
    private Long fetusNum__c;

    @ApiModelProperty(value = "餐食禁忌")
    private Integer[] customItem202__c;

    @ApiModelProperty(value = "其他餐食要求")
    private String customItem203__c;

    @ApiModelProperty(value = "备房禁忌")
    private Integer[] customItem204__c;

    @ApiModelProperty(value = "其他备房禁忌")
    private String customItem205__c;

    @ApiModelProperty(value = "妊娠糖尿病")
    @ScrmRequestParam(value = "account")
    private Integer customItem206__c;

    @ApiModelProperty(value = "高血压")
    @ScrmRequestParam(value = "account")
    private Integer customItem207__c;

    @ApiModelProperty(value = "其他医疗注意事项")
    private String customItem210__c;

    @ApiModelProperty(value = "母婴护理宣教时间")
    @ScrmRequestParam(value = "account")
    private Integer customItem211__c;

    @ApiModelProperty(value = "晨间音乐疗愈时间")
    @ScrmRequestParam(value = "account")
    private Integer customItem212__c;

    @ApiModelProperty(value = "其他服务注意事项")
    private String customItem213__c;

    @ApiModelProperty(value = "嗅香味道")
    private Integer[] customItem214__c;

    @ApiModelProperty(value = "关注修复部位")
    private Integer[] customItem215__c;

    @ApiModelProperty(value = "按摩力度")
    @ScrmRequestParam(value = "account")
    private Integer customItem216__c;

    @ApiModelProperty(value = "希望参加时间")
    @ScrmRequestParam(value = "account")
    private Integer customItem217__c;

    @ApiModelProperty(value = "喜好类型")
    private Integer[] customItem218__c;

    @ApiModelProperty(value = "进房提醒")
    @ScrmRequestParam(value = "account")
    private Integer customItem219__c;

    @ApiModelProperty(value = "邀约模式")
    @ScrmRequestParam(value = "account")
    private Integer customItem220__c;

    @ApiModelProperty(value = "管家关怀频次")
    @ScrmRequestParam(value = "account")
    private Integer customItem221__c;

    @ApiModelProperty(value = "勿扰服务")
    private Integer[] customItem222__c;

    @ApiModelProperty(value = "其他服务要求")
    private String customItem223__c;

    @ApiModelProperty(value = "客房准备")
    private Integer[] customItem224__c;

    @ApiModelProperty(value = "其他准备")
    private String customItem225__c;

    @ApiModelProperty(value = "特殊身份")
    private Integer[] customItem226__c;

    @ApiModelProperty(value = "VIP等级")
    private Integer customItem227__c;

    @ApiModelProperty(value = "预约门店")
    private String customItem228__c;

    @ApiModelProperty(value = "是否重新分配预约门店")
    private Integer customItem229__c;

    @ApiModelProperty(value = "客户状态")
    @ScrmRequestParam(value = "account")
    private Integer customItem230__c;

    @ApiModelProperty(value = "线上来源渠道")
    @ScrmRequestParam(value = "account")
    private Integer customItem231__c;

    @ApiModelProperty(value = "线下来源渠道")
    @ScrmRequestParam(value = "account")
    private Integer customItem232__c;

    @ApiModelProperty(value = "微信号")
    private String customItem233__c;

    @ApiModelProperty(value = "传染病检查")
    private String customItem234__c;

    @ApiModelProperty(value = "药物过敏史")
    private String customItem235__c;

    @ApiModelProperty(value = "无效标签")
    private Integer customItem236__c;

    @ApiModelProperty(value = "预产期剩余天数")
    private Double customItem237__c;

    @ApiModelProperty(value = "赢单销售")
    private Long customItem238__c;

    @ApiModelProperty(value = "服务管家")
    private Long customItem239__c;

    @ApiModelProperty(value = "服务产康师")
    private Long customItem240__c;

    @ApiModelProperty(value = "圣贝拉会员等级")
    private Integer customItem242__c;

    @ApiModelProperty(value = "组织名称")
    private String customItem245__c;

    @ApiModelProperty(value = "职位")
    private String customItem246__c;

    @ApiModelProperty(value = "关系人姓名")
    private String customItem247__c;

    @ApiModelProperty(value = "关系")
    private String customItem248__c;

    @ApiModelProperty(value = "VIP品牌")
    @ScrmRequestParam(value = "account")
    private Integer customItem249__c;

    @ApiModelProperty(value = "VIP类型")
    @ScrmRequestParam(value = "account")
    private Integer customItem250__c;

    @ApiModelProperty(value = "备注")
    private String customItem251__c__c;

    @ApiModelProperty(value = "来源拓展信息")
    @ScrmRequestParam(value = "account")
    private Integer customItem253__c;

    @ApiModelProperty(value = "服务管家2")
    private Long customItem254__c;

    @ApiModelProperty(value = "月子套餐预算")
    @ScrmRequestParam(value = "account")
    private Integer customItem255__c;

    @ApiModelProperty(value = "小贝拉会员等级")
    private Integer customItem261__c;

    @ApiModelProperty(value = "手机号类型")
    @ScrmRequestParam(value = "account")
    private Integer customItem262__c;

    @ApiModelProperty(value = "opendid")
    private String openid__c;

    @ApiModelProperty(value = "性别")
    private Integer gender__c;

    @ApiModelProperty(value = "一胎月子方式")
    private String customItem265__c;

    @ApiModelProperty(value = "客户关注点")
    private Integer[] customItem266__c;

    @ApiModelProperty(value = "购买意向")
    private String purchaseintention__c;

    @ApiModelProperty(value = "微信小程序来源渠道拓展")
    private String customItem268__c;

    @ApiModelProperty(value = "艾屿会员等级")
    private Integer islaMemberLevel__c;

    @ApiModelProperty(value = "客户预算")
    private Integer customItem270__c;

    @ApiModelProperty(value = "客户等级")
    @ScrmRequestParam(value = "account")
    private Integer customerLevel__c;

    @ApiModelProperty(value = "产康来源渠道")
    private Integer productionFromType__c;

    @ApiModelProperty(value = "产康阶段")
    private Integer customItem277__c;

    @ApiModelProperty(value = "予家来源渠道")
    private Integer ctsFromType__c;

    @ApiModelProperty(value = "予家来源渠道月子会所门店")
    @ScrmRequestParam(value = "account")
    private Integer ctsFromTypeStore__c;

    @ApiModelProperty(value = "予家上户城市")
    @ScrmRequestParam(value = "account")
    private Integer serveCityId__c;
}
