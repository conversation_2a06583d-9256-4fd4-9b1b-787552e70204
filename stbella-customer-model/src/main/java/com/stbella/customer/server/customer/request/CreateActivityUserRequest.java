package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Valid
@ApiModel(value = "CreateActivityUserRequest", description = "C端-周年庆活动用户报名Request")
public class CreateActivityUserRequest implements Serializable {

    private static final long serialVersionUID = -1113898222620308938L;


    @ApiModelProperty(value = "品牌类型 0-圣贝拉 1-小贝拉")
    @NotNull(message = "brandType不能为空")
    private Integer brandType;

    @ApiModelProperty(value = "周年庆活动表id")
    @NotNull(message = "activityId不能为空")
    private Integer activityId;

    @ApiModelProperty(value = "活动场地id")
    @NotNull(message = "activitySiteId不能为空")
    private Integer activitySiteId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    @NotNull(message = "userPhone不能为空")
    private String userPhone;

    @ApiModelProperty(value = "用户全局id")
    @NotNull(message = "basicUid不能为空")
    private Integer basicUid;

}
