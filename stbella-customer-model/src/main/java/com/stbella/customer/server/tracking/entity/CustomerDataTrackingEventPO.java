package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户C端点击事件埋点
 * </p>
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_event")
@ApiModel(value = "CustomerDataTrackingEventPO对象", description = "")
public class CustomerDataTrackingEventPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    private String sessionid;

    @ApiModelProperty(value = "来源渠道")
    private String source;

    @ApiModelProperty(value = "事件id")
    private String eventId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件内容")
    private String eventContent;

    @ApiModelProperty(value = "事件query")
    private String eventQuery;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

}
