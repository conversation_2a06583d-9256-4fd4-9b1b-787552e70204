package com.stbella.customer.server.nursingboard.enums;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;


/**
 * 时间格式
 */
public enum NursingBoardDateTimeEnum {


    ONE_DAY_MILLISECOND(0, "86400000"),
    NURSE_TIME_FORMAT(1, "MM/dd"),
    YYYY(2, "yyyy"),
    YYYY_MM_DD_SLASH_FORMAT(3, "yyyy/MM/dd"),
    MINUTES(4, "min"),
    YYYY_MM_DD(5, "yyyy-MM-dd");


    private final Integer code;
    private final String name;

    NursingBoardDateTimeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    public static NursingBoardDateTimeEnum fromCode(Integer code) {
        if (!Objects.isNull(code)) {
            return Arrays.stream(NursingBoardDateTimeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
        }
        return null;

    }

    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            Optional<NursingBoardDateTimeEnum> first = Arrays.stream(NursingBoardDateTimeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            if (first.isPresent()) {
                return first.get().getName();
            }
        }
        return null;

    }


    public static NursingBoardDateTimeEnum fromName(String name) {
        if (!StringUtils.isBlank(name)) {
            return Arrays.stream(NursingBoardDateTimeEnum.values()).filter(i -> Objects.equals(i.getName(), name)).findFirst().orElse(null);
        }
        return null;

    }
}
