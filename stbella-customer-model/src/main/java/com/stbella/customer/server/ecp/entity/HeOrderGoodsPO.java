package com.stbella.customer.server.ecp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单商品表
 * </p>
 *
 * <AUTHOR> @since 2023-11-08
 */
@Data
@Accessors(chain = true)
@TableName("he_order_goods")
@ApiModel(value = "HeOrderGoodsPO对象", description = "订单商品表")
public class HeOrderGoodsPO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_ID
    )
    private Long id;

    @ApiModelProperty(value = "作为附属商品存在时，主订单商品ID（当前表ID）")
    private Integer parentId;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "客户类型：0月子客户，5到家客户，2到家阿姨")
    private Integer clientType;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "同步自order支付状态：0待支付，1未全付，2已付清，3已取消")
    private Integer payStatus;

    @ApiModelProperty(value = "与goods表的good_type保持一致")
    private Integer goodsType;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "规格id")
    private Integer skuId;

    @ApiModelProperty(value = "商品ID（根据商品类型分布在不同类型的商品表，取主键id）")
    private Integer goodsId;

    @ApiModelProperty(value = "下单时的商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品缩略图")
    private String goodsImage;

    @ApiModelProperty(value = "购买数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品原单价")
    private Integer goodsPriceOrgin;

    @ApiModelProperty(value = "商品实付单价")
    private Integer goodsPricePay;

    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    @ApiModelProperty(value = "护理服务时长，单位：天")
    private Integer serviceDays;

    @ApiModelProperty(value = "物流状态：0无需发货，1待发货，2已发货/待签收，4已签收/待确认收货，3确认收货")
    private Integer sendStatus;

    @ApiModelProperty(value = "售后状态：0未发起，1已发起(售后中)，2已同意退货，4.已同意退款，3已打款")
    private Integer refundStatus;

    @ApiModelProperty(value = "订单是否关闭：0否，1是（退款完成，确认收货后n天）")
    private Integer isClose;

    @ApiModelProperty(value = "加购来源：同步自cart表的rource字段值，用于标示立即购买或加购来自哪里")
    private String source;

    private String content;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "预约商品选择的时间 天")
    private String skuExtendDay;

    @ApiModelProperty(value = "预约时间段")
    private String skuExtendValue;

    @ApiModelProperty(value = "时间段id")
    private Integer skuExtendId;

    @ApiModelProperty(value = "商城商品类型 0=实物 1=虚拟 2=预约, 3套餐")
    private Integer goodsSellType;

    @ApiModelProperty(value = "单个商品积分兑换数量")
    private Integer integral;

    @ApiModelProperty(value = "应付总积分数")
    private Integer payIntegral;

    @ApiModelProperty(value = "优惠券优惠金额，单位：分")
    private Integer couponAmount;

    @ApiModelProperty(value = "优惠券领取id")
    private Integer couponUserId;

    @ApiModelProperty(value = "70S-BRA订单类型用到的 件数")
    private Integer piece;

    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "房间名称")
    private String roomName;

    @ApiModelProperty(value = "ecp房型配置")
    private Integer ecpRoomType;

    @ApiModelProperty(value = "否礼赠: 0不是，1是")
    private Integer gift;

    @ApiModelProperty(value = "序号（关联用此字段）")
    private String orderGoodsSn;
}
