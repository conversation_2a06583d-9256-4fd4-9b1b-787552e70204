package com.stbella.customer.server.nursingboard.vo;

import com.stbella.care.server.care.vo.api.CareBoardBaseVO.WriteMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "NurseBoardBabyDataVO", description = "护理看板-宝宝护理数据")
public class NurseBoardBabyDataVO implements Serializable {

    private static final long serialVersionUID = -7309150865963634335L;

    @ApiModelProperty(value = "大便")
    private BaseIndexVO shit;

    @ApiModelProperty(value = "小便")
    private BaseIndexVO pee;

    @ApiModelProperty(value = "脐部")
    private BaseIndexVO umbilicus;

    @ApiModelProperty(value = "臀部")
    private BaseIndexVO hips;

    //皮肤新增枚举
    @ApiModelProperty(value = "皮肤")
    private BaseIndexVO skin;

    @ApiModelProperty(value = "体温")
    private BaseLineChartVO bodyTemperatureData;

    //C端宝宝哺乳方式原来就没有不用管
    @ApiModelProperty(value = "哺乳方式")
    private Integer lactationType;

    @ApiModelProperty(value = "喝奶量")
    private DrinkMilkDetail drinkMilkMl;

    @ApiModelProperty(value = "喝奶时长")
    private BaseIndexVO drinkMilkTime;

    @ApiModelProperty(value = "黄疸")
    private BaseLineChartVO jaundice;

    @ApiModelProperty(value = "体重数据列表")
    private BaseLineChartVO weightData;

    @ApiModelProperty(value = "本次入住护理记录日期")
    private List<WriteMap> writeMap;

    @ApiModelProperty(value = "是否存在异常, 0-不存在, 1-存在")
    private Integer exception = 0;

    @ApiModelProperty(value = "喂奶数据")
    private BreastFeedDetail breastFeedDetail;

    @ApiModelProperty(value = "排便数据")
    private DefecationDetail defecationDetail;

    @ApiModelProperty(value = "吸奶总次数")
    private BaseIndexVO momMilkingTotalTimes;

    @ApiModelProperty(value = "吸奶总量")
    private BaseIndexVO momMilkingTotalAmount;

    @Data
    @ApiModel(value = "DrinkMilkDetail", description = "喝奶量详情")
    public static class DrinkMilkDetail implements Serializable {

        private static final long serialVersionUID = -9116398333523018466L;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "单位")
        private String unit;

        @ApiModelProperty(value = "喝奶量总值")
        private String countValue;

        @ApiModelProperty(value = "较昨日变化值")
        private String contrastYesterdayIndexValue;

        @ApiModelProperty(value = "指标昨日值")
        private String yesterdayIndexValue;

        @ApiModelProperty("瓶喂母乳次数")
        private String momBottleNum = "";

        @ApiModelProperty("瓶喂母乳量")
        private String suckleMomCC = "";

        @ApiModelProperty(value = "瓶喂母乳较昨日变化值")
        private String contrastMomBottleYesterdayValue;

        @ApiModelProperty(value = "瓶喂母乳昨日值")
        private String momBottleYesterdayIndexValue;

        @ApiModelProperty(value = "配方奶次数")
        private String suckleRecipeNum = "";

        @ApiModelProperty(value = "配方奶量")
        private String suckleRecipeCC = "";

        @ApiModelProperty(value = "配方奶较昨日变化值")
        private String contrastSuckleRecipeYesterdayValue;

        @ApiModelProperty(value = "配方奶昨日值")
        private String suckleRecipeYesterdayValue;


    }

    @Data
    @ApiModel(value = "BreastFeedDetail", description = "喂奶数据")
    public static class BreastFeedDetail implements Serializable {

        private static final long serialVersionUID = 7693533068764440308L;

        @ApiModelProperty(value = "亲喂时长(min)")
        private BaseIndexVO oneselfFeedDuration;

        @ApiModelProperty(value = "亲喂次数(次)")
        private BaseIndexVO suckleMonNum;

        @ApiModelProperty(value = "瓶喂总量(ml)")
        private BaseIndexVO suckleMomCC;

        @ApiModelProperty(value = "瓶喂次数(次)")
        private BaseIndexVO momBottleNum;

        @ApiModelProperty(value = "配方奶总量(ml)")
        private BaseIndexVO suckleRecipeCC;

        @ApiModelProperty(value = "配方奶次数(次)")
        private BaseIndexVO suckleRecipeNum;

        @ApiModelProperty(value = "总喂奶次数(次)")
        private BaseIndexVO totalFeedNum;

        @ApiModelProperty (value = "喂奶总量(ml)")
        private BaseIndexVO totalSuckleCC;

        @ApiModelProperty(value = "喂奶记录详情")
        private List<RecordDetail> recordDetailList;
    }

    @Data
    @ApiModel(value = "DefecationDetail", description = "排便数据")
    public static class DefecationDetail implements Serializable {

        private static final long serialVersionUID = -3558377456539486811L;

        @ApiModelProperty(value = "大便次数(次)")
        private BaseIndexVO shitNum;

        @ApiModelProperty(value = "小便次数(次)")
        private BaseIndexVO peeNum;

        @ApiModelProperty(value = "排便记录详情")
        private List<RecordDetail> recordDetailList;
    }

    @Data
    @ApiModel(value = "RecordDetail", description = "记录详情")
    public static class RecordDetail implements Serializable {

        private static final long serialVersionUID = -202646105231243272L;

        @ApiModelProperty(value = "记录时间段")
        private String timeName;

        @ApiModelProperty(value = "记录内容")
        private String content;
    }

}
