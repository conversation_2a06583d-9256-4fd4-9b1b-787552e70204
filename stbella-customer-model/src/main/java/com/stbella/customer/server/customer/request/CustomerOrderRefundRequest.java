package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "CustomerOrderRefundRequest", description = "c端-商城订单退款申请或取消Request")
public class CustomerOrderRefundRequest implements Serializable {

    private static final long serialVersionUID = 5549562110186750890L;

    @ApiModelProperty(value = "主键id")
    private Integer applyAmount;

    @ApiModelProperty(value = "订单id")
    private Integer id;

    @ApiModelProperty(value = "商品id")
    private Integer orderGoodsId;

    @ApiModelProperty(value = "退款记录id")
    private Integer refundId;

    @ApiModelProperty(value = "退款理由")
    private String remark;


}
