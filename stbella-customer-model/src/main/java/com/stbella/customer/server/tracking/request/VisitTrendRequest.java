package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "VisitTrendRequest" ,description = "页面统计趋势")
public class VisitTrendRequest implements Serializable {
    private static final long serialVersionUID = -3459483014615658621L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "页面名称")
    @NotNull(message = "页面名称不能为空")
    private String pageName;

    @ApiModelProperty(value = "页面路径")
    @NotNull(message = "页面路径不能为空")
    private String pagePath;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    @NotNull(message = "开始时间不能为空")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    @NotNull(message = "结束时间不能为空")
    private Date dateEnd;

}
