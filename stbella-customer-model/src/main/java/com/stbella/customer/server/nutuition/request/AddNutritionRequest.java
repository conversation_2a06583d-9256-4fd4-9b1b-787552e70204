package com.stbella.customer.server.nutuition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> @since 2021-11-25
 */
@Data
@ApiModel(value = "AddNutritionRequest", description = "新增客户入参")
public class AddNutritionRequest extends NutritionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户主表主键id")
    private Long id;

}
