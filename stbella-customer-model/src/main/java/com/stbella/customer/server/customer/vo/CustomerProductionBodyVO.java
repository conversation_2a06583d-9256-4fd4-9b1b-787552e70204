package com.stbella.customer.server.customer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel(value = "CustomerProductionBodyVO", description = "客户产康预约-身体信息")
public class CustomerProductionBodyVO implements Serializable {

    private static final long serialVersionUID = -7410228575061541003L;

    @ApiModelProperty(value = "模型类型，0-产后修复测量数据 1-体测镜模型数据")
    private Integer type;

    @ApiModelProperty(value = "修复数据")
    private PostpartumDataVO postpartumDataVO;

    @ApiModelProperty(value = "体测镜模型数据")
    private MirrorPostpartumDataVO mirrorPostpartumDataVO;

    @ApiModelProperty(value = "测量时间")
    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private Date recordTime;

    @Data
    @ApiModel(value = "PostpartumDataVO", description = "客户产康预约-身体信息-修复数据")
    public static class PostpartumDataVO implements Serializable {

        private static final long serialVersionUID = 1937587868240127981L;

        @ApiModelProperty(value = "体重")
        private BodyIndexVO weight;

        @ApiModelProperty(value = "胸围")
        private BodyIndexVO bust;

        @ApiModelProperty(value = "腰围")
        private BodyIndexVO waist;

        @ApiModelProperty(value = "臀围")
        private BodyIndexVO hip;
    }

    @Data
    @ApiModel(value = "BodyIndexVO", description = "客户产康预约-身体信息-指标信息")
    public static class BodyIndexVO implements Serializable {

        private static final long serialVersionUID = -5367181647902286234L;

        @ApiModelProperty(value = "指标名")
        private String indexName;

        @ApiModelProperty(value = "指标单位")
        private String indexUnit;

        @ApiModelProperty(value = "指标当前值")
        private String currentIndexValue;

        @ApiModelProperty(value = "指标上次值")
        private String lastIndexValue;

        @ApiModelProperty(value = "较上次变化值")
        private String contrastLastIndexValue;

        @ApiModelProperty(value = "指标变化率")
        private String rate;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(value = "MirrorPostpartumDataVO", description = "客户产康预约-身体信息-体测镜模型数据")
    public static class MirrorPostpartumDataVO extends PostpartumDataVO {

        private static final long serialVersionUID = -5543514398783061679L;

        @ApiModelProperty(value = "体测镜数据报告ID")
        private Long reportId;

        @ApiModelProperty(value = "胸部数据")
        private MirrorDataExceptionVO breast;

        @ApiModelProperty(value = "脊柱数据")
        private MirrorDataExceptionVO spine;

        @ApiModelProperty(value = "骨盆数据")
        private MirrorDataExceptionVO pelvis;
    }

    @Data
    @ApiModel(value = "MirrorDataExceptionVO", description = "客户产康预约-身体信息-体测镜模型数据异常信息")
    public static class MirrorDataExceptionVO implements Serializable {

        private static final long serialVersionUID = -9082709939253161420L;

        @ApiModelProperty(value = "部位名称")
        private String indexName;

        @ApiModelProperty(value = "异常数量")
        private Integer exceptionCount;

        @ApiModelProperty(value = "异常列表")
        private List<String> exceptionList;
    }
}
