package com.stbella.customer.server.ecp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 全局用户表
 * </p>
 *
 * <AUTHOR> @since 2022-11-03
 */
@Data
@Accessors(chain = true)
@TableName("he_user_basic")
@ApiModel(value="HeUserBasicPO对象", description="全局用户表")
public class HeUserBasicPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(
        value = "id",
        type = IdType.AUTO
    )
    private Long id;

    @ApiModelProperty(value = "手机")
    private String phone;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "当前积分")
    private Integer integral;

    @ApiModelProperty(value = "虚拟消费金额单位分")
    private Integer virtualConsumAmount;

    @ApiModelProperty(value = "真实消费金额（单位分）")
    private Integer realAmount;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "性别：0=保密，1=男，2=女")
    private Integer gender;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "合力亿捷客户唯一字段1:空值还没插入对方插入接口系统2:非空已插入对方系统只要调用对方更新接口")
    private String hlUniqueId;

    @ApiModelProperty(value = "手动设置预产期时间戳 -1 为前端设置为备孕中")
    private Long manualPredictBornTime;

    @ApiModelProperty(value = "孕期枚举 0)未知 1）备孕中 2) 孕早期 3）孕中期 4）孕晚期 5）育儿中")
    private Integer pregnancy;

    @ApiModelProperty(value = "员工头像")
    private String photo;

    @ApiModelProperty(value = "手机号类型")
    private Integer phoneType;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "有赞优惠券是否同步 0-未同步 1-已同步")
    private Integer youzanVoucherSync;

    @ApiModelProperty(value = "成为产康客资时间")
    private Date productionNewleadTime;

    @ApiModelProperty(value = "是否为蓝宝石俱乐部成员, 0-否 1-是")
    private Integer sapphire;

}
