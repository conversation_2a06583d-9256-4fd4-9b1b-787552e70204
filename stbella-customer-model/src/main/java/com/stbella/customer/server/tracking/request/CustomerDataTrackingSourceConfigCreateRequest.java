package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel(value = "CustomerDataTrackingSourceConfigCreateRequest", description = "创建渠道来源请求")
public class CustomerDataTrackingSourceConfigCreateRequest implements Serializable {

    private static final long serialVersionUID = -3567533778321769358L;

    @ApiModelProperty(value = "来源key，用于定义不同的来源渠道")
    @NotBlank(message = "来源key不能为空")
    private String sourceKey;

    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    @ApiModelProperty(value = "来源备注")
    private String sourceContent;
}
