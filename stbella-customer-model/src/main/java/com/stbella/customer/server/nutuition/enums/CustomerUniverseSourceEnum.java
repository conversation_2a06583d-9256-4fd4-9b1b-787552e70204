package com.stbella.customer.server.nutuition.enums;

import lombok.Getter;

/**
 * 全域客户所有来源渠道
 */
public enum CustomerUniverseSourceEnum {

    TAO_BAO(1, "淘宝"),
    TIAN_MAO(2, "天猫"),
    JING_DONG(3, "京东"),
    STORE(4, "门店"),
    DISTRIBUTION(5, "分销"),
    SMALL_STORE(6, "新零售");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CustomerUniverseSourceEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
