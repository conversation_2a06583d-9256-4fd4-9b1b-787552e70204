package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.core.base.BasePageQuery;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.ChannelSourceEnum;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerVisitorListRequest", description="")
public class CustomerVisitorListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

}
