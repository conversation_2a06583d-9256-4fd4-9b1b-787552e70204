package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "EventStatVO", description = "事件统计列表")
public class EventStatVO implements Serializable {

    private static final long serialVersionUID = -2036098566796566528L;

    @ApiModelProperty(value = "事件id")
    private String eventId;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件备注")
    private String eventContent;

    @ApiModelProperty(value = "触发次数")
    private Long pageView;

    @ApiModelProperty(value = "触发人数")
    private Long userView;
}
