package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Valid
@Data
@ApiModel(value = "ActivityFormRecordRequest", description = "预约活动报名")
public class ActivityFormRecordRequest implements Serializable {


    private static final long serialVersionUID = 6801406977409456472L;

    @ApiModelProperty(value = "品牌类型 0圣贝拉 1小贝拉")
    @NotNull(message = "品牌类型不能为空")
    private Integer brandType;

    @ApiModelProperty(value = "用户全局id")
    @NotNull(message = "用户全局id不能为空")
    private Integer basicUid;

    @ApiModelProperty(value = "活动id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源id不能为空")
    private Integer formId;


    @ApiModelProperty(value = "报名信息")
    @NotNull(message = "报名信息不能为空")
    private SubmitContent submitContent;


    @Data
    @ApiModel(value = "SubmitContent", description = "活动报名信息")
    public static class SubmitContent implements Serializable {

        private static final long serialVersionUID = -927667337741843277L;

        @ApiModelProperty(value = "手机号")
        private String phone;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "地址")
        private String address;
    }

}
