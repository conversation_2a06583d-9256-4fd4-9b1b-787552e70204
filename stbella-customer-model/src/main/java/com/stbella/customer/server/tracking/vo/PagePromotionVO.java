package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "PagePromotionVO", description = "页面投放结果VO")
public class PagePromotionVO implements Serializable {

    private static final long serialVersionUID = 7707491217536307565L;

    @ApiModelProperty(value = "短链地址")
    private String link;

    @ApiModelProperty(value = "小程序码图片url")
    private String imageUrl;
}
