package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 基本的日期与数据VO
 */
@Data
@ApiModel(value = "BaseDateNumVO", description = "基本的日期与数据VO")
public class BaseDateNumVO implements Serializable {

    private static final long serialVersionUID = -8444196685009134090L;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "整数，数量")
    private Long num;


}
