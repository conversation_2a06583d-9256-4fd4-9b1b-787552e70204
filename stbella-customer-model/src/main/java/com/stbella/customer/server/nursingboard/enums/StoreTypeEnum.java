package com.stbella.customer.server.nursingboard.enums;


import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

/**
 * 品牌类型
 */

public enum StoreTypeEnum {

    //0圣贝拉 1小贝拉
    SAN_BELLA(0, "圣贝拉"),
    BABY_BELLA(1, "小贝拉");


    private final Integer code;
    private final String name;

    StoreTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    public static StoreTypeEnum fromCode(Integer code) {
        if (!Objects.isNull(code)) {
            return Arrays.stream(StoreTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
        }
        return null;

    }

    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            Optional<StoreTypeEnum> first = Arrays.stream(
                StoreTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            if (first.isPresent()) {
                return first.get().getName();
            }
        }
        return null;

    }


    public static StoreTypeEnum fromName(String name) {
        if (!StringUtils.isBlank(name)) {
            return Arrays.stream(StoreTypeEnum.values()).filter(i -> Objects.equals(i.getName(), name)).findFirst().orElse(null);
        }
        return null;

    }
}
