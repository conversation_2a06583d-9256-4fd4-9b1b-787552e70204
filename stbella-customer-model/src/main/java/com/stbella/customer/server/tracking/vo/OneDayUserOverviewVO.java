package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 某天的今日概况统计
 */
@Data
@ApiModel(value = "OneDayUserOverviewVO", description = "某天的今日概况统计")
public class OneDayUserOverviewVO implements Serializable {

    private static final long serialVersionUID = 8117344765676482019L;

    @ApiModelProperty(value = "累计新增用户")
    private Long totalNewUserNum;

    @ApiModelProperty(value = "累计老用户活跃")
    private Long totalOldUserNum;

    @ApiModelProperty(value = "累计日活跃用户数")
    private Long totalActiveUserNum;

    @ApiModelProperty(value = "累计访问次数")
    private Long totalVisitNum;
}
