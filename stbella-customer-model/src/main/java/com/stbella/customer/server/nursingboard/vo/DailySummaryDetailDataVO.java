package com.stbella.customer.server.nursingboard.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "DailySummaryDetailDataVO",description = "护理看板-医生建议详情")
public class DailySummaryDetailDataVO implements Serializable {

    private static final long serialVersionUID = -829764109023733181L;

    @ApiModelProperty("care主表id")
    private Long careId;

    @ApiModelProperty("最新一次修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date gmtModified;

    @ApiModelProperty(value = "小结类型 0护理长总结 1中医查房 2产科查房 3儿科查房")
    private Integer type;

    @ApiModelProperty(value = "小结内容")
    private String content;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "宝宝Id")
    private Long babyId;
}
