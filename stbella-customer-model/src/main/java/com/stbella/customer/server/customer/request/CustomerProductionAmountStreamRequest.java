package com.stbella.customer.server.customer.request;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "CustomerProductionAmountStreamRequest", description = "客户产康金流水查询类")
public class CustomerProductionAmountStreamRequest extends PageBaseReq {

    private static final long serialVersionUID = -1537609373513167830L;

    @ApiModelProperty(value = "客户basicId")
    @NotNull(message = "客户basicId不能为空")
    private Integer basicId;

    @ApiModelProperty(value = "流水类型 0-增加 1-扣减")
    private Integer type;
}
