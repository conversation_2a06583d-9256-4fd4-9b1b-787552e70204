package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "ApiListRequest", description = "接口分析-接口列表请求类")
public class ApiListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -219733466053052187L;


    @ApiModelProperty(value = "接口地址")
    private String url;

    @ApiModelProperty(value = "品牌类型 0圣贝拉 1小贝拉")
    private Integer brandType;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    private Date dateEnd;


}
