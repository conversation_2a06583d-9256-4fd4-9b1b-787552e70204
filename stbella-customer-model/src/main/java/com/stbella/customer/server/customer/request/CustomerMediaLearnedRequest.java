package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Date 2022/08/16
 * <AUTHOR>
 */
@Data
public class CustomerMediaLearnedRequest implements Serializable {
    /**
     * 节id(媒体视频id)
     */
    @ApiModelProperty(value = "媒体视频ID")
    private Long courseId;

    @ApiModelProperty(value = "用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "媒体类型 1）视频")
    private Integer sectionType;

    @ApiModelProperty(value = "增量时间，非视频为0，视频就传新增的看视频时长")
    private Long deltaDuration;

    @ApiModelProperty(value = "应用品牌 1)小贝拉 2）圣贝拉")
    private Integer fromType;

    @ApiModelProperty(value = "视频播放节点，非视频为0，视频就传已看到的视频节点")
    private Long mediaProgress;

    @ApiModelProperty(value = "是否第一次打开当前媒体。(方便以后扩展使用)")
    private Boolean first;
}
