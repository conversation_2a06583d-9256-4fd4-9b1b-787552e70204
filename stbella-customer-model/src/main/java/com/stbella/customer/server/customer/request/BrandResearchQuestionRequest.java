package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "BrandResearchQuestionRequest", description = "填写品牌第一次登录调研信息")
public class BrandResearchQuestionRequest implements Serializable {

    private static final long serialVersionUID = -7010721397349591078L;

    @ApiModelProperty(value = "品牌")
    private Integer brandType;

    @ApiModelProperty(value = "用户小程序的openid'")
    @NotNull(message = "用户未授权")
    private String openid;

    @ApiModelProperty(value = "客户调研问题的内容")
    @NotNull(message = "调研内容不能为空")
    private String question;
}
