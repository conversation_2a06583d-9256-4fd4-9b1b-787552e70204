package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel(value = "MomentClientPhoneReportRequest", description = "朋友圈广告客户手机号上报")
public class MomentClientPhoneReportRequest implements Serializable {

    private static final long serialVersionUID = -4205131327439350462L;

    @ApiModelProperty(value = "客户手机号")
    @NotBlank(message = "客户手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "手机号区号")
    private String phoneType;

    @ApiModelProperty(value = "品牌类型, 0-圣贝拉 1-小贝拉")
    private Integer brandType;

    @ApiModelProperty(value = "来源拓展渠道")
    private String sourceExtend;
}
