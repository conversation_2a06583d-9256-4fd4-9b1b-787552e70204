package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "SealMooNewLeadStatusReportRequest", description = "小芯AI客资状态上报")
public class SealMooNewLeadStatusReportRequest implements Serializable {

    private static final long serialVersionUID = 7408654951081436742L;

    @ApiModelProperty(value = "粉丝的openid")
    @JsonProperty("open_id")
    private String open_id;

    @ApiModelProperty(value = "部门编号(数字类型的值，但是传字符串)")
    @JsonProperty("department_id")
    private String department_id;

    @ApiModelProperty(value = "客资状态上报参数")
    private ParamCustomerRequest customer;

    @Data
    @ApiModel(value = "ParamCustomerRequest", description = "客资状态上报参数")
    public static class ParamCustomerRequest implements Serializable {

        private static final long serialVersionUID = 9187675395249457374L;

        @ApiModelProperty(value = "顾客状态(无效客户/意向客户)")
        private String status;

        @ApiModelProperty(value = "顾客手机号")
        private String mobile;

        @ApiModelProperty(value = "顾客姓名")
        private String name;

        @ApiModelProperty(value = "顾客等级")
        private String level;

        @ApiModelProperty(value = "顾客交易金额")
        private String money;

        @ApiModelProperty(value = "顾客无效原因（空号， 停机，无人接听，挂断，无需求）")
        @JsonProperty("invalid_reason")
        private String invalid_reason;
    }
}
