package com.stbella.customer.server.nursingboard.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 妈妈入住信息
 */
@Data
@ApiModel(value = "MomCheckRoomDataVO",description = "护理看板-妈妈入住信息")
public class MomCheckRoomDataVO implements Serializable {

    private static final long serialVersionUID = -7782318644795273132L;

    @ApiModelProperty(value = "妈妈id")
    private Long customerId;

    @ApiModelProperty(value = "妈妈姓名")
    private String name;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "入住时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date checkInDate;

    @ApiModelProperty(value = "离馆时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date checkOutDate;

    @ApiModelProperty(value = "入住时长 单位：天")
    private Long checkInTime;

    @ApiModelProperty(value = "订单类型 0标准月子 1小月子")
    private Integer orderType;

    @ApiModelProperty(value = "欢迎语-名称")
    private String greetingName;

    @ApiModelProperty(value = "欢迎语-内容")
    private String greetingContent;


    @ApiModelProperty(value = "是否小月龄订单")
    private boolean ifSmallMonthAgeOrder;



    @ApiModelProperty(value = "宝宝房态数据")
    private List<BabyCheckRoomDataVO> BabyCheckRoomDataList;

}
