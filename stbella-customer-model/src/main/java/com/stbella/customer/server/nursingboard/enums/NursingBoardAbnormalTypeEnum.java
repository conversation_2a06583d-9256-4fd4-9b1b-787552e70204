package com.stbella.customer.server.nursingboard.enums;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

/**
 * 异常类型 1:宝妈异常 2:宝宝异常
 */
public enum NursingBoardAbnormalTypeEnum {


    MOM_Abnormal(1,"宝妈异常"),
    BABY_Abnormal(2,"宝宝异常");



    private final Integer code;
    private final String name;

    NursingBoardAbnormalTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    public static NursingBoardAbnormalTypeEnum fromCode(Integer code) {
        if (!Objects.isNull(code)) {
            return Arrays.stream(NursingBoardAbnormalTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
        }
        return null;

    }

    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            Optional<NursingBoardAbnormalTypeEnum> first = Arrays.stream(
                NursingBoardAbnormalTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            if (first.isPresent()) {
                return first.get().getName();
            }
        }
        return null;

    }


    public static NursingBoardAbnormalTypeEnum fromName(String name) {
        if (!StringUtils.isBlank(name)) {
            return Arrays.stream(NursingBoardAbnormalTypeEnum.values()).filter(i -> Objects.equals(i.getName(), name)).findFirst().orElse(null);
        }
        return null;

    }
}
