package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "EventTrendRequest",  description = "事件趋势请求")
public class EventTrendRequest implements Serializable {

    private static final long serialVersionUID = 8119566375164437223L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "事件id")
    @NotBlank(message = "事件id不能为空")
    private String eventId;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    @NotNull(message = "开始时间不能为空")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    @NotNull(message = "结束时间不能为空")
    private Date dateEnd;
}
