package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CustomerUserNurseInfoRequest", description = "用户-护理信息request")
public class CustomerUserNurseInfoRequest implements Serializable {

    private static final long serialVersionUID = -4373772641243025033L;

    @ApiModelProperty("用户手机号")
    private String phone;

    @ApiModelProperty("演示状态 1=演示状态 其他=正常")
    private Integer damonStatus;

    @ApiModelProperty("用户状态 0 - 默认游客，1 - 已入驻的会员，2 - 会员待入住，3 - 会员已离店")
    private Integer isOrder;

    @ApiModelProperty("品牌类型 0圣贝拉 1小贝拉")
    private Integer storeType;

    @ApiModelProperty(value = "品牌， 0-圣贝拉 1-小贝拉")
    private Integer brandType = null;

}
