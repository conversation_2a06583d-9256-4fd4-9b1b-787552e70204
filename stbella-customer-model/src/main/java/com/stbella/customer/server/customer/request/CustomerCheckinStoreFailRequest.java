package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel(value = "CustomerCheckinStoreFailRequest", description = "客户打卡失败上报")
public class CustomerCheckinStoreFailRequest implements Serializable {

    private static final long serialVersionUID = -5388230196602651482L;

    @ApiModelProperty(value = "品牌类型, 0-圣贝拉 1-小贝拉")
    private Integer brandType;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "维度")
    private BigDecimal lat;

    @ApiModelProperty(value = "经度")
    private BigDecimal lng;
}
