package com.stbella.customer.server.tracking.constant;

/**
 * 缓存key
 *
 * <AUTHOR>
 * @date 2023/12/01 01:55:12
 */
public class TrackingRedisConstant {

    public final static long TIME_OUT = 10L;

    //今日统计：分时新增用户
    public final static String DATA_TRACKING_TODAY_STAT_USER_NEW = "customer:tracking:todayStat:newUser";
    //今日统计：分时老用户活跃
    public final static String DATA_TRACKING_TODAY_STAT_USER_OLD = "customer:tracking:todayStat:oldUser";
    //今日统计：分时活跃用户
    public final static String DATA_TRACKING_TODAY_STAT_USER_ALL = "customer:tracking:todayStat:allUser";
    //今日统计：分时访问次数
    public final static String DATA_TRACKING_TODAY_STAT_VISIT_NUM = "customer:tracking:todayStat:visitNum";
    //今日统计：分时累计概括
    public final static String DATA_TRACKING_TODAY_STAT_USER_OVERVIEW = "customer:tracking:todayStat:userOverview";


    //老用户openid
    public final static String DATA_TRACKING_OLD_OPENID = "customer:tracking:oldOpenId";


    //平台统计-用户概览 累计新增 累计活跃
    public final static String DATA_TRACKING_PLATFORM_STAT_USER_OVERVIEW = "customer:tracking:platformStat:userOverview";

    //平台统计-活跃用户趋势
    public final static String DATA_TRACKING_PLATFORM_STAT_ACTIVE_USER = "customer:tracking:platformStat:activeUser";

    //平台统计-平均单日使用时长 ArgDurationTimeByDate
    public final static String DATA_TRACKING_PLATFORM_STAT_ARG_DURATION_TIME_ONE_DAY = "customer:tracking:platformStat:argDurationTimeOneDay";

    //平台统计-用户访问次数
    public final static String DATA_TRACKING_PLATFORM_STAT_PAGE_VIEW = "customer:tracking:platformStat:pageView";

    //平台统计-平均单次访问时长
    public final static String DATA_TRACKING_PLATFORM_STAT_ARG_DURATION_TIME_ONE = "customer:tracking:platformStat:argDurationTimeOne";

    //平台统计-新老活跃用户趋势
    public final static String DATA_TRACKING_PLATFORM_STAT_ACTIVE_OLD_OR_NEW_USER = "customer:tracking:platformStat:activeOldOrNewUser";

    //平台统计-新用户占比趋势
    public final static String DATA_TRACKING_PLATFORM_STAT_NEW_USER_PROPORTION = "customer:tracking:platformStat:newUserProportion";

    //用户概览-新增用户占比
    public final static String DATA_TRACKING_USER_OVERVIEW_NEW_USER_PROPORTION = "customer:tracking:userOverview:newUserProportion";

    //用户概览-新增用户数
    public final static String DATA_TRACKING_USER_OVERVIEW_NEW_USER_NUM = "customer:tracking:userOverview:newUserNum";

    //用户概览-活跃用户数
    public final static String DATA_TRACKING_USER_OVERVIEW_ACTIVE_USER_NUM = "customer:tracking:userOverview:activeUserNum";


    //用户统计-列表
    public final static String DATA_TRACKING_USER_STAT_LIST = "customer:tracking:userStat:list";


}
