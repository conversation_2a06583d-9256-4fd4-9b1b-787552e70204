package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "SessionVisitTrendVO", description = "session概览-访问趋势")
public class SessionVisitTrendVO implements Serializable {

    private static final long serialVersionUID = -5598856487740678952L;

    @ApiModelProperty(value = "合计")
    private Long totalValue;

    @ApiModelProperty(value = "均值")
    private BigDecimal avgValue;

    @ApiModelProperty(value = "数据列表")
    private List<BaseDateNumVO> dataList;
}
