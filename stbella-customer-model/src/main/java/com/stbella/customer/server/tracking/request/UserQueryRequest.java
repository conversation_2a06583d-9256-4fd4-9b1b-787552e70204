package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "UserQueryRequest", description = "用户姓名/手机号查询")
public class UserQueryRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 3260630638235032336L;

    @ApiModelProperty(value = "姓名/手机号")
    private String nameOrPhone;

    @ApiModelProperty(value = "状态, 0-正常 1-白名单 2-黑名单")
    private Integer status;
}
