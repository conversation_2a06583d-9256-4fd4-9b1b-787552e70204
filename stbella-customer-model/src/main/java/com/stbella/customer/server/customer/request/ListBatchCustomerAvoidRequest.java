package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ListBatchCustomerAvoidRequest implements Serializable {

    private static final long serialVersionUID = -9054839250054533874L;

    @ApiModelProperty(value = "客户basicId")
    private List<Long> basicIds;

    @ApiModelProperty(value = "clientIds")
    private List<Long> clientIds;

    @ApiModelProperty(value = "禁忌类型 0:餐食禁忌 1:过敏食物")
    private Integer avoidType;

}
