package com.stbella.customer.server.tracking.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "UserInfoVO", description = "客户信息")
public class UserInfoVO implements Serializable {

    private static final long serialVersionUID = 9062598063503220775L;

    @ApiModelProperty(value = "记录id")
    private Long id;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户唯一标识")
    private String openid;

    @ApiModelProperty(value = "状态, 0-正常 1-白名单 2-黑名单")
    private Integer status;

    @ApiModelProperty(value = "设置时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settingTime;

    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;
}
