package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "VisitTrendVO",description = "页面统计趋势vo")
public class VisitTrendVO implements Serializable {
    private static final long serialVersionUID = 5912921914990406866L;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面路径")
    private String pagePath;

    @ApiModelProperty(value = "数据列表")
    private List<VisitDataStatDetailVO> visitDataList;

    @ApiModelProperty(value = "数据圆型图")
    private List<VisitSourceStatDetailVO> visitSourceList;


    @Data
    @ApiModel(value = "VisitDataStatDetailVO", description = "页面统计趋势数据详情VO")
    public static class VisitDataStatDetailVO implements Serializable {

        private static final long serialVersionUID = 1163986459241674940L;

        @ApiModelProperty(value = "日期，yyyy-MM-dd")
        private String statDate;

        @ApiModelProperty(value = "访问人数")
        private Long userView;

        @ApiModelProperty(value = "访问次数")
        private Long pageView;

        @ApiModelProperty(value = "访问时长")
        private BigDecimal durationTime;

    }

   @Data
   @ApiModel(value = "VisitSourceStatDetailVO",description = "页面统计趋势访问渠道详情VO")
   public static class  VisitSourceStatDetailVO implements Serializable{


       private static final long serialVersionUID = -4761815088029575342L;

       @ApiModelProperty(value = "来源KEY")
       private String sourceKey;

       @ApiModelProperty(value = "来源名称")
       private String sourceName;

       @ApiModelProperty(value = "访问来源人数")
       private long sourceNum;
   }

}
