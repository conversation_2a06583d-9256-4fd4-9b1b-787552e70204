package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "PageShareRankVO", description = "页面分享排名VO")
public class PageShareRankVO implements Serializable {

    private static final long serialVersionUID = 3797610430739144650L;

    @ApiModelProperty(value = "排序序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "分享人数")
    private Long shareUser;
}
