package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.ChannelSourceEnum;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import com.stbella.customer.server.customer.enums.delivery.DeliveryIactationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerDeliveryEditRequest", description="后台修改分娩基本信息")
public class CustomerDeliveryEditRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分娩表主键")
    private Long id;

    @ApiModelProperty(value = "客户id",required = true)
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    @ApiModelProperty(value = "胎次")
    @NotNull(message = "胎次不能为空")
    @Min(value = 1,message = "胎次不能小于1")
    @Max(value = 20,message = "胎次不能大于20")
    private Integer parity;

    @ApiModelProperty(value = "医院")
    @Size(max = 50,message = "医院字数小于50")
    private String hospital;

    @ApiModelProperty(value = "分娩孕周")
    @Max(value = 50,message = "分娩孕周周数不能大于50")
    @Min(value = 0,message = "分娩孕周周数不能小于0")
    private Integer gestationWeek;

    @ApiModelProperty(value = "分娩孕周-天")
    @Max(value = 6,message = "分娩孕周天数不能大于6")
    @Min(value = 0,message = "分娩孕周天数不能小于0")
    private Integer gestationDay;

    @ApiModelProperty(value = "预产期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date anticipateDate;

    @ApiModelProperty(value = "医院具体地址")
    @Size(max = 50,message ="医院具体地址字数不能大于50" )
    private String hospitalAddress;

    /**
     * 预留字段
     */
    @JsonIgnore
    @ApiModelProperty(value = "民族")
    private Integer nation;


    @ApiModelProperty(value = "入住天数")
    @Min(value = 1,message = "入住天数不能小于1")
    private Integer arrivalDay;

    @ApiModelProperty(value = "预计入馆时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    @ApiModelProperty(value = "预定房型id")
    private Long roomTypeId;

    @ApiModelProperty(value = "预定房型名称")
    private String roomTypeName;

    @ApiModelProperty(value = "需要车子  0否   1是")
    @EnumValue(enumClass = TrueFalseEnum.class, enumMethod = "isValidCode")
    private Integer needCar;

    @ApiModelProperty(value = "生产日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productionDate;

    @ApiModelProperty(value = "宝宝数据list")
    @NotNull
    private List<CustomerDeliveryBabyRequest> babyList;

}
