package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendIntegralMessageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户手机号码",required = true)
    private String mobile;

    @ApiModelProperty(value = "积分",required = true)
    private Integer integral;

    @ApiModelProperty(value = "到账时间",required = true)
    private String arrivalDate;


    private String openId;

}
