package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerGoodsCollectListRequest", description = "客户收藏套餐列表")
public class CustomerGoodsCollectListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 2303084434778876711L;

    @ApiModelProperty(value = "品牌类型")
    @NotNull(message = "品牌不能为空")
    private Integer brandType;

    @ApiModelProperty(value = "客户basicUid")
    @Positive(message = "客户不能为空")
    private Integer basicUid;
}
