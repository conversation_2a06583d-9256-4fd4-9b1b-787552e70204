package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class VisitDetailExport implements Serializable {
    private static final long serialVersionUID = -9109334232951837017L;


    @Column(value = "姓名",order = 1)
    private String clientName;

    @Column(value = "手机号",order = 2)
    private String phone;

    @Column(value = "访问次数",order = 3)
    private Long pageView;

    @Column(value = "访问时长",order = 4)
    private BigDecimal durationTime;

    @Column(value = "平均访问时长",order = 5)
    private BigDecimal avgDurationTime;

    public VisitDetailExport(String clientName, String phone, Long pageView, BigDecimal durationTime, BigDecimal avgDurationTime) {
        this.clientName = clientName;
        this.phone = phone;
        this.pageView = pageView;
        this.durationTime = durationTime;
        this.avgDurationTime = avgDurationTime;
    }
}
