package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.CustomerAttentionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerArchiveEditRequest", description="小程序修改客户基本信息")
public class CustomerArchiveEditRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "customerId",required = true)
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    @ApiModelProperty(value = "姓名",required = true)
    @Size(max = 20,message = "姓名不能大于20个字符")
    @NotBlank(message = "姓名不能为空")
    private String customerName;

    @ApiModelProperty(value = "手机号-同主表的手机号码")
    private String phoneNumber;

    @ApiModelProperty(value = "证件类型")
    private Integer cardType;

    @ApiModelProperty(value = "证件号码")
    @NotBlank(message = "证件号码不能为空")
    private String cardNo;

    @ApiModelProperty(value = "家庭地址")
    @Size(max = 100,message = "家庭地址字数不能大于100")
    private String address;

    @ApiModelProperty(value = "分娩孕周")
    @Max(value = 50,message = "分娩孕周周数不能大于50")
    @Min(value = 0,message = "分娩孕周周数不能小于0")
    private Integer gestationWeek;

    @ApiModelProperty(value = "分娩孕周-天")
    @Max(value = 6,message = "分娩孕周天数不能大于6")
    @Min(value = 0,message = "分娩孕周天数不能小于0")
    private Integer gestationDay;

    @ApiModelProperty(value = "预产期")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date anticipateDate;

    @ApiModelProperty(value = "入住日期")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date entryDate;

    @ApiModelProperty(value = "紧急联系人")
    @Size(max = 20)
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人号码")
    @Pattern(regexp = "1\\d{10}", message="紧急联系人码格式错误")
    private String emergencyContactPhone;

    @JsonIgnore
    @ApiModelProperty(value = "修改人id")
    private Long updateBy;

    @JsonIgnore
    @ApiModelProperty(value = "修改人名称")
    private String updateByName;


}
