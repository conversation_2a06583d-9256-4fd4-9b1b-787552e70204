package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "CustomerInfoAddRequestNew", description = "添加客户主表new")
public class CustomerInfoAddRequestNew implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户编号", required = true)
    private Long customerInfoId;

    @ApiModelProperty(value = "客户姓名", required = true)
    @Size(max = 15, message = "客户姓名不能大于15字")
    private String customerName;


    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "1\\d{10}", message = "手机号码必须为1开头11位数字")
    @ApiModelProperty(value = "注册时的手机号", required = true)
    private String phoneNumber;


    @ApiModelProperty(value = "客户标签", required = true)
    @NotNull(message = "客户标签不能为空")
    private Integer labelCode;


    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

}
