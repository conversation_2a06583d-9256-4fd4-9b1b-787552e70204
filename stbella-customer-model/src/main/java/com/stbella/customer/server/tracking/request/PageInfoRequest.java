package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "PageInfoRequest", description = "页面信息上报")
public class PageInfoRequest implements Serializable {

    private static final long serialVersionUID = -8744301287272487397L;

    @ApiModelProperty(value = "页面id")
    private Long id;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;
}
