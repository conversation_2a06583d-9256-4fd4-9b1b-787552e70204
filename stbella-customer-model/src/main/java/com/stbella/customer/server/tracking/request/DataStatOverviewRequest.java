package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "DataStatOverviewRequest", description = "数据概览请求")
public class DataStatOverviewRequest implements Serializable {

    private static final long serialVersionUID = -8460569640270687775L;

    @ApiModelProperty(value = "查询类型，0:按年查，1:按月查")
    private Integer type;

    @ApiModelProperty(value = "时间，当type为0时, 精确到年; 当type为1时, 精确到月份")
    private String date;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "是否缓存，0-不走缓存 1-走缓存")
    private Integer cache;

}
