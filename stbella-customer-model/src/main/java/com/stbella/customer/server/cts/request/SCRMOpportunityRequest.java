package com.stbella.customer.server.cts.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.customer.server.scrm.ScrmRequestParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "商机", description = "商机入参")
public class SCRMOpportunityRequest implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    @ApiModelProperty(value = "业务类型")
    private Long entityType;

    @ApiModelProperty(value = "销售机会所有人")
    private Long ownerId;

    @ApiModelProperty(value = "机会名称")
    private String opportunityName;

    @ApiModelProperty(value = "客户名称")
    private Long customerId;

    @ApiModelProperty(value = "价格表名称")
    private Long priceId;

    @ApiModelProperty(value = "机会类型")
    private Integer opportunityType;

    @ApiModelProperty(value = "销售金额")
    private BigDecimal money;

    @ApiModelProperty(value = "销售阶段")
    private Long saleStageId;

    @ApiModelProperty(value = "输单阶段")
    private Long lostStageId;

    @ApiModelProperty(value = "赢率")
    private Long winRate;

    @ApiModelProperty(value = "输单原因")
    private Integer reason;

    @ApiModelProperty(value = "输单描述")
    private String reasonDesc;

    @ApiModelProperty(value = "结单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date closeDate;

    @ApiModelProperty(value = "承诺")
    private Integer commitmentFlg;

    @ApiModelProperty(value = "机会来源")
    private Integer sourceId;

    @ApiModelProperty(value = "项目预算")
    private BigDecimal projectBudget;

    @ApiModelProperty(value = "实际花费")
    private BigDecimal actualCost;

    @ApiModelProperty(value = "产品")
    private String product;

    @ApiModelProperty(value = "阶段更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date stageUpdatedAt;

    @ApiModelProperty(value = "最新活动记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recentActivityRecordTime;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    @ApiModelProperty(value = "创建人")
    private Long createdBy;

    @ApiModelProperty(value = "最新修改日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedAt;

    @ApiModelProperty(value = "最新修改人")
    private Long updatedBy;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "所属部门")
    private Long dimDepart;

    @ApiModelProperty(value = "区域")
    private Long territoryId;

    @ApiModelProperty(value = "锁定状态")
    private Integer lockStatus;

    @ApiModelProperty(value = "市场活动")
    private Long campaignId;

    @ApiModelProperty(value = "审批状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "销售线索")
    private Long leadId;

    @ApiModelProperty(value = "商机得分")
    private BigDecimal opportunityScore;

    @ApiModelProperty(value = "区域公海状态")
    private Integer territoryHighSeaStatus;

    @ApiModelProperty(value = "预计回收时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date territoryExpireTime;

    @ApiModelProperty(value = "所属区域公海")
    @TableField("territory_high_sea_id")
    private Long territoryHighSeaId;

    @ApiModelProperty(value = "预测金额")
    private BigDecimal fcastMoney;

    @ApiModelProperty(value = "阶段分类")
    private Integer forecastCategory;

    @ApiModelProperty(value = "疑似查重")
    private Integer duplicateFlg;

    @ApiModelProperty(value = "赢单原因")
    private Integer winReason;

    @ApiModelProperty(value = "赢单描述")
    private String winReasonDesc;

    @ApiModelProperty(value = "审批提交人")
    private Long applicantId;

    @ApiModelProperty(value = "退回原因")
    private Integer reasonReturn;

    @ApiModelProperty(value = "退回备注")
    private String reasonComment;

    @ApiModelProperty(value = "延期原因")
    private Integer delayReason;

    @ApiModelProperty(value = "延期备注")
    private String delayComment;

    @ApiModelProperty(value = "分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date allocateTime;

    @ApiModelProperty(value = "签到信息")
    private Long signInInformation;

    @ApiModelProperty(value = "咨询日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date consultationTime;

    @ApiModelProperty(value = "到店日期")
    private String arrivalTime;

    @ApiModelProperty(value = "月子订单")
    private Long monthOrder;

    @ApiModelProperty(value = "非月子订单")
    private Long nonMonthOrder;

    @ApiModelProperty(value = "退回原因")
    private Integer customItem162__c;

    @ApiModelProperty(value = "退回备注")
    private String customItem163__c;

    @ApiModelProperty(value = "延期原因")
    private Integer customItem164__c;

    @ApiModelProperty(value = "延期备注")
    private String customItem165__c;

    @ApiModelProperty(value = "签到信息")
    private Long customItem168__c;

    @ApiModelProperty(value = "月子订单")
    private Long customItem171__c;

    @ApiModelProperty(value = "非月子订单")
    private Long customItem172__c;

    @ApiModelProperty(value = "分配时间")
    private Long customItem173__c;

    @ApiModelProperty(value = "机会所属门店关系")
    private Long customItem174__c;

    @ApiModelProperty(value = "无效机会, 1-疑似探子 2-广告合作 3-应聘 4-无月子需求 5-联系不上 6-产康需求 7-月子餐服务 8-其他")
    private String customItem175__c;

    @ApiModelProperty(value = "母婴品牌区域配置表中id")
    private Long store__c;

    @ApiModelProperty(value = "老客户标签, 1-历史未签约客户 2-历史已签约客户 3-无")
    private Integer customItem186__c;

    @ApiModelProperty(value = "意向省份")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedProvince__c;

    @ApiModelProperty(value = "意向城市")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedCity__c;

    @ApiModelProperty(value = "意向区/县")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedArea__c;

    @ApiModelProperty(value = "意向门店")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedStore__c;

    @ApiModelProperty(value = "意向品牌, 0-圣贝拉 1-小贝拉")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedBrand__c;

    @ApiModelProperty(value = "客户预算, 0:10万以内 1:10-20万 2:20-30万 3:30万以上 4:无明确预算")
    @ScrmRequestParam(value = "opportunity")
    private Integer customerBudget__c;

    @ApiModelProperty(value = "意向国家")
    @ScrmRequestParam(value = "opportunity")
    private Integer intendedCountry__c;

    @ApiModelProperty(value = "意向套餐")
    private Integer customItem201__c;

    @ApiModelProperty(value = "意向护理模式")
    private Integer customItem202__c;

    @ApiModelProperty(value = "客户身上的预算字段")
    private Integer customerBudgetValue;

    @ApiModelProperty(value = "客户希望联系方式, 1:添加微信 2:电话联系")
    private Integer contactway__c;

    @ApiModelProperty(value = "参观预约时间")
    private String visitAppointmentTime__c;

    @ApiModelProperty(value = "参观项目")
    private String visitProject__c;

    @ApiModelProperty(value = "客户意向产品（产康）")
    private Integer[] intendedProduct__c;

    @ApiModelProperty(value = "自定义商机类别, 1-默认 2-馆外产康")
    private Integer customType__c;

    @ApiModelProperty(value = "24小时回访标签")
    private Integer returnVisit24Flag__c;

    @ApiModelProperty(value = "3天回访标签")
    private Integer returnVisitThreeDayFlag__c;

    @ApiModelProperty(value = "7天回访标签")
    private Integer returnVisitWeekFlag__c;
}
