package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "BirthdayWishWxMessageRequest", description = "生日祝福公众号消息推送")
public class BirthdayWishWxMessageRequest implements Serializable {

    private static final long serialVersionUID = -4790563011971690328L;

    @ApiModelProperty(value = "客户在公众号中的openid")
    private String openid;

    @ApiModelProperty(value = "客户生日")
    private String birthday;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "祝福语")
    private String wishMessage;
}
