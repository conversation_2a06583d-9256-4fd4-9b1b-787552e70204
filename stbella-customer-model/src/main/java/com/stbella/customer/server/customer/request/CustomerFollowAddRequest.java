package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.ChannelSourceEnum;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import com.stbella.customer.server.customer.enums.follow.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerFollowAddRequest", description="")
public class CustomerFollowAddRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id")
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @ApiModelProperty(value = "年龄区间 0=22-25;  1=26-30;  2=31-35;  3=35以上;")
    @EnumValue(enumClass = FollowAgeEnum.class, enumMethod = "isValidCode")
    private Integer customerAge;

    @ApiModelProperty(value = "省-id")
    private Integer provinceId;

    @ApiModelProperty(value = "市-id")
    private Integer cityId;

    @ApiModelProperty(value = "区-id")
    private Integer districtId;

    @ApiModelProperty(value = "省-name")
    private String provinceName;

    @ApiModelProperty(value = "市-name")
    private String cityName;

    @ApiModelProperty(value = "区-name")
    private String districtName;

    @ApiModelProperty(value = "分娩医院名称")
    @Size(max = 50,message = "分娩医院名称字数不能大于50")
    private String hospital;

    @ApiModelProperty(value = "用户来源 0=口碑介绍;  1=大众点评;  2=百度/谷歌;  3=合作渠道;  4=线下广告;  5=线下活动;  6=官网;  " +
            "7=小红书;  8=微信公众号;  9=微博;  10=微信小程序;  11=抖音;  12=已入馆宝妈;  13=朋友圈广告;  14=医疗渠道;  15=其他; ")
    @EnumValue(enumClass = ChannelSourceEnum.class, enumMethod = "isValidCode")
    private Integer customerSource;

    @ApiModelProperty(value = "客户职业(多选) 0=民营企业主;  1=医生;  2=教师;  3=公务员;  4=律师;  5=企业主管; ")
    private List<Integer> customerJob;

    @ApiModelProperty(value = "文化属性 0=传统;  1=欧美;  2=新潮; ")
    @EnumValue(enumClass = FollowCulturalAttributeEnum.class, enumMethod = "isValidCode")
    private Integer culturalAttribute;

    @ApiModelProperty(value = "客户家庭收入  0=50w以内;  1=50-100w;  2=100-200w;  3=200w以上; ")
    @EnumValue(enumClass = FollowIncomeEnum.class, enumMethod = "isValidCode")
    private Integer customerIncome;

    @ApiModelProperty(value = "消费能力 0=强;  1=中;  2=弱;  ")
    @EnumValue(enumClass = FollowConCapacityEnum.class, enumMethod = "isValidCode")
    private Integer consumptionCapacity;

    @ApiModelProperty(value = "品牌喜好(多选) 0=奢侈品牌;  1=国际一线大牌;  2=国内品牌;")
    private List<Integer> brandLike;

    @ApiModelProperty(value = "客户影响力(多选) 0=引领者;  1=追随者;  2=随意;")
    private List<Integer> customerInfluence;

    @ApiModelProperty(value = "支付方式(多选) 0=支付宝;  1=微信;  2=银行转账(刷卡); ")
    private List<Integer> payType;

    @ApiModelProperty(value = "配偶年龄区间 0 22-25、1 26-30、2 31-35、3 35以上")
    @EnumValue(enumClass = FollowAgeEnum.class, enumMethod = "isValidCode")
    private Integer spouseAge;

    @ApiModelProperty(value = "配偶职业(多选) 0=民营企业主;  1=医生;  2=教师;  3=公务员;  4=律师;  5=企业主管;")
    private List<Integer> spouseJob;

    @ApiModelProperty(value = "比较在意月子会所的素质(多选) 0=楼层、环境、房型;  1=月子餐口感;  2=产后修复;  3=泌乳调理;  4=宝宝护理;  5=专业水平、服务能力;  6=其他;")
    private List<Integer> clubQuality;

    @ApiModelProperty(value = "下次跟进时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date nextFllowDate;

    @ApiModelProperty(value = "备注")
    @Size(max = 200,message = "备注字数大于200")
    private String remark;

    @ApiModelProperty(value = "操作人id")
    private Long empId;

    @ApiModelProperty(value = "操作人名称")
    private String empName;


}
