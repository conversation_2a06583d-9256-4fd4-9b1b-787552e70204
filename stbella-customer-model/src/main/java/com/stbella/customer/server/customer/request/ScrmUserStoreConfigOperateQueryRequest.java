package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value="ScrmUserStoreConfigOperateRequest", description="scrm用户门店关联")
public class ScrmUserStoreConfigOperateQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "scrm记录id",required = true)
    @NotNull(message = "scrm记录id不能为空")
    private Long scrmRecordId;

    @ApiModelProperty(value = "用户id",required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;


    @ApiModelProperty(value = "操作类型",required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;
}

