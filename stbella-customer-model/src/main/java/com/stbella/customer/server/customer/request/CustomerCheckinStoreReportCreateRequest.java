package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "CustomerCheckinStoreReportCreateRequest", description = "新增探店打卡报告request")
public class CustomerCheckinStoreReportCreateRequest implements Serializable {

    private static final long serialVersionUID = -5584893467242521162L;

    @ApiModelProperty(value = "客户姓")
    private String lastName;

    @ApiModelProperty(value = "客户名")
    private String firstName;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "手机号区域, 0-中国大陆 1-中国香港 2-中国澳门 3-中国台湾 4, 新加坡5, 韩国6, 美国,7,  英国,8,澳大利亚,9泰国")
    private Integer phoneZone;

    @ApiModelProperty(value = "客户手机号")
    private String clientPhone;

    @ApiModelProperty(value = "性别，0:未知 1:男 2:女")
    private Integer clientGender;

    @ApiModelProperty(value = "怀孕状态: 0:怀孕中 1:备孕中")
    private Integer pregnancyStatus;

    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    @ApiModelProperty(value = "胎次")
    private Integer predictBornNum;

    @ApiModelProperty(value = "预产期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date predictBornDate;

    @ApiModelProperty(value = "孕周")
    private Integer gestationWeekNow;

    @ApiModelProperty(value = "生产医院")
    private String hospital;

    @ApiModelProperty(value = "关注的问题")
    private List<ConcernedIssueRequest> concernedIssueList;

    @ApiModelProperty(value = "特别需求")
    private String specialRequest;

    @ApiModelProperty(value = "门店套餐")
    private String storeGoods;

    @ApiModelProperty(value = "参观的套餐")
    private List<String> visitGoods;

    @ApiModelProperty(value = "意向套餐")
    private List<String> intentionalGoods;

    @ApiModelProperty(value = "参观日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitDate;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店品牌，0:圣贝拉 1:小贝拉")
    private Integer storeType;

    @ApiModelProperty(value = "销售id")
    private Integer saleId;

    @ApiModelProperty(value = "销售姓名")
    private String saleName;

    @ApiModelProperty(value = "销售手机号")
    private String salePhone;

    @Data
    @ApiModel(value = "ConcernedIssueRequest", description = "客户关注问题")
    public static class ConcernedIssueRequest implements Serializable {

        private static final long serialVersionUID = 3578934666873103168L;

        @ApiModelProperty(value = "关注问题的标题")
        private String title;

        @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
        private Integer isSelect;

        @ApiModelProperty(value = "组件类型")
        private String type;

        @ApiModelProperty(value = "是否有更多")
        private Integer showMore;

        @ApiModelProperty(value = "选项列表")
        private List<ConcernedIssueOptionRequest> optionList;

        @Data
        @ApiModel(value = "ConcernedIssueOptionRequest", description = "客户关注问题")
        public static class ConcernedIssueOptionRequest implements Serializable {

            private static final long serialVersionUID = -1797534031974401546L;

            @ApiModelProperty(value = "选项名称")
            private String optionName;

            @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
            private Integer isSelect;

            @ApiModelProperty(value = "内容类型，0:无内容 1:字符串类型 2:数组类型")
            private Integer contentType;

            @ApiModelProperty(value = "选项内容描述(String类型)")
            private String content;

            @ApiModelProperty(value = "最大长度")
            private Integer maxlength;

            @ApiModelProperty(value = "placeholder")
            private String placeholder;

            @ApiModelProperty(value = "组件类型")
            private String type;

            @ApiModelProperty(value = "输入内容")
            private String value;

            @ApiModelProperty(value = "尾部文字")
            private String inputNext;

            @ApiModelProperty(value = "选项内容(List类型)")
            private List<ConcernedIssueOptionContentRequest> list;

            @Data
            @ApiModel(value = "ConcernedIssueOptionContentRequest", description = "子选项内容")
            public static class ConcernedIssueOptionContentRequest implements Serializable {

                private static final long serialVersionUID = 4359925294866985045L;

                @ApiModelProperty(value = "子选项内容名称")
                private String label;

                @ApiModelProperty(value = "是否选中, 0:未选中 1:已选中")
                private Integer isSelect;

                @ApiModelProperty(value = "子选项内容描述")
                private String content;

                @ApiModelProperty(value = "内容类型，0:无内容 1:字符串类型 2:数组类型")
                private Integer contentType;
            }
        }
    }
}
