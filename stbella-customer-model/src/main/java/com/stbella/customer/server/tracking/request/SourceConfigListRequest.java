package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "SourceConfigListRequest", description = "来源渠道列表request")
public class SourceConfigListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 764672051375609688L;

    @ApiModelProperty(value = "来源key，用于定义不同的来源渠道")
    private String sourceKey;

    @ApiModelProperty(value = "来源名称")
    private String sourceName;
}
