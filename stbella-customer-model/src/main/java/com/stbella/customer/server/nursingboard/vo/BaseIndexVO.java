package com.stbella.customer.server.nursingboard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "BaseIndexVO", description = "基础指标数据VO")
public class BaseIndexVO implements Serializable {

    private static final long serialVersionUID = -2854370015920376920L;

    @ApiModelProperty(value = "指标名")
    private String indexName;

    @ApiModelProperty(value = "指标单位")
    private String indexUnit;

    //新增乳头异常内容 新增 乳头：异常
    @ApiModelProperty(value = "指标当前值")
    private String currentIndexValue;

    @ApiModelProperty(value = "指标昨日值")
    private String yesterdayIndexValue;

    @ApiModelProperty(value = "指标上次值")
    private String lastIndexValue;

    @ApiModelProperty(value = "指标首日值")
    private String firstIndexValue;

    @ApiModelProperty(value = "较昨日变化值")
    private String contrastYesterdayIndexValue;

    @ApiModelProperty(value = "较上次变化值")
    private String contrastLastIndexValue;

    @ApiModelProperty(value = "较首日变化值")
    private String contrastFirstIndexValue;

    //异常新增判断
    @ApiModelProperty(value = "是否异常, 0-正常 1-异常")
    private Integer exception = 0;
}
