package com.stbella.customer.server.tracking.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NullOrAlphanumeric.Validator.class)
public @interface NullOrAlphanumeric {
    String message() default "Field must be null, empty string, or alphanumeric";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class Validator implements ConstraintValidator<NullOrAlphanumeric, String> {

        @Override
        public void initialize(NullOrAlphanumeric constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
            return s == null || s.trim().isEmpty() || s.matches("^[a-zA-Z0-9-_?&/]+$");
        }
    }
}
