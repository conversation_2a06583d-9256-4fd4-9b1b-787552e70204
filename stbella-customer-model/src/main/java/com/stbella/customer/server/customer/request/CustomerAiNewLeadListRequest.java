package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "CustomerAiNewLeadListRequest", description = "Ai客服新客留资列表请求")
public class CustomerAiNewLeadListRequest implements Serializable {

    private static final long serialVersionUID = 4077298875892936243L;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "品牌类型")
    private Integer brandType;

    @ApiModelProperty(value = "来源渠道")
    private Integer fromType;

    /**
     * @see com.stbella.customer.server.customer.enums.CustomerAiNewLeadBizTypeEnum
     */
    @ApiModelProperty(value = "业务类型")
    private Integer bizType;
}
