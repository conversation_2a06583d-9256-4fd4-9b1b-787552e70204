package com.stbella.customer.server.nursingboard.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 宝宝入住信息
 */
@Data
@ApiModel(value = "BabyCheckRoomDataVO",description = "护理看板-宝宝入住信息")
public class BabyCheckRoomDataVO implements Serializable {

    private static final long serialVersionUID = 5585298208450913174L;

    @ApiModelProperty(value = "宝宝id")
    private Long babyId;

    @ApiModelProperty(value = "宝宝姓名")
    private String name;

    @ApiModelProperty(value = "入住时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    public Date checkInDate;

    @ApiModelProperty(value = "离馆时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    public Date checkOutDate;

}
