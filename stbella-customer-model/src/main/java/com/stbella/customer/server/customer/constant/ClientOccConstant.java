package com.stbella.customer.server.customer.constant;

public class ClientOccConstant {

    //未预约
    public final static Integer APPOINT_STATUS_NO = 0;
    //已预约
    public final static Integer APPOINT_STATUS_YES = 1;

    //未咨询
    public final static Integer CONSULTING_STATUS_NO = 0;
    //已咨询
    public final static Integer CONSULTING_STATUS_YES = 1;

    //未到店
    public final static Integer ARRIVAL_STATUS_NO = 0;
    //已到店
    public final static Integer ARRIVAL_STATUS_YES = 1;

    //未签单
    public final static Integer SIGN_STATUS_NO = 0;
    //已签单
    public final static Integer SIGN_STATUS_YES = 1;

    //有效标签
    public final static Integer VALID_STATUS_NO = 1;
    //无效标签
    public final static Integer VALID_STATUS_YES = 0;

    //未分配门店
    public final static Integer DISTRIBUTION_STATUS_NO = 0;
    //已分配门店
    public final static Integer DISTRIBUTION_STATUS_YES = 1;

    //预付款未满⾜50%⽉⼦标准订单
    public final static Integer STANDARD_STATUS_NO = 0;
    //预付款满⾜50%⽉⼦标准订单
    public final static Integer STANDARD_STATUS_YES = 1;

    //到店时间和预付款<50%签单金额的⽉⼦标准订单时间不在同一天未满足
    public final static Integer TARGET_STATUS_NO = 0;
    //到店时间和预付款》=50%签单金额的⽉⼦标准订单时间在同一天满足
    public final static Integer TARGET_STATUS_YES = 1;


    // extranet_red_status 1)馆外网红状态 未签单，注册数、有效咨询数、到店数都不算客资。
    public final static Integer EXTRANET_RED_STATUS_NO = 0;

    public final static Integer EXTRANET_RED_STATUS_YES = 1;

    //用户逻辑标签:0=新客户;1=老客户
    public final static Integer OLD_CLIENT_STATUS_NO = 0;

    public final static Integer OLD_CLIENT_STATUS_YES = 1;

    //用户逻辑标签:0=非亲友关系;1=亲友关系
    public final static Integer FRIEND_STATUS_NO = 0;

    public final static Integer FRIEND_STATUS_YES = 1;

}
