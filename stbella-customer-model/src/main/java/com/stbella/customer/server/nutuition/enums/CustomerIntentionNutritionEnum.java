package com.stbella.customer.server.nutuition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;


/**
 * 用户意向
 */
public enum CustomerIntentionNutritionEnum {

    LOW_INTENTION(0, "低意向"),
    MIDDLE_INTENTION(1, "中意向"),
    HIGH_INTENTION(2, "高意向");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CustomerIntentionNutritionEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CustomerIntentionNutritionEnum getEnumByCode(Integer code) {
        for (CustomerIntentionNutritionEnum enums : CustomerIntentionNutritionEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的渠道：" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code) || code == -1) {
            return true;
        }
        for (CustomerIntentionNutritionEnum customerStatusEnum : values()) {
            if (Objects.equals(customerStatusEnum.code, code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CustomerIntentionNutritionEnum enums : CustomerIntentionNutritionEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的渠道：" + code);
    }
}