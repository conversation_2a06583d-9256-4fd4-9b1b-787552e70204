package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "CustomerUserEditPregnancyRequest",description = "c端-设置我的预产期request")
public class CustomerUserEditPregnancyRequest implements Serializable {

    private static final long serialVersionUID = -8727916371348238833L;


    @ApiModelProperty(value = "用户全局id")
    @NotNull(message = "basicUid不能为空")
    private Long basicUid;

    @ApiModelProperty(value = "预产期")
    @NotNull(message = "预产期不能为空")
    private Integer predictBornTime;

    /**
     * 修改来源，1-C端，2-PI端
     */
    private Integer modifySource;
}
