package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * C端场景值配置
 * </p>
 *
 * <AUTHOR> @since 2024-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_scene")
@ApiModel(value="CustomerDataTrackingScenePO对象", description="C端场景值配置")
public class CustomerDataTrackingScenePO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "场景编码")
    private String scene;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;


}
