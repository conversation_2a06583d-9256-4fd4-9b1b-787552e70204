package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerInfoAddRequest", description="添加客户信息请求类")
public class CustomerDeliveryAddRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // 基本信息start

    @ApiModelProperty(value = "姓名",required = true)
    @Size(max = 20,message = "姓名不能大于20个字符")
    @NotBlank(message = "姓名不能为空")
    private String customerName;

    @ApiModelProperty(value = "手机号-同主表的手机号码",required = true)
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "1\\d{10}", message = "手机号码必须为1开头11位数字")
    private String phoneNumber;

    @ApiModelProperty(value = "身份证号码 15或者18位号码、大写字母",required = true)
    @NotBlank(message = "身份证号码不能为空")
    @Pattern(regexp = "/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/", message = "身份证格式不正确")
    private String cardNo;

    @ApiModelProperty(value = "生日",required = true)
    @NotNull(message = "生日不能为空")
    private Date birthday;

    @ApiModelProperty(value = "星座 0-白羊座、1-金牛座、2-双子座、3-巨蟹座、4-狮子座、5-处女座、6-天秤座、7-天蝎座、8-射手座、9-摩羯座、10-水瓶座、11-双鱼座",required = true)
    @NotNull(message = "星座不能为空")
    @Min(0)
    @Max(11)
    private Integer constellation;

    @ApiModelProperty(value = "身高 整数 单位cm",required = true)
    @NotNull(message = "身高不能为空")
    @DecimalMin("1")
    @DecimalMax(value = "300")
    private BigDecimal height;

    @ApiModelProperty(value = "体重 整数 单位kg",required = true)
    @NotNull(message = "体重不能为空")
    @DecimalMin("1")
    @DecimalMax("300")
    private BigDecimal weight;

    @ApiModelProperty(value = "饮食禁忌",required = true)
    @Size(max = 50)
    private String foodProhibition;

    @ApiModelProperty(value = "省-id",required = true)
    private Integer provinceId;

    @ApiModelProperty(value = "市-id",required = true)
    private Integer cityId;

    @ApiModelProperty(value = "区-id",required = true)
    private Integer districtId;

    @ApiModelProperty(value = "省-name",required = true)
    private String provinceName;

    @ApiModelProperty(value = "市-name",required = true)
    private String cityName;

    @ApiModelProperty(value = "区-name",required = true)
    private String districtName;

    @ApiModelProperty(value = "家庭地址",required = true)
    @NotNull(message = "地址不能为空")
    @Size(max = 100)
    private String address;

    @ApiModelProperty(value = "家属电话",required = true)
    @NotBlank(message = "家属电话不能为空")
    @Pattern(regexp = "1\\d{10}", message = "家属电话必须为1开头11位数字")
    private String familyPhone;

    @ApiModelProperty(value = "紧急联系人")
    @Size(max = 20)
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人电话")
    @Pattern(regexp = "1\\d{10}", message = "紧急联系人电话必须为1开头11位数字")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "基本信息备注")
    @Size(max = 500)
    private String remark;

    // 基本信息end

    // 健康信息start

    @ApiModelProperty(value = "妊娠糖尿病 1有 0无",required = true)
    @NotNull(message = "需妊娠糖尿病不能为空")
    @Min(0)
    @Max(1)
    private Integer gestationDiabetes;

    @ApiModelProperty(value = "高血压 1有 0无",required = true)
    @NotNull(message = "高血压字段不能为空")
    @Min(0)
    @Max(1)
    private Integer hypertension;

    @ApiModelProperty(value = "传染病",required = true)
    @NotBlank(message = "传染病字段不能为空")
    @Size(max = 100)
    private String contagion;

    @ApiModelProperty(value = "过敏反应",required = true)
    @NotBlank(message = "过敏反应字段不能为空")
    @Size(max = 100)
    private String allergy;

    // 健康信息end

    // 分娩信息start

    @ApiModelProperty(value = "胎次",required = true)
    @NotNull(message = "胎次不能为空")
    private Integer parity;

    @ApiModelProperty(value = "医院",required = true)
    @NotNull(message = "医院不能为空")
    @Size(max = 50)
    private String hospital;

    @ApiModelProperty(value = "分娩孕周",required = true)
    @NotNull(message = "分娩孕周不能为空")
    private Integer gestation;

    @ApiModelProperty(value = "预产期",required = true)
    @NotNull(message = "预产期不能为空")
    private Date anticipateDate;

    @ApiModelProperty(value = "医院具体地址",required = true)
    @NotNull(message = "医院具体地址不能为空")
    @Size(max = 100)
    private String hospitalAddress;

    @ApiModelProperty(value = "分娩方式  0-自然分娩 1-剖腹产",required = true)
    @NotNull(message = "分娩方式不能为空")
    @Min(0)
    @Max(1)
    private Integer deliveryMode;

    @ApiModelProperty(value = "宝宝昵称",required = true)
    @NotNull(message = "宝宝昵称不能为空")
    @Size(max = 20)
    private String babyNickname;

    @JsonIgnore
    @ApiModelProperty(value = "民族")
    private Integer nation;

    @ApiModelProperty(value = "哺乳方式 0-母乳 1-配方奶粉",required = true)
    @NotNull(message = "哺乳方式不能为空")
    @Min(0)
    @Max(1)
    private Integer lactationType;

    @NotNull(message = "入住天数不能为空")
    @ApiModelProperty(value = "入住天数",required = true)
    @Min(1)
    @Max(365)
    private Integer arrivalDay;

    @ApiModelProperty(value = "预计入馆时间",required = true)
    @NotNull(message = "预计入馆时间不能为空")
    private Date entryDate;

    @ApiModelProperty(value = "预定房型id",required = true)
    @NotNull(message = "预定房型不能为空")
    private Long roomTypeId;

    @ApiModelProperty(value = "预定房型名称",required = true)
    @NotBlank(message = "预定房型名称不能为空")
    private String roomTypeName;

    @ApiModelProperty(value = "黄疸值  单位mg/dl",required = true)
    @NotNull(message = "黄疸值不能为空")
    private Integer jaundiceValue;

    @ApiModelProperty(value = "出生体重  单位 g克",required = true)
    @NotNull(message = "出生体重不能为空")
    private Integer birthWeight;

    @ApiModelProperty(value = "需要车子  0是   1否",required = true)
    @NotNull(message = "需要车子字段不能为空")
    @Min(0)
    @Max(1)
    private Integer needCar;

    // 分娩信息end
}
