package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="StoreCustomerRelationRequest", description="")
public class StoreCustomerRelationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @ApiModelProperty(value = "客户id",required = true)
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @ApiModelProperty(value = "操作节点标签。0 员工添加新客户、1 员工添加访客记录",required = true)
    private Integer tagNode;

    @ApiModelProperty(value = "员工id",required = true)
    private Long empId;

    @ApiModelProperty(value = "员工姓名",required = true)
    private String empName;

}
