package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @date : 2024/9/23 4:06 PM
 */
@Data
public class DeletePushKnowledgeRequest implements Serializable {
	private static final long serialVersionUID = -161869138554412721L;

	private List<String> orderNos;

	@ApiModelProperty("文章主类型,0护理宣教")
	private Integer type;

	@ApiModelProperty("宣教类型 0:理论宣教,1:差异化宣教")
	private List<Integer> subtypes;

	@ApiModelProperty("是否小月子宣教")
	private Boolean isSmallMonth;
}
