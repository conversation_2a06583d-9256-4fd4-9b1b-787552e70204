package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "StoreAppointmentSaleUpdateRequest", description = "门店预约销售后台配置更新")
public class StoreAppointmentSaleUpdateRequest extends BasicReq {

    private static final long serialVersionUID = 7801742902696700807L;

    @ApiModelProperty(value = "门店列表id")
    @NotNull(message = "门店不能为空")
    private List<Integer> storeIdList;

    @ApiModelProperty(value = "销售id")
    @NotNull(message = "销售id不能为空")
    private Long saleId;

    @ApiModelProperty(value = "销售手机号")
    @NotNull(message = "销售手机号不能为空")
    private String salePhone;

    @ApiModelProperty(value = "销售中文名")
    @NotNull(message = "销售中文名不能为空")
    private String staffName;

    @ApiModelProperty(value = "销售英文名")
    private String staffNameEn;

    @ApiModelProperty(value = "销售头像")
    private String avatar;
}
