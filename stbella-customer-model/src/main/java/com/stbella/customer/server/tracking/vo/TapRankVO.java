package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 页面点击排名信息VO
 */
@Data
@ApiModel(value = "TapRankVO", description = "页面点击排名信息VO")
public class TapRankVO implements Serializable {

    private static final long serialVersionUID = -3484108365193459757L;

    @ApiModelProperty(value = "排序序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "事件id")
    private String eventId;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件备注")
    private String eventContent;

    @ApiModelProperty(value = "触发人数")
    private Long userView;

    @ApiModelProperty(value = "触发次数")
    private Long pageView;
}
