package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "BaseDataRequest", description = "(不分页)数据统计日期查询请求")
public class BaseDateRequest implements Serializable {


    private static final long serialVersionUID = 2334455451908426492L;
    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    private Date dateEnd;


    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;


    @ApiModelProperty(value = "是否读缓存，0-不读缓存 1-读缓存")
    private Integer cache;

}
