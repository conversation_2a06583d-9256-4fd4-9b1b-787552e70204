package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UserStatDetailExport implements Serializable {

    private static final long serialVersionUID = 1946948088765339612L;


    @Column(value = "日期",order = 1)
    private String statDate;

    @Column(value = "用户名",order = 2)
    private String clientName;

    @Column(value = "手机号",order = 3)
    private String phone;


    @Column(value = "访问次数",order = 4)
    private Long pageView;

    @Column(value = "访问时长(min)",order = 5)
    private BigDecimal durationTime;

    @Column(value = "平均使用时长(min)",order = 6)
    private BigDecimal avgDurationTime;

    @Column(value = "平均交互深度",order = 7)
    private BigDecimal avgInteractionNum;

    @Column(value = "页面平均停留时长(min)",order = 8)
    private BigDecimal avgPageDurationTime;

    public UserStatDetailExport(String statDate, String clientName, String phone, Long pageView, BigDecimal durationTime, BigDecimal avgDurationTime, BigDecimal avgInteractionNum, BigDecimal avgPageDurationTime) {
        this.statDate = statDate;
        this.clientName = clientName;
        this.phone = phone;
        this.pageView = pageView;
        this.durationTime = durationTime;
        this.avgDurationTime = avgDurationTime;
        this.avgInteractionNum = avgInteractionNum;
        this.avgPageDurationTime = avgPageDurationTime;
    }
}
