package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "CustomerUserEditInfoRequest", description = "c端修改用户信息request")
public class CustomerUserEditInfoRequest implements Serializable {

    private static final long serialVersionUID = -4712956626105830853L;

    @ApiModelProperty(value = "basicUid全局id")
    @NotNull(message = "basicUid不能为空")
    private Integer basicUid;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "头像地址")
    private String avatarUrl;

    @ApiModelProperty(value = "性别")
    private Integer gender;

    @ApiModelProperty(value = "品牌， 0-圣贝拉 1-小贝拉")
    private Integer brandType = 0;
}
