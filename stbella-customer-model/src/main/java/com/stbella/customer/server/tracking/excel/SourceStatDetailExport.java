package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SourceStatDetailExport implements Serializable {


    private static final long serialVersionUID = -8174007722690544713L;


    @Column(value = "渠道key",order = 1)
    private String sourceKey;

    @Column(value = "渠道名称",order = 2)
    private String sourceName;

    @Column(value = "渠道备注",order = 3)
    private String sourceContent;

    @Column(value = "访问人数",order = 4)
    private Long userView;

    @Column(value = "访问次数",order = 5)
    private Long pageView;

    @Column(value = "新增用户数",order = 6)
    private Long newUser;

    public SourceStatDetailExport(String sourceKey, String sourceName, String sourceContent, Long userView, Long pageView, Long newUser) {
        this.sourceKey = sourceKey;
        this.sourceName = sourceName;
        this.sourceContent = sourceContent;
        this.userView = userView;
        this.pageView = pageView;
        this.newUser = newUser;
    }
}
