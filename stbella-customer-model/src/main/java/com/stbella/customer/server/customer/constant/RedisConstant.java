package com.stbella.customer.server.customer.constant;

public class RedisConstant {

    public final static String CUSTOMER_SIGN_IN_LOCK = "customer:sign_in:lock:";


    //我的订单带来的积分
    public final static String MY_ORDER_INTEGRAL_COUNT = "myOrderIntegralCount";

    //我的到店参观带来的积分
    public final static String MY_VISIT_INTEGRAL_COUNT = "myVisitIntegralCount";

    //我的好友订单给我 带来的积分
    public final static String MY_FRIEND_ORDER_INTEGRAL_COUNT = "myFriendOrderIntegralCount";

    //我的好友到店参观给我带来的积分
    public final static String MY_FRIEND_VISIT_INTEGRAL_COUNT = "myFriendVisitIntegralCount";

    // 虎年新春积分赠送缓存key
    public final static String NEW_YEAR_INTEGRAL_GIFT_COUNT = "newYearIntegralGiftCount";


    public static final String APPOINTMENT_LOCK = "appointment_lock_";//预约商品


    public static final String CARITA_ACTIVITY_APPOINTMENT_ADD_LOCK = "CARITA_ACTIVITY_APPOINTMENT_ADD_LOCK_";

    public static final String CARITA_ACTIVITY_APPOINTMENT_UPDATE_LOCK = "CARITA_ACTIVITY_APPOINTMENT_UPDATE_LOCK_";

    public static final String CARITA_ACTIVITY_APPOINTMENT_CANCEL_LOCK = "CARITA_ACTIVITY_APPOINTMENT_CANCEL_LOCK_";

    /**
     * 用户离馆时生成授权记录锁
     */
    public static final String CHECKOUT_STORE_AUTHORIZATION_LOCK = "customer:checkout_authorization:";

    /**
     * 用户修改数据授权状态锁
     */
    public static final String CHECKOUT_STORE_AUTHORIZATION_STATUS_LOCK = "customer:checkout_authorization:status:";

    // -- 小月龄

    public static final String GOODS_APPOINTMENT_ADD_LOCK = "GOODS_APPOINTMENT_ADD_LOCK";

    public static final String GOODS_APPOINTMENT_CANCEL_LOCK = "GOODS_APPOINTMENT_CANCEL_LOCK_";

    public static final String GOODS_APPOINTMENT_CONFIRMED_LOCK = "GOODS_APPOINTMENT_CONFIRMED_LOCK";

    public static final String GOODS_APPOINTMENT_UPDATE_LOCK = "GOODS_APPOINTMENT_UPDATE_LOCK_";

    //-- 官网后台通用配置
    public static final String CUSTOMER_CONFIG_ = "customer_config_";

    //2025圣贝拉新年活动
    public static final String SBL_NEW_YEAR_ACTIVITY_2025_VISIT_ = "sbl_new_year:activity_2025_VISIT_";

    public static final String SBL_NEW_YEAR_ACTIVITY_2025_CHECKIN_ = "sbl_new_year:activity_2025_CHECKIN_";

    // 抖音广告平台的token和refre_token
    public static final String DOUYIN_ADS_ACCESS_TOKEN = "douyin_ads:access_token";
    public static final String DOUYIN_ADS_REFRESH_TOKEN = "douyin_ads:refresh_token";

    // better yeah ai会话key(robotId:userId)
    public static final String BETTER_YEAH_SESSION_KEY = "better_yeah:session:%s:%s";
    public static final String JOIN_CODE_REDIS_KEY = "join_code_redis_key:";
    public static final String JOIN_FAMILY = "join_family";

    // 门店邀请函天气缓存
    public static final String STORE_INVITATION_APPOINTMENT_WEATHER_CACHE = "store_invitation_appointment:";
	public static final String REMOVE_FAMILY = "remove_family";
}
