package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ListCustomerAvoidRequest implements Serializable {

    private static final long serialVersionUID = -9054839250054533874L;

    @ApiModelProperty(value = "客户basicId")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "禁忌类型 0:餐食禁忌 1:过敏食物 2:客户喜好")
    private Integer avoidType;

}
