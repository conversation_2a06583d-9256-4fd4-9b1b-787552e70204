package com.stbella.customer.server.customer.request;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "StoreAppointmentSaleListRequest", description = "门店及销售对应关系后台配置查询")
public class StoreAppointmentSaleListRequest extends PageBaseReq {

    private static final long serialVersionUID = 1956555283761616731L;

    @ApiModelProperty(value = "品牌类型")
    private Integer brandType;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;
}
