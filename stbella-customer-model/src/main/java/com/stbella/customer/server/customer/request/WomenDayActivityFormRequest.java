package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "WomenDayActivityFormRequest", description = "38活动报名请求")
public class WomenDayActivityFormRequest implements Serializable {

    private static final long serialVersionUID = -8600140226421475831L;

    @ApiModelProperty(value = "昵称")
    @NotBlank(message = "昵称不能为空")
    private String nickname;

    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户不能为空")
    private Long basicUid;

    @ApiModelProperty(value = "用户小程序的openid")
    private String openid;

    @ApiModelProperty(value = "品牌类型, 0-圣贝拉 1-小贝拉")
    private Integer brandType;
}
