package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "PagePromotionRequest", description = "生成通用小程序码或者短链请求")
public class PagePromotionRequest implements Serializable {

    private static final long serialVersionUID = 2305306489865206269L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序 100-艾屿小程序")
    @NotNull(message = "品牌类型不能为空")
    private Integer brandType;

    @ApiModelProperty(value = "页面地址")
    @NotNull(message = "页面地址不能为空")
    private String pagePath;

    @ApiModelProperty(value = "渠道或页面参数", notes = "在打开小程序时，会将该字段内容放在scene中，需客户端自行解析。\n"
        + "仅填字符串时，会默认为来源渠道，如wechat, 在scene中显示为source=wechat；需要其他参数，请自行用key=value进行拼接，如id=10001")
    private String source;

    /**
     * @see com.stbella.customer.server.tracking.enums.PagePromotionTypeEnum
     */
    @ApiModelProperty(value = "投放类型，0-小程序码 1-短连接")
    private Integer type;

    //isHyaline – 是否需要透明底色， is_hyaline 为true时，生成透明底色的小程序码
    @ApiModelProperty(value = "是否需要透明底色，默认为false")
    private Boolean hyaline;
}
