package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "SourceStatUserVO", description = "渠道概览数据-用户分布")
public class SourceStatUserVO implements Serializable {

    private static final long serialVersionUID = 9034232908708833096L;

    @ApiModelProperty(value = "渠道key")
    private String sourceKey;

    @ApiModelProperty(value = "渠道名称")
    private String sourceName;

    @ApiModelProperty(value = "客户数量")
    private Long userNumber;
}
