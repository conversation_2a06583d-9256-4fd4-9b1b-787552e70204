package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value="CustomerAdvertisementConfigSearchRequest", description="C端广告位配置列表请求Request")
public class CustomerAdvertisementConfigSearchRequest extends BasePageQuery {

    @ApiModelProperty(value = "0-首页 1-个人中心 2-会员中心")
    private Integer pageType;

    @ApiModelProperty(value = "页面选首页：选项为 0-首页 ,页面选个人中心：选项为 0-顶部广告位、1-腰部广告位")
    private Integer module;

    @ApiModelProperty(value = "门店品牌 0-圣贝拉 1-小贝拉")
    private Integer storeBrand;

    @ApiModelProperty(value = "是否有效 true false")
    private Boolean validFlag;

}
