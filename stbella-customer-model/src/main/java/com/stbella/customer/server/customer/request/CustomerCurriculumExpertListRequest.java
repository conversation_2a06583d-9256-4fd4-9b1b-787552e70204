package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerCurriculumExpertListRequest", description="专家专栏请求Request")
public class CustomerCurriculumExpertListRequest extends BasePageQuery implements Serializable {

    @ApiModelProperty(value = "课程分类")
    private Integer courseClass;
    @ApiModelProperty(value = "应用品牌 1）小贝拉 2）圣贝拉")
    private Integer brand;
    @ApiModelProperty(value = "用户全局ID")
    private Integer basicUid;
}
