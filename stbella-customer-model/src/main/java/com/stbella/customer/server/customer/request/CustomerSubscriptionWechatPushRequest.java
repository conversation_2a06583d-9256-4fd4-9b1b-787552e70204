package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "CustomerSubscriptionWechatPushRequest", description = "订阅消息推送请求")
public class CustomerSubscriptionWechatPushRequest implements Serializable {

    private static final long serialVersionUID = 8372468045924841334L;

    @ApiModelProperty(value = "应用品牌 0）圣贝拉,默认圣贝拉 1）小贝拉", required = true)
    private Integer brand = 0;

    @ApiModelProperty(value = "订阅类型1-电子杂志", required = true)
    @NotNull(message = "订阅类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "客户手机号列表")
    private List<String> phoneList;

    @ApiModelProperty(value = "客户openIdList")
    private List<String> openIdList;

    @ApiModelProperty(value = "服务项目")
    private String serviceName;

    @ApiModelProperty(value = "预约状态")
    private String appointmentStatus;

    @ApiModelProperty(value = "跳转小程序的路径地址")
    private String pageUrl;
}
