package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "YouzanVoucherInfoRequest", description = "有赞优惠券信息请求参数")
public class YouzanVoucherInfoRequest implements Serializable {

    private static final long serialVersionUID = 8258972842717476238L;

    @ApiModelProperty(value = "用户id")
    private Long basicId;

    @ApiModelProperty(value = "卡券信息")
    private VoucherIdentity voucherIdentity;

    @ApiModelProperty(value = "发放来源：(none, 非法渠道),(coupon_sending, 定向发券),(apps_cards, 刮刮卡),(apps_checkin, 签到),(apps_guess, 疯狂猜猜猜),(apps_shake, 摇一摇),(apps_vote, 投票调查),(apps_wheel, 大转盘),(apps_zodiac, 生肖翻翻看),(apps_visit_gift, 进店有礼),(scrm_card, 会员卡),(carmen, 有赞云接口),(baymax, 三方券或打通外部 CRM，通过开放平台发券),(meetreduce, 满减送),(poinsstore, 积分商城兑换),(wap_fetch, 普通领取),(wap_showcase, 微页面),(scrm_prepaid, 会员储值发卡),(fission, 裂变优惠券),(yz_anniversary, 有赞周年庆),(yz_meeting, 有赞会议),(divide_coupon, 瓜分券),(share_coupon,分享领券),(spotlight_platform, 有赞精选),(apps_help_cut, 砍价),(cim_interest, 兴趣人群),(mini_program, 小程序领取),(mini_program_11, 有赞精选双11小程序),(thirdparty, 三方券),(exchange, 下单页码值兑换),(spring_night, 春晚),(youzanke_promote, 有赞客优惠券领券页),(goods_details_take, 商详页领取),(goods_details_rcmd_take, 商详页推荐券领取),(groupbuy_take, 社区团购领取),(all, 通用券),(pay_coupon, 付费优惠券),(prepaid_timed_send, 储值礼包定时发券),(mediator_platform, 跨店权益平台),(goods_details_auto_take, 商详页推荐券自动领取),(wx_channel_live, 视频号直播发放),(xhs_channel, 小红书),(aliPay, 支付宝订单券领取),(wx_group_spread, 微信群定向券领取),(wecom_division_reward, 企微裂变奖励),(quntuantuan, 群团团小程序发放),(video_xiaodian, 视频号发送),(cim_precision, 精准人群)")
    private String sendSource;

    @ApiModelProperty(value = "买家端优惠券/码/码核销码，即买家在店铺个人中心看到的优惠券/码对应的核销码值，可用youzan.ump.coupon.consume.verify接口进行核销")
    private String verifyCode;

    @ApiModelProperty(value = "优惠券/码活动结束时间，Unix时间戳，单位：毫秒")
    private Date validStartTime;

    @ApiModelProperty(value = "优惠券/码活动结束时间，Unix时间戳，单位：毫秒")
    private Date validEndTime;

    @ApiModelProperty(value = "优惠券/码发放时间，Unix时间戳，单位：毫秒")
    private Date sendAt;

    @ApiModelProperty(value = "优惠券/码核销时间，Unix时间戳，单位：毫秒")
    private Date verifiedAt;

    @ApiModelProperty(value = "优惠券/码标题")
    private String title;

    @ApiModelProperty(value = "优惠券使用说明")
    private String description;

    @ApiModelProperty(value = "优惠券使用门槛类型，0无门槛，1满元，2满件")
    private Integer thresholdType;

    @ApiModelProperty(value = "优惠方式，1：代金券，2：折扣券，3：兑换券")
    private Integer preferentialMode;

    @ApiModelProperty(value = "门槛类型为1，满元时使用，使用门槛0：无门槛（默认值）n：门槛值（单位：分)")
    private Long thresholdAmount;

    @ApiModelProperty(value = "优惠面额（单位：分）,折扣（例：88，8.8折）")
    private Long value;

    @ApiModelProperty(value = "凭证状态(1, 正常), (2, 冻结), (3, 已核销), (4, 不可用), (5, 已删除), (6, 已转赠)")
    private Integer status;

    @ApiModelProperty(value = "卖家优惠码id，即卖家在店铺后台看到的优惠券/码对应的id")
    private Long activityId;

    @Data
    @ApiModel(value = "VoucherIdentity", description = "卡券信息")
    public static class VoucherIdentity implements Serializable {

        private static final long serialVersionUID = 3888932337757897055L;

        @ApiModelProperty(value = "优惠券ID")
        private Long couponId;

        @ApiModelProperty(value = "优惠券/码类型，0：优惠券，1：优惠码")
        private Integer couponType;
    }
}
