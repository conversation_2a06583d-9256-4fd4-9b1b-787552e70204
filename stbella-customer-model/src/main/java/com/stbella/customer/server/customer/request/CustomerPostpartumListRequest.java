package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="CustomerPostpartumListRequest", description="CustomerPostpartumListRequest")
public class CustomerPostpartumListRequest  {

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "关键字搜索")
    private String keyword;

}
