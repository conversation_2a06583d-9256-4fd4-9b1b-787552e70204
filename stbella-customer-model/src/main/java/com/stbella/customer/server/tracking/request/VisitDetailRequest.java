package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "VisitDetailRequest",description = "页面列表详情request")
public class VisitDetailRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -3691164637383533748L;


    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "页面名称")
    @NotNull(message = "页面名称不能为空")
    private String pageName;

    @ApiModelProperty(value = "页面路径")
    @NotNull(message = "页面路径不能为空")
    private String pagePath;

    @ApiModelProperty(value = "开始时间, 毫秒时间戳")
    private Date dateStart;

    @ApiModelProperty(value = "结束时间, 毫秒时间戳")
    private Date dateEnd;

    @ApiModelProperty(value = "姓名/手机号")
    private String nameOrPhone;

    @ApiModelProperty(value = "用户归属 0 全部，1 老用户,2 新用户")
    private Integer type;

}
