package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户C端跳转事件埋点
 * </p>
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_session")
@ApiModel(value = "CustomerDataTrackingSessionPO对象", description = "")
public class CustomerDataTrackingSessionPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @Deprecated
    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @Deprecated
    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @Deprecated
    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @Deprecated
    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    private String sessionid;

    @ApiModelProperty(value = "来源渠道key")
    private String source;

    @ApiModelProperty(value = "微信渠道来源")
    private String scene;

    @ApiModelProperty(value = "微信渠道来源key值")
    private Integer sceneKey;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "停留时长")
    private Long durationTime;

    @Deprecated
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "离开的页面地址")
    private String lastPagePath;

    @ApiModelProperty(value = "访问深度")
    private Integer accessDepth;
}
