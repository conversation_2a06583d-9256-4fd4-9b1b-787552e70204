package com.stbella.customer.server.customer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * customer user nurse info vo
 * customer user nurse info vo
 * customer user nurse info vo
 * customer user nurse info vo
 * customer user nurse info vo
 * customer user nurse info vo
 * customer user nurse info vo
 *
 * <AUTHOR>
 * @date 2023/10/23 05:19:54
 */
@ApiModel(value = "CustomerUserNurseInfoVO", description = "用户-护理信息vo")
@Data
@Accessors(chain = true)
public class CustomerUserNurseInfoVO implements Serializable {
    private static final long serialVersionUID = 3038438898269499758L;


    @ApiModelProperty(value = "")
    private Integer checkDay;

    @ApiModelProperty(value = "")
    private RoomInfo room;

    @ApiModelProperty(value = "")
    public Integer isOrder;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "品牌名")
    private String storeType;

    @ApiModelProperty(value = "")
    private Integer appointmentStatus;

    @ApiModelProperty(value = "")
    private Integer productionAppoinmentShow = 0;

    @ApiModelProperty(value = "basicId")
    private Integer basicId;

    @ApiModelProperty(value = "")
    private String nickName;

    @ApiModelProperty(value = "")
    private Integer clientId;

    @ApiModelProperty(value = "")
    private String clientName;

    @ApiModelProperty(value = "")
    private String clientPhone;

    @ApiModelProperty(value = "")
    private String encourage;

    @ApiModelProperty(value = " 是否有需要弹框的离馆小结, 0：没有，非0：有，且值为要跳转的离馆小结id")
    private Long checkoutSummaryPop;

    @ApiModelProperty(value = "客户当前状态,0:未入住 1:入住中 2:已离馆 如果存在已离馆订单，则优先返回已离馆。用于前端控制“离馆指南”是否可点击")
    private Integer clientCurrentCheckinStatus;

    @ApiModelProperty(value = "离馆小结")
    private Integer hasCheckoutSummary;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "预产期")
    private Integer predictBornTime;

    @ApiModelProperty(value = "预产备注")
    private String bornTimeRemark;

    @ApiModelProperty(value = "是否为小月龄")
    private Boolean smallAgeOrder;

    @ApiModelProperty(value = "客户服务次数")
    private Integer customerServiceNum;


    @Data
    public static class RoomInfo implements Serializable {

        private static final long serialVersionUID = 136574399832065992L;

        @ApiModelProperty(value = "入住时间")
        private String clientInTime;

        @ApiModelProperty(value = "离馆时间")
        private String clientOutTime;

    }

}
