package com.stbella.customer.server.nutuition.request;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.core.base.BaseEntity;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.delivery.DeliveryModeEnum;
import com.stbella.customer.server.nutuition.enums.CustomerIntentionNutritionEnum;
import com.stbella.customer.server.nutuition.enums.CustomerNutritionSocialRelationsEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @since 2021-11-25
 */
@Data
@ApiModel(value = "AddNutritionRequest", description = "新增客户入参")
public class NutritionRequest extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户姓名")
    @NotBlank(message = "用户姓名不能为空")
    private String customerName;

    @ApiModelProperty(value = "用户手机号")
    @Pattern(regexp = "^[1]([3-9])[0-9]{9}$", message = "请输入正确的手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "年龄")
    @Min(value = 0, message = "最小年龄为0")
    @Max(value = 99999, message = "最大年龄为99999")
    private Integer age;

    @ApiModelProperty(value = "地址-省")
    @NotBlank(message = "省不能为空")
    private String addressProvince;

    @ApiModelProperty(value = "地址-市")
    @NotBlank(message = "市不能为空")
    private String addressCity;

    @ApiModelProperty(value = "地址-区")
    @NotBlank(message = "区不能为空")
    private String addressArea;

    @ApiModelProperty(value = "地址-详情")
    @NotBlank(message = "详情地址不能为空")
    private String addressDetails;

    @ApiModelProperty(value = "预产期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "预产期不能为空")
    private Date anticipateDate;

    @ApiModelProperty(value = "分娩方式  0-自然产；1-剖腹产；2-其他")
    @EnumValue(enumClass = DeliveryModeEnum.class, enumMethod = "isValidCode")
    @NotNull(message = "分娩方式不能为空")
    private Integer deliveryMode;

    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty(value = "邮箱")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$", message = "请输入正确的邮箱")
    private String email;

    @ApiModelProperty(value = "紧急联系人")
    @NotBlank(message = "紧急联系人不能为空")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人电话")
    @NotBlank(message = "紧急联系人电话不能为空")
    @Pattern(regexp = "^[1]([3-9])[0-9]{9}$", message = "请输入正确的紧急联系人电话")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "社会关系：0-配偶；1-父母；2-其他")
    @EnumValue(enumClass = CustomerNutritionSocialRelationsEnum.class, enumMethod = "isValidCode")
    @NotNull(message = "社会关系不能为空")
    private Integer socialRelations;

    @ApiModelProperty(value = "客户来源：0：自行开发；")
    private Integer source;

    @ApiModelProperty(value = "所属销售")
    private Long sellId;

    @ApiModelProperty(value = "所属销售名字")
    private String sellName;

    @ApiModelProperty(value = "标签编号：0-低意向；1-中意向；2-高意向")
    @EnumValue(enumClass = CustomerIntentionNutritionEnum.class, enumMethod = "isValidCode")
    private Integer lebelId;

    @ApiModelProperty(value = "套餐编号")
    private Integer comboId;

    @ApiModelProperty(value = "创建编号", hidden = true)
    private Long createBy;

    @ApiModelProperty(value = "创建名字", hidden = true)
    private String createByName;

    @ApiModelProperty(value = "更新编号", hidden = true)
    private Long updateBy;

    @ApiModelProperty(value = "更新名字", hidden = true)
    private String updateByName;

    public Integer getLebelId() {
        if (ObjectUtil.isNull(lebelId) || lebelId.equals(-1)) {
            return null;
        }
        return this.lebelId;
    }
}
