package com.stbella.customer.server.scrm.enums;

import lombok.Getter;

/**
 * scrm配置类型枚举
 */
public enum ScrmConfigBizTypeEnum {
    ACCOUNT_DEFAULT_TYPE(10001, "客户-默认业务类型"),
    ACCOUNT_BUSINESS_TYPE(10002, "客户-企业客户"),
    OPPORTUNITY_STANDARD_MONTH_ORDER(10003, "销售机会-标准月子"),
    OPPORTUNITY_SMALL_MONTH_ORDER(10004, "销售机会-小月子"),
    OPPORTUNITY_NURSE_ORDER(10005, "销售机会-护士外派"),
    OPPORTUNITY_PRODUCTION_ORDER(10006, "销售机会-产康"),
    OPPORTUNITY_SBRA_ORDER(10007, "销售机会-sbra"),
    OPPORTUNITY_OTHER_ORDER(10008, "销售机会-其他"),
    OPPORTUNITY_HIGH_SEA_SAINT_BELLA(10009, "销售机会公海池-圣贝拉"),
    OPPORTUNITY_HIGH_SEA_BABY_BELLA(10010, "销售机会公海池-小贝拉"),
    OPPORTUNITY_DEFAULT_PRICE(10011, "销售机会-默认价格表"),
    ORDER_OBJECT(10012, "订单对象名"),
    SALE_STAG_FIRST(10013, "销售阶段-标准月子-首次触达"),
    SALE_STAG_CHECKIN_STORE(10014, "销售阶段-标准月子-邀约到店"),
    SALE_STAG_WIN_ORDER(10015, "销售阶段-标准月子-签单"),
    SALE_STAG_LOSE_ORDER(10016, "销售阶段-标准月子-输单"),
    ORDER_MONTH(10017, "支付订单-标准月子订单"),
    ORDER_SMALL_MONTH(10018, "支付订单-小月子订单"),
    ORDER_NURSE_OUTSIDE(10019, "支付订单-护士外派订单"),
    ORDER_PRODUCTION(10020, "支付订单-产康订单"),
    ORDER_SBRA(10021, "支付订单-sbra"),
    ORDER_OTHER_MONTH(10022, "支付订单-其他订单"),
    CHECKIN_DEFAULT_TYPE(10023, "扫码信息-默认业务类型"),
    CHECKIN_OBJECT(10024, "到店打卡对象名"),
    CONTACT_RELATIVES_AND_FRIENDS(10025, "联系人-亲友"),
    CONTACT_BABY(10026, "联系人-宝宝"),
    ACCOUNT_OBJECT(10027, "客户对象名"),
    USER_OBJECT(10028, "用户(员工)对象名"),
    CONTACT_OBJECT(10029, "联系人对象名"),
    OPPORTUNITY_OBJECT(10030, "销售机会对象名"),
    TEAMMEMBER_OBJECT(10031, "团队成员对象名"),
    ACTIVITYRECORD_OBJECT(10032, "活动记录对象名"),
    OPPORTUNITY_TEMP_OBJECT(10033, "销售机会中间表对象名"),
    HIGH_SEAAS_POOL_MANAGER(10038, "商机-公海池管理员"),
    SCRM_SYSTEM_MANAGER_ID(20001, "scrm系统中系统管理员账号id"),
    CUSTOM_USER_STORE_OBJECTT(10039, "销售与门店关系对象名"),
    ACCOUNT_OBJECT_ID(30001, "客户对象id"),
    CONTACT_OBJECT_ID(30002, "联系人对象id"),
    OPPORTUNITY_OBJECT_ID(30003, "销售机会对象id"),
    TEAM_MEMBER_DEFAULT_TYPE(10040, "团队成员-默认业务类型"),
    SALESPHASE_SMALLMONTH_FIRSTTOUCH(10041,"销售阶段-小月子-首次触达"),
    SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE(10042,"销售阶段-小月子-邀约到店"),
    SALESPHASE_SMALLMONTH_WINSINGLE(10043,"销售阶段-小月子-签单"),
    SALESSTAGE_SMALLMONTH_LOSETHEBILL(10044,"销售阶段-小月子-输单"),
    SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH(10045,"销售阶段-护士外派-首次触达"),
    SALESPHASE_NURSEASSIGNMENT_WINASINGLE(10046,"销售阶段-护士外派-签单"),
    SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS(10047,"销售阶段-护士外派-输单"),
    SALESPHASE_PRODUCTION_FIRSTTOUCH(10048,"销售阶段-产康-首次触达"),
    SALESPHASE_PRODUCTION_WINSINGLE(10049,"销售阶段-产康-签单"),
    SALESSTAGE_PRODUCTION_DELIVERY(10050,"销售阶段-产康-输单"),
    SALESPHASE_SBAR_FIRSTTOUCH(10051,"销售阶段-Sbar-首次触达"),
    SALESPHASE_SBAR_WINNINGORDER(10052,"销售阶段-Sbar-签单"),
    SALESPHASE_SBAR_TOLOSETHEORDER(10053,"销售阶段-Sbar-输单"),
    SALESPHASE_OTHER_FIRSTTOUCH(10054,"销售阶段-其他-首次触达"),
    SALESPHASE_OTHER_WINNINGSINGLE(10055,"销售阶段-其他-签单"),
    SALESPHASE_OTHER_LOSINGORDERS(10056,"销售阶段-其他-输单"),
    OPPORTUNITY_TEMP_DEFAULT_TYPE(10057,"销售机会中间表-默认业务类型"),
    ASSIGN_ACCOUNT_TEMP_DEFAULT_TYPE(10058,"400分配客资中间表-默认业务类型"),
    ASSIGN_ACCOUNT_TEMP_OBJECT(10059,"400分配客资中间表对象名"),
    ORDER_REFUND_DEFAULT_TYPE(10060, "母婴业务退单明细表-默认业务类型"),
    ORDER_REFUND_OBJECT(10061, "母婴业务退单明细表对象名"),
    SATISFACTIONINVESTIGATE_DEFAULT_TYPE(10062, "满意度调查-默认业务类型"),
    SATISFACTIONINVESTIGATE_MAIN_NURSE_DEFAULT_TYPE(10063, "满意度调查-主要护士-默认业务类型"),
    SATISFACTIONINVESTIGATE_OTHER_NURSE_DEFAULT_TYPE(10064, "满意度调查-其他护士-默认业务类型"),
    SALE_RESPONSIBILITY_KEY(10065, "销售职能key值"),
    OPPORTUNITY_HIGH_SEA_BELLA_ISLA(10066, "销售机会公海池-艾屿"),
    SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE(10067,"销售阶段-产康-邀约到店"),
    OPPORTUNITY_400_BUSINESS(10068, "销售机会-400商机"),
    SALESPHASE_400_BUSINESS_INITIAL_COMMUNICATION(10069, "销售阶段-400商机-初步沟通"),
    SALESPHASE_400_BUSINESS_WAIT_1_DAY(10070, "销售阶段-400商机-等待1天"),
    SALESPHASE_400_BUSINESS_WAIT_3_DAYS(10071, "销售阶段-400商机-等待3天"),
    SALESPHASE_400_BUSINESS_RELEASED(10072, "销售阶段-400商机-已释放"),
    SALESPHASE_400_BUSINESS_CONVERTED(10073, "销售阶段-400商机-已转商机"),
    SALESPHASE_400_BUSINESS_INVALID(10074, "销售阶段-400商机-无效线索"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    ScrmConfigBizTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
