package com.stbella.customer.server.customer.request;

import com.stbella.store.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "CustomerCheckoutSummaryListRequest", description = "离馆小结列表查看request")
public class CustomerCheckoutSummaryListRequest extends PageBaseReq implements Serializable {

    private static final long serialVersionUID = 1375405280037906506L;

    @ApiModelProperty(value = "客户basicId", required = true)
    @NotNull(message = "客户id不能为空")
    private Integer basicId;

    @ApiModelProperty(value = "门店类型，0:圣贝拉 1:小贝拉", required = true)
    @NotNull(message = "品牌类型不能为空")
    private Integer storeType;
}
