package com.stbella.customer.server.tracking.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EventDetailExport implements Serializable {


    private static final long serialVersionUID = 2602461033288223260L;
    @Column(value = "日期", order = 1)
    private String startTime;

    @Column(value = "姓名", order = 2)
    private String clientName;

    @Column(value = "手机号", order = 3)
    private String phone;

    @Column(value = "触发页面", order = 4)
    private String pageName;

    @Column(value = "页面地址", order = 5)
    private String pagePath;

    @Column(value = "事件备注", order = 6)
    private String eventContent;

    public EventDetailExport(String startTime, String clientName, String phone, String pageName, String pagePath, String eventContent) {
        this.startTime = startTime;
        this.clientName = clientName;
        this.phone = phone;
        this.pageName = pageName;
        this.pagePath = pagePath;
        this.eventContent = eventContent;
    }
}
