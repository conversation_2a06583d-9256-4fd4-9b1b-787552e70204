package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "CustomerWechatInfoRequest", description = "查询用户的微信信息")
public class CustomerWechatInfoRequest implements Serializable {

    private static final long serialVersionUID = -4251262368711440933L;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "品牌， 0-圣贝拉 1-小贝拉")
    private Integer brandType;
}
