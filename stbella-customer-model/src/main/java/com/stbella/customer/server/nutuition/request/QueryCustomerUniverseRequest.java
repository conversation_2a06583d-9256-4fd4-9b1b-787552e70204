package com.stbella.customer.server.nutuition.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCustomerUniverseRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "客户来源渠道")
    private List<Integer> customerSource;

    @ApiModelProperty(value = "最近下单开始查询时间")
    private Date orderTimeStart;

    @ApiModelProperty(value = "最近下单开始查询时间")
    private Date orderTimeEnd;

}
