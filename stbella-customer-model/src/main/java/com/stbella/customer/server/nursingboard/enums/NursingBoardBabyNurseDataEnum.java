package com.stbella.customer.server.nursingboard.enums;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum NursingBoardBabyNurseDataEnum {

    SHIT_NUM(0, "大便次数"),
    PEE_NUM(1, "小便次数"),
    DRINK_MILK_ML(2, "喝奶量"),
    DRINK_MILK_TIME(3, "喝奶时长"),
    SHIT(4, "大便"),
    PEE(5, "小便");

    private final Integer code;
    private final String name;

    NursingBoardBabyNurseDataEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    public static NursingBoardBabyNurseDataEnum fromCode(Integer code) {
        if (!Objects.isNull(code)) {
            return Arrays.stream(NursingBoardBabyNurseDataEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
        }
        return null;

    }

    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            final Optional<NursingBoardBabyNurseDataEnum> first = Arrays.stream(
                NursingBoardBabyNurseDataEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            return first.map(NursingBoardBabyNurseDataEnum::getName).orElse(null);
        }
        return null;

    }


    public static NursingBoardBabyNurseDataEnum fromName(String name) {
        if (!StringUtils.isBlank(name)) {
            return Arrays.stream(NursingBoardBabyNurseDataEnum.values()).filter(i -> Objects.equals(i.getName(), name)).findFirst().orElse(null);
        }
        return null;

    }
}
