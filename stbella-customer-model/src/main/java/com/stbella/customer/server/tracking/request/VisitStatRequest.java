package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "VisitStatRequest",description = "页面统计列表查询")
public class VisitStatRequest extends DataStatDateRequest implements Serializable {
    private static final long serialVersionUID = -797088025730946352L;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "排序字段, userView:访问人数 pageView:访问次数 durationTime:访问时长")
    private String field = "userView";

    @ApiModelProperty(value = "排序方式, asc:正序 desc:倒序")
    private String sort = "desc";

}
