package com.stbella.customer.server.nursingboard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "DailySummaryDataVO",description = "护理看板-医生建议")
public class DailySummaryDataVO implements Serializable {

    private static final long serialVersionUID = 6503901207035340203L;

    @ApiModelProperty(value = "护理长查房记录")
    private DailySummaryDetailDataVO innerCheckRoomRecord;

    @ApiModelProperty(value = "中医查房-医师建议（宝妈）")
    private DailySummaryDetailDataVO chineseMedicineCheckDoctorAdvice;

    @ApiModelProperty(value = "产科查房-医师建议（宝妈）")
    private DailySummaryDetailDataVO customerCheckRoomDoctorAdvice;

    @ApiModelProperty(value = "儿科查房-医师建议（宝宝）")
    private DailySummaryDetailDataVO boardPediatricsCheckDoctorAdvice;

}
