package com.stbella.customer.server.nursingboard.request;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "MomCheckRoomDetailRequest", description = "护理看板-妈妈查房详情request")
public class MomCheckRoomDetailRequest extends AbstractBrandRequest implements Serializable {


    private static final long serialVersionUID = 6193839116247122079L;


    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "门店类型 0圣贝拉 1小贝拉")
    private Integer storeType;

    @ApiModelProperty(value = "是否是演示状态, 0-否 1-是")
    private Integer damonStatus;

    @ApiModelProperty(value = "演示状态，0-游客 1-入住中 2-入住前 3-已离馆")
    private Integer damonType;


    @ApiModelProperty(value = "客户家庭成员id 产妇家属身份进来必传")
    private Long customerFamilyId;

    @Override
    public void processBrandType(Object req) {
        MomCheckRoomDetailRequest request = (MomCheckRoomDetailRequest) req;
        //老版本brandType不会传
        if(request.getBrandType()==null) {
            this.setBrandType(request.getStoreType());
        }else{
            //新版本storeType不会传
            this.setStoreType(request.getBrandType());
        }
        if(request.getBrandType()==null && request.getStoreType()==null){
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "品牌参数必传");
        }
    }
}
