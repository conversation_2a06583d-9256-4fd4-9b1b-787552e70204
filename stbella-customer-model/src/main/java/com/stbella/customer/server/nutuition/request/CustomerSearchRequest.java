package com.stbella.customer.server.nutuition.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerSearchRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模糊搜索文本", required = true)
    private String text;
}
