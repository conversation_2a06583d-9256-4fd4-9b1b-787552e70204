package com.stbella.customer.server.nutuition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CustomerUniverseListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "全域客户id")
    private Long customerUniverseId;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "来源渠道")
    private List<String> customerSource;

    @ApiModelProperty(value = "全域订单金额")
    private BigDecimal allAmount;

    @ApiModelProperty(value = "全域结算金额")
    private BigDecimal allRealityAmount;

    @ApiModelProperty(value = "最近下单时间")
    private Date lastOrderTime;

    @ApiModelProperty(value = "创建时间时间")
    private Date createTime;

}
