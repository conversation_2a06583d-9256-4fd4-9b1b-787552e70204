package com.stbella.customer.server.nutuition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> @since 2021-11-25
 */
@Data
@ApiModel(value = "UserBaseInfoVO", description = "客户基本信息")
public class UserBaseInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户主表主键id")
    private Long id;

    @ApiModelProperty(value = "用户手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "用户姓名")
    private String customerName;

}
