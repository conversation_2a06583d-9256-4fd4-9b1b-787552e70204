package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户C端跳转事件埋点
 * </p>
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_visit")
@ApiModel(value = "CustomerDataTrackingVisitPO对象", description = "")
public class CustomerDataTrackingVisitPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    private String sessionid;

    @ApiModelProperty(value = "来源渠道")
    private String source;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "停留时长")
    private Long durationTime;

    @ApiModelProperty(value = "下一页面名称")
    private String pageNameNext;

    @ApiModelProperty(value = "下一页面地址")
    private String pagePathNext;

    @ApiModelProperty(value = "上一页面名称")
    private String pageNamePre;

    @ApiModelProperty(value = "上一页面地址")
    private String pagePathPre;
}
