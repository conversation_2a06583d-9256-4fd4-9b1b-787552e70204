package com.stbella.customer.server.scrm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "ServiceOpportunityQuery", description = "400商机查询")
public class ServiceOpportunityQuery implements Serializable {

    private static final long serialVersionUID = 2874072562242917522L;

    @ApiModelProperty("商机阶段ID")
    private Long stageId;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;
}
