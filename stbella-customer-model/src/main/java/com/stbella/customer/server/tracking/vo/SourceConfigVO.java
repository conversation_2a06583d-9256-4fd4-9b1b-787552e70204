package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "SourceConfigVO", description = "渠道列表VO")
public class SourceConfigVO implements Serializable {

    private static final long serialVersionUID = 1065152461770382304L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "来源key，用于定义不同的来源渠道")
    private String sourceKey;

    @ApiModelProperty(value = "来源名称")
    private String sourceName;
}
