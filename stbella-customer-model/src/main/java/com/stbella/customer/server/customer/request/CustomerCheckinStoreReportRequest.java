package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerCheckinStoreReportRequest", description = "探店打卡报告查询")
public class CustomerCheckinStoreReportRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -8128201090885883825L;

    @ApiModelProperty(value = "销售id")
    private Integer saleId;

    @ApiModelProperty(value = "销售门店id")
    private Integer saleStoreId;

    @ApiModelProperty(value = "销售手机号")
    private String salePhone;

    @ApiModelProperty(value = "客户手机号")
    private String clientPhone;

    @ApiModelProperty(value = "搜索的姓名和手机号")
    private String nameOrPhone;

    @ApiModelProperty(value = "状态 0草稿，1已发送， 不传查全部")
    private Integer status;

    @ApiModelProperty(value = "活动品牌, 0-圣贝拉 1-小贝拉 100-艾屿")
    private Integer brandType;
}
