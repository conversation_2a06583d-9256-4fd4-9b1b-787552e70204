package com.stbella.customer.server.customer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "CustomerProductionAmountStreamVO", description = "客户产康金流水信息")
public class CustomerProductionAmountStreamVO implements Serializable {

    private static final long serialVersionUID = 8873659023934486448L;

    @ApiModelProperty(value = "流水id")
    private Long id;

    @ApiModelProperty(value = "流水标题")
    private String title;

    @ApiModelProperty(value = "账户类型")
    private Long assetType;

    @ApiModelProperty(value = "账户类型名称")
    private String assetTypeName;

    @ApiModelProperty(value = "流水金额")
    private Long balance;

    @ApiModelProperty(value = "余额")
    private Long afterBalance;

    @ApiModelProperty(value = "流水类型, +/-")
    private String affectTag;

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "年月, 格式:yyyy.MM")
    private String month;
}
