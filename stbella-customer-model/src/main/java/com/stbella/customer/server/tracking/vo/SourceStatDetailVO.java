package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "SourceStatDetailVO", description = "渠道统计详情VO")
public class SourceStatDetailVO implements Serializable {

    private static final long serialVersionUID = -3388851083853439708L;

    @ApiModelProperty(value = "渠道key")
    private String sourceKey;

    @ApiModelProperty(value = "渠道名称")
    private String sourceName;

    @ApiModelProperty(value = "渠道备注")
    private String sourceContent;

    @ApiModelProperty(value = "访问人数")
    private Long userView;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "新增用户数")
    private Long newUser;
}
