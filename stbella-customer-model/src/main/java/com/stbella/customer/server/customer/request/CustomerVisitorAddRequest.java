package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.ChannelSourceEnum;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerVisitorAddRequest", description="新增访客列表")
public class CustomerVisitorAddRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @JsonIgnore
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户表ID",required = true)
    @NotNull(message = "客户表ID不能为空")
    private Long customerId;

    @ApiModelProperty(value = "母婴顾问员工id",required = true)
    @NotNull(message = "母婴顾问员工ID不能为空")
    private Long receptionId;

    @ApiModelProperty(value = "母婴顾问员工姓名")
    @JsonIgnore
    private String receptionName;

    @ApiModelProperty(value = "接待参观日期 yyyy-MM-dd",required = true)
    @NotNull(message = "接待参观日期不能为空")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date visitorDate;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "手机号码国家前缀")
    private String nationCode;

    @NotBlank(message = "联系方式不能为空")
    @Pattern(regexp = "1\\d{10}", message = "联系方式必须为1开头11位数字")
    @ApiModelProperty(value = "联系方式",required = true)
    private String phoneNumber;

    @ApiModelProperty(value = "年龄")
    @Min(value = 1,message = "年龄最小1")
    @Max(value = 200,message ="年龄最大200" )
    private Integer age;

    @Min(value = 1,message = "胎次不能小于1")
    @Max(value = 20,message = "胎次不能大于20")
    @ApiModelProperty(value = "胎次")
    private Integer parity;

    @ApiModelProperty(value = "渠道来源 0-口碑介绍、1-大众点评、2-百度/谷歌、3-合作渠道、4-线下广告、5-线下活动、6-官网、7-小红书、8-微信公众号、9-微博、10-微信小程序、11-抖音、12-已入馆宝妈、13-朋友圈广告、14-医疗渠道、15-其他")
    @EnumValue(enumClass = ChannelSourceEnum.class, enumMethod = "isValidCode")
    private Integer channelSource;

    @ApiModelProperty(value = "预产期 yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date anticipateDate;

    @ApiModelProperty(value = "居住区域 省-id")
    private Integer provinceId;

    @ApiModelProperty(value = "居住区域 市-id")
    private Integer cityId;

    @ApiModelProperty(value = "居住区域 区-id")
    private Integer districtId;

    @ApiModelProperty(value = "居住区域 省-name")
    private String provinceName;

    @ApiModelProperty(value = "居住区域 市-name")
    private String cityName;

    @ApiModelProperty(value = "居住区域 区-name")
    private String districtName;

    @ApiModelProperty(value = "居住区域详情")
    @Size(max=50)
    private String address;

    @ApiModelProperty(value = "是否有推荐码。0没有 1有")
    @EnumValue(enumClass = TrueFalseEnum.class, enumMethod = "isValidCode")
    private Integer referralCodeFlag;

    @ApiModelProperty(value = "推荐码")
    @Size(max=6)
    private String referralCode;

    @JsonIgnore
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @JsonIgnore
    @ApiModelProperty(value = "创建人名称")
    private String createByName;

}
