package com.stbella.customer.server.scrm.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-03-15
 */
@Data
@Accessors(chain = true)
@TableName("scrm_business_opportunity")
@ApiModel(value="ScrmBusinessOpportunityPO对象", description="")
public class ScrmBusinessOpportunityPO {

    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("逻辑删除标识 0-未删除 1-已删除")
    @TableLogic(value = "0", delval = "1")
    @JsonIgnore
    private Integer deleted;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long scrmId;

    @ApiModelProperty(value = "业务类型")
    private Long entityType;

    @ApiModelProperty(value = "销售机会所有人")
    private Long ownerId;

    @ApiModelProperty(value = "机会名称")
    private String opportunityName;

    @ApiModelProperty(value = "客户名称")
    private Long customerId;

    @ApiModelProperty(value = "价格表名称")
    private Long priceId;

    @ApiModelProperty(value = "机会类型")
    private Integer opportunityType;

    @ApiModelProperty(value = "销售金额")
    private BigDecimal money;

    @ApiModelProperty(value = "销售阶段")
    private Long saleStageId;

    @ApiModelProperty(value = "输单阶段")
    private Long lostStageId;

    @ApiModelProperty(value = "赢率")
    private Long winRate;

    @ApiModelProperty(value = "输单原因")
    private Integer reason;

    @ApiModelProperty(value = "输单描述")
    private String reasonDesc;

    @ApiModelProperty(value = "结单日期")
    private Date closeDate;

    @ApiModelProperty(value = "承诺")
    private Integer commitmentFlg;

    @ApiModelProperty(value = "机会来源")
    private Integer sourceId;

    @ApiModelProperty(value = "项目预算")
    private BigDecimal projectBudget;

    @ApiModelProperty(value = "实际花费")
    private BigDecimal actualCost;

    @ApiModelProperty(value = "产品")
    private String product;

    @ApiModelProperty(value = "阶段更新时间")
    private Date stageUpdatedAt;

    @ApiModelProperty(value = "最新活动记录时间")
    private Date recentActivityRecordTime;

    @ApiModelProperty(value = "创建日期")
    private Date createdAt;

    @ApiModelProperty(value = "创建人")
    private Long createdBy;

    @ApiModelProperty(value = "最新修改日")
    private Date updatedAt;

    @ApiModelProperty(value = "最新修改人")
    private Long updatedBy;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "所属部门")
    private Long dimDepart;

    @ApiModelProperty(value = "区域")
    private Long territoryId;

    @ApiModelProperty(value = "锁定状态")
    private Integer lockStatus;

    @ApiModelProperty(value = "市场活动")
    private Long campaignId;

    @ApiModelProperty(value = "审批状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "销售线索")
    private Long leadId;

    @ApiModelProperty(value = "商机得分")
    private BigDecimal opportunityScore;

    @ApiModelProperty(value = "区域公海状态")
    private Integer territoryHighSeaStatus;

    @ApiModelProperty(value = "预计回收时间")
    private Date territoryExpireTime;

    @ApiModelProperty(value = "所属区域公海")
    @TableField("territory_high_sea_id")
    private Long territoryHighSeaId;

    @ApiModelProperty(value = "预测金额")
    private BigDecimal fcastMoney;

    @ApiModelProperty(value = "阶段分类")
    private Integer forecastCategory;

    @ApiModelProperty(value = "疑似查重")
    private Integer duplicateFlg;

    @ApiModelProperty(value = "赢单原因")
    private Integer winReason;

    @ApiModelProperty(value = "赢单描述")
    private String winReasonDesc;

    @ApiModelProperty(value = "审批提交人")
    private Long applicantId;

    @ApiModelProperty(value = "退回原因")
    private Integer reasonReturn;

    @ApiModelProperty(value = "退回备注")
    private String reasonComment;

    @ApiModelProperty(value = "延期原因")
    private Integer delayReason;

    @ApiModelProperty(value = "延期备注")
    private String delayComment;

    @ApiModelProperty(value = "分配时间")
    private Date allocateTime;

    @ApiModelProperty(value = "签到信息")
    private Long signInInformation;

    @ApiModelProperty(value = "咨询日期")
    private Date consultationTime;

    @ApiModelProperty(value = "到店日期")
    private String arrivalTime;

    @ApiModelProperty(value = "无效商机")
    private String ownedStore;

    @ApiModelProperty(value = "商机所属门店关系")
    private Long contractedStore;

    @ApiModelProperty(value = "月子订单")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long monthOrder;

    @ApiModelProperty(value = "非月子订单")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long nonMonthOrder;

    @ApiModelProperty(value = "意向省份")
    private Integer intendedProvince;

    @ApiModelProperty(value = "意向城市")
    private Integer intendedCity;

    @ApiModelProperty(value = "意向区/县")
    private Integer intendedArea;

    @ApiModelProperty(value = "意向门店")
    private Integer intendedStore;

    @ApiModelProperty(value = "意向品牌, 0-圣贝拉 1-小贝拉")
    private Integer intendedBrand;

    @ApiModelProperty(value = "客户预算, 0:10万以内 1:10-20万 2:20-30万 3:30万以上 4:无明确预算")
    private Integer customerBudget;

    @ApiModelProperty(value = "意向国家，取自ecp.cfg_country")
    private Integer intendedCountry;

    @ApiModelProperty(value = "老客户标签, 1-历史未签约客户 2-历史已签约客户 3-无")
    private Integer oldTag;

    @ApiModelProperty(value = "自定义商机类别, 1-默认 2-馆外产康")
    private Integer customType;

    @ApiModelProperty(value = "24小时回访标签")
    private Integer returnVisitOneDay;

    @ApiModelProperty(value = "3天回访标签")
    private Integer returnVisitThreeDay;

    @ApiModelProperty(value = "7天回访标签")
    private Integer returnVisitWeek;
}
