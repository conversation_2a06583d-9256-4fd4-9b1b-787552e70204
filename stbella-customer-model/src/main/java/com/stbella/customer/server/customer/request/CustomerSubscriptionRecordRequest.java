package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 客户订阅记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@ApiModel(value = "CustomerSubscriptionRecordRequest对象", description = "添加-查询 客户订阅记录")
public class CustomerSubscriptionRecordRequest extends BasicReq {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户openid")
    @NotNull(message = "用户openid不能为空")
    private String openid;

    @ApiModelProperty(value = "订阅类型1-圣贝拉电子杂志")
    @NotNull(message = "订阅类型不能为空")
    private Integer type;


}
