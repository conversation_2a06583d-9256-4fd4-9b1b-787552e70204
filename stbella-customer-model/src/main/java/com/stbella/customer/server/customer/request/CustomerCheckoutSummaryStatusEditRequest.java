package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "CustomerCheckoutSummaryStatusEditRequest", description = "离馆小结(月子报告)状态修改request")
public class CustomerCheckoutSummaryStatusEditRequest implements Serializable {

    private static final long serialVersionUID = 8419563379738868188L;

    @ApiModelProperty(value = "月子报告id")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "状态, 0-未发送 1-已发送，未查看 2-已查看")
    private Integer status;
}
