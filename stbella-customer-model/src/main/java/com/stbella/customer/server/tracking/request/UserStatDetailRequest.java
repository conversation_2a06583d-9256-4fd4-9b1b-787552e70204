package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "UserStatDetailRequest",description = "用户统计详情")
public class UserStatDetailRequest extends DataStatDateRequest implements Serializable {

    private static final long serialVersionUID = -6761636641856101306L;

    @ApiModelProperty(value = "姓名/手机号")
    private String nameOrPhone;

    @ApiModelProperty(value = "用户归属 0 全部，1 老用户,2 新用户")
    private Integer type;

    @ApiModelProperty(value = "分页offset值", hidden = true)
    private Integer offset;

    @ApiModelProperty(value = "每页记录数", hidden = true)
    private Integer limit;






}
