package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "UserGrowthAndIntegralInfoRequest",description = "查询用户成长值信息和积分信息request")
public class UserGrowthAndIntegralInfoRequest implements Serializable {
    private static final long serialVersionUID = 7902782748105751593L;

    @ApiModelProperty("用户全局id")
    @NotNull(message = "用户全局id不能为空")
    private Long basicUid;

    @ApiModelProperty("品牌类型 0圣贝拉 1小贝拉")
    @NotNull(message = "用户全局id不能为空")
    private Integer brandType;

    @ApiModelProperty("是否只查积分 0否 1是")
    @NotNull(message = "integral（是否只查积分）不能为空")
    private Integer integral;

}
