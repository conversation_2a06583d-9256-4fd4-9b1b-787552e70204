package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "UserStatVO", description = "用户统计VO")
public class UserStatVO implements Serializable {
    private static final long serialVersionUID = 8942726370756492439L;

    @ApiModelProperty(value = "日期，yyyy-MM-dd")
    private String statDate;

    @ApiModelProperty(value = "用户openid")
    private String openId;

    @ApiModelProperty(value = "访问人数")
    private Long userView;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "新增用户数")
    private Long newUser;

    @ApiModelProperty(value = "毛客资数")
    private Long phoneNewUser;

    @ApiModelProperty(value = "总访问时长(min)")
    private BigDecimal durationTime;

    @ApiModelProperty(value = "平均访问时长(min)")
    private BigDecimal avgDurationTime;

    @ApiModelProperty(value = "平均交互深度")
    private BigDecimal avgInteractionNum;

    @ApiModelProperty(value = "页面平均停留时长(min)")
    private BigDecimal avgPageDurationTime;

}
