package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Valid
@Data
@ApiModel(value = "CheckinStoreReportSendIntegralRequest", description = "查看探店报告发送积分request")
public class CheckinStoreReportSendIntegralRequest implements Serializable {

    private static final long serialVersionUID = 2948060798654776701L;

    @ApiModelProperty(value = "探店报告id")
    @NotNull(message = "探店报告id不能为为空")
    private Long id;

    @ApiModelProperty(value = "客户全局id不能为空")
    private Long basicUid;

}
