package com.stbella.customer.server.customer.request;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CustomerAiConversationListRequest", description = "AI机器人会话列表查询")
public class CustomerAiConversationListRequest extends PageBaseReq {

    private static final long serialVersionUID = 5460899093610520608L;

    private Integer brandType;

    private Integer fromType;
}
