package com.stbella.customer.server.nursingboard.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "BaseLineChartVO", description = "折线图VO")
public class BaseLineChartVO implements Serializable {

    private static final long serialVersionUID = 5356998377809060714L;

    @ApiModelProperty(value = "类目名称")
    private String name;

    @ApiModelProperty(value = "日期, 格式:yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "当日值")
    private String value;

    @ApiModelProperty(value = "较昨日值")
    private String contrastYesterdayValue;

    @ApiModelProperty(value = "较首日值")
    private String contrastFirstDayValue;

    @ApiModelProperty(value = "较上次变化值")
    private String contrastLastIndexValue;

    @ApiModelProperty(value = "上限值")
    private String upperLimit;

    @ApiModelProperty(value = "下限值")
    private String lowerLimit;

    @ApiModelProperty(value = "是否异常，0正常 1异常")
    private Integer exception = 0;

    @ApiModelProperty(value = "列表数据")
    private List<DailyDataVO> list;

    @Data
    @ApiModel(value = "DailyData", description = "每日数据VO")
    public static class DailyDataVO implements Serializable {

        private static final long serialVersionUID = -3240104775979197279L;

        @ApiModelProperty(value = "key")
        private String date;

        @ApiModelProperty(value = "值")
        private String value = "";
    }
}
