package com.stbella.customer.server.customer.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerCurriculumSearchRequest", description="课程列表请求Request")
public class CustomerCurriculumSearchRequest extends BasePageQuery implements Serializable {

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程分类")
    private Integer courseClass;

    @ApiModelProperty(value = "应用品牌 1）小贝拉 2）圣贝拉")
    private Integer brand;

    @ApiModelProperty(value = "课程状态 1）正常 2）草稿 3）停用")
    private Integer status;

    @ApiModelProperty(value = "创建时间开始")
    private Date gmtCreateStart;

    @ApiModelProperty(value = "创建时间结束")
    private Date gmtCreateEnd;
}
