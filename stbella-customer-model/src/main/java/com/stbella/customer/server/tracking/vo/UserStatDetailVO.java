package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "UserStatDetailVO",description = "用户统计详情VO")
public class UserStatDetailVO implements Serializable {

    private static final long serialVersionUID = -443833773861098323L;

    @ApiModelProperty(value = "日期，yyyy-MM-dd")
    private String statDate;

    @ApiModelProperty(value = "用户名")
    private String clientName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "用户openId")
    private String openId;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "访问时长(min)")
    private BigDecimal durationTime;

    @ApiModelProperty(value = "平均使用时长(min)")
    private BigDecimal avgDurationTime;

    @ApiModelProperty(value = "总交互深度")
    private Long interactionNum;

    @ApiModelProperty(value = "平均交互深度")
    private BigDecimal avgInteractionNum;

    @ApiModelProperty(value = "页面平均停留时长(min)")
    private BigDecimal avgPageDurationTime;

    @ApiModelProperty(value = "状态, 0-正常 1-白名单 2-黑名单")
    private Integer status;

    @ApiModelProperty(value = "用户id")
    private Long userId;
}
