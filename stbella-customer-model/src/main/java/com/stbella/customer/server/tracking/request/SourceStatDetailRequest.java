package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 渠道概览request
 */
@Data
@ApiModel(value = "SourceStatDetailRequest", description = "渠道概览request")
public class SourceStatDetailRequest extends DataStatDateRequest implements Serializable {

    private static final long serialVersionUID = -5525885538311238734L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "渠道名称")
    private String sourceName;
}
