package com.stbella.customer.server.scrm.request;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "ServiceOpportunityWinRequest", description = "400商机赢单处理")
public class ServiceOpportunityWinRequest implements Serializable {

    private static final long serialVersionUID = 7705203395083422431L;

    @ApiModelProperty(value = "400商机id")
    private Long opportunityId;

    @ApiModelProperty(value = "400员工id")
    private Long ownerId;

    @ApiModelProperty(value = "需要新建商机的销售id")
    private List<Long> saleIds;

    @ApiModelProperty(value = "业务类型 1-标准月子 2-小月子 3-护士外派 4-产康 5-S-BRA 6-其他")
    private Integer businessType;

    public String getBusinessTypeName() {
        switch (businessType) {
            case 1:
                return "标准月子";
            case 2:
                return "小月子";
            case 3:
                return "护士外派";
            case 4:
                return "产康";
            case 5:
                return "S-BRA";
            case 6:
                return "其他";
        }
        return "标准月子";
    }

    public OmniOrderTypeEnum getOrderType() {
        switch (businessType) {
            case 1:
                return OmniOrderTypeEnum.MONTH_ORDER;
            case 2:
                return OmniOrderTypeEnum.SMALL_MONTH_ORDER;
            case 3:
                return OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER;
            case 4:
                return OmniOrderTypeEnum.PRODUCTION_ORDER;
            case 5:
                return OmniOrderTypeEnum.SBRA_ORDER;
            case 6:
                return OmniOrderTypeEnum.OTHER_MONTH_ORDER;
        }

        return OmniOrderTypeEnum.MONTH_ORDER;
    }
}
