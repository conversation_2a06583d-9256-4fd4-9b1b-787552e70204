package com.stbella.customer.server.tracking.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "SessionDetailVO",description = "Session统计详情VO")
public class SessionDetailRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 4278536401711946287L;

    @ApiModelProperty(value = "sessionId,会话id")
    @NotNull(message = "sessionId不能为空")
    private String sessionId;

}
