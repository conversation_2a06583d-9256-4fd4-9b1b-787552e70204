package com.stbella.customer.server.nutuition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SatisfactionQuestionnaireDTO {

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "服务店铺")
    private String storeName;

    @ApiModelProperty(value = "门店类型")
    private Integer storeType;

    @ApiModelProperty(value = "推送记录ID")
    private Long pushRecordId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "客户名称")
    private Integer clientId;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

}
