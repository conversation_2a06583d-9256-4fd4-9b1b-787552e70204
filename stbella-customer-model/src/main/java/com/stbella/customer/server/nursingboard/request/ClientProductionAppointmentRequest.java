package com.stbella.customer.server.nursingboard.request;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ClientProductionAppointmentRequest", description = "客户预约产康信息")
public class ClientProductionAppointmentRequest extends BasicReq {

    private static final long serialVersionUID = 7699531072135045043L;

    @ApiModelProperty(value = "客户basicId")
    @NotNull(message = "客户不能为空")
    private Integer basicUid;

    @ApiModelProperty(value = "查询时间")
    private Date searchDate;
}
