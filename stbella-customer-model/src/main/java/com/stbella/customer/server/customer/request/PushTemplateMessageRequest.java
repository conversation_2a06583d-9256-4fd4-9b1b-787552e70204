package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PushTemplateMessageRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "推送的列表")
    private List<PushList> pushLists;

    @Data
    public static class PushList implements Serializable {

        @ApiModelProperty(value = "unionid")
        private String unionid;

        @ApiModelProperty(value = "门店名称")
        private String storeName;

        @ApiModelProperty(value = "门店类型")
        private Integer storeType;

        @ApiModelProperty(value = "推送记录ID")
        private Long pushRecordId;

        @ApiModelProperty(value = "订单编号")
        private String orderNo;

        @ApiModelProperty(value = "客户名称")
        private Integer clientId;

        @ApiModelProperty(value = "客户名称")
        private String clientName;
    }
}
