package com.stbella.customer.server.tracking.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SessionStatExport implements Serializable {

    private static final long serialVersionUID = -7778721767799119815L;

    @Column(value = "日期",order = 1)
    private String statDate;

    @Column(value = "姓名",order = 2)
    private String clientName;

    @Column(value = "手机号",order = 3)
    private String phone;

    @Column(value = "访问时间",order = 4)
    private String startTime;


    @Column(value = "退出时间",order = 5)
    private String endTime;

    @Column(value = "访问时长（min）",order = 6)
    private BigDecimal durationTime;

    @Column(value = "访问渠道",order = 7)
    private String source;

    @Column(value = "访问场景", order = 8)
    private String scene;

    @Column(value = "首次进入页面",order = 9)
    private String firstName;

    @Column(value = "退出页面",order = 10)
    private String lastName;

    @Column(value = "交互深度",order = 11)
    private Long pageNumber;

    public SessionStatExport(String statDate, String clientName, String phone, String startTime, String endTime, BigDecimal durationTime, String source, String scene, String firstName, String lastName, Long pageNumber) {
        this.statDate = statDate;
        this.clientName = clientName;
        this.phone = phone;
        this.startTime = startTime;
        this.endTime = endTime;
        this.durationTime = durationTime;
        this.source = source;
        this.scene = scene;
        this.firstName = firstName;
        this.lastName = lastName;
        this.pageNumber = pageNumber;
    }
}
