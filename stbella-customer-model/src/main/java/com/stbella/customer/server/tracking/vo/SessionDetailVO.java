package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "SessionDetailVO",description = "Session统计详情VO")
public class SessionDetailVO implements Serializable {


    private static final long serialVersionUID = -8165292195998459412L;

    @ApiModelProperty(value = "用户名")
    private String clientName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "停留时长")
    private Long durationTime;

    @ApiModelProperty(value = "来源渠道")
    private String source;

}
