package com.stbella.customer.server.tracking.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * 页面投放类型
 */
public enum PagePromotionTypeEnum {
    MINIPROGRAM_QR_CODE(0, "小程序二维码"),
    SHORT_LINK(1, "短地址"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    PagePromotionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        } else {
            PagePromotionTypeEnum[] var1 = values();

            for (PagePromotionTypeEnum pagePromotionTypeEnum : var1) {
                if (pagePromotionTypeEnum.code.equals(code)) {
                    return pagePromotionTypeEnum.desc;
                }
            }

            return "";
        }
    }
}
