package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "EventTrendVO", description = "事件趋势VO")
public class EventTrendVO implements Serializable {

    private static final long serialVersionUID = 6032397974228436346L;

    @ApiModelProperty(value = "事件id")
    private String eventId;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "数据列表")
    private List<DataStatDetailVO> dataList;

    @Data
    @ApiModel(value = "DataStatDetailVO", description = "事件趋势数据详情VO")
    public static class DataStatDetailVO implements Serializable {

        private static final long serialVersionUID = -6340819079291020118L;

        @ApiModelProperty(value = "日期，yyyy-MM-dd")
        private String statDate;

        @ApiModelProperty(value = "触发次数")
        private Long pageView;

        @ApiModelProperty(value = "触发人数")
        private Long userView;
    }
}
