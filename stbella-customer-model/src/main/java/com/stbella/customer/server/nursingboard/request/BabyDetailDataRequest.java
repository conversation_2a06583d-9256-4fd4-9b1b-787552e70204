package com.stbella.customer.server.nursingboard.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel(value = "DailySummaryDetailDataRequest", description = "护理看板-宝宝护理详情数据Request")
public class BabyDetailDataRequest extends AbstractBrandRequest {

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "当天时间零点 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "日期不能为空")
    private Date day;

    @ApiModelProperty(value = "门店类型 0圣贝拉 1小贝拉")
    private Integer storeType;

    @ApiModelProperty(value = "宝妈id")
    private Long customerId;

    @ApiModelProperty(value = "宝宝id")
    private Long babyId;

    @ApiModelProperty(value = "是否是演示状态, 0-否 1-是")
    private Integer damonStatus;

    @ApiModelProperty(value = "演示状态，0-游客 1-入住中 2-入住前 3-已离馆")
    private Integer damonType;

    @ApiModelProperty(value = "是否是首页展示，0-否 1-是")
    private Integer firstPage;

    @Override
    public void processBrandType(Object req) {
        BabyDetailDataRequest request = (BabyDetailDataRequest)req;
        //老版本brandType不会传
        if (request.getBrandType() == null) {
            this.setBrandType(request.getStoreType());
        } else {
            //新版本storeType不会传
            this.setStoreType(request.getBrandType());
        }
    }
}
