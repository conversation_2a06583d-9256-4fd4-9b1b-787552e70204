package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "BaseCalculateDoubleNumVO",description = "用户概览-新用户占比VO")
public class BaseCalculateDoubleNumVO implements Serializable {

    private static final long serialVersionUID = -1076609818508464095L;

    @ApiModelProperty(value = "合计")
    private BigDecimal totalValue;

    @ApiModelProperty(value = "均值")
    private BigDecimal avgValue;

    @ApiModelProperty(value = "数据列表")
    private List<BaseCalculateDoubleNumDetailVO> dataList;

    @Data
    @ApiModel(value = "BaseCalculateDoubleNumDetailVO",description = "用户概览-新用户占比详情VO")
    public static class BaseCalculateDoubleNumDetailVO implements Serializable {

        private static final long serialVersionUID = -304245148022367734L;

        @ApiModelProperty(value = "日期")
        private String date;

        @ApiModelProperty(value = "小数，数量")
        private Double proportionNum;

        @ApiModelProperty(value = "小数，数量")
        private BigDecimal num;
    }








}
