package com.stbella.customer.server.tracking.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "EventDetailVO", description = "事件详情VO")
public class EventDetailVO implements Serializable {

    private static final long serialVersionUID = -3668715151733398630L;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "会话id")
    private String sessionid;

    @ApiModelProperty(value = "事件id")
    private String eventId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件内容")
    private String eventContent;

    @ApiModelProperty(value = "事件query")
    private String eventQuery;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
}
