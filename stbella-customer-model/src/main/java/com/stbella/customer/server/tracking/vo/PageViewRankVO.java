package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
@ApiModel(value = "PageViewVO", description = "页面浏览排名VO")
public class PageViewRankVO implements Serializable {

    private static final long serialVersionUID = 4329149202611582922L;

    @ApiModelProperty(value = "排序序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "浏览人数")
    private Long userView;

    @ApiModelProperty(value = "平均停留时长")
    private BigDecimal avgDurationTime;
}
