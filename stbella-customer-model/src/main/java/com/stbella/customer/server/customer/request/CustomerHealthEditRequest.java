package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import com.stbella.customer.server.customer.enums.follow.FollowAgeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerHealthEditRequest", description="")
public class CustomerHealthEditRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id")
    @NotNull
    private Long customerId;

    @ApiModelProperty(value = "妊娠糖尿病 0无 1有")
    @NotNull(message = "需妊娠糖尿病不能为空")
    @EnumValue(enumClass = TrueFalseEnum.class, enumMethod = "isValidCode")
    private Integer gestationDiabetes;

    @ApiModelProperty(value = "高血压 0无 1有")
    @NotNull(message = "高血压字段不能为空")
    @EnumValue(enumClass = TrueFalseEnum.class, enumMethod = "isValidCode")
    private Integer hypertension;

    @ApiModelProperty(value = "传染病")
    @NotBlank(message = "传染病字段不能为空")
    @Size(max = 100)
    private String contagion;

    @ApiModelProperty(value = "过敏反应")
    @NotBlank(message = "过敏反应字段不能为空")
    @Size(max = 100)
    private String allergy;



}
