package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "VisitDetailVO", description = "页面统计-详情")
public class VisitDetailVO implements Serializable {

    private static final long serialVersionUID = -844962022569133670L;

    @ApiModelProperty(value = "姓名")
    private String clientName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "访问次数")
    private Long pageView;

    @ApiModelProperty(value = "访问时长")
    private BigDecimal durationTime;

    @ApiModelProperty(value = "平均访问时长")
    private BigDecimal avgDurationTime;

}
