package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "ActivityCaritaMessageRequest", description = "Carita活动消息发送请求")
public class ActivityCaritaMessageRequest implements Serializable {

    private static final long serialVersionUID = -543110924005564242L;

    @ApiModelProperty(value = "品牌, 0-圣贝拉")
    private Integer brandType;

    @ApiModelProperty(value = "消息来源, 0-微信小程序订阅消息, 1-短信")
    private Integer source;

    @ApiModelProperty(value = "客户id")
    private Long basicId;

    @ApiModelProperty(value = "预约id")
    private Long appointId;

    @ApiModelProperty(value = "活动名称")
    private String projectName;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "预约时间")
    private Date stratTime;

    @ApiModelProperty(value = "预约结束时间")
    private Date endTime;
}
