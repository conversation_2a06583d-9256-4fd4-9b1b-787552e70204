package com.stbella.customer.server.customer.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.customer.server.customer.annotations.EnumValue;
import com.stbella.customer.server.customer.enums.CustomerNationEnum;
import com.stbella.customer.server.customer.enums.TrueFalseEnum;
import com.stbella.customer.server.customer.enums.delivery.DeliveryIactationEnum;
import com.stbella.customer.server.customer.enums.delivery.DeliveryModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerDeliveryBabyRequest", description="")
public class CustomerDeliveryBabyRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "宝宝表主键id")
    private Long id;

    @JsonIgnore
    @ApiModelProperty(value = "分娩表id")
    private Long customerDeliveryId;

    @ApiModelProperty(value = "性别 0男 1女")
    private Integer sex;

    @ApiModelProperty(value = "宝宝昵称")
    @Size(max = 20,message = "宝宝昵称字数不能大于20")
    private String babyName;

    @ApiModelProperty(value = "黄疸值  单位mg/dl")
    @Min(value = 1,message = "黄疸值不能小于1")
    private Integer jaundiceValue;

    @ApiModelProperty(value = "出生体重  单位 g克")
    @Min(value = 1,message = "出生体重不能小于1")
    private Integer birthWeight;

    @ApiModelProperty(value = "民族")
    @EnumValue(enumClass = CustomerNationEnum.class, enumMethod = "isValidCode")
    private Integer nation;

    @ApiModelProperty(value = "哺乳方式 0-纯母乳 1-混合 2-配方奶")
    @EnumValue(enumClass = DeliveryIactationEnum.class, enumMethod = "isValidCode")
    private Integer lactationType;

    @ApiModelProperty(value = "配方奶备注")
    private String lactationRemark;

    @ApiModelProperty(value = "分娩方式  0-自然分娩 1-剖腹产")
    @EnumValue(enumClass = DeliveryModeEnum.class, enumMethod = "isValidCode")
    private Integer deliveryMode;


}
