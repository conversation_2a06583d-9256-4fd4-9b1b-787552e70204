package com.stbella.customer.server.tracking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "ApiListVO", description = "接口分析-接口列表VO")
public class ApiListVO implements Serializable {

    private static final long serialVersionUID = -3325406401708659072L;

    @ApiModelProperty(value = "接口地址")
    private String url;

    @ApiModelProperty(value = "请求次数")
    private Long requestNum;

    @ApiModelProperty(value = "成功次数")
    private Long successNum;

    @ApiModelProperty(value = "失败次数")
    private Long failNum;

    @ApiModelProperty(value = "异常次数")
    private Long abnormalNum;

    @ApiModelProperty(value = "异常率")
    private BigDecimal anomalyRate;

    @ApiModelProperty(value = "平均访问时长")
    private BigDecimal avgDurationTime;

}
