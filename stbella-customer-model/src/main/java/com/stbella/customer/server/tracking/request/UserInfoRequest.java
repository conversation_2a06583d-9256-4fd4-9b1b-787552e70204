package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "UserInfoRequest", description = "用户信息请求")
public class UserInfoRequest implements Serializable {

    private static final long serialVersionUID = -7154979560246130295L;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户唯一标识")
    @NotNull(message = "openid不能为空")
    private String openid;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "微信渠道来源")
    private String source;

    @ApiModelProperty(value = "客户来源场景值（自定义）")
    private String scene;

    @ApiModelProperty(value = "微信来源场景key值")
    private String sceneKey;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    @NotNull(message = "品牌类型不能为空")
    private Integer brandType;

    @ApiModelProperty(value = "注册时间")
    private Date registerTime;
}
