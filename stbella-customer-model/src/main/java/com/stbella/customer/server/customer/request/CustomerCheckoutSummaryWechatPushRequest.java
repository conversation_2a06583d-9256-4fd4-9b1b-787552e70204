package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "CustomerCheckoutSummaryWechatPushRequest", description = "客户生成离馆小结微信消息推送请求")
public class CustomerCheckoutSummaryWechatPushRequest implements Serializable {

    private static final long serialVersionUID = 8372468045924841334L;

    @ApiModelProperty(value = "客户openId")
    private String openId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店类型")
    private Integer storeType;

    @ApiModelProperty(value = "离馆小结id")
    private Long summaryId;

    @ApiModelProperty(value = "入住时间")
    private String checkinDate;

    @ApiModelProperty(value = "离馆时间")
    private String checkoutDate;

    @ApiModelProperty(value = "手机号")
    private String phone;
}
