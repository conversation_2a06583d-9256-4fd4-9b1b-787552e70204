package com.stbella.customer.server.tracking.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UserStatExport implements Serializable {

    private static final long serialVersionUID = 7801919199658336113L;

    @Column(value = "日期",order = 1)
    private String statDate;

    @Column(value = "访问人数",order = 2)
    private Long userView;

    @Column(value = "新增用户数",order = 3)
    private Long newUser;

    @Column(value = "毛客资数", order = 4)
    private Long phoneNewUser;

    @Column(value = "访问次数",order = 5)
    private Long pageView;

    @Column(value = "人均访问时长(min)",order = 6)
    private BigDecimal avgDurationTime;

    @Column(value = "人均交互深度",order = 7)
    private BigDecimal avgInteractionNum;

    @Column(value = "人均平均停留时长(min)",order = 8)
    private BigDecimal avgPageDurationTime;

    public UserStatExport(String statDate, Long userView, Long pageView, Long newUser, Long phoneNewUser, BigDecimal avgDurationTime, BigDecimal avgInteractionNum, BigDecimal avgPageDurationTime) {
        this.statDate = statDate;
        this.userView = userView;
        this.pageView = pageView;
        this.newUser = newUser;
        this.phoneNewUser = phoneNewUser;
        this.avgDurationTime = avgDurationTime;
        this.avgInteractionNum = avgInteractionNum;
        this.avgPageDurationTime = avgPageDurationTime;
    }
}
