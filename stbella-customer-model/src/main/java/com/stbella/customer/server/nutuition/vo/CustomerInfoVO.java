package com.stbella.customer.server.nutuition.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "营养客户主表id")
    private Long customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "所属销售名称")
    private String saleName;

    @ApiModelProperty(value = "地址-省")
    private String addressProvince;

    @ApiModelProperty(value = "地址-市")
    private String addressCity;

    @ApiModelProperty(value = "地址-区")
    private String addressArea;

    @ApiModelProperty(value = "地址-详情")
    private String addressDetails;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "预产期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date anticipateDate;

    @ApiModelProperty(value = "分娩方式  0-自然分娩；1-剖腹产；2-其他")
    private Integer deliveryMode;

    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "社会关系：0-配偶；1-父母；2-其他")
    private Integer socialRelations;

    @ApiModelProperty(value = "客户来源：0：自行开发；")
    private Integer source;

    @ApiModelProperty(value = "标签编号：0-低意向；1-中意向；2-高意向")
    private Integer lebelId;

    @ApiModelProperty(value = "签约状态；0-未签约；1-已签约")
    private Integer contractStatus;
}
