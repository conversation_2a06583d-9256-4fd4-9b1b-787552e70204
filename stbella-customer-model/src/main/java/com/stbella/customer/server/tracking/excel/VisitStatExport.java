package com.stbella.customer.server.tracking.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ExcelIgnoreUnannotated
//水平居中//垂直居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentFontStyle(fontName = "宋体")
public class VisitStatExport implements Serializable {

    private static final long serialVersionUID = 272894053720745867L;

    /*@ExcelProperty(value = "页面名称", order = 1)
    private String pageName;

    @ExcelProperty(value = "页面地址", order = 2)
    private String pagePath;

    @ExcelProperty(value = "访问人数", order = 3)
    private Long userView;

    @ExcelProperty(value = "访问次数", order = 4)
    private Long pageView;

    @ExcelProperty(value = "访问时长", order = 5)
    private BigDecimal durationTime;

    @ExcelProperty(value = "平均访问时长", order = 6)
    private BigDecimal avgDurationTime;*/

    @Column(value = "页面名称", order = 1)
    private String pageName;

    @Column(value = "页面地址", order = 2)
    private String pagePath;

    @Column(value = "访问人数", order = 3)
    private Long userView;

    @Column(value = "访问次数", order = 4)
    private Long pageView;

    @Column(value = "访问时长", order = 5)
    private BigDecimal durationTime;

    @Column(value = "平均访问时长", order = 6)
    private BigDecimal avgDurationTime;

    public VisitStatExport( String pageName, String pagePath, Long userView, Long pageView, BigDecimal durationTime, BigDecimal avgDurationTime) {
        this.pageName = pageName;
        this.pagePath = pagePath;
        this.userView = userView;
        this.pageView = pageView;
        this.durationTime = durationTime;
        this.avgDurationTime = avgDurationTime;
    }
}
