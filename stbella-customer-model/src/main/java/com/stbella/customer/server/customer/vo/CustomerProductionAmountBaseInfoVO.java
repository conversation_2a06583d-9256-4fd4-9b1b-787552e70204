package com.stbella.customer.server.customer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "CustomerProductionAmountBaseInfoVO", description = "客户产康金基本信息")
public class CustomerProductionAmountBaseInfoVO implements Serializable {

    private static final long serialVersionUID = 1665809739109222859L;

    @ApiModelProperty(value = "客户basicId")
    private Integer basicId;

    @ApiModelProperty(value = "总产康金")
    private Integer totalNum;

    @ApiModelProperty(value = "可用产康金")
    private Integer availableNum;

    @ApiModelProperty(value = "冻结产康金")
    private Integer freezeNum;
}
