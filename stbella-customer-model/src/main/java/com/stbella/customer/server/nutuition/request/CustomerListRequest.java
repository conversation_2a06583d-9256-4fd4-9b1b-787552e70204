package com.stbella.customer.server.nutuition.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "签约状态；0-未签约；1-已签约")
    private Integer contractStatus;

    @ApiModelProperty(value = "全部还是我的 1代表全部  2代表我的")
    private Integer allOrMy;
}
