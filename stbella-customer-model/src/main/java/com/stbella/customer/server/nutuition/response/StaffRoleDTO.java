package com.stbella.customer.server.nutuition.response;

import com.stbella.core.base.UserTokenInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class StaffRoleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前登录员工是否拥有营养普通销售角色")
    private Boolean ifSale;

    @ApiModelProperty(value = "当前登录员工是否拥有营养销售主管角色")
    private Boolean ifSaleManager;

    @ApiModelProperty(value = "当前登录员工是否拥有营养财务角色")
    private Boolean ifNutritionFinance;

    @ApiModelProperty(value = "角色是否拥有营养业务菜单权限")
    private Boolean ifLookNutritionMenu;

    @ApiModelProperty(value = "登录用户")
    private UserTokenInfoDTO userTokenInfoDTO;
}
