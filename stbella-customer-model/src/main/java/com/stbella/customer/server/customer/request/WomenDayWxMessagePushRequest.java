package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "WomenDayWxMessagePushRequest", description = "38活动微信公众号消息推送")
public class WomenDayWxMessagePushRequest implements Serializable {

    private static final long serialVersionUID = -1798218235198376496L;

    @ApiModelProperty(value = "应用品牌 0）圣贝拉,默认圣贝拉 1）小贝拉", required = true)
    private Integer brandType = 0;

    @ApiModelProperty(value = "客户手机号列表")
    private List<String> phoneList;

    @ApiModelProperty(value = "客户openIdList")
    private List<String> openIdList;
}
