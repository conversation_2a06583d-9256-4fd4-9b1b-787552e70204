package com.stbella.customer.server.scrm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OpportunityDistributionMessage", description = "商机分配成功消息")
public class OpportunityDistributionMessage implements Serializable {

    private static final long serialVersionUID = 827866548052101802L;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "销售id")
    private Long saleId;

    @ApiModelProperty(value = "门店id")
    private Long contractedStoreId;

    @ApiModelProperty(value = "意向套餐")
    private String intendedGoods;

    @ApiModelProperty(value = "意向护理方式")
    private String intendedNursingMode;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "参观预约时间")
    private String visitAppointmentTime;

    @ApiModelProperty(value = "参观项目")
    private String visitProject;

    @ApiModelProperty(value = "客户意向产品（产康）")
    private List<Integer> intendedProductIds;

    @ApiModelProperty(value = "是否发送上级消息")
    private Boolean sendManager = true;
}
