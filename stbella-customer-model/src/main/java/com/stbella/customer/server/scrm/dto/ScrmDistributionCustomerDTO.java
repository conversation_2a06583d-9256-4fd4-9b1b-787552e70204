package com.stbella.customer.server.scrm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: ScrmDistributionCustomerDTO
 * @date 2023/3/16 21:16
 */
@Data
@ApiModel(value = "ScrmDistributionCustomerDTO", description = "scrm客户中心同步")
public class ScrmDistributionCustomerDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty("scrmId系统客户ID")
    private Long userId;

    @ApiModelProperty("销售ID")
    private Long ownerId;

    @ApiModelProperty("操作类型0-新增;1-修改;2-删除")
    private Integer operateType;

    @ApiModelProperty("数据类型 1:客户 2:联系人 3:商机")
    private Long recordType;

    @ApiModelProperty("销售职能列表")
    private List<String> saleResponsibilities;
}
