package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户C端页面配置
 * </p>
 *
 * <AUTHOR> @since 2024-01-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_tracking_page_path")
@ApiModel(value="CustomerDataTrackingPagePathPO对象", description="客户C端页面配置")
public class CustomerDataTrackingPagePathPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "页面地址")
    private String pagePath;

    @ApiModelProperty(value = "是否启用, 0-是 1-否")
    private Integer enable;

    @ApiModelProperty(value = "是否支持投放，0-支持 1-不支持")
    private Integer advert;

}
