package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SessionStatRequest", description = "session统计")
public class SessionStatRequest extends DataStatDateRequest implements Serializable {

    private static final long serialVersionUID = 7107649845859485931L;

    @ApiModelProperty(value = "姓名/手机号")
    private String nameOrPhone;
}
