package com.stbella.customer.server.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="CustomerCurriculumOperationRequest", description="课程操作VO")
public class CustomerCurriculumOperationRequest implements Serializable {

    @ApiModelProperty(value = "课程主键ID")
    private Long id;

    @ApiModelProperty(value = "课程名称")
    @Size(max = 20, message = "课程名称上限20")
    @NotBlank(message = "课程名称不能为空")
    private String courseName;

    @ApiModelProperty(value = "课程分类 1）孕期护理指南 2）产后护理指南")
    private Integer courseClass;

    @ApiModelProperty(value = "应用品牌 1）小贝拉 2）圣贝拉")
    private List<Integer> brand;

    @ApiModelProperty(value = "课程排序 选择的排序，首次添加传-1")
    private Integer courseSort;

    @ApiModelProperty(value = "课程版式 0)无 1）竖屏 2）横屏")
    @NotNull(message = "课程板式不能为空")
    private Integer courseLayout;

    @ApiModelProperty(value = "课程视频")
    @NotBlank(message = "课程视频不能为空")
    private String courseVideo;

    @ApiModelProperty(value = "课程封面")
    @NotBlank(message = "课程封面不能为空")
    private String courseCover;

    @ApiModelProperty(value = "课程时长")
    private Long courseDuration;

    @ApiModelProperty(value = "基础浏览人数")
    private Integer basicVisitNumber;

    @ApiModelProperty(value = "课程状态 1）正常 2）草稿 3）停用")
    private Integer status;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件大小")
    private String fileSize;
}
