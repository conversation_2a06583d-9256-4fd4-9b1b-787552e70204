package com.stbella.customer.server.customer.request;

import com.stbella.care.server.care.entity.QuestionnaireAnswerDetailPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "QuestionnaireSubmitRequest", description = "问卷提交request")
public class QuestionnaireSubmitRequest implements Serializable {


    private static final long serialVersionUID = -450077307123751838L;

    @ApiModelProperty(value = "用户id")
    private Long clientId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "问卷id")
    private Long questionnaireId;

    @ApiModelProperty(value = "推送记录id")
    private Long templatePushId;

    @ApiModelProperty(value = "题目回答详情")
    private List<AnswerDetail> questionnaireDetailList;


    @Data
    @ApiModel(value = "AnswerDetail", description = "答题信息提交request")
    public static class AnswerDetail implements Serializable {
        private static final long serialVersionUID = -8457336520802887504L;
        @ApiModelProperty("问题主表id")
        private Long answerId;
        @ApiModelProperty("题目详情id")
        private Long questionnaireDetailsId;
        @ApiModelProperty("题号")
        private Integer questionNumber;
        @ApiModelProperty("得分")
        private Integer score;
        // 过时了，但是不能删除
        @Deprecated
        @ApiModelProperty("追加问题选项")
        private Integer additionalOptions;
        @ApiModelProperty("回答备注")
        private String additionalRemark;
        @ApiModelProperty("0-满意  1-不满意")
        private Integer satisfactionFlag;
        @ApiModelProperty(value = "追加问题多个选项")
        private List<Integer> additionalOptionsMultiple;
    }


}
