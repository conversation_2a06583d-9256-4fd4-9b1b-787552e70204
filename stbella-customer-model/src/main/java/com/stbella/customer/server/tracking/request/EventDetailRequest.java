package com.stbella.customer.server.tracking.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "EventDetailRequest", description = "事件详情查询")
public class EventDetailRequest extends DataStatDateRequest implements Serializable {

    private static final long serialVersionUID = -6892676564913807407L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "姓名/手机号")
    private String nameOrPhone;

    @ApiModelProperty(value = "事件id")
    private String eventId;

}
