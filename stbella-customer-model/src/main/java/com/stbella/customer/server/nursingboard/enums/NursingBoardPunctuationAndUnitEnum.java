package com.stbella.customer.server.nursingboard.enums;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

/**
 * 标点符号 和 单位
 */
public enum NursingBoardPunctuationAndUnitEnum {

    COMMA(0, "，"),
    FullSTOP(1, "。"),
    SEMICOLON(2, "；"),
    DEGREE_CENTIGRADE(3, "°C"),
    G(4, "g"),
    ML(5, "ml"),
    FREQUENCY(6, "次"),
    COLONS(7, "："),
    LINE_BREAK(8, "\n"),
    PAUSE_SYMBOL(9, "、"),
    BACKSLASH(10, "\""),
    SLASH(11, "/"),
    GRA<PERSON>(12, "克"),
    JAUNDICE_UNIT(13, "mg/dl"),
    STRING_TWO_DECIMAL_PLACES(14, "%.1f"),
    MINUS(15, "-");


    private final Integer code;
    private final String name;

    NursingBoardPunctuationAndUnitEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    public static NursingBoardPunctuationAndUnitEnum fromCode(Integer code) {
        if (!Objects.isNull(code)) {
            return Arrays.stream(NursingBoardPunctuationAndUnitEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
        }
        return null;

    }

    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            Optional<NursingBoardPunctuationAndUnitEnum> first = Arrays.stream(
                NursingBoardPunctuationAndUnitEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            if (first.isPresent()) {
                return first.get().getName();
            }
        }
        return null;

    }


    public static NursingBoardPunctuationAndUnitEnum fromName(String name) {
        if (!StringUtils.isBlank(name)) {
            return Arrays.stream(NursingBoardPunctuationAndUnitEnum.values()).filter(i -> Objects.equals(i.getName(), name)).findFirst().orElse(null);
        }
        return null;

    }
}
