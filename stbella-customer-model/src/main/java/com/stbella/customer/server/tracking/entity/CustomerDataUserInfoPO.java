package com.stbella.customer.server.tracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 埋点平台客户信息表
 * </p>
 *
 * <AUTHOR> @since 2023-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("customer_data_user_info")
@ApiModel(value="CustomerDataUserInfoPO对象", description="埋点平台客户信息表")
public class CustomerDataUserInfoPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌类型，0-圣贝拉小程序 1-小贝拉小程序")
    private Integer brandType;

    @ApiModelProperty(value = "basic_id")
    private Long basicId;

    @ApiModelProperty(value = "客户id")
    private Long clientId;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户唯一标识")
    private String openid;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "微信渠道来源")
    private String source;

    @ApiModelProperty(value = "客户来源场景值（自定义）")
    private String scene;

    @ApiModelProperty(value = "微信来源场景key值")
    private String sceneKey;

    @ApiModelProperty(value = "状态, 0-正常 1-白名单 2-黑名单")
    private Integer status;

    @ApiModelProperty(value = "设置时间")
    private Date settingTime;

    @ApiModelProperty(value = "注册时间")
    private Date registerTime;
}
