<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>stbella-customer</artifactId>
        <groupId>com.stbella</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>stbella-customer-model</artifactId>
    <version>${stbella-customer-api.version}</version>


    <dependencies>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>base-common</artifactId>
            <version>2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-common</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
