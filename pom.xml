<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.stbella</groupId>
    <version>1.0-SNAPSHOT</version>
    <artifactId>stbella-customer</artifactId>
    <packaging>pom</packaging>
    <properties>
        <!--core依赖-->
        <stbella-core-dependenices.version>1.0-SNAPSHOT</stbella-core-dependenices.version>
        <stbella-core-swagger.version>1.0-SNAPSHOT</stbella-core-swagger.version>
        <stbella-core-common.version>1.2.4</stbella-core-common.version>
        <stbella-core-mvc.version>1.0.10</stbella-core-mvc.version>
        <stbella-core-mybatis.version>1.0-SNAPSHOT</stbella-core-mybatis.version>
        <stbella-core-pulsar.version>1.1.4</stbella-core-pulsar.version>
        <stbella-core-mail.version>1.0.7</stbella-core-mail.version>

        <!--项目依赖-->
        <stbella-message-api.version>1.0-SNAPSHOT</stbella-message-api.version>
        <stbella-base-api.version>2.2.9</stbella-base-api.version>
        <stbella-base-model.version>2.2.9</stbella-base-model.version>
        <!-- 暂时用测试版本-->
        <stbella-care-api.version>2.3.3</stbella-care-api.version>
<!--        <stbella-care-api.version>2.1.4</stbella-care-api.version>-->
<!--        <stbella-care-model.version>2.0.18</stbella-care-model.version>-->
        <stbella-store-api.version>2.2.12</stbella-store-api.version>
        <stbella-order-api.version>2.7.12</stbella-order-api.version>


        <stbella-customer-api.version>2.6.1</stbella-customer-api.version>

        <stbella-sso-api.version>2.1.6</stbella-sso-api.version>
        <stbella-sso-model.version>2.1.6</stbella-sso-model.version>
        <rule-link-api.version>1.0.0</rule-link-api.version>
        <stbella-asset-api.version>1.4.4</stbella-asset-api.version>
        <stbella-store-core-api.version>2.2.2</stbella-store-core-api.version>
        <stbella-report-api.version>1.0-SNAPSHOT</stbella-report-api.version>
        <stbella-marketing-api.version>1.0.8</stbella-marketing-api.version>

        <!--三方依赖-->
        <snowball-starter.version>1.2.0</snowball-starter.version>
        <hibernate-validator.version>6.1.0.Final</hibernate-validator.version>
        <org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
        <lombok>1.18.24</lombok>
        <dozer.version>5.5.1</dozer.version>
        <com.qcloud.version>4.4</com.qcloud.version>
        <com.meituan.sdk.version>1.0</com.meituan.sdk.version>
        <gson-version>2.9.1</gson-version>
        <okhttp-version>4.10.0</okhttp-version>
    </properties>
    <modules>
        <module>stbella-customer-model</module>
        <module>stbella-customer-api</module>
        <module>stbella-customer-server</module>
    </modules>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${com.qcloud.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-customer-api</artifactId>
                <version>${stbella-customer-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-customer-model</artifactId>
                <version>${stbella-customer-api.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.15</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-store-api</artifactId>
                <version>${stbella-store-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mybatis</artifactId>
                <version>${stbella-core-mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mail</artifactId>
                <version>${stbella-core-mail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-sso-api</artifactId>
                <version>${stbella-sso-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-sso-model</artifactId>
                <version>${stbella-sso-model.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-care-api</artifactId>
                <version>${stbella-care-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-care-model</artifactId>
                <version>${stbella-care-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-marketing-api</artifactId>
                <version>${stbella-marketing-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-swagger</artifactId>
                <version>${stbella-core-swagger.version}</version>
            </dependency>


            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-common</artifactId>
                <version>${stbella-core-common.version}</version>


                <exclusions>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-dao-redis-jackson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>

                </exclusions>


            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mvc</artifactId>
                <version>${stbella-core-mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-base-api</artifactId>
                <version>${stbella-base-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-base-model</artifactId>
                <version>${stbella-base-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-order-api</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-order-model</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>order-common</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>message-api</artifactId>
                <version>${stbella-message-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-asset-api</artifactId>
                <version>${stbella-asset-api.version}</version>
            </dependency>


            <dependency>
                <groupId>com.stbella.platform</groupId>
                <artifactId>rule-link-api</artifactId>
                <version>${rule-link-api.version}</version>
            </dependency>


            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>store-api</artifactId>
                <version>${stbella-store-core-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-report-api</artifactId>
                <version>${stbella-report-api.version}</version>
            </dependency>


            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-pulsar</artifactId>
                <version>${stbella-core-pulsar.version}</version>
            </dependency>
            <!-- 项目依赖 -->

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-dependenices</artifactId>
                <version>${stbella-core-dependenices.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>${dozer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencent.ads</groupId>
                <artifactId>marketing-api-java-sdk</artifactId>
                <version>1.1.59</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.sdk</groupId>
                <artifactId>mt-op-java-sdk</artifactId>
                <version>${com.meituan.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.openapitools</groupId>
                <artifactId>oceanengine-mapi-java-client</artifactId>
                <version>1.1.39</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson-version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp-version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp-version}</version>
            </dependency>

            <dependency>
                <groupId>top.primecare</groupId>
                <artifactId>snowball-spring-boot-starter</artifactId>
                <version>${snowball-starter.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <!-- maven私仓 -->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>primecare tencent</name>
            <url>https://nexus.primecare.top/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>primecare tencent</name>
            <url>https://nexus.primecare.top/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <!--执行顺序 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

        </plugins>
    </build>
</project>
