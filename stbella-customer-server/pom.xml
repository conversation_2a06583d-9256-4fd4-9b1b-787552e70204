<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>stbella-customer</artifactId>
        <groupId>com.stbella</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>stbella-customer-server</artifactId>
    <version>2.0-SNAPSHOT</version>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.stbella.customer.server.CustomerLauncher</mainClass>
                    <outputDirectory>../target/</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
    </build>

    <dependencies>
        <!-- 项目依赖 -->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-customer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-sso-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-sso-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-base-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-asset-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella.platform</groupId>
            <artifactId>rule-link-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-report-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-pulsar</artifactId>
        </dependency>
        <!-- 项目依赖 -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--依赖undertow，红帽基于netty的服务器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!-- nacos-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- dubbo相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.3.2</version>
        </dependency>
        <!-- 数据源 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!-- 微信公众号-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>4.3.0</version>
        </dependency>
        <!-- 微信小程序 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.3.0</version>
        </dependency>
        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>


        <!-- 有赞商城sdk -->
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-core</artifactId>
            <version>1.0.22-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-gen</artifactId>
            <version>1.0.22.80701202109281055-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-gen</artifactId>
            <version>1.0.22.80701202109281055-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.15.0</version>
        </dependency>

        <dependency>
            <groupId>com.tencent.ads</groupId>
            <artifactId>marketing-api-java-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.2</version>
        </dependency>


        <dependency>
            <groupId>com.meituan.sdk</groupId>
            <artifactId>mt-op-java-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>oceanengine-mapi-java-client</artifactId>
        </dependency>

        <!-->解析html jsoup <-->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>
        <dependency>
            <groupId>top.primecare</groupId>
            <artifactId>snowball-spring-boot-starter</artifactId>
            <version>${snowball-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-dubbo</artifactId>
            <version>5.13.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-marketing-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${com.qcloud.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>
