<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--${logpath} 是日志-->
    <contextName>care</contextName>
    <property name="logpath" value="./logs"/>
    <define name="ip" class="com.stbella.core.utils.LogIpConfig"/>
    <conversionRule conversionWord="ip" converterClass="com.stbella.core.utils.IPConverterConfig"/>
    <property name="logretainday" value="6"/>
    <property name="logLayout"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%ip] [%thread] %-5level %logger{10} :%-3L [%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}] - %msg %n"/>
    <!--%d 日期 | %color() 文字颜色 | -5level 日志级别 | %logger{10} 是类路径  | %-3L 行数| %msg 日志内容| %ex 是log.error(xx,exption)中exception| -->
    <!--<property name="logLayout" value="%blue(%-4relative) %d{yyyy-MM-dd HH:mm:ss.SSS} | [%thread] | %highlight(%-5level) | %logger{10} :%-3L - %yellow(%msg) %X{span}%n"/>-->

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${logpath}/app-${ip}.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${logpath}/%d{yyyy-MM,aux}/app.%d.%i.gz</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过2M，若超过2M，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>300MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${logretainday}</maxHistory>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${logLayout}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="ASYN_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
        <includeCallerData>true</includeCallerData>
        <queueSize>20480</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${logpath}/error.log</file>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${logpath}/%d{yyyy-MM,aux}/error.%d.%i.gz</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过2M，若超过2M，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${logretainday}</maxHistory>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${logLayout}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--encoder 默认配置为PatternLayoutEncoder-->
        <encoder>
            <pattern>${logLayout}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--encoder 默认配置为PatternLayoutEncoder-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L - %msg%n%X{span}%n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>
    <appender name="errorAlarmAppender"
              class="com.stbella.core.utils.ErrorAlarmCustomAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <!-- 权量日志  -->
        <appender-ref ref="FILE"/>
        <!-- 错误日志 -->
        <appender-ref ref="ERROR"/>
    </root>
</configuration>