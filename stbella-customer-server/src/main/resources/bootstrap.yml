server:
  port: ${server.port:8080} #服务端口
spring:
  application:
    name: customer-center
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.clould.nacos.server-addr}
        namespace: ${spring.clould.nacos.discovery.namespace}
        username: ${discovery.name}
        password: ${discovery.pwd}
        group : ${discovery.group:DEFAULT_GROUP}
      config:
        server-addr: ${spring.clould.nacos.server-addr}
        namespace: ${spring.clould.nacos.config.namespace}
        username: ${config.name}
        password: ${config.pwd}
        prefix: customer-config
        group : ${discovery.group:DEFAULT_GROUP}
        file-extension: yml
  cache:
    type: redis

springfox:
  documentation:
    swagger:
      v2:
        path: /customer/v2/api-docs


snowball:
  global:
    appId: customer-center
    transactionTemplateBeanName: transactionTemplate
  flow:
    drive: local-xsd

