server:
  port: 8080 #服务端口

spring:
  application:
    name: customer-center
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: https://ncs.primecare.top
        namespace: 296fdf4d-3992-4979-af7c-b1d142209d89
        username: prod
        password: ynCN$Rune9aS
        group: DEFAULT_GROUP
      config:
        server-addr: https://ncs.primecare.top
        namespace: 296fdf4d-3992-4979-af7c-b1d142209d89
        username: prod
        password: ynCN$Rune9aS
        prefix: customer-config
        file-extension: yml
        shared-configs:
          - { data-id: customer-config.yml, refresh: true }
        group: DEFAULT_GROUP
