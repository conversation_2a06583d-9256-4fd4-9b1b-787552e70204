server:
  port: 8080 #服务端口

spring:
  application:
    name: customer-center
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: https://ncs.primecare.top
        namespace: 7e6ececf-903f-476e-a614-9569fcca20dd
        username: prod
        password: ynCN$Rune9aS
        group: DEFAULT_GROUP
      config:
        server-addr: https://ncs.primecare.top
        namespace: 7e6ececf-903f-476e-a614-9569fcca20dd
        username: prod
        password: ynCN$Rune9aS
        prefix: customer-config
        file-extension: yml
        shared-configs:
          - { data-id: customer-config.yml, refresh: true }
        group: DEFAULT_GROUP
