server:
  port: 8080 #服务端口

spring:
  application:
    name: customer-center
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: https://ncs.primecare.top
        namespace: 1614acfe-08ec-4127-a54f-561f65f271b5
        username: test
        password: test@primecare
        group: DEFAULT_GROUP
      config:
        server-addr: https://ncs.primecare.top
        namespace: 1614acfe-08ec-4127-a54f-561f65f271b5
        username: test
        password: test@primecare
        prefix: customer-config
        file-extension: yml
        shared-configs:
          - { data-id: customer-config.yml, refresh: true }
        group: DEFAULT_GROUP