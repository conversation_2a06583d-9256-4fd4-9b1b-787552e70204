<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerCheckinStoreReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerCheckinStoreReportPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="first_name" property="firstName" />
        <result column="last_name" property="lastName" />
        <result column="basic_uid" property="basicUid" />
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="phone_zone" property="phoneZone" />
        <result column="client_phone" property="clientPhone" />
        <result column="client_gender" property="clientGender" />
        <result column="pregnancy_status" property="pregnancyStatus" />
        <result column="fetus_num" property="fetusNum" />
        <result column="predict_born_num" property="predictBornNum" />
        <result column="predict_born_date" property="predictBornDate" />
        <result column="gestation_week_now" property="gestationWeekNow" />
        <result column="hospital" property="hospital" />
        <result column="concerned_issue" property="concernedIssue" />
        <result column="special_request" property="specialRequest" />
        <result column="store_goods" property="storeGoods" />
        <result column="visit_goods" property="visitGoods" />
        <result column="intentional_goods" property="intentionalGoods" />
        <result column="visit_date" property="visitDate" />
        <result column="intentional_quotation" property="intentionalQuotation" />
        <result column="store_id" property="storeId" />
        <result column="store_type" property="storeType" />
        <result column="sale_id" property="saleId" />
        <result column="sale_name" property="saleName" />
        <result column="sale_phone" property="salePhone" />
        <result column="version" property="version" />
        <result column="status" property="status" />
        <result column="send_time" property="sendTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        first_name, last_name, basic_uid, client_id, client_name, phone_zone, client_phone, client_gender, pregnancy_status, fetus_num, predict_born_num, predict_born_date,
        gestation_week_now, hospital, concerned_issue, special_request, store_goods, visit_goods, intentional_goods, visit_date, intentional_quotation, store_id, store_type,
        sale_id, sale_name, sale_phone, version, status, send_time
    </sql>

</mapper>
