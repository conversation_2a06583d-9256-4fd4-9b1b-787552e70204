<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmTeamMemberRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="type" property="type" />
        <result column="record_id" property="recordId" />
        <result column="user_id" property="userId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        type, record_id, user_id
    </sql>
    <select id="searchByKeyword" resultType="com.stbella.customer.server.customer.vo.ClientSearchMemberVO">
        SELECT
        r.id,
        r.type,
        r.record_id,
        r.user_id,
        c.NAME,
        c.phone
        FROM
        scrm_team_member_record r
        INNER JOIN scrm_customer c ON r.record_id = c.scrm_customer_id
        WHERE
        r.user_id = #{scrmId}
        and r.`deleted`=0
        and r.`type`=1
        GROUP BY phone
    </select>

    <select id="searchPageByKeyword" resultType="com.stbella.customer.server.customer.vo.ClientSearchMemberVO">
        SELECT
        r.id,
        r.type,
        r.record_id,
        r.user_id,
        c.NAME,
        c.phone
        FROM
        scrm_team_member_record r
        INNER JOIN scrm_customer c ON r.record_id = c.scrm_customer_id
        WHERE
        r.user_id = #{scrmId}
        and r.`deleted`=0
        GROUP BY phone
    </select>

</mapper>
