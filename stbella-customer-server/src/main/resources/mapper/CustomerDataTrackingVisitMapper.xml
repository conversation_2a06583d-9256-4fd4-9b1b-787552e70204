<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingVisitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingVisitPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="basic_id" property="basicId"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName" />
        <result column="phone" property="phone" />
        <result column="openid" property="openid"/>
        <result column="sessionid" property="sessionid"/>
        <result column="source" property="source" />
        <result column="page_name" property="pageName"/>
        <result column="page_path" property="pagePath"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime" />
        <result column="duration_time" property="durationTime" />
        <result column="page_name_next" property="pageNameNext" />
        <result column="page_path_next" property="pagePathNext" />
        <result column="page_name_pre" property="pageNamePre" />
        <result column="page_path_pre" property="pagePathPre" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type,
        basic_id, client_id, client_name, phone, openid, sessionid, source, page_name, page_path, start_time, end_time, duration_time, page_name_next, page_path_next, page_name_pre, page_path_pre
    </sql>

    <!-- 页面浏览排名 -->
    <select id="pageViewRankList"
            resultType="com.stbella.customer.server.tracking.vo.PageViewRankVO">
        SELECT
            path.`page_name`,
            v.`page_path`,
            COUNT(DISTINCT v.`openid`) AS 'user_view',
            ROUND((SUM(v.`duration_time`) / COUNT(DISTINCT v.`openid`))/1000, 2) AS 'avg_duration_time'
        FROM
            `customer_data_tracking_visit` AS v
        LEFT JOIN
            `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        LEFT JOIN
            `customer_data_tracking_page_path` AS path ON v.`page_path` = path.`page_path`
        WHERE
            v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            AND v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="brandType != null">
                AND v.`brand_type` = #{brandType}
            </if>
        GROUP BY v.`page_path`
        ORDER BY user_view DESC, avg_duration_time DESC
    </select>

    <!-- 页面统计列表 -->
    <select id="visitStatList" resultType="com.stbella.customer.server.tracking.vo.VisitStatVO">
        SELECT
            path.`page_name`,
            v.`page_path`,
            IFNULL(COUNT(DISTINCT v.`openid`),0) AS 'user_view',
            IFNULL(count(v.openid),0) as 'page_view',
            IFNULL(ROUND(SUM(v.`duration_time`)/60000,2),0) AS 'duration_time',
            IFNULL(ROUND((SUM(v.`duration_time`)/count(v.openid))/60000,2),0) AS avgDurationTime
        FROM
            `customer_data_tracking_visit` AS v
        LEFT JOIN
            `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        LEFT JOIN
            `customer_data_tracking_page_path` AS path ON v.`page_path` = path.`page_path` AND path.`brand_type` = #{brandType}
        WHERE
            v.`deleted` = 0
            AND u.`deleted` = 0
            <if test="brandType != null">
                and v.brand_type = #{brandType}
            </if>
            <if test="dateStart !=null">
                AND v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <if test="pageName !=null and pageName !=''">
                AND path.`page_name` = #{pageName}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            AND v.`openid` != ''
        GROUP BY v.`page_path`
        ORDER BY `user_view` desc ,`page_view` desc,`duration_time` desc
    </select>

    <select id="visitDataStatDetailVOList" resultType="com.stbella.customer.server.tracking.vo.VisitTrendVO$VisitDataStatDetailVO">
        select
            DATE_FORMAT(v.`start_time`, '%Y-%m-%d') AS stat_date,
            IFNULL(COUNT(DISTINCT v.`openid`),0) AS 'user_view',
            IFNULL(count(v.`page_path`),0) AS 'page_view',
            IFNULL(SUM(v.`duration_time`),0) AS 'duration_time'
        from
            customer_data_tracking_visit AS v
        LEFT JOIN
             `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        where
            v.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            AND v.`page_path` = #{request.pagePath}
            AND v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="request.brandType != null">
                and v.brand_type = #{request.brandType}
            </if>
        GROUP BY stat_date
        ORDER BY stat_date ASC
    </select>

    <select id="visitSourceStatDetailVOList" resultType="com.stbella.customer.server.tracking.vo.VisitTrendVO$VisitSourceStatDetailVO">
        SELECT
            t.source AS source_key,
            t.source_name,
            COUNT( 1 ) AS source_num
        FROM
            (
                SELECT
                    IF(v.source != '', v.source, IF(v.page_path_pre != '', v.page_path_pre, 'other')) AS source,
                    IF(v.source != '', sc.source_name, IF(v.page_path_pre != '', path.page_name, '其他')) AS source_name
                FROM
                    customer_data_tracking_visit AS v
                LEFT JOIN
                    customer_data_tracking_source_config AS sc ON v.source = sc.source_key
                LEFT JOIN
                    `customer_data_user_info` AS u ON v.`openid` = u.`openid`
                LEFT JOIN
                    `customer_data_tracking_page_path` AS path ON v.`page_path_pre` = path.`page_path` AND path.`brand_type` = #{request.brandType}
                WHERE
                    `start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
                    AND v.`page_path` = #{request.pagePath}
                    AND v.`deleted` = 0
                    AND v.`openid` != ''
                    AND u.`deleted` = 0
                    <if test="userStatus != null  and userStatus.size() > 0">
                        AND u.`status` IN
                        <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="request.brandType != null">
                        and v.brand_type = #{request.brandType}
                    </if>
            ) AS t
        GROUP BY t.source
        ORDER BY source_num DESC
    </select>

    <!--<select id="defaultSourceList" resultType="com.stbella.customer.server.tracking.vo.VisitTrendVO$VisitSourceStatDetailVO">
        select
            source_key,
            source_name
        from  customer_data_tracking_source_config
        where
           id &lt; 50


    </select>-->

    <!-- 页面统计详情总数 -->
    <select id="getVisitDetailCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT v.`openid`)
        FROM
            `customer_data_tracking_visit` AS v
        LEFT JOIN
            `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        WHERE
            v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="request.brandType != null">
                AND v.`brand_type` = #{request.brandType}
            </if>
            <if test="request.dateStart !=null">
                AND v.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="request.pagePath !=null and request.pagePath !=''">
                AND v.`page_path` = #{request.pagePath}
            </if>

            <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
                AND (u.`client_name` LIKE concat('%',#{request.nameOrPhone},'%') OR u.`phone` LIKE concat('%',#{request.nameOrPhone},'%') OR u.`nick_name` LIKE concat('%',#{request.nameOrPhone},'%'))
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="request.type != null and request.type == 1">
                AND (u.`register_time` &lt; #{request.dateStart} OR u.`register_time` &gt; #{request.dateEnd})
            </if>

            <if test="request.type != null and request.type == 2">
                AND u.`register_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
    </select>

    <!-- 页面统计详情列表 -->
    <select id="getVisitDetail" resultType="com.stbella.customer.server.tracking.vo.VisitDetailVO">
        SELECT
            IFNULL(u.`client_name`, u.`nick_name`) AS 'client_name',
            u.`phone`,
            IFNULL(count(v.`openid`),0) AS 'pageView',
            IFNULL(SUM(v.`duration_time`),0) AS 'durationTime',
            IFNULL((sum(v.`duration_time`) / COUNT(v.openid)),0) AS 'avgDurationTime'
        FROM
             `customer_data_tracking_visit` AS v
        LEFT JOIN
            `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        where
            v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="request.brandType != null">
                and v.brand_type = #{request.brandType}
            </if>
            <if test="request.dateStart !=null">
                AND v.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="request.pagePath !=null and request.pagePath !=''">
                AND v.`page_path` = #{request.pagePath}
            </if>

            <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
                and (u.`client_name` like concat('%',#{request.nameOrPhone},'%') or u.`phone` like concat('%',#{request.nameOrPhone},'%') or u.`nick_name` like concat('%',#{request.nameOrPhone},'%'))
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="request.type != null and request.type == 1">
                AND (u.`register_time` &lt; #{request.dateStart} OR u.`register_time` &gt; #{request.dateEnd})
            </if>

            <if test="request.type != null and request.type == 2">
                AND u.`register_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>

        group by v.openid
        order by pageView desc
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>


    <!-- 老用户页面统计详情列表 -->
    <select id="getOldUserVisitDetail" resultType="com.stbella.customer.server.tracking.vo.VisitDetailVO">
        select
        u.client_name,
        u.phone,
        IFNULL(count(v.openid),0) as 'pageView',
        IFNULL(SUM(v.`duration_time`),0) AS 'durationTime',
        IFNULL((sum(v.`duration_time`)/COUNT(v.openid)),0) as 'avgDurationTime'

        from customer_data_tracking_visit AS v
        LEFT JOIN
        `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        where
        v.`deleted` = 0
        AND v.`openid` != ''
        AND u.`deleted` = 0
        <if test="request.brandType != null">
            and v.brand_type = #{request.brandType}
        </if>
        <if test="request.dateStart !=null">
            AND v.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
        </if>
        <if test="request.pagePath !=null and request.pagePath !=''">
            AND v.`page_path` = #{request.pagePath}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND v.`openid` IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
            and (u.`client_name` like concat('%',#{request.nameOrPhone},'%') or u.`phone` like
            concat('%',#{request.nameOrPhone},'%'))
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        group by v.openid
        order by pageView desc

    </select>

    <!-- 新用户页面统计详情列表 -->
    <select id="getNewUserVisitDetail" resultType="com.stbella.customer.server.tracking.vo.VisitDetailVO">
        select
        u1.client_name,
        u1.phone,
        IFNULL(count(v1.openid),0) as 'pageView',
        IFNULL(SUM(v1.`duration_time`),0) AS 'durationTime',
        IFNULL((sum(v1.`duration_time`)/COUNT(v1.openid)),0) as 'avgDurationTime'

        from customer_data_tracking_visit AS v1
        LEFT JOIN
        `customer_data_user_info` AS u1 ON v1.`openid` = u1.`openid`
        where
        (v1.`openid`, v1.`start_time`) IN (
        select
        v.openid, MIN( v.`start_time` ) AS register_time

        from customer_data_tracking_visit AS v
        LEFT JOIN
        `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        where
        v.`deleted` = 0
        AND v.`openid` != ''
        AND u.`deleted` = 0
        <if test="request.brandType != null">
            and v.brand_type = #{request.brandType}
        </if>
        <if test="request.dateStart !=null">
            AND v.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
        </if>
        <if test="request.pagePath !=null and request.pagePath !=''">
            AND v.`page_path` = #{request.pagePath}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND v.`openid` NOT IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY v.openid
        )
        <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
            and (u1.`client_name` like concat('%',#{request.nameOrPhone},'%') or u1.`phone` like
            concat('%',#{request.nameOrPhone},'%'))
        </if>
        group by v1.openid
        order by pageView desc
    </select>

    <!-- 查询指定时间段内每天的访问深度及访问时长 -->
    <select id="queryVisitDepthAndTimeByData"
            resultType="com.stbella.customer.server.tracking.vo.VisitStatVO">
        SELECT
            DATE_FORMAT(v.`start_time`, '%Y-%m-%d') AS 'stat_date',
            COUNT(1) AS 'page_view',
            COUNT(DISTINCT v.`openid`) AS 'user_view',
            IFNULL(COUNT(DISTINCT v.`sessionid`),0) AS 'session_count',
            IFNULL(sum(v.duration_time),0) AS 'duration_time'
        FROM
            `customer_data_tracking_visit` AS v
        LEFT JOIN
            `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        WHERE
            v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="brandType != null">
                AND v.`brand_type` = #{brandType}
            </if>
            <if test="dateStart!=null">
                AND v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `stat_date`
    </select>

    <!-- 查询一批用户指定时间范围内的访问次数 -->
    <select id="queryUserVisitTimes"
            resultType="com.stbella.customer.server.tracking.vo.UserStatDetailVO">
        SELECT
            v.`openid` AS 'openId',
            COUNT(1) AS 'page_view'
        FROM
        `customer_data_tracking_visit` AS v
        LEFT JOIN
        `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        WHERE
        v.`deleted` = 0
        AND v.`openid` != ''
        AND u.`deleted` = 0
        <if test="brandType != null">
            AND v.`brand_type` = #{brandType}
        </if>
        <if test="dateStart!=null">
            AND v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="openIdList != null and openIdList.size() > 0">
            AND v.`openid` IN
            <foreach collection="openIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY v.`openid`
    </select>

</mapper>
