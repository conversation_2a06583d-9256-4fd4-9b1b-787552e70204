<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerArchiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerArchivePO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_info_id" property="customerInfoId" />
        <result column="card_type" property="cardType" />
        <result column="card_no" property="cardNo" />
        <result column="birthday" property="birthday" />
        <result column="constellation" property="constellation" />
        <result column="age" property="age" />
        <result column="height" property="height" />
        <result column="weight" property="weight" />
        <result column="food_prohibition" property="foodProhibition" />
        <result column="hospital" property="hospital" />
        <result column="consult_date" property="consultDate" />
        <result column="ding_talk_report" property="dingTalkReport" />
        <result column="appointment_flag" property="appointmentFlag" />
        <result column="appointment_store_id" property="appointmentStoreId" />
        <result column="appointment_store_name" property="appointmentStoreName" />
        <result column="appointment_people_num" property="appointmentPeopleNum" />
        <result column="appointment_goods_id" property="appointmentGoodsId" />
        <result column="appointment_goods_name" property="appointmentGoodsName" />
        <result column="appointment_combo_id" property="appointmentComboId" />
        <result column="appointment_combo_name" property="appointmentComboName" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_remark" property="appointmentRemark" />
        <result column="gestation_week" property="gestationWeek" />
        <result column="gestation_day" property="gestationDay" />
        <result column="anticipate_date" property="anticipateDate" />
        <result column="entry_date" property="entryDate" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="district_id" property="districtId" />
        <result column="province_name" property="provinceName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="address" property="address" />
        <result column="attention" property="attention"/>
        <result column="family_phone" property="familyPhone" />
        <result column="emergency_contact" property="emergencyContact" />
        <result column="emergency_contact_phone" property="emergencyContactPhone" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_info_id, card_type, card_no, birthday, constellation, age, height, weight, food_prohibition, hospital, consult_date, ding_talk_report, appointment_flag, appointment_store_id, appointment_store_name, appointment_people_num, appointment_goods_id, appointment_goods_name, appointment_combo_id, appointment_combo_name, appointment_date, appointment_remark,attention, gestation_week, gestation_day, anticipate_date, entry_date, province_id, city_id, district_id, province_name, city_name, district_name, address, family_phone, emergency_contact, emergency_contact_phone, remark, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>
