<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.ActivityRightsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.ActivityRightsPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="rights_code" property="rightsCode" />
        <result column="rights_name" property="rightsName" />
        <result column="rights_type" property="rightsType" />
        <result column="rights_start_date" property="rightsStartDate" />
        <result column="rights_end_date" property="rightsEndDate" />
        <result column="is_collect" property="isCollect" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        rights_code, rights_name, rights_type, rights_start_date, rights_end_date, is_collect
    </sql>

</mapper>
