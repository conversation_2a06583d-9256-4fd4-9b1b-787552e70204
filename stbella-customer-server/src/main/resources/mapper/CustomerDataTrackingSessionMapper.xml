<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingSessionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingSessionPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="basic_id" property="basicId"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName" />
        <result column="phone" property="phone" />
        <result column="openid" property="openid"/>
        <result column="sessionid" property="sessionid"/>
        <result column="source" property="source" />
        <result column="scene" property="scene" />
        <result column="scene_key" property="sceneKey" />
        <result column="page_name" property="pageName"/>
        <result column="page_path" property="pagePath"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime" />
        <result column="duration_time" property="durationTime" />
        <result column="last_page_path" property="lastPagePath" />
        <result column="access_depth" property="accessDepth" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type,
        basic_id, client_id, client_name, phone, openid, sessionid, source, scene, scene_key, page_name, page_path, start_time, end_time, duration_time, last_page_path, access_depth
    </sql>

    <!-- 根据会话id 查询会话浏览详情 -->
    <select id="querySessionVisitInfoBySessionId" resultType="com.stbella.customer.server.tracking.entity.CustomerDataTrackingVisitPO">
       select
            id
        FROM
            `customer_data_tracking_visit`
        WHERE
            `deleted` = 0
            and `sessionid` = #{sessionId}
            and  (ISNULL(client_name) or client_name = '' )
            and  (ISNULL(phone) or phone = '' )
    </select>

    <!-- 根据会话id 修改会话事件详情 -->
    <update id="updateSessionVisitInfoBySessionId">
        update customer_data_tracking_visit
        set client_name = #{clientName},
            phone=#{phone}
        where
            `sessionid` = #{sessionId}
        and `deleted` = 0
    </update>

    <!-- 根据会话id 查询会话事件详情 -->
    <select id="querySessionEventInfoBySessionId" resultType="com.stbella.customer.server.tracking.entity.CustomerDataTrackingEventPO">
        select
            id
        FROM
            `customer_data_tracking_event`
        WHERE
            `deleted` = 0
            and `sessionid` = #{sessionId}
            and  (ISNULL(client_name) or client_name = '' )
            and  (ISNULL(phone) or phone = '' )

    </select>

    <!-- 根据会话id 修改会话事件详情 -->
    <update id="updateSessionEventInfoBySessionId">
        update customer_data_tracking_event
        set client_name = #{clientName},
            phone=#{phone}
        where
            `sessionid` = #{sessionId}
          and `deleted` = 0
    </update>

    <!-- 根据渠道统计指定时间段内的访问人数 -->
    <select id="queryTotalUserStatBySource"
            resultType="com.stbella.customer.server.tracking.vo.SourceStatUserVO">
        SELECT
            IFNULL(s.`source`,"wechat") AS 'sourceKey',
            IFNULL(COUNT(DISTINCT s.`openid`),0) AS 'userNumber'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="brandType != null">
                AND s.`brand_type` = #{brandType}
            </if>
        GROUP BY s.`source`
    </select>

    <!-- 根据渠道统计指定时间段内的新增用户数 -->
    <select id="queryNewUserStatBySource"
        resultType="com.stbella.customer.server.tracking.vo.SourceStatUserVO">
        SELECT
            IFNULL(`source`,"other") AS 'sourceKey',
            IFNULL(COUNT(DISTINCT `openid`),0) AS 'userNumber'
        FROM
            `customer_data_tracking_session`
        WHERE
            (`openid`, `start_time`) IN (
        SELECT
        s.`openid`,
        MIN(s.`start_time`) AS register_time
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        <if test="dateStart!=null">
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND s.`openid` NOT IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>

                GROUP BY s.`openid`
            )
            <if test="brandType != null">
                AND `brand_type` = #{brandType}
            </if>
        GROUP BY `source`
    </select>

    <!-- 根据指定的渠道key查询某个时间段内的pv、uv -->
    <select id="querySourceStatUserNumByKeys"
            resultType="com.stbella.customer.server.tracking.dto.SourceStatUserNumDTO">
        SELECT
            s.`source` AS 'source_key',
            s.openid AS 'openid',
            COUNT(*) AS 'page_view'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND u.`deleted` = 0
            AND s.`openid` != ''
            <if test="dateStart!=null">
                AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <if test="sourceKeys != null  and sourceKeys.size() > 0">
                AND s.`source` IN
                <foreach collection="sourceKeys" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="brandType != null">
                AND s.`brand_type` = #{brandType}
            </if>
        GROUP BY s.`source`,s.openid
        ORDER BY s.start_time
    </select>

    <!-- 根据指定时间段查询每个月或每天访问次数 -->
    <select id="queryVisitTrendStatByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
        <if test="type == 0">
            DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'date',
        </if>
        <if test="type == 1">
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
        </if>
        COUNT(*) AS 'num'
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        `date`;
    </select>

    <!-- 根据指定时间段查询每个月或每天访问时长 -->
    <select id="queryVisitTimestatByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
        <if test="type == 0">
            DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'date',
        </if>
        <if test="type == 1">
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
        </if>
        IFNULL(sum(s.`duration_time`),0) AS 'num'
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY `date`;

    </select>

    <!-- 根据指定时间段查询每个月或每天活跃用户数 -->
    <select id="queryActiveUserViewByDate"
            resultType="com.stbella.customer.server.tracking.vo.AllUserNumTotalVO">
        SELECT
            DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'dateMonth',
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
            IFNULL(count(distinct s.openid),0) AS 'num'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            GROUP BY `date`;
    </select>

    <!-- 根据指定时间段查询每个月或每天平均单日使用时长 = 总访问时间/活跃人数 -->
    <select id="queryArgDurationTimeByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseCalculateDoubleNumVO$BaseCalculateDoubleNumDetailVO">
        SELECT
            <if test="type == 0">
                DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'date',
            </if>
            <if test="type == 1">
                DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
            </if>
            IFNULL(ROUND((sum(s.duration_time)/COUNT(DISTINCT s.`openid`))/60000,2), 0) as 'num'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`;

    </select>

    <!-- 根据指定时间段查询每个月或每天平均单次使用时长 = 总访问时间/访问次数 -->
    <select id="queryOneSessionArgDurationTimeByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
            <if test="type == 0">
                DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'date',
            </if>
            <if test="type == 1">
                DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
            </if>
            IFNULL(ROUND((sum(s.duration_time)/COUNT(s.`openid`))/1000),0) as 'num'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`;
    </select>

    <!-- 根据指定时间段查询每个月或每天老用户数 -->
    <select id="queryOldUserViewByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
            <if test="type == 0">
                DATE_FORMAT(s.`start_time`, '%Y-%m') AS 'date',
            </if>
            <if test="type == 1">
                DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'date',
            </if>
            IFNULL(count(distinct s.openid),0) AS 'num'
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            AND (u.`register_time` &lt; #{dateStart} OR u.`register_time` &gt; #{dateEnd})
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
        </if>
        GROUP BY `date`;
    </select>

    <!-- 根据指定时间段查询每个月或每天新增用户数 -->
    <select id="queryNewUserViewByDate"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
            <if test="type == 0">
                DATE_FORMAT(u.`register_time`, '%Y-%m') AS 'date',
            </if>
            <if test="type == 1">
                DATE_FORMAT(u.`register_time`, '%Y-%m-%d') AS 'date',
            </if>
            IFNULL(count(distinct u.`openid`),0) AS 'num'
        FROM
            `customer_data_user_info` AS u
        WHERE
            u.`deleted` = 0
            AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and u.`brand_type` = #{brandType}
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`;
    </select>

    <!-- 根据指定时间段查询用户统计 -->
    <select id="queryUserStatByDate" resultType="com.stbella.customer.server.tracking.vo.UserStatVO">
        SELECT
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'stat_date',
            IFNULL(COUNT(DISTINCT s.`openid`),0) AS 'user_view',
            IFNULL(count(*),0) as 'page_view',
            IFNULL(sum(s.duration_time),0) AS durationTime
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
        <if test="request.brandType != null">
                and s.brand_type = #{request.brandType}
            </if>
            <if test="request.dateStart!=null">
                and s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `stat_date`
        ORDER BY `stat_date` DESC
        <if test="request.offset != null and request.limit != null">
            LIMIT #{request.offset}, #{request.limit}
        </if>
    </select>

    <select id="userStatDetailVOList" resultType="com.stbella.customer.server.tracking.vo.UserStatDetailVO">
        SELECT
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'stat_date',
            u.`client_name`,
            u.`phone`,
            u.`status`,
            u.`id` AS user_id,
            s.`openid`,
            IFNULL(count(s.id),0) as pageView,
            IFNULL(sum(s.duration_time),0) as durationTime
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            <if test="request.brandType != null">
                AND s.`brand_type` = #{request.brandType}
            </if>
            <if test="request.dateStart != null ">
                AND s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
                AND (u.`client_name` like concat('%',#{request.nameOrPhone},'%') or u.`phone` like concat('%',#{request.nameOrPhone},'%'))
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="request.type != null and request.type == 1">
                <!-- 老用户 -->
                AND (u.register_time &lt; #{request.dateStart} OR u.register_time &gt; #{request.dateEnd})
            </if>

            <if test="request.type != null and request.type == 2">
                <!-- 新用户 -->
                AND u.register_time BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
        GROUP BY s.`openid`
        ORDER BY pageView DESC, durationTime DESC, s.`start_time` DESC
        <if test="request.offset != null and request.limit != null">
            LIMIT #{request.offset}, #{request.limit}
        </if>
    </select>

    <!-- 根据指定时间段查询新用户信息 -->
    <select id="queryNewUserInfo" resultType="com.stbella.customer.server.tracking.vo.UserStatDetailVO">
        SELECT
        DATE_FORMAT(s1.`start_time`, '%Y-%m-%d') AS 'stat_date',
        u1.client_name,
        u1.phone,
        u1.`status`,
        u1.`id` AS user_id,
        s1.openid,
        IFNULL(count(s1.openid),0) as 'page_view',
        IFNULL(sum(s1.duration_time),0) as 'duration_time'
        FROM
        `customer_data_tracking_session` AS s1
        LEFT JOIN
        `customer_data_user_info` AS u1 ON s1.`openid` = u1.`openid`
        WHERE
        (s1.`openid`, s1.`start_time`) IN (
        SELECT
        s.`openid`, MIN( s.`start_time` ) AS register_time
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        <if test="dateStart!=null">
            and s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND s.`openid` NOT IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        GROUP BY s.`openid`
        )
        <if test="nameOrPhone!=null and nameOrPhone!=''">
            and ( u1.client_name like concat('%',#{nameOrPhone},'%') or u1.phone like
            concat('%',#{nameOrPhone},'%'))
        </if>
        group by `stat_date`, s1.openid
        order by `stat_date` asc
    </select>

    <!-- 根据指定时间段查询老用户信息 -->
    <select id="queryOldUserInfo" resultType="com.stbella.customer.server.tracking.vo.UserStatDetailVO">
        SELECT
        DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'stat_date',
        u.client_name,
        u.phone,
        u.`status`,
        u.`id` AS user_id,
        s.openid,
        IFNULL(count(s.openid),0) as 'page_view',
        IFNULL(sum(s.duration_time),0) as 'duration_time'
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        <if test="dateStart!=null">
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        </if>
        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND s.`openid` IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="nameOrPhone!=null and nameOrPhone!=''">
            AND ( u.client_name like concat('%',#{nameOrPhone},'%') or u.phone like
            concat('%',#{nameOrPhone},'%'))
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        group by `stat_date`, s.openid
        order by `stat_date` asc
    </select>

    <!-- 根据时间段和openId 查询 用户访问总深度 -->
    <select id="queryPageInteractionNumByData"
        resultType="com.stbella.customer.server.tracking.vo.UserStatDetailVO">
        SELECT
        DATE_FORMAT(v.`start_time`, '%Y-%m-%d') AS 'stat_date',
        v.openid,
        IFNULL(count(v.sessionid),0) as interactionNum
        FROM
        `customer_data_tracking_visit` AS v
        LEFT JOIN
        `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        WHERE
        v.`deleted` = 0
        AND v.`openid` != ''
        AND u.`deleted` = 0
        <if test="brandType != null">
            and v.brand_type = #{brandType}
        </if>
        <if test="dateStart!=null">
            AND v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND v.`openid` IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        group by `stat_date`, v.`openid`
    </select>



    <!-- 根据指定时间段查询每天总访问页面深度 -->
    <select id="queryPageNum" resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        select
            DATE_FORMAT(v.`start_time`, '%Y-%m-%d') AS `date`,
            IFNULL(count(v.sessionid),0) as num
        from
            customer_data_tracking_visit AS v
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        where
            v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
            <if test="dateStart!=null">
                and v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        group by `date`
        order by `date` asc
    </select>

    <!-- 根据指定时间段查询每天总访问页面深度 -->
    <select id="queryPageNumByDateOrUser" resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        select
            DATE_FORMAT(v.`start_time`, '%Y-%m-%d') AS `date`,
            IFNULL(count(v.sessionid),0) as num
        from
            customer_data_tracking_visit AS v
        WHERE
            v.`deleted` = 0
            AND v.`openid` != ''
            <if test="dateStart!=null">
                and v.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <if test="nameOrPhone!=null and nameOrPhone!=''">
                and (u.client_name like concat('%',#{nameOrPhone},'%') or u.phone like concat('%',#{nameOrPhone},'%'))
            </if>
            <if test="pageName!=null and pageName!=''">
                and v.page_name = #{pageName}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        group by `date`
        order by `date` asc
    </select>

    <!-- 根据指定时间段查询session统计列表 -->
    <select id="querySessionStatVOByDate" resultType="com.stbella.customer.server.tracking.vo.SessionStatVO">
        SELECT
            DATE_FORMAT(s.`start_time`, '%Y-%m-%d') AS 'stat_date',
            IFNULL(u.client_name, u.nick_name) AS 'client_name',
            u.phone,
            s.start_time,
            s.end_time,
            s.sessionid,
            IFNULL(s.duration_time,0) as durationTime ,
            sc.source_name as source,
            ts.scene_name AS scene,
            p1.`page_name` AS 'first_name',
            p2.`page_name` AS 'last_name'
        FROM
            customer_data_tracking_session AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        LEFT JOIN
            customer_data_tracking_source_config as sc on s.source = sc.source_key
        LEFT JOIN
            customer_data_tracking_scene AS ts ON s.`scene` = ts.`scene`
        LEFT JOIN
            `customer_data_tracking_page_path` AS p1 ON s.`page_path` = p1.`page_path` AND p1.`brand_type` = #{request.brandType}
        LEFT JOIN
            `customer_data_tracking_page_path` AS p2 ON s.`last_page_path` = p2.`page_path` AND p2.`brand_type` = #{request.brandType}
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            <if test="request.brandType != null">
                and s.brand_type = #{request.brandType}
            </if>
            <if test="request.dateStart!=null">
                and s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="request.nameOrPhone!=null and request.nameOrPhone!=''">
                and (u.client_name like concat('%',#{request.nameOrPhone},'%') or u.phone like concat('%',#{request.nameOrPhone},'%') or u.nick_name like concat('%',#{request.nameOrPhone},'%'))
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
        order by `stat_date` desc , s.start_time desc

        <if test="request.offset !=null and request.limit > -1 ">
            limit #{request.offset} ,#{request.limit}
        </if>


    </select>

    <select id="querySessionStatCountByDate" resultType="java.lang.Integer">
        SELECT
        IFNULL(count(s.sessionid),0)
        FROM
        customer_data_tracking_session AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        left join
        customer_data_tracking_source_config as sc on s.source = sc.source_key

        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        <if test="request.brandType != null">
            and s.brand_type = #{request.brandType}
        </if>
        <if test="request.dateStart!=null">
            and s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
        </if>
        <if test="request.nameOrPhone!=null and request.nameOrPhone!=''">
            and (u.client_name like concat('%',#{request.nameOrPhone},'%') or u.phone like
            concat('%',#{request.nameOrPhone},'%'))
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="queryLastPageBySessionId" resultType="com.stbella.customer.server.tracking.vo.SessionStatVO">
        select
        sessionid,
--         SUBSTRING_INDEX(GROUP_CONCAT(page_name ORDER BY start_time ASC), ',', 1) AS first_name,
--         SUBSTRING_INDEX(GROUP_CONCAT(page_name ORDER BY start_time DESC), ',', 1) AS last_name,
        COUNT(sessionid) as pageNumber
        from
        `customer_data_tracking_visit`
        where
            sessionid in
            <foreach collection="sessionIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        GROUP BY sessionid
    </select>

    <!-- 根据指定时间段查询session详情列表 -->
    <select id="querySessionDetailBySessionId" resultType="com.stbella.customer.server.tracking.vo.SessionDetailVO">
        SELECT
            IFNULL(u.`client_name`, u.`nick_name`) AS 'client_name',
            u.phone,
            path.page_name,
            v.page_path,
            v.start_time,
            v.end_time,
            v.duration_time,
            sourcePath.`page_name` AS source
        FROM
            `customer_data_tracking_visit` AS v
            LEFT JOIN `customer_data_tracking_source_config` AS sc ON v.`source` = sc.`source_key`
            LEFT JOIN `customer_data_tracking_page_path` AS path ON v.`page_path` = path.`page_path` AND v.`brand_type` = path.`brand_type`
            LEFT JOIN `customer_data_tracking_page_path` AS sourcePath ON v.`page_path_pre` = sourcePath.`page_path` AND v.`brand_type` = sourcePath.`brand_type`
            LEFT JOIN `customer_data_user_info` AS u ON v.`openid` = u.`openid`
        WHERE
            v.`sessionid` = #{sessionId}
            AND v.`deleted` = 0
            AND v.`openid` != ''
            AND u.`deleted` = 0
        ORDER BY v.`end_time` ASC, v.`start_time` ASC
    </select>

    <!-- 根据时段查询某天的新增用户数，按时段来区分 -->
    <select id="queryNewAddUserNumByTime"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
        DATE_FORMAT(`start_time`, '%H:00') AS 'date',
        IFNULL(count(distinct openid),0) AS 'num'
        FROM
        `customer_data_tracking_session`
        WHERE
        (`openid`, `start_time`) IN (
        SELECT
        s.`openid`, MIN( s.`start_time` ) AS register_time
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>

        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND s.`openid` NOT IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY s.`openid`
        )
        GROUP BY `date`;
    </select>

    <!-- 根据时段查询某天的老用户数，按时段来区分 -->
    <select id="queryAllUserNumByTime"
            resultType="com.stbella.customer.server.tracking.vo.AllUserNumTotalVO">
        SELECT
            DATE_FORMAT(s.`start_time`, '%H:00') AS 'date',
            <if test="type == 0 or type == 1 or type == 2">
                COUNT(DISTINCT s.`openid`) AS 'num'
            </if>
            <!-- 用户访问次数 -->
            <if test="type == 3">
                COUNT(s.`sessionid`) as num
            </if>
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <!-- 新增用户数 -->
            <if test="type == 0">
                AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <!-- 老用户数 -->
            <if test="type == 1">
                AND (u.`register_time` &lt; #{dateStart} OR u.register_time &gt; #{dateEnd})
            </if>
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`
    </select>

    <!-- 根据时段查询某天的访问次数 -->
    <select id="queryAllUserVisitNumByTime"
        resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
        DATE_FORMAT(s.`start_time`, '%H:00') AS 'date',
        count(1) AS 'num'
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY `date`;
    </select>

    <!-- 根据时间段获取新增用户数 -->
    <select id="queryNewUserNumTotal" resultType="java.lang.Long">
        SELECT
        count(distinct s.openid) AS 'num'
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
        <if test="brandType != null">
            and s.brand_type = #{brandType}
        </if>
        <if test="oldOpenids != null  and oldOpenids.size() > 0">
            AND s.`openid` NOT IN
            <foreach collection="oldOpenids" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 根据时间段获取全部活跃用户数 -->
    <select id="queryAllUserNumTotal" resultType="com.stbella.customer.server.tracking.vo.AllUserNumTotalVO">
        SELECT
            COUNT(s.sessionid) as num,
            s.openid
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            GROUP BY s.openid
    </select>

    <!-- 通过用户类型查询某个时间段内渠道的pv、uv -->
    <select id="queryStatUserNumBySource"
            resultType="com.stbella.customer.server.tracking.dto.SourceStatUserNumDTO">
        SELECT
            s.`source` AS source_key,
            COUNT(1) AS page_view,
            COUNT(DISTINCT s.`openid`) AS user_view
        FROM `customer_data_tracking_session` AS s
            LEFT JOIN `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}

            <if test="sourceList != null  and sourceList.size() > 0">
                AND s.`source` IN
                <foreach collection="sourceList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="brandType != null">
                and s.`brand_type` = #{brandType}
            </if>

            <if test="userStatus != null and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="userType != null and userType == 1">
                AND u.register_time BETWEEN #{dateStart} AND #{dateEnd}
            </if>

            <if test="userType != null and userType == 2">
                AND (u.register_time &lt; #{dateStart} OR u.register_time &gt; #{dateEnd})
            </if>
        GROUP BY s.`source`
    </select>

    <!-- 通过用户类型查询某个时间段内场景的pv、uv -->
    <select id="queryStatUserNumByScene"
            resultType="com.stbella.customer.server.tracking.dto.SourceStatUserNumDTO">
        SELECT
        s.`scene` AS source_key,
        COUNT(1) AS page_view,
        COUNT(DISTINCT s.`openid`) AS user_view
        FROM `customer_data_tracking_session` AS s
        LEFT JOIN `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND s.`openid` != ''
        AND u.`deleted` = 0
        AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}

        <if test="brandType != null">
            and s.`brand_type` = #{brandType}
        </if>

        <if test="sceneList != null and sceneList.size() > 0">
            AND s.`scene` IN
            <foreach collection="sceneList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>

        <if test="userType != null and userType == 1">
            AND u.register_time BETWEEN #{dateStart} AND #{dateEnd}
        </if>

        <if test="userType != null and userType == 2">
            AND (u.register_time &lt; #{dateStart} OR u.register_time &gt; #{dateEnd})
        </if>
        GROUP BY s.`scene`
    </select>

    <!-- 根据指定时间范围查询 新增有手机的用户数 -->
    <select id="queryNewUserHasPhoneByDate"
            resultType="com.stbella.customer.server.tracking.vo.BaseDateNumVO">
        SELECT
            <if test="type == 0">
                DATE_FORMAT(u.`register_time`, '%Y-%m') AS 'date',
            </if>
            <if test="type == 1">
                DATE_FORMAT(u.`register_time`, '%Y-%m-%d') AS 'date',
            </if>
            IFNULL(count(distinct u.`openid`),0) AS 'num'
        FROM
            `customer_data_user_info` AS u
        WHERE
            u.`deleted` = 0
            AND u.`phone` != ''
            AND u.`phone` IS NOT NULL
            AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and u.`brand_type` = #{brandType}
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`
    </select>

    <!-- 查询用户统计列表总条数 -->
    <select id="queryUserStatTotalRecord" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT DATE_FORMAT(s.`start_time`, '%Y-%m-%d'))
        FROM
        `customer_data_tracking_session` AS s
        LEFT JOIN
        `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
        s.`deleted` = 0
        AND u.`deleted` = 0
        AND s.`openid` != ''
        <if test="request.brandType != null">
            and s.brand_type = #{request.brandType}
        </if>
        <if test="request.dateStart!=null">
            and s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
        </if>
        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询用户统计详情总数目 -->
    <select id="userStatDetailCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT s.`openid`)
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND s.`openid` != ''
            AND u.`deleted` = 0
            <if test="request.brandType != null">
                AND s.`brand_type` = #{request.brandType}
            </if>
            <if test="request.dateStart != null ">
                AND s.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
            <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
                AND (u.`client_name` like concat('%',#{request.nameOrPhone},'%') or u.`phone` like concat('%',#{request.nameOrPhone},'%'))
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="request.type != null and request.type == 1">
                <!-- 老用户 -->
                AND (u.register_time &lt; #{request.dateStart} OR u.register_time &gt; #{request.dateEnd})
            </if>

            <if test="request.type != null and request.type == 2">
                <!-- 新用户 -->
                AND u.register_time BETWEEN #{request.dateStart} AND #{request.dateEnd}
            </if>
    </select>

    <!-- 根据类型获取指定时间段的用户数 -->
    <select id="queryUserNumByType" resultType="java.lang.Long">
        SELECT
            <if test="type == 0 or type == 1 or type == 2">
                COUNT(DISTINCT s.`openid`)
            </if>
            <!-- 累计访问次数 -->
            <if test="type == 3">
                COUNT(s.`sessionid`)
            </if>
        FROM
            `customer_data_tracking_session` AS s
        LEFT JOIN
            `customer_data_user_info` AS u ON s.`openid` = u.`openid`
        WHERE
            s.`deleted` = 0
            AND u.`deleted` = 0
            AND s.`openid` != ''
            AND s.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            <!-- 新增用户数 -->
            <if test="type == 0">
                AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            </if>
            <!-- 老用户数 -->
            <if test="type == 1">
                AND (u.`register_time` &lt; #{dateStart} OR u.register_time &gt; #{dateEnd})
            </if>
            <if test="brandType != null">
                and s.brand_type = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
    </select>

    <!-- 根据时段查询某天的新用户数，按时段来区分 -->
    <select id="queryNewUserNumByTime"
            resultType="com.stbella.customer.server.tracking.vo.AllUserNumTotalVO">
        SELECT
            DATE_FORMAT(u.`register_time`, '%H:00') AS 'date',
            COUNT(DISTINCT u.`openid`) AS 'num'
        FROM
            `customer_data_user_info` AS u
        WHERE
            u.`deleted` = 0
            AND u.`openid` != ''
            AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and u.`brand_type` = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY `date`
    </select>

    <!-- 查询某个时间段内新用户的场景的pv、uv -->
    <select id="queryStatNewUserNumByScene"
            resultType="com.stbella.customer.server.tracking.dto.SourceStatUserNumDTO">
        SELECT
            u.`scene` AS source_key,
            COUNT( DISTINCT u.`openid` ) AS user_view
        FROM
            `customer_data_user_info` AS u
        WHERE
            u.`deleted` = 0
            AND u.`openid` != ''
            AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and u.`brand_type` = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="sceneList != null and sceneList.size() > 0">
                AND u.`scene` IN
                <foreach collection="sceneList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY u.`scene`
    </select>

    <!-- 查询某个时间段内渠道新用户的pv、uv -->
    <select id="queryStatNewUserNumBySource"
            resultType="com.stbella.customer.server.tracking.dto.SourceStatUserNumDTO">
        SELECT
            u.`source` AS source_key,
            COUNT( DISTINCT u.`openid` ) AS user_view
        FROM
            `customer_data_user_info` AS u
        WHERE
            u.`deleted` = 0
            AND u.`openid` != ''
            AND u.`register_time` BETWEEN #{dateStart} AND #{dateEnd}
            <if test="brandType != null">
                and u.`brand_type` = #{brandType}
            </if>
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="sourceList != null and sourceList.size() > 0">
                AND u.`source` IN
                <foreach collection="sourceList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY u.`source`
    </select>

</mapper>
