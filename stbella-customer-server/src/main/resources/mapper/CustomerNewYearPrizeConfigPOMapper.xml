<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.newYear.mapper.CustomerNewYearPrizeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.newYear.entity.CustomerNewYearPrizeConfigPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="project" property="project" />
        <result column="name" property="name" />
        <result column="image" property="image" />
        <result column="type" property="type" />
        <result column="day_category" property="dayCategory" />
        <result column="detail_rule" property="detailRule" />
        <result column="amount" property="amount" />
        <result column="total_num" property="totalNum" />
        <result column="per_day_num" property="perDayNum" />
        <result column="used_num" property="usedNum" />
        <result column="integral_num" property="integralNum" />
        <result column="ratio" property="ratio" />
        <result column="brand_type" property="brandType" />
        <result column="version" property="version" />
        <result column="extend" property="extend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        project, `name`, image, `type`, day_category, detail_rule, amount, total_num, per_day_num, used_num, integral_num, ratio, brand_type, version, extend
    </sql>

</mapper>
