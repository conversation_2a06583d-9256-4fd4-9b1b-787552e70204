<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerWechatFansMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerWechatFansPO">
    <result column="id" property="id" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
        <result column="app_type" property="appType" />
        <result column="subscribe" property="subscribe" />
        <result column="subscribe_scene" property="subscribeScene" />
        <result column="subscribe_time" property="subscribeTime" />
        <result column="subscribe_num" property="subscribeNum" />
        <result column="cancel_subscribe_time" property="cancelSubscribeTime" />
        <result column="open_id" property="openId" />
        <result column="nick_name" property="nickName" />
        <result column="sex" property="sex" />
        <result column="city" property="city" />
        <result column="country" property="country" />
        <result column="province" property="province" />
        <result column="phone" property="phone" />
        <result column="language" property="language" />
        <result column="headimg_url" property="headimgUrl" />
        <result column="union_id" property="unionId" />
        <result column="group_id" property="groupId" />
        <result column="tagid_list" property="tagidList" />
        <result column="qr_scene_str" property="qrSceneStr" />
        <result column="latitude" property="latitude" />
        <result column="longitude" property="longitude" />
        <result column="precision" property="precision" />
        <result column="session_key" property="sessionKey" />
        <result column="source" property="source" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_id, create_time, update_id, update_time, remark, del_flag, app_type, subscribe, subscribe_scene, subscribe_time, subscribe_num, cancel_subscribe_time, open_id, nick_name, sex, city, country, province, phone, language, headimg_url, union_id, group_id, tagid_list, qr_scene_str, latitude, longitude, precision, session_key, source
    </sql>

</mapper>
