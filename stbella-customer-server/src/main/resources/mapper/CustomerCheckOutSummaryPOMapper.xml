<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerCheckOutSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerCheckOutSummaryPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="order_no" property="orderNo" />
        <result column="goods_name" property="goodsName" />
        <result column="store_id" property="storeId"/>
        <result column="store_type" property="storeType"/>
        <result column="store_name" property="storeName"/>
        <result column="content" property="content"/>
        <result column="pop_notice" property="popNotice"/>
        <result column="checkin_date" property="checkinDate"/>
        <result column="checkout_date" property="checkoutDate"/>
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_name, customer_id, order_no, goods_name, store_id, store_type, store_name, content, basic_id, pop_notice, share_content, checkin_date, checkout_date, status
    </sql>

    <select id="customerCheckOutSummaryInfo" resultMap="BaseResultMap">
        select
            *
        from
            customer_check_out_summary
        where  deleted = 0

        <if test="request.customerId != null and request.customerId != ''">
            and customer_id = #{request.customerId}
        </if>
        <if test="request.basicId != null and request.basicId != ''">
            and basic_id = #{request.basicId}
        </if>
        <if test="request.orderNo != null and request.orderNo != ''">
            and order_no = #{request.orderNo}
        </if>
        <if test="request.storeType != null">
            and store_type = #{request.storeType}
        </if>
        <if test="request.id != null and request.id != ''">
            and id = #{request.id}
        </if>

        order by `gmt_create` desc
        limit 1
    </select>


</mapper>
