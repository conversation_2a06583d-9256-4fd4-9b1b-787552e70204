<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.TeamMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.TeamMemberPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="sales_name" property="salesName" />
        <result column="scrm_id" property="scrmId"/>
        <result column="team_id" property="teamId"/>
        <result column="employee_id" property="employeeId" />
        <result column="last_assign_time" property="lastAssignTime" />
        <result column="weight" property="weight" />
<!--        <result column="enable" property="enable" />-->
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        sales_name, scrm_id, team_id, employee_id, last_assign_time, weight
    </sql>

    <update id="batchResetTeamMemberCurrent">
        update scrm_team_member
        set current = 0
        where team_id = #{teamId}
    </update>

</mapper>
