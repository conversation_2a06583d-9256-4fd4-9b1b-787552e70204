<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerGrowthRewardConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.vip.CustomerGrowthRewardConfigPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="reward_config_id" property="rewardConfigId" />
        <result column="level_id" property="levelId" />
        <result column="type" property="type" />
        <result column="num" property="num" />
        <result column="validity_type" property="validityType" />
        <result column="validity_number" property="validityNumber" />
        <result column="active" property="active" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type, reward_config_id, level_id, type, num, validity_type, validity_number, active
    </sql>

</mapper>
