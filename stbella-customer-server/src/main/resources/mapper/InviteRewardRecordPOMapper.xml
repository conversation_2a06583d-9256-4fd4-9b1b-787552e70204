<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.invite.mapper.InviteRewardRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.invite.entity.InviteRewardRecordPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="reward_id" property="rewardId" />
        <result column="reward_type" property="rewardType" />
        <result column="reward_number" property="rewardNumber" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="invitee_basic_id" property="inviteeBasicId" />
        <result column="stage_time" property="stageTime" />
        <result column="verify_store_id" property="verifyStoreId" />
        <result column="verify_staff_id" property="verifyStaffId" />
        <result column="verify_staff_name" property="verifyStaffName" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_remark" property="verifyRemark" />
        <result column="extend" property="extend" />
        <result column="coupon_id" property="couponId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_id, reward_id, reward_type, reward_number, status, start_time, end_time, invitee_basic_id, stage_time, verify_store_id, verify_staff_id, verify_staff_name, verify_time, verify_remark, extend
    </sql>

    <!-- 通过request查询符合条件的奖励记录 -->
    <select id="queryByCondition" resultType="com.stbella.customer.server.invite.entity.InviteRewardRecordPO">
        SELECT
            r.*
        FROM
            `invite_reward_record` AS r
        LEFT JOIN
            `invite_reward_config` AS rc ON r.reward_id = rc.id
        WHERE
            r.deleted = 0
            <if test="request.basicId != null">
                AND r.basic_id = #{request.basicId}
            </if>

            <if test="request.inviteeBasicId != null">
                AND r.invitee_basic_id = #{request.inviteeBasicId}
            </if>

            <if test="request.status != null">
                AND r.status = #{request.status}
            </if>

            <if test="request.scene != null">
                AND rc.scene = #{request.scene}
            </if>

            <if test="request.bizType != null">
                AND rc.biz_type = #{request.bizType}
            </if>

            <if test="request.extend != null">
                AND r.extend = #{request.extend}
            </if>

            <if test="request.rewardType != null">
                AND r.reward_type = #{request.rewardType}
            </if>

            <if test="request.ids != null and request.ids.size > 0">
                AND r.id IN
                <foreach item="id" index="index" collection="request.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

    </select>
    <select id="getTotalByRewardType" resultType="java.lang.Integer">
        SELECT SUM(irr.reward_number) AS total
        FROM invite_reward_record irr
                 LEFT JOIN invite_reward_config irc ON irr.reward_id = irc.id
                 LEFT JOIN invite_ticket_config itc ON irc.ticket_id = itc.id
        WHERE irr.deleted = 0
          AND irr.basic_id = #{basicId}
          <if test="rewardType == 1">
              AND irr.reward_type in (1,3)
          </if>
          <if test="rewardType != 1">
            AND irr.reward_type = #{rewardType}
          </if>
          <if test="statusList != null and statusList.size > 0">
            AND irr.status IN
            <foreach item="status" index="index" collection="statusList"
                     open="(" separator="," close=")">
                #{status}
            </foreach>
          </if>
    </select>
    <select id="getInviteRewardRecordsByUnion" resultMap="BaseResultMap">
        SELECT *
        FROM (
        SELECT t1.*
        FROM invite_reward_record t1
        WHERE t1.basic_id IN (
        SELECT DISTINCT invitee_basic_id
        FROM invite_reward_record
        WHERE basic_id = #{request.basicId}
        )
        <if test="request.rewardType!= null">
            AND t1.reward_type = #{request.rewardType}
        </if>
        <if test="request.status!= null">
            AND t1.status = #{request.status}
        </if>
        <if test="request.statusList!= null and request.statusList.size() > 0">
            AND t1.status IN
            <foreach collection="request.statusList" item="statusItem" open="(" close=")" separator=",">
                #{statusItem}
            </foreach>
        </if>
        AND t1.deleted = 0
        UNION
        SELECT t1.*
        FROM invite_reward_record t1
        WHERE t1.basic_id = #{request.basicId}
        <if test="request.rewardType!= null">
            AND t1.reward_type = #{request.rewardType}
        </if>
        <if test="request.status!= null">
            AND t1.status = #{request.status}
        </if>
        <if test="request.statusList!= null and request.statusList.size() > 0">
            AND t1.status IN
            <foreach collection="request.statusList" item="statusItem" open="(" close=")" separator=",">
                #{statusItem}
            </foreach>
        </if>
        AND t1.deleted = 0
        ) combined_results
        <!-- 应用排序条件 -->
        <if test="request.orderByRewardList">
            ORDER BY combined_results.gmt_create DESC, combined_results.reward_type
        </if>
        <if test="request.orderByRewardList == false">
            ORDER BY combined_results.gmt_create DESC
        </if>
    </select>

    <select id="getInviteRewardRecordsByUnionCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (
        SELECT t1.id
        FROM invite_reward_record t1
        WHERE t1.basic_id IN (
        SELECT DISTINCT invitee_basic_id
        FROM invite_reward_record
        WHERE basic_id = #{request.basicId}
        )
        <!-- 应用其他筛选条件 -->
        <if test="request.rewardType!= null">
            AND t1.reward_type = #{request.rewardType}
        </if>
        <if test="request.status!= null">
            AND t1.status = #{request.status}
        </if>
        <if test="request.statusList!= null and request.statusList.size() > 0">
            AND t1.status IN
            <foreach collection="request.statusList" item="statusItem" open="(" close=")" separator=",">
                #{statusItem}
            </foreach>
        </if>
        AND t1.deleted = 0
        UNION
        SELECT t1.id
        FROM invite_reward_record t1
        WHERE t1.basic_id = #{request.basicId}
        <!-- 应用其他筛选条件 -->
        <if test="request.rewardType!= null">
            AND t1.reward_type = #{request.rewardType}
        </if>
        <if test="request.status!= null">
            AND t1.status = #{request.status}
        </if>
        <if test="request.statusList!= null and request.statusList.size() > 0">
            AND t1.status IN
            <foreach collection="request.statusList" item="statusItem" open="(" close=")" separator=",">
                #{statusItem}
            </foreach>
        </if>
        AND t1.deleted = 0
        ) combined_results
    </select>

    <update id="jobUpdateStatus">
        UPDATE invite_reward_record
        SET status =
        CASE
        WHEN start_time > NOW() THEN 0
        WHEN start_time &lt;= NOW() AND end_time >= NOW() THEN 1
        WHEN end_time &lt; NOW() THEN 3
        ELSE status
        END
        where status =1
    </update>

    <update id="updateCouponStatusBasedOnInviteRecord">
        UPDATE customer_base_voucher c
            JOIN invite_reward_record irr ON c.coupon_id = irr.id
        SET c.status = irr.status
        WHERE c.coupon_type = 0
    </update>

    <update id="updateYouzanCouponStatus">
        UPDATE `customer_base_voucher`
        SET `status` = 3
        WHERE
            `deleted` = 0
            AND `biz_type` = 100002
            AND `status` = 1
            AND `end_time` &lt; NOW()
    </update>


</mapper>
