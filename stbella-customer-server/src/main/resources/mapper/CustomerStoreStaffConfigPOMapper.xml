<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerStoreStaffConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerStoreStaffConfigPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="type" property="type" />
        <result column="brand_type" property="brandType" />
        <result column="store_id" property="storeId" />
        <result column="staff_id" property="staffId" />
        <result column="staff_phone" property="staffPhone" />
        <result column="staff_name" property="staffName" />
        <result column="staff_name_en" property="staffNameEn" />
        <result column="avatar" property="avatar" />
        <result column="qw_qrcode" property="qwQrcode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        type, brand_type, store_id, staff_id, staff_phone, staff_name, staff_name_en, avatar, qw_qrcode
    </sql>

</mapper>
