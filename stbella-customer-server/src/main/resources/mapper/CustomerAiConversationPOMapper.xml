<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAiConversationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAiConversationPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="brand_type" property="brandType" />
        <result column="from_type" property="fromType" />
        <result column="agent_id" property="agentId" />
        <result column="conversation_id" property="conversationId" />
        <result column="client_id" property="clientId" />
        <result column="last_chat_time" property="lastChatTime" />
        <result column="read_status" property="readStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        brand_type, from_type, agent_id, conversation_id, client_id, last_chat_time, read_status
    </sql>

</mapper>
