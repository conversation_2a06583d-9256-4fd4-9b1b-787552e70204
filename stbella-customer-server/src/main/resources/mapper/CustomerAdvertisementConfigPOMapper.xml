<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAdvertisementConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAdvertisementConfigPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="page_type" property="pageType" />
        <result column="module" property="module" />
        <result column="picture_url" property="pictureUrl" />
        <result column="link" property="link" />
        <result column="link_type" property="linkType" />
        <result column="weight" property="weight" />
        <result column="valid_start_date" property="validStartDate" />
        <result column="valid_end_date" property="validEndDate" />
        <result column="show_flag" property="showFlag" />
        <result column="store_brand" property="storeBrand" />
        <result column="auth_flag" property="authFlag" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        page_type, module, picture_url, link, link_type, weight, valid_start_date, valid_end_date, show_flag, store_brand, auth_flag, created_by, updated_by
    </sql>

    <select id="configPage" resultMap="BaseResultMap">
        select con.*
        from customer_advertisement_config con
        where con.deleted =0
        <if test="request.pageType != null">
            and con.page_type = #{request.pageType}
        </if>
        <if test="request.module != null">
            and con.module = #{request.module}
        </if>
        <if test="request.storeBrand != null">
            and con.store_brand = #{request.storeBrand}
        </if>
        <if test="request.validFlag != null  and request.validFlag == true">
            and  exists ( select 1 from customer_advertisement_config where now() >= valid_start_date and now() &lt;= valid_end_date and id = con.id )
        </if>
        <if test="request.validFlag != null  and request.validFlag == false">
            and not exists ( select 1 from customer_advertisement_config where now() >= valid_start_date and now() &lt;= valid_end_date and id = con.id )
        </if>
        order by con.weight desc
    </select>

</mapper>
