<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerFormMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerFormPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="name" property="name" />
        <result column="phone_num" property="phoneNum" />
        <result column="city" property="city" />
        <result column="produce_state" property="produceState" />
        <result column="budget" property="budget" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        name, phone_num, city, produce_state, budget
    </sql>

</mapper>
