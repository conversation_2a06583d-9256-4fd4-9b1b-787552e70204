<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeUserNewestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeUserNewestPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="phone" property="phone" />
        <result column="user_name" property="userName" />
        <result column="state" property="state" />
        <result column="constellation_type" property="constellationType" />
        <result column="blood_type" property="bloodType" />
        <result column="predict_born_date" property="predictBornDate" />
        <result column="hospital" property="hospital" />
        <result column="gestational_week" property="gestationalWeek" />
        <result column="invalid_cause" property="invalidCause" />
        <result column="register_time" property="registerTime" />
        <result column="from_type" property="fromType" />
        <result column="age" property="age" />
        <result column="tags" property="tags" />
        <result column="budget" property="budget" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="region" property="region" />
        <result column="address" property="address" />
        <result column="profession" property="profession" />
        <result column="seller_id" property="sellerId" />
        <result column="up_id" property="upId" />
        <result column="urgent_name" property="urgentName" />
        <result column="urgent_phone" property="urgentPhone" />
        <result column="relation_with_client" property="relationWithClient" />
        <result column="card_type" property="cardType" />
        <result column="id_card" property="idCard" />
        <result column="id_card_front" property="idCardFront" />
        <result column="id_card_back" property="idCardBack" />
        <result column="remark" property="remark" />
        <result column="store_ids" property="storeIds" />
        <result column="birthdate" property="birthdate" />
        <result column="user_type" property="userType" />
        <result column="basic_uid" property="basicUid" />
        <result column="parent_basic_id" property="parentBasicId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        phone, user_name, state, constellation_type, blood_type, predict_born_date, hospital, gestational_week, invalid_cause, register_time, from_type, age, tags, budget, province, city, region, address, profession, seller_id, up_id, urgent_name, urgent_phone, relation_with_client, card_type, id_card, id_card_front, id_card_back, remark, store_ids, birthdate, user_type, basic_uid, parent_basic_id
    </sql>

</mapper>
