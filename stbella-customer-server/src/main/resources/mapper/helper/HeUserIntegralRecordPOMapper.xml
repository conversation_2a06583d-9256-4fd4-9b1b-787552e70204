<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeUserIntegralRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeUserIntegralRecordPO">
    <result column="id" property="id" />
        <result column="basic_uid" property="basicUid" />
        <result column="handle" property="handle" />
        <result column="type" property="type" />
        <result column="integral" property="integral" />
        <result column="handle_val" property="handleVal" />
        <result column="this_val" property="thisVal" />
        <result column="state" property="state" />
        <result column="content" property="content" />
        <result column="operator_basic_uid" property="operatorBasicUid" />
        <result column="operator_client_uid" property="operatorClientUid" />
        <result column="operator_client_type" property="operatorClientType" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="order_id" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        basic_uid, handle, type, integral, handle_val, this_val, state, content, operator_basic_uid, operator_client_uid, operator_client_type, is_delete, created_at, updated_at, order_id
    </sql>

</mapper>
