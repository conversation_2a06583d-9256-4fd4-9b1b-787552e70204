<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeGoodsPO">
        <result column="id" property="id"/>
        <result column="store_type" property="storeType"/>
        <result column="store_id" property="storeId"/>
        <result column="goods_type" property="goodsType"/>
        <result column="goods_name" property="goodsName"/>
        <result column="description" property="description"/>
        <result column="goods_price" property="goodsPrice"/>
        <result column="spec_name" property="specName" />
        <result column="category_back" property="categoryBack" />
        <result column="category_front" property="categoryFront" />
        <result column="sale_type" property="saleType" />
        <result column="up_time" property="upTime" />
        <result column="down_time" property="downTime" />
        <result column="service_days" property="serviceDays" />
        <result column="percent_options" property="percentOptions" />
        <result column="image" property="image" />
        <result column="images" property="images" />
        <result column="content_images" property="contentImages" />
        <result column="button_name" property="buttonName" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="goods_sell_type" property="goodsSellType" />
        <result column="cancel_after_verification_type" property="cancelAfterVerificationType" />
        <result column="goods_sub_sell_type" property="goodsSubSellType" />
        <result column="remark_is_required" property="remarkIsRequired" />
        <result column="remark_info" property="remarkInfo" />
        <result column="goods_detail" property="goodsDetail" />
        <result column="offline_store_id" property="offlineStoreId" />
        <result column="service_person_count" property="servicePersonCount" />
        <result column="unit_time" property="unitTime" />
        <result column="infor_fields" property="inforFields" />
        <result column="valid_start" property="validStart" />
        <result column="valid_end" property="validEnd" />
        <result column="limit_type" property="limitType" />
        <result column="limit_rule" property="limitRule" />
        <result column="tags" property="tags" />
        <result column="can_modify_type" property="canModifyType" />
        <result column="activity_time_type" property="activityTimeType" />
        <result column="is_full_moon_type" property="isFullMoonType" />
        <result column="package_cost" property="packageCost" />
        <result column="production_amount" property="productionAmount" />
        <result column="produce_amount_deduction" property="produceAmountDeduction" />
        <result column="sbra_piece" property="sbraPiece" />
        <result column="ecp_room_type" property="ecpRoomType" />
        <result column="parent_id" property="parentId" />
        <result column="new_flag" property="newFlag" />
        <result column="service_type" property="serviceType" />
        <result column="service_tag" property="serviceTag" />
        <result column="validity_value" property="validityValue" />
        <result column="validity_type" property="validityType" />
        <result column="production_instr_id" property="productionInstrId" />
        <result column="part_discount" property="partDiscount" />
        <result column="production_discount_rule_type" property="productionDiscountRuleType" />
        <result column="gift" property="gift" />
        <result column="goods_unit" property="goodsUnit" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_bind_time" property="supplierBindTime" />
        <result column="is_config" property="isConfig" />
        <result column="config_update_at" property="configUpdateAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        store_type, store_id, goods_type, goods_name, description, goods_price, spec_name, category_back, category_front, sale_type, up_time, down_time, service_days, percent_options, image, images, content_images, button_name, is_delete, created_at, updated_at, goods_sell_type, cancel_after_verification_type, goods_sub_sell_type, remark_is_required, remark_info, goods_detail, offline_store_id, service_person_count, unit_time, infor_fields, valid_start, valid_end, limit_type, limit_rule, tags, can_modify_type, activity_time_type, is_full_moon_type, package_cost, production_amount, produce_amount_deduction, sbra_piece, ecp_room_type, parent_id, new_flag, service_type, service_tag, validity_value, validity_type, production_instr_id, part_discount, production_discount_rule_type, gift, goods_unit, supplier_id, supplier_bind_time, is_config, config_update_at
    </sql>

</mapper>
