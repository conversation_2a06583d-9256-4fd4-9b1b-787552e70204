<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderGiftExtendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderGiftExtendPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="goods_name" property="goodsName" />
        <result column="sku_name" property="skuName" />
        <result column="store_id" property="storeId" />
        <result column="order_id" property="orderId" />
        <result column="basic_id" property="basicId" />
        <result column="goods_id" property="goodsId" />
        <result column="nature_type_title" property="natureTypeTitle" />
        <result column="category_back_title" property="categoryBackTitle" />
        <result column="goods_type_title" property="goodsTypeTitle" />
        <result column="sku_id" property="skuId" />
        <result column="type" property="type" />
        <result column="price" property="price" />
        <result column="cost" property="cost" />
        <result column="content" property="content" />
        <result column="goods_num" property="goodsNum" />
        <result column="status" property="status" />
        <result column="valid_start_time" property="validStartTime" />
        <result column="valid_end_time" property="validEndTime" />
        <result column="verification_status" property="verificationStatus" />
        <result column="options_verification" property="optionsVerification" />
        <result column="source" property="source" />
        <result column="validity_value" property="validityValue" />
        <result column="validity_type" property="validityType" />
        <result column="service_time" property="serviceTime" />
        <result column="serve_type" property="serveType" />
        <result column="service_tag" property="serviceTag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        category_id, category_name, goods_name, sku_name, store_id, order_id, basic_id, goods_id, nature_type_title, category_back_title, goods_type_title, sku_id, type, price, cost, content, goods_num, status, valid_start_time, valid_end_time, verification_status, options_verification, source, validity_value, validity_type, service_time, serve_type, service_tag
    </sql>

</mapper>
