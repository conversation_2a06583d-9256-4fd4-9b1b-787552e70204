<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeTagsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeTagsPO">
    <result column="id" property="id" />
        <result column="tag_name" property="tagName" />
        <result column="tag_obj" property="tagObj" />
        <result column="tag_type" property="tagType" />
        <result column="tag_group_id" property="tagGroupId" />
        <result column="sync_c" property="syncC" />
        <result column="source" property="source" />
        <result column="basic_uid" property="basicUid" />
        <result column="is_pi_show" property="isPiShow" />
        <result column="description" property="description" />
        <result column="active" property="active" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        tag_name, tag_obj, tag_type, tag_group_id, sync_c, source, basic_uid, is_pi_show, description, active, is_delete, created_at, updated_at
    </sql>

</mapper>
