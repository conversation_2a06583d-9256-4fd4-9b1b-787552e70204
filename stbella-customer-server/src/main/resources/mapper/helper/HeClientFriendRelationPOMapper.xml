<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeClientFriendRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeClientFriendRelationPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_id" property="clientId"/>
        <result column="client_type" property="clientType"/>
        <result column="store_id" property="storeId" />
        <result column="friend_basic_uid" property="friendBasicUid" />
        <result column="friend_client_uid" property="friendClientUid" />
        <result column="friend_client_type" property="friendClientType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        basic_uid, client_id, client_type, store_id, friend_basic_uid, friend_client_uid, friend_client_type
    </sql>

</mapper>
