<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderPO">
        <id column="order_id" property="orderId" />
        <result column="order_sn" property="orderSn" />
        <result column="order_type" property="orderType" />
        <result column="task_id" property="taskId" />
        <result column="basic_uid" property="basicUid" />
        <result column="client_uid" property="clientUid" />
        <result column="client_type" property="clientType" />
        <result column="staff_id" property="staffId" />
        <result column="store_id" property="storeId" />
        <result column="is_delivery" property="isDelivery" />
        <result column="is_has_refund" property="isHasRefund" />
        <result column="is_close" property="isClose" />
        <result column="pay_status" property="payStatus" />
        <result column="order_amount" property="orderAmount" />
        <result column="holiday_amount" property="holidayAmount" />
        <result column="holiday_items" property="holidayItems" />
        <result column="holiday_num" property="holidayNum" />
        <result column="pay_amount" property="payAmount" />
        <result column="pay_first_time" property="payFirstTime" />
        <result column="paid_amount" property="paidAmount" />
        <result column="pay_integral" property="payIntegral" />
        <result column="production_amount_pay" property="productionAmountPay" />
        <result column="freight" property="freight" />
        <result column="want_in" property="wantIn" />
        <result column="remark" property="remark" />
        <result column="nowpaytask" property="nowpaytask" />
        <result column="express_name" property="expressName" />
        <result column="express_phone" property="expressPhone" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="area_id" property="areaId" />
        <result column="address" property="address" />
        <result column="is_sprint" property="isSprint" />
        <result column="is_send_all" property="isSendAll" />
        <result column="source" property="source" />
        <result column="deposit_task_id" property="depositTaskId" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="order_sell_type" property="orderSellType" />
        <result column="production_type" property="productionType" />
        <result column="infor_fields" property="inforFields" />
        <result column="percent_first_time" property="percentFirstTime" />
        <result column="bk_percent_first_time" property="bkPercentFirstTime" />
        <result column="operation_type" property="operationType" />
        <result column="extends_content" property="extendsContent" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="coupon_user_id" property="couponUserId" />
        <result column="contract_type" property="contractType" />
        <result column="invite_add_integral_type" property="inviteAddIntegralType" />
        <result column="gross_margin" property="grossMargin" />
        <result column="net_margin" property="netMargin" />
        <result column="discount_margin" property="discountMargin" />
        <result column="approval_discount_status" property="approvalDiscountStatus" />
        <result column="is_notice" property="isNotice" />
        <result column="order_tag" property="orderTag" />
        <result column="cert_type" property="certType" />
        <result column="id_card" property="idCard" />
        <result column="id_card_front" property="idCardFront" />
        <result column="id_card_back" property="idCardBack" />
        <result column="sbra_achievement_type" property="sbraAchievementType" />
        <result column="appointment_second_type" property="appointmentSecondType" />
        <result column="pay_finish_at" property="payFinishAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_id, order_sn, order_type, task_id, basic_uid, client_uid, client_type, staff_id, store_id, is_delivery, is_has_refund, is_close, pay_status, order_amount, holiday_amount, holiday_items, holiday_num, pay_amount, pay_first_time, paid_amount, pay_integral, production_amount_pay, freight, want_in, remark, nowpaytask, express_name, express_phone, province, city, area, province_id, city_id, area_id, address, is_sprint, is_send_all, source, deposit_task_id, is_delete, created_at, updated_at, order_sell_type, production_type, infor_fields, percent_first_time, bk_percent_first_time, operation_type, extends_content, coupon_amount, coupon_user_id, contract_type, invite_add_integral_type, gross_margin, net_margin, discount_margin, approval_discount_status, is_notice, order_tag, cert_type, id_card, id_card_front, id_card_back, sbra_achievement_type, appointment_second_type, pay_finish_at
    </sql>

    <select id="queryOrderCountByBasicId" resultType="java.lang.Integer">
        select count(1) as total
        from he_order
        where basic_uid = #{basicId}
          and order_type in (0, 1, 40, 50)
          and is_has_refund != 3
          and paid_amount > 0
          and is_delete = 0
    </select>

    <select id="queryLastOrderStoreByBasicIds" resultType="com.stbella.customer.server.ecp.entity.HeOrderPO">
        SELECT
            o1.basic_uid,
            o1.store_id
        FROM
            he_order o1
                INNER JOIN (
                SELECT
                    basic_uid,
                    MAX(CASE WHEN percent_first_time > 0 THEN percent_first_time ELSE 0 END) AS max_percent_time,
                    MAX(CASE WHEN percent_first_time = 0 THEN created_at ELSE 0 END) AS max_created_time
                FROM
                    he_order
                WHERE
                `basic_uid` IN
                <foreach collection="basicIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                  AND is_delete = 0
                  AND order_type = 0
                GROUP BY
                    basic_uid
            ) o2 ON o1.basic_uid = o2.basic_uid
                AND (
                        (o1.percent_first_time = o2.max_percent_time AND o2.max_percent_time > 0)
                        OR
                        (o1.percent_first_time = 0 AND o1.created_at = o2.max_created_time AND o2.max_percent_time = 0)
                    )
        ORDER BY
            o1.basic_uid;
    </select>

    <select id="queryDoubleIntegralOrder" resultType="com.stbella.customer.server.ecp.entity.HeOrderPO">
        SELECT
            o.order_id,
            o.basic_uid,
            o.order_sn,
            o.pay_amount
        FROM
            he_order AS o
        INNER JOIN he_order_goods AS og ON o.order_id = og.order_id
        INNER JOIN he_goods AS g ON og.goods_id = g.id AND g.parent_id = #{goodsId}
        WHERE
            o.created_at BETWEEN #{startTime} AND #{endTime}
            AND o.order_type = 0
            AND o.pay_finish_at IS NOT NULL
    </select>

    <select id="queryTotalCashPaidAmount" resultType="java.lang.Long">
        SELECT
            SUM( cash_paid_amount - cash_refund_amount - temp_refund_amount ) AS total_income
        FROM
            he_order
        WHERE
            is_delete = 0
            AND basic_uid = #{basicUid}
    </select>
</mapper>
