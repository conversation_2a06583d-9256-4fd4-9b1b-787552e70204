<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeSystemConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeSystemConfigPO">
    <result column="id" property="id" />
        <result column="key" property="key" />
        <result column="value" property="value" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        `key`, `value`, `name`, description, is_delete, created_at, updated_at
    </sql>

    <select id="queryOneByKey"
        resultType="com.stbella.customer.server.ecp.entity.HeSystemConfigPO">
        select
        <include refid="Base_Column_List" />
        from `he_system_config`
        where `key` = #{key, jdbcType=VARCHAR}
        and `is_delete` = 0
        limit 1
    </select>

</mapper>
