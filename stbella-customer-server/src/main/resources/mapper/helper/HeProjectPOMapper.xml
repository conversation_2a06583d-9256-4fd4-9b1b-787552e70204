<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeProjectPO">
        <id column="pk_id" property="pkId"/>
        <result column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="active_type" property="activeType"/>
        <result column="project_name" property="projectName"/>
        <result column="project_member" property="projectMember"/>
        <result column="project_leader" property="projectLeader"/>
        <result column="project_leader_name" property="projectLeaderName" />
        <result column="expect_time" property="expectTime" />
        <result column="real_time" property="realTime" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="admin_id" property="adminId" />
        <result column="admin_name" property="adminName" />
        <result column="staff_id" property="staffId" />
        <result column="staff_name" property="staffName" />
        <result column="nodes_info" property="nodesInfo" />
        <result column="edges_info" property="edgesInfo" />
        <result column="permissions" property="permissions" />
        <result column="client_id" property="clientId" />
        <result column="client_type" property="clientType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
        <result column="client_temp" property="clientTemp" />
        <result column="source" property="source" />
        <result column="completeness" property="completeness" />
        <result column="is_completeness" property="isCompleteness" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        pk_id, template_id, active_type, project_name, project_member, project_leader, project_leader_name, expect_time, real_time, remark, status, admin_id, admin_name, staff_id, staff_name, nodes_info, edges_info, permissions, client_id, client_type, created_at, updated_at, deleted_at, client_temp, source, completeness, is_completeness
    </sql>

</mapper>
