<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeClientOccMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeClientOccPO">
    <result column="id" property="id" />
        <result column="basic_uid" property="basicUid" />
        <result column="client_uid" property="clientUid" />
        <result column="client_type" property="clientType" />
        <result column="store_id" property="storeId" />
        <result column="store_type" property="storeType" />
        <result column="consulting_status" property="consultingStatus" />
        <result column="consulting_time" property="consultingTime" />
        <result column="appoint_status" property="appointStatus" />
        <result column="appoint_time" property="appointTime" />
        <result column="appoint_order_id" property="appointOrderId" />
        <result column="distribution_time" property="distributionTime" />
        <result column="distribution_status" property="distributionStatus" />
        <result column="arrival_status" property="arrivalStatus" />
        <result column="arrival_time" property="arrivalTime" />
        <result column="sign_status" property="signStatus" />
        <result column="sign_time" property="signTime" />
        <result column="from_type" property="fromType" />
        <result column="from_type_extend_id" property="fromTypeExtendId" />
        <result column="register_from" property="registerFrom" />
        <result column="sort" property="sort" />
        <result column="valid_status" property="validStatus" />
        <result column="valid_status_remark" property="validStatusRemark" />
        <result column="last_admin_id" property="lastAdminId" />
        <result column="tags" property="tags" />
        <result column="standard_status" property="standardStatus" />
        <result column="standard_time" property="standardTime" />
        <result column="target_status" property="targetStatus" />
        <result column="checkin_time" property="checkinTime" />
        <result column="checkout_time" property="checkoutTime" />
        <result column="checkin_status" property="checkinStatus" />
        <result column="return_visit_time" property="returnVisitTime" />
        <result column="return_visit_status" property="returnVisitStatus" />
        <result column="old_client_status" property="oldClientStatus" />
        <result column="friend_status" property="friendStatus" />
        <result column="extranet_red_status" property="extranetRedStatus" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        basic_uid, client_uid, client_type, store_id, store_type, consulting_status, consulting_time, appoint_status, appoint_time, appoint_order_id, distribution_time, distribution_status, arrival_status, arrival_time, sign_status, sign_time, from_type, from_type_extend_id, register_from, sort, valid_status, valid_status_remark, last_admin_id, tags, standard_status, standard_time, target_status, checkin_time, checkout_time, checkin_status, return_visit_time, return_visit_status, old_client_status, friend_status, extranet_red_status, is_delete, created_at, updated_at
    </sql>

    <select id="countUserByTag" resultType="java.lang.Integer">
        SELECT
            count( 1 ) AS total
        FROM
            he_client_occ
        WHERE
            is_delete = 0
        <if test="basicUid != null and basicUid != 0">
            AND
            basic_uid = #{basicUid}
        </if>
        <if test="tagId != null and tagId != 0">
            AND FIND_IN_SET(${tagId}, tags)
        </if>
    </select>

</mapper>
