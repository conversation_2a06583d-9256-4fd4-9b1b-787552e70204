<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeIntegralCfgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeIntegralCfgPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="cfg_name" property="cfgName" />
        <result column="cfg_code" property="cfgCode" />
        <result column="integral_num" property="integralNum" />
        <result column="day_upper" property="dayUpper" />
        <result column="total_upper" property="totalUpper" />
        <result column="created_id" property="createdId" />
        <result column="type" property="type" />
        <result column="text" property="text" />
        <result column="ctype" property="ctype" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        cfg_name, cfg_code, integral_num, day_upper, total_upper, created_id, type, text, ctype
    </sql>

</mapper>
