<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeCustomerInStoreRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeCustomerInStoreRecordPO">
    <result column="id" property="id" />
        <result column="consultant_id" property="consultantId" />
        <result column="store_id" property="storeId" />
        <result column="basic_id" property="basicId" />
        <result column="client_id" property="clientId" />
        <result column="client_type" property="clientType" />
        <result column="visit_date" property="visitDate" />
        <result column="visit_room" property="visitRoom" />
        <result column="client_name" property="clientName" />
        <result column="client_age" property="clientAge" />
        <result column="client_phone" property="clientPhone" />
        <result column="professional" property="professional" />
        <result column="production_date" property="productionDate" />
        <result column="source" property="source" />
        <result column="hospital" property="hospital" />
        <result column="living_business" property="livingBusiness" />
        <result column="is_second_child" property="isSecondChild" />
        <result column="focus_on_lili_club" property="focusOnLiliClub" />
        <result column="question" property="question" />
        <result column="base_remark" property="baseRemark" />
        <result column="customer_intention" property="customerIntention" />
        <result column="store_record_remark" property="storeRecordRemark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="is_delete" property="isDelete" />
        <result column="project_id" property="projectId" />
        <result column="invite_info" property="inviteInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        consultant_id, store_id, basic_id, client_id, client_type, visit_date, visit_room, client_name, client_age, client_phone, professional, production_date, source, hospital, living_business, is_second_child, focus_on_lili_club, question, base_remark, customer_intention, store_record_remark, created_at, updated_at, is_delete, project_id, invite_info
    </sql>

</mapper>
