<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeUserEsignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeUserEsignPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="id_card_type" property="idCardType" />
        <result column="id_card_no" property="idCardNo" />
        <result column="state" property="state" />
        <result column="esign_user_id" property="esignUserId" />
        <result column="auth_type" property="authType" />
        <result column="email" property="email" />
        <result column="verify_state" property="verifyState" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        name, phone, id_card_type, id_card_no, state, esign_user_id, auth_type, email, verify_state
    </sql>

</mapper>
