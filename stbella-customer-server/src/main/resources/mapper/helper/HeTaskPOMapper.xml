<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeTaskPO">
        <id column="pk_id" property="pkId" />
    <result column="id" property="id" />
        <result column="task_uid" property="taskUid" />
        <result column="confirm_uid" property="confirmUid" />
        <result column="transfer_uid" property="transferUid" />
        <result column="project_id" property="projectId" />
        <result column="project_name" property="projectName" />
        <result column="task_name" property="taskName" />
        <result column="front_id" property="frontId" />
        <result column="up_id" property="upId" />
        <result column="up_ids" property="upIds" />
        <result column="status" property="status" />
        <result column="execute_status" property="executeStatus" />
        <result column="is_enable" property="isEnable" />
        <result column="type" property="type" />
        <result column="star" property="star" />
        <result column="end_time" property="endTime" />
        <result column="other_file" property="otherFile" />
        <result column="video_file" property="videoFile" />
        <result column="voice_file" property="voiceFile" />
        <result column="image_file" property="imageFile" />
        <result column="upload_info" property="uploadInfo" />
        <result column="remark" property="remark" />
        <result column="remark_bak" property="remarkBak" />
        <result column="transfer_reason" property="transferReason" />
        <result column="transfer_refuse_reason" property="transferRefuseReason" />
        <result column="review_refuse_reason" property="reviewRefuseReason" />
        <result column="file_type" property="fileType" />
        <result column="reviewer" property="reviewer" />
        <result column="expire_time" property="expireTime" />
        <result column="is_transfer" property="isTransfer" />
        <result column="is_read" property="isRead" />
        <result column="nodes_info" property="nodesInfo" />
        <result column="edges_info" property="edgesInfo" />
        <result column="operating_hints" property="operatingHints" />
        <result column="listsort" property="listsort" />
        <result column="formtemplate" property="formtemplate" />
        <result column="formtemplate_id" property="formtemplateId" />
        <result column="formtemplate_type" property="formtemplateType" />
        <result column="extra_content" property="extraContent" />
        <result column="is_babybella" property="isBabybella" />
        <result column="sop_tag" property="sopTag" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
        <result column="is_ending" property="isEnding" />
        <result column="operation_type" property="operationType" />
        <result column="dd_instance_id" property="ddInstanceId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        pk_id, task_uid, confirm_uid, transfer_uid, project_id, project_name, task_name, front_id, up_id, up_ids, status, execute_status, is_enable, type, star, end_time, other_file, video_file, voice_file, image_file, upload_info, remark, remark_bak, transfer_reason, transfer_refuse_reason, review_refuse_reason, file_type, reviewer, expire_time, is_transfer, is_read, nodes_info, edges_info, operating_hints, listsort, formtemplate, formtemplate_id, formtemplate_type, extra_content, is_babybella, sop_tag, created_at, updated_at, deleted_at, is_ending, operation_type, dd_instance_id
    </sql>

</mapper>
