<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeGoodsSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeGoodsSkuPO">
    <result column="id" property="id" />
        <result column="sort" property="sort" />
        <result column="goods_id" property="goodsId" />
        <result column="sku_name" property="skuName" />
        <result column="origin_price" property="originPrice" />
        <result column="goods_price" property="goodsPrice" />
        <result column="stock" property="stock" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="cost_price" property="costPrice" />
        <result column="spec_code" property="specCode" />
        <result column="integral" property="integral" />
        <result column="options_verification" property="optionsVerification" />
        <result column="service_time" property="serviceTime" />
        <result column="producer_config" property="producerConfig" />
        <result column="template_sku_id" property="templateSkuId" />
        <result column="sku_sale_state" property="skuSaleState" />
        <result column="ext" property="ext" />
        <result column="sku_num" property="skuNum" />
        <result column="scale" property="scale" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        sort, goods_id, sku_name, origin_price, goods_price, stock, is_delete, created_at, updated_at, cost_price, spec_code, integral, options_verification, service_time, producer_config, template_sku_id, sku_sale_state, ext, sku_num, scale
    </sql>

</mapper>
