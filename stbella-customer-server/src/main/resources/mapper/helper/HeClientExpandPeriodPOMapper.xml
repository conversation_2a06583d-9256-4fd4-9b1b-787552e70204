<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeClientExpandPeriodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeClientExpandPeriodPO">
    <result column="id" property="id" />
        <result column="ecp_cid" property="ecpCid" />
        <result column="predict_born_time" property="predictBornTime" />
        <result column="manual_predict_born_time" property="manualPredictBornTime" />
        <result column="pregnancy" property="pregnancy" />
        <result column="hospital" property="hospital" />
        <result column="gestation_week_now" property="gestationWeekNow" />
        <result column="want_in" property="wantIn" />
        <result column="check_in" property="checkIn" />
        <result column="holiday_items" property="holidayItems" />
        <result column="holiday_num" property="holidayNum" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        ecp_cid, predict_born_time, manual_predict_born_time, pregnancy, hospital, gestation_week_now, want_in, check_in, holiday_items, holiday_num, is_delete, created_at, updated_at
    </sql>

</mapper>
