<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeAddressPO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="type" property="type"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name, parent_id, type, created_at, updated_at, is_delete
    </sql>

    <!-- 根据id查询所有省市区，只返回省市区字段 -->
    <select id="selectProvincesCitiesDistrictsById" resultType="com.stbella.customer.server.activity.vo.ActivityAddressInfoVO">
        SELECT
            province.id as provinceId, province.name as provinceName,
            city.id as cityId, city.name as cityName,
            district.id as districtId, district.name as districtName
        FROM he_address province
                 LEFT JOIN he_address city ON province.id = city.parent_id AND city.type = 1
                 LEFT JOIN he_address district ON city.id = district.parent_id AND district.type = 2
        WHERE district.id = #{id}
    </select>

</mapper>
