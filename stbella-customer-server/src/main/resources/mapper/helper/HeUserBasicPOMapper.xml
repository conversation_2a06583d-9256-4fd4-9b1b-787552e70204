<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeUserBasicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeUserBasicPO">
    <result column="id" property="id" />
        <result column="phone" property="phone" />
        <result column="name" property="name" />
        <result column="integral" property="integral" />
        <result column="virtual_consum_amount" property="virtualConsumAmount" />
        <result column="real_amount" property="realAmount" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="gender" property="gender" />
        <result column="id_card_no" property="idCardNo" />
        <result column="hl_unique_id" property="hlUniqueId" />
        <result column="manual_predict_born_time" property="manualPredictBornTime" />
        <result column="pregnancy" property="pregnancy" />
        <result column="photo" property="photo" />
        <result column="phone_type" property="phoneType" />
        <result column="password" property="password" />
        <result column="youzan_voucher_sync" property="youzanVoucherSync" />
        <result column="production_newlead_time" property="productionNewleadTime" />
        <result column="sapphire" property="sapphire" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        phone, name, integral, virtual_consum_amount, real_amount, is_delete, created_at, updated_at, gender, id_card_no, hl_unique_id, manual_predict_born_time, pregnancy, photo, phone_type, password, youzan_voucher_sync, production_newlead_time, sapphire
    </sql>

    <!-- 门店邮箱手机号配置, 给对应的人发邮件或消息提醒, 临时使用 -->
    <select id="queryStoreMailConfig"
            resultType="com.stbella.customer.server.ecp.dto.HeStoreMailConfigDTO">
        SELECT
            `type`,
            `store_type`,
            `warzone`,
            `store_id`,
            `email`,
            `phone`
        FROM
            `he_store_email_config`
        WHERE
            `deleted` = 0
    </select>

</mapper>
