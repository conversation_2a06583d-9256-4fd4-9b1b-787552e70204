<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeVvipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeVvipPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="grade" property="grade" />
        <result column="user_type" property="userType" />
        <result column="user_describe" property="userDescribe" />
        <result column="hobby" property="hobby" />
        <result column="company_name" property="companyName" />
        <result column="company_relation" property="companyRelation" />
        <result column="relevance_name" property="relevanceName" />
        <result column="resource" property="resource" />
        <result column="position" property="position" />
        <result column="fans_quantity" property="fansQuantity" />
        <result column="magnum_opus" property="magnumOpus" />
        <result column="politics_name" property="politicsName" />
        <result column="politics_relation" property="politicsRelation" />
        <result column="post_introduce" property="postIntroduce" />
        <result column="platform_type" property="platformType" />
        <result column="blogger_type" property="bloggerType" />
        <result column="blogger_id" property="bloggerId" />
        <result column="investment_comp_store" property="investmentCompStore" />
        <result column="investment_company" property="investmentCompany" />
        <result column="investment_relation" property="investmentRelation" />
        <result column="occupation" property="occupation" />
        <result column="leading_figure" property="leadingFigure" />
        <result column="leading_figure_relation" property="leadingFigureRelation" />
        <result column="become_vvip_date" property="becomeVvipDate" />
        <result column="user_newest_id" property="userNewestId" />
        <result column="vip_brand_type" property="vipBrandType" />
        <result column="vip_type" property="vipType" />
        <result column="organization_name" property="organizationName" />
        <result column="job_title" property="jobTitle" />
        <result column="relation_name" property="relationName" />
        <result column="relation_title" property="relationTitle" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        grade, user_type, user_describe, hobby, company_name, company_relation, relevance_name, resource, position, fans_quantity, magnum_opus, politics_name, politics_relation, post_introduce, platform_type, blogger_type, blogger_id, investment_comp_store, investment_company, investment_relation, occupation, leading_figure, leading_figure_relation, become_vvip_date, user_newest_id, vip_brand_type, vip_type, organization_name, job_title, relation_name, relation_title
    </sql>

</mapper>
