<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderGiftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderGiftPO">
        <result column="id" property="id"/>
        <result column="og_id" property="ogId"/>
        <result column="order_id" property="orderId"/>
        <result column="client_uid" property="clientUid"/>
        <result column="staff_id" property="staffId"/>
        <result column="store_id" property="storeId"/>
        <result column="goods_type" property="goodsType"/>
        <result column="goods_id" property="goodsId" />
        <result column="goods_price" property="goodsPrice" />
        <result column="goods_num" property="goodsNum" />
        <result column="goods_amount" property="goodsAmount" />
        <result column="used_goods_num" property="usedGoodsNum" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        og_id, order_id, client_uid, staff_id, store_id, goods_type, goods_id, goods_price, goods_num, goods_amount, used_goods_num, is_delete, created_at, updated_at
    </sql>

    <select id="getOrderVipGiftList" resultType="com.stbella.customer.server.ecp.dto.HeOrderVipGiftListDTO">
        SELECT og.goods_id,
               og.goods_num,
               og.used_goods_num,
               og.store_id,
               s.store_name,
               g.goods_name,
               g.goods_type,
               g.goods_sub_sell_type,
               g.can_modify_type,
               g.is_full_moon_type,
               g.image,
               g.images
        FROM `sbl-saas-pro`.he_order_gift AS og
                 JOIN `sbl-saas-pro`.he_goods AS g ON og.goods_id = g.id
                 JOIN `ecp`.cfg_store AS s ON og.store_id = s.store_id
        WHERE og.is_delete = 0
          AND og.client_uid = #{clientUid}
          AND og.store_id = #{storeId}
          AND og.goods_type = #{goodsType}
          AND og.order_id = #{orderId}
        ORDER BY og.goods_num
    </select>


</mapper>
