<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeFreezingPointsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeFreezingPointsRecordPO">
    <result column="id" property="id" />
        <result column="basic_uid" property="basicUid" />
        <result column="from_type" property="fromType" />
        <result column="from_basic_uid" property="fromBasicUid" />
        <result column="order_id" property="orderId" />
        <result column="income_record_id" property="incomeRecordId" />
        <result column="refund_id" property="refundId" />
        <result column="integral" property="integral" />
        <result column="status" property="status" />
        <result column="content" property="content" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        basic_uid, from_type, from_basic_uid, order_id, income_record_id, refund_id, integral, status, content, is_delete, created_at, updated_at
    </sql>

</mapper>
