<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderRefundPO">
    <result column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="order_good_id" property="orderGoodId" />
        <result column="income_sn" property="incomeSn" />
        <result column="type" property="type" />
        <result column="refund_order_sn" property="refundOrderSn" />
        <result column="apply_amount" property="applyAmount" />
        <result column="refund_type" property="refundType" />
        <result column="project_id" property="projectId" />
        <result column="agree_at" property="agreeAt" />
        <result column="actual_amount" property="actualAmount" />
        <result column="refund_achievement" property="refundAchievement" />
        <result column="finish_at" property="finishAt" />
        <result column="payment_result" property="paymentResult" />
        <result column="status" property="status" />
        <result column="confirm_uid" property="confirmUid" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="refund_info" property="refundInfo" />
        <result column="remark" property="remark" />
        <result column="refund_reason_type" property="refundReasonType" />
        <result column="refund_reason" property="refundReason" />
        <result column="has_liquidated_damages" property="hasLiquidatedDamages" />
        <result column="express_code" property="expressCode" />
        <result column="express_name" property="expressName" />
        <result column="express_number" property="expressNumber" />
        <result column="task_id" property="taskId" />
        <result column="last_task_id" property="lastTaskId" />
        <result column="check_status" property="checkStatus" />
        <result column="transaction_id" property="transactionId" />
        <result column="is_admin_modify" property="isAdminModify" />
        <result column="gift_extend_disabled" property="giftExtendDisabled" />
        <result column="apply_id" property="applyId" />
        <result column="apply_name" property="applyName" />
        <result column="apply_phone" property="applyPhone" />
        <result column="payment_result_bk" property="paymentResultBk" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        order_id, order_good_id, income_sn, type, refund_order_sn, apply_amount, refund_type, project_id, agree_at, actual_amount, refund_achievement, finish_at, payment_result, status, confirm_uid, is_delete, created_at, updated_at, refund_info, remark, refund_reason_type, refund_reason, has_liquidated_damages, express_code, express_name, express_number, task_id, last_task_id, check_status, transaction_id, is_admin_modify, gift_extend_disabled, apply_id, apply_name, apply_phone, payment_result_bk
    </sql>

</mapper>
