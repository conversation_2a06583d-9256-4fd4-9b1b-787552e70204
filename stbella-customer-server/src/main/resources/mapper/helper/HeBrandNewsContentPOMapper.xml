<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeBrandNewsContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeBrandNewsContentPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="type" property="type"/>
        <result column="source" property="source"/>
        <result column="title" property="title"/>
        <result column="cover_image" property="coverImage" />
        <result column="images" property="images" />
        <result column="details" property="details" />
        <result column="sub_title" property="subTitle" />
        <result column="sort_num" property="sortNum" />
        <result column="publish_time" property="publishTime" />
        <result column="publish_user" property="publishUser" />
        <result column="base_like_num" property="baseLikeNum" />
        <result column="actual_like_num" property="actualLikeNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        type, source, title, cover_image, images, details, sub_title, sort_num, publish_time, publish_user, base_like_num, actual_like_num
    </sql>

</mapper>
