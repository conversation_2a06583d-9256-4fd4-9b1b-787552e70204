<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderDeliveryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderDeliveryRecordPO">
    <result column="id" property="id" />
        <result column="order_good_id" property="orderGoodId" />
        <result column="order_id" property="orderId" />
        <result column="express_code" property="expressCode" />
        <result column="express_name" property="expressName" />
        <result column="express_number" property="expressNumber" />
        <result column="delivery_time" property="deliveryTime" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        order_good_id, order_id, express_code, express_name, express_number, delivery_time, operator_id, operator_name, is_delete, created_at, updated_at
    </sql>

</mapper>
