<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeInviteRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeInviteRelationPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="parent_basic_id" property="parentBasicId" />
        <result column="integral" property="integral" />
        <result column="qr_code" property="qrCode" />
        <result column="qr_id" property="qrId" />
        <result column="brand_type" property="brandType" />
        <result column="checkin_store_time" property="checkinStoreTime" />
        <result column="sign_order_time" property="signOrderTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_id, parent_basic_id, integral, qr_code, qr_id, brand_type, checkin_store_time, sign_order_time
    </sql>

</mapper>
