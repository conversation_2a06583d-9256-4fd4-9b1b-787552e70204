<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.helper.HeOrderGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.HeOrderGoodsPO">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_uid" property="clientUid"/>
        <result column="client_type" property="clientType"/>
        <result column="staff_id" property="staffId"/>
        <result column="store_id" property="storeId"/>
        <result column="order_id" property="orderId" />
        <result column="pay_status" property="payStatus" />
        <result column="goods_type" property="goodsType" />
        <result column="sku_name" property="skuName" />
        <result column="sku_id" property="skuId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_image" property="goodsImage" />
        <result column="goods_num" property="goodsNum" />
        <result column="goods_price_orgin" property="goodsPriceOrgin" />
        <result column="goods_price_pay" property="goodsPricePay" />
        <result column="pay_amount" property="payAmount" />
        <result column="service_days" property="serviceDays" />
        <result column="send_status" property="sendStatus" />
        <result column="refund_status" property="refundStatus" />
        <result column="is_close" property="isClose" />
        <result column="source" property="source" />
        <result column="content" property="content" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="sku_extend_day" property="skuExtendDay" />
        <result column="sku_extend_value" property="skuExtendValue" />
        <result column="sku_extend_id" property="skuExtendId" />
        <result column="goods_sell_type" property="goodsSellType" />
        <result column="integral" property="integral" />
        <result column="pay_integral" property="payIntegral" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="coupon_user_id" property="couponUserId" />
        <result column="piece" property="piece" />
        <result column="room_id" property="roomId" />
        <result column="room_name" property="roomName" />
        <result column="ecp_room_type" property="ecpRoomType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        parent_id, basic_uid, client_uid, client_type, staff_id, store_id, order_id, pay_status, goods_type, sku_name, sku_id, goods_id, goods_name, goods_image, goods_num, goods_price_orgin, goods_price_pay, pay_amount, service_days, send_status, refund_status, is_close, source, content, is_delete, created_at, updated_at, sku_extend_day, sku_extend_value, sku_extend_id, goods_sell_type, integral, pay_integral, coupon_amount, coupon_user_id, piece, room_id, room_name, ecp_room_type
    </sql>

</mapper>
