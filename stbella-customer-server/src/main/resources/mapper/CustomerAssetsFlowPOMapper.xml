<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAssetsFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAssetsFlowPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="biz_type" property="bizType" />
        <result column="assets_val" property="assetsVal" />
        <result column="multiplier" property="multiplier" />
        <result column="flow_source" property="flowSource" />
        <result column="flow_source_biz_id" property="flowSourceBizId" />
        <result column="flow_source_biz_no" property="flowSourceBizNo" />
        <result column="flow_state" property="flowState" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_id, biz_type, assets_val, multiplier, flow_source, flow_source_biz_id, flow_source_biz_no, flow_state, remark, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>
