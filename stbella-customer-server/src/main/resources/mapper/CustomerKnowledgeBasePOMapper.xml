<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerKnowledgeBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerKnowledgeBasePO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="knowledge_type" property="knowledgeType"/>
        <result column="brand_type" property="brandType"/>
        <result column="item_cls" property="itemCls"/>
        <result column="education_project_id" property="educationProjectId" />
        <result column="title" property="title"/>
        <result column="detail" property="detail"/>
        <result column="img" property="img"/>
        <result column="thumb" property="thumb"/>
        <result column="knowledge_status" property="knowledgeStatus"/>
        <result column="proofreader" property="proofreader"/>
        <result column="writer" property="writer"/>
        <result column="upvote_num" property="upvoteNum"/>
        <result column="base_upvote_num" property="baseUpvoteNum"/>
        <result column="base_upvote_num_type" property="baseUpvoteNumType"/>
        <result column="not_upvote_num" property="notUpvoteNum"/>
        <result column="page_view" property="pageView"/>
        <result column="base_page_view" property="basePageView"/>
        <result column="base_page_view_type" property="basePageViewType"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        knowledge_type,brand_type, item_cls, education_project_id, title, detail, img, knowledge_status, proofreader, writer, upvote_num,base_upvote_num
         ,base_upvote_num_type,base_page_view,base_page_view_type, not_upvote_num, page_view, gmt_create, gmt_modified
    </sql>

</mapper>
