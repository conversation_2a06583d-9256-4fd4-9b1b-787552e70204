<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.invite.mapper.InviteTicketConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.invite.entity.InviteTicketConfigPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="reward_title" property="rewardTitle" />
        <result column="reward_image" property="rewardImage" />
        <result column="validity_type" property="validityType" />
        <result column="validity_number" property="validityNumber" />
        <result column="active_type" property="activeType" />
        <result column="reward_type" property="rewardType" />
        <result column="yz_activity_id" property="yzActivityId" />
        <result column="rule_content" property="ruleContent" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        reward_title, reward_image, validity_type, validity_number, active_type, reward_type, yz_activity_id, rule_content
    </sql>

</mapper>
