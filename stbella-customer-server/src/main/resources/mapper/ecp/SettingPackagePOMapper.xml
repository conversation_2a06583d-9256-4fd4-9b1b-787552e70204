<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.SettingPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.SettingPackagePO">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="order_id" property="orderId"/>
        <result column="name" property="name"/>
        <result column="store_id" property="storeId"/>
        <result column="meal_menu_id" property="mealMenuId"/>
        <result column="room_type_id" property="roomTypeId"/>
        <result column="mom_item_vo" property="momItemVo" />
        <result column="baby_item_vo" property="babyItemVo" />
        <result column="fixed_item_vo" property="fixedItemVo" />
        <result column="server_vo" property="serverVo" />
        <result column="health_vo" property="healthVo" />
        <result column="service_days" property="serviceDays" />
        <result column="price" property="price" />
        <result column="is_small" property="isSmall" />
        <result column="active" property="active" />
        <result column="last_admin_id" property="lastAdminId" />
        <result column="update_time" property="updateTime" />
        <result column="record_time" property="recordTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        parent_id, order_id, name, store_id, meal_menu_id, room_type_id, mom_item_vo, baby_item_vo, fixed_item_vo, server_vo, health_vo, service_days, price, is_small, active, last_admin_id, update_time, record_time
    </sql>

</mapper>
