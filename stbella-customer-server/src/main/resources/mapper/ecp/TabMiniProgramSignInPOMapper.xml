<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.TabMiniProgramSignInMapper">

    <!-- 根据经纬度查询门店列表 (Integer distance, SignInStoreListRequest request); -->
    <select id="getStoreList" resultType="com.stbella.customer.server.ecp.dto.StoreDTO">
        select store_id,
               name_alias                                                   as store_name,
               address,
               cover_url,
               round((6370.996 * acos(cos(radians(#{request.lat}))
                                          * cos(radians(lat))
                                          * cos(radians(lng) - radians(#{request.lng}))
                   + sin(radians(#{request.lat})) * sin(radians(lat)))), 2) AS distance
        from cfg_store
        where active = 1
          and `type` = #{request.brandType}
          and lat is not null
          and lng is not null
        <if test="request.hideStoreIds != null and request.hideStoreIds.size() > 0">
            AND `store_id` NOT IN
            <foreach collection="request.hideStoreIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        having distance &lt; #{distance}
        order by distance
    </select>

    <!-- 根据城市获取门店列表 -->
    <select id="getStoreListByCity"
            resultType="com.stbella.customer.server.ecp.dto.StoreDTO">
        SELECT
            `store_id`,
            `name_alias` as store_name,
            `address`,
            `cover_url`,
            round((6370.996 * acos(cos(radians(#{request.lat}))
                * cos(radians(lat))
                * cos(radians(lng) - radians(#{request.lng}))
                + sin(radians(#{request.lat})) * sin(radians(lat)))), 2) AS distance
        FROM `cfg_store`
        WHERE `active` = 1
            AND `type` = #{request.brandType}
            AND `lat` is not null
            AND `lng` is not null
            <if test="request.cityId != null and request.cityId != -1">
                <if test="request.areaType == 1">
                    AND `city` = #{request.cityId}
                </if>
                <if test="request.areaType == 2">
                    AND `country` = #{request.cityId}
                </if>
            </if>

            <if test="request.storeNameSearch != null and request.storeNameSearch != '' ">
                AND (name_alias LIKE concat('%', #{request.storeNameSearch},  '%') OR store_name LIKE concat('%', #{request.storeNameSearch},  '%'))
            </if>

            <if test="request.hideStoreIds != null and request.hideStoreIds.size() > 0">
                AND `store_id` NOT IN
                <foreach collection="request.hideStoreIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        ORDER BY distance
    </select>


</mapper>