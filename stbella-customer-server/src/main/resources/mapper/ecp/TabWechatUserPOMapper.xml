<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.TabWechatUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.TabWechatUserPO">
    <result column="id" property="id" />
        <result column="openid" property="openid" />
        <result column="session_key" property="sessionKey" />
        <result column="unionid" property="unionid" />
        <result column="phone_number" property="phoneNumber" />
        <result column="phone_code" property="phoneCode" />
        <result column="pure_phone_number" property="purePhoneNumber" />
        <result column="nickname" property="nickname" />
        <result column="birthday" property="birthday" />
        <result column="realname" property="realname" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="gender" property="gender" />
        <result column="city" property="city" />
        <result column="country" property="country" />
        <result column="province" property="province" />
        <result column="address" property="address" />
        <result column="language" property="language" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
        <result column="qr_code_key" property="qrCodeKey" />
        <result column="from_type" property="fromType" />
        <result column="active_fat" property="activeFat" />
        <result column="height" property="height" />
        <result column="active_amp" property="activeAmp" />
        <result column="basic_uid" property="basicUid" />
        <result column="research_question" property="researchQuestion" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        openid, session_key, unionid, phone_number, phone_code, pure_phone_number, nickname, birthday, realname, avatar_url, gender, city, country, province, address, language, created_at, updated_at, deleted_at, qr_code_key, from_type, active_fat, height, active_amp, basic_uid, research_question
    </sql>

    <delete id="clearWechatTokenByBasicUid">
        DELETE FROM `tab_miniprogram_token` WHERE `basic_uid` = #{basicUid}
    </delete>

</mapper>
