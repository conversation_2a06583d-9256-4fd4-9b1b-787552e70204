<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.CfgStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.EcpStorePO">
        <id column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="region" property="region"/>
        <result column="address" property="address"/>
        <result column="manager_id" property="managerId"/>
        <result column="active" property="active"/>
        <result column="update_time" property="updateTime"/>
        <result column="prefix" property="prefix"/>
        <result column="city_no" property="cityNo"/>
        <result column="bill_date" property="billDate"/>
        <result column="bill_count" property="billCount"/>
        <result column="nurse_shifts" property="nurseShifts"/>
        <result column="order_vo" property="orderVo"/>
        <result column="storage_vo" property="storageVo"/>
        <result column="pact_vo" property="pactVo"/>
        <result column="uv_vo" property="uvVo"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="head_url" property="headUrl"/>
        <result column="share_image" property="shareImage"/>
        <result column="imgs" property="imgs"/>
        <result column="type" property="type"/>
        <result column="child_type" property="childType"/>
        <result column="style_type" property="styleType"/>
        <result column="name_alias" property="nameAlias"/>
        <result column="name_alias_ding" property="nameAliasDing"/>
        <result column="name_en" property="nameEn"/>
        <result column="created_at" property="createdAt"/>
        <result column="company_name" property="companyName"/>
        <result column="bank_sn" property="bankSn"/>
        <result column="bank_open" property="bankOpen"/>
        <result column="stamp_url" property="stampUrl"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="region_code" property="regionCode"/>
        <result column="contract_signature_name" property="contractSignatureName"/>
        <result column="contract_signature_image" property="contractSignatureImage"/>
        <result column="regional" property="regional"/>
        <result column="store_name_to_c" property="storeNameToC"/>
        <result column="contract_type" property="contractType"/>
        <result column="appointment_status" property="appointmentStatus"/>
        <result column="dept_id" property="deptId"/>
        <result column="deposit" property="deposit"/>
        <result column="content_id" property="contentId"/>
        <result column="city_sort" property="citySort"/>
        <result column="holiday_cost" property="holidayCost"/>
        <result column="holiday_price" property="holidayPrice"/>
        <result column="stores_figure" property="storesFigure"/>
        <result column="open_time" property="openTime"/>
        <result column="sort" property="sort"/>
        <result column="view_sort" property="viewSort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        store_id
        , store_name, province, city, region, address, manager_id, active, update_time, prefix, city_no, bill_date, bill_count, nurse_shifts, order_vo, storage_vo, pact_vo, uv_vo, lng, lat, cover_url, head_url, share_image, imgs, type, child_type, name_alias, name_alias_ding, name_en, created_at, company_name, bank_sn, bank_open, stamp_url, province_code, city_code, region_code, contract_signature_name, contract_signature_image, regional, store_name_to_c, contract_type, appointment_status, dept_id, deposit, content_id, city_sort, holiday_cost, holiday_price, stores_figure, open_time, sort, view_sort
    </sql>

    <select id="queryStoreList" resultType="com.stbella.customer.server.ecp.dto.StoreDTO">
        select s.*
        <if test="queryType == 3 or queryType == 4">
            ,d.name
        </if>
        <if test="request.lat != null and request.lng!=null">
            ,round((6370.996 * acos(cos ( radians(#{request.lat}) )
            * cos( radians( s.lat ) )
            * cos( radians( s.lng ) - radians( #{request.lng} ) )
            + sin ( radians( #{request.lat} ) )* sin ( radians( s.lat ) ))), 1) AS distance
        </if>

        from cfg_store as s
        <if test="queryType == 3 or queryType == 4">
            ,cfg_district as d
        </if>
        where s.active = 1
        and s.`type` = #{request.type}

        <if test="storeIdList != null  and storeIdList.size() > 0">
            and s.store_id
            not in
            <foreach collection="storeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="inStoreIdList != null  and inStoreIdList.size() > 0">
            and s.store_id
            in
            <foreach collection="inStoreIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryType == 1">
            and s.city = #{request.cityId}
        </if>

        <if test="queryType == 2">
            and s.country = #{request.cityId}
        </if>

        <if test="queryType == 3">
            and s.city = d.id and d.country_id = 1
        </if>

        <if test="queryType == 4">
            and s.province = d.id and d.country_id != 1
        </if>

        <if test="request.lat != null and request.lng != null">
            and s.lat is not null
            and s.lng is not null
            <if test="request.nearby == 1">
                having distance &lt; 100
            </if>
            order by distance
        </if>

    </select>

</mapper>
