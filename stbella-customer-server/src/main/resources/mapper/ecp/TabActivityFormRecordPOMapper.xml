<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.TabActivityFormRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.TabActivityFormRecordPO">
    <result column="id" property="id" />
        <result column="openid" property="openid" />
        <result column="form_id" property="formId" />
        <result column="activity_id" property="activityId" />
        <result column="submit_content" property="submitContent" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
        <result column="show_type" property="showType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        openid, form_id, activity_id, submit_content, created_at, updated_at, deleted_at, show_type
    </sql>

</mapper>
