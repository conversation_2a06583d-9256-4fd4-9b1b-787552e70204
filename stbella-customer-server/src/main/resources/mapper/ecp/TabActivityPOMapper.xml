<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.TabActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.TabActivityPO">
        <result column="id" property="id"/>
        <result column="store_id" property="storeId"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="redirect_url" property="redirectUrl"/>
        <result column="redirect_type" property="redirectType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="active" property="active"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
        <result column="is_top" property="isTop"/>
        <result column="signin_img" property="signinImg"/>
        <result column="signin_url" property="signinUrl"/>
        <result column="signin_type" property="signinType"/>
        <result column="repeat_signin_img" property="repeatSigninImg"/>
        <result column="repeat_signin_url" property="repeatSigninUrl"/>
        <result column="repeat_signin_type" property="repeatSigninType"/>
        <result column="overdue_signin_img" property="overdueSigninImg"/>
        <result column="overdue_signin_url" property="overdueSigninUrl"/>
        <result column="overdue_signin_type" property="overdueSigninType"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="signin_code" property="signinCode"/>
        <result column="is_recommended" property="isRecommended"/>
        <result column="show_type" property="showType"/>
        <result column="class_id" property="classId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        store_id, name, desc, cover_url, redirect_url, redirect_type, start_time, end_time, active, status, created_at,
        updated_at, deleted_at, is_top, signin_img, signin_url, signin_type, repeat_signin_img, repeat_signin_url,
        repeat_signin_type, overdue_signin_img, overdue_signin_url, overdue_signin_type, contact_phone, signin_code,
        is_recommended, show_type, class_id
    </sql>

    <sql id="store">
        s.store_id as storeId,
        s.store_name as `storeName`,
        s.store_name_to_c as `storeNameToC`,
        s.name_alias,
        s.name_en,
        s.lng,
        s.lat,
        s.address
    </sql>

    <select id="getActivityList" resultType="com.stbella.customer.server.ecp.dto.TabActivityDTO">
        select a.*,
        <include refid="store"/>

        <if test="request.cityId !=null and request.cityId!=''">
            <if test="request.lat!=null an request.lng!=null">
                ,6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI() / 180 - s.lat * PI() / 180) / 2), 2) +
                COS(#{request.lat} *
                PI() / 180) *
                COS(s.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - s.lng * PI() / 180) / 2), 2))) *1000 AS
                distance
            </if>
        </if>

        from tab_activity as a
        left join cfg_store as s on a.store_id = s.store_id

        where
        a.show_type = 1
        and a.active = 1
        <if test="request.classId!=null and request.classId > 0">
            and a.class_id = #{request.classId}
        </if>
        <if test="request.categoryType!=null and request.categoryType == 0">
            and a.is_recommended = 1
        </if>
        <if test="request.categoryType!=null and request.categoryType != 0">
            and a.end_time &lt; #{request.date}
        </if>

        <if test="request.cityId !=null and request.cityId!=''">
            and (s.city= #{request.cityId} or s.city = 999999)
            <if test="request.lat!=null an request.lng!=null">
                order by distance
            </if>
        </if>

    </select>


    <select id="getActivityListV2" resultType="com.stbella.customer.server.ecp.dto.TabActivityDTO">
        select a.*,
        <include refid="store"/>
        <if test="request.cityId !=null and request.cityId==0">
            <if test="request.lat!=null and request.lng!=null">
                ,6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI() / 180 - a.lat * PI() / 180) / 2), 2) +
                COS(#{request.lat} *
                PI() / 180) *
                COS(a.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - a.lng * PI() / 180) / 2), 2))) *1000 AS
                distance
            </if>
        </if>
        from tab_activity as a
        left join cfg_store as s on a.store_id = s.store_id
        where a.deleted_at is null and a.active = 1
        <if test="request.brandType !=null and request.brandType >=100">
            and a.show_type = #{request.brandType}
        </if>
        <if test="(request.brandType ==null or request.brandType &lt;100)">
            and a.show_type = 1
        </if>
        <if test="request.classId!=null and request.classId!=0">
            <if test="request.brandType !=null and request.brandType >=100">
                and a.class_id = #{request.classId}
            </if>
            <if test="(request.brandType ==null or request.brandType &lt;100)">
                <choose>
                    <when test="request.classId==3">
                        and a.class_id not in (1,2)
                    </when>
                    <otherwise>
                        and a.class_id = #{request.classId}
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="request.categoryType != null">
            <choose>
                <when test="request.categoryType == 0">
                    and a.is_recommended = 1
                </when>
                <otherwise>
                    and a.end_time &lt; #{request.date}
                </otherwise>
            </choose>
        </if>

        <if test="request.status!=null">
            and a.status = #{request.status}
        </if>
        <if test="request.cityId !=null">
            <if test="request.cityId>0">
                and a.city= #{request.cityId}
            </if>
        </if>
        <!--order、group -->
        <if test="request.cityId !=null">
            <if test="request.cityId==0 and request.lat!=null and request.lng!=null">
                having distance &lt;= 100000
                order by a.created_at desc
            </if>
            <if test="request.cityId!=0">
                order by a.created_at desc;
            </if>
        </if>

    </select>

    <select id="getActivityCityList" resultType="com.stbella.customer.server.ecp.vo.ActiveCityListVO">
        select count(1) as count,a.city,c.name as cityName from tab_activity a
        left join cfg_district c on a.city=c.id
        where
        <if test="brandType !=null and brandType >=100">
            a.show_type = #{brandType}
        </if>
        <if test="(brandType ==null or brandType &lt;100)">
            a.show_type = 1
        </if>
        and a.deleted_at is null
        and a.active = 1
        and a.city is not null
        group by a.city
        order by count(1) desc;
    </select>

    <select id="getLastestActivity" resultType="com.stbella.customer.server.ecp.dto.TabActivityDTO">
        select * from tab_activity a  where
        <if test="brandType !=null and brandType >=100">
            a.show_type = #{brandType}
        </if>
        <if test="(brandType ==null or brandType &lt;100)">
            a.show_type = 1
        </if>
        and a.active = 1 and a.deleted_at is null
        order by created_at desc
        limit 1
    </select>

</mapper>
