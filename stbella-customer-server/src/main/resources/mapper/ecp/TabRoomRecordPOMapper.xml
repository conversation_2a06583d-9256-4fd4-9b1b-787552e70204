<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.TabRoomRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.TabRoomRecordPO">
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="room_type" property="roomType"/>
        <result column="room_number" property="roomNumber"/>
        <result column="client_id" property="clientId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="status" property="status" />
        <result column="last_admin_id" property="lastAdminId" />
        <result column="store_id" property="storeId" />
        <result column="staff_id" property="staffId" />
        <result column="staff_ids" property="staffIds" />
        <result column="active" property="active" />
        <result column="record_time" property="recordTime" />
        <result column="update_time" property="updateTime" />
        <result column="order_id" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        room_id, room_type, room_number, client_id, start_date, end_date, status, last_admin_id, store_id, staff_id, staff_ids, active, record_time, update_time, order_id
    </sql>

</mapper>
