<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.ecp.mapper.ecp.CfgCountryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.ecp.entity.CfgCountryPO">
        <result column="id" property="id"/>
        <result column="country_name" property="countryName"/>
        <result column="country_en_name" property="countryEnName"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        country_name, country_en_name, sort
    </sql>

</mapper>
