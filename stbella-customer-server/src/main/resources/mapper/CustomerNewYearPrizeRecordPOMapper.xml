<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.newYear.mapper.CustomerNewYearPrizeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.newYear.entity.CustomerNewYearPrizeRecordPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_uid" property="basicUid" />
        <result column="prize_id" property="prizeId" />
        <result column="prize_name" property="prizeName" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="verify_store_id" property="verifyStoreId" />
        <result column="verify_staff_id" property="verifyStaffId" />
        <result column="verify_staff_name" property="verifyStaffName" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_remark" property="verifyRemark" />
        <result column="brand_type" property="brandType" />
        <result column="winning_time" property="winningTime" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_uid, prize_id, prize_name, status, start_time, end_time, verify_store_id, verify_staff_id, verify_staff_name, verify_time, verify_remark, brand_type, winning_time, version
    </sql>

</mapper>
