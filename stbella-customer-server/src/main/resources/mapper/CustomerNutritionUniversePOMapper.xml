<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerNutritionUniverseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.nutuition.entity.CustomerNutritionUniversePO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="phone" property="phone"/>
        <result column="source" property="source"/>
        <result column="all_amount" property="allAmount"/>
        <result column="all_reality_amount" property="allRealityAmount"/>
        <result column="last_order_time" property="lastOrderTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        phone, source, all_amount, all_reality_amount, last_order_time
    </sql>

</mapper>
