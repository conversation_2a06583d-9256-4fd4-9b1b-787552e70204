<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmCustomerOrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmCustomerOrderRefundPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="scrm_id" property="scrmId" />
        <result column="phone" property="phone" />
        <result column="customer_id" property="customerId" />
        <result column="order_id" property="orderId" />
        <result column="refund_id" property="refundId" />
        <result column="order_refund_sn" property="orderRefundSn" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_achievement" property="refundAchievement" />
        <result column="refund_apply_date" property="refundApplyDate" />
        <result column="refund_finish_date" property="refundFinishDate" />
        <result column="income_type" property="incomeType" />
        <result column="refund_reason" property="refundReason" />
        <result column="refund_type" property="refundType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        scrm_id, phone, customer_id, order_id, refund_id, order_refund_sn, refund_amount, refund_achievement, refund_apply_date, refund_finish_date, income_type, refund_reason, refund_type
    </sql>

</mapper>
