<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerCurriculumMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerCurriculumPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="course_name" property="courseName" />
        <result column="course_class" property="courseClass" />
        <result column="brand" property="brand" />
        <result column="course_sort" property="courseSort" />
        <result column="course_layout" property="courseLayout" />
        <result column="course_video" property="courseVideo" />
        <result column="course_cover" property="courseCover" />
        <result column="course_duration" property="courseDuration" />
        <result column="basic_visit_number" property="basicVisitNumber" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        tenant_id, course_id, course_name, course_class, brand, course_sort, course_layout, course_video, course_cover, course_duration, basic_visit_number, status
    </sql>
    <select id="selectLater" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from customer_curriculum
        where course_class = #{courseClass}
        order by course_id desc limit 1
    </select>

</mapper>
