<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerKnowledgeBasePushRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerKnowledgeBasePushRecordPO">
        <result column="id" property="id"/>
        <result column="push_type" property="pushType" />
        <result column="mobile" property="mobile" />
        <result column="order_no" property="orderNo" />
        <result column="openid" property="openid" />
        <result column="knowledge_base_id" property="knowledgeBaseId" />
        <result column="article_title" property="articleTitle" />
        <result column="basic_uid" property="basicUid"/>
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="store_type" property="storeType"/>
        <result column="push_channel" property="pushChannel"/>
        <result column="push_state" property="pushState"/>
        <result column="push_time" property="pushTime"/>
        <result column="push_user_name" property="pushUserName"/>
        <result column="push_user_id" property="pushUserId"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="read" property="read"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
    push_type, mobile, order_no, openid, knowledge_base_id, article_title,basic_uid, client_id, client_name, store_id, store_name, store_type, push_channel, push_state, push_time, push_user_name, push_user_id, error_msg,  read, gmt_create,
        gmt_modified,
        deleted
    </sql>


    <select id="queryKnowledgeBasePushRecordStatisticsList"
            resultType="com.stbella.customer.server.customer.dto.KnowledgeBasePushRecordStatisticsListDTO">
        SELECT r.order_no,
        r.client_id,
        r.mobile,
        r.client_name,
        r.store_id,
        r.store_name,
        r.push_state,
        min(r.push_time) as pushTime,
        COUNT(r.push_state) as num,
        GROUP_CONCAT(DISTINCT r.knowledge_base_id) as idList
        from customer_knowledge_base as b
        LEFT JOIN customer_knowledge_base_push_record as r on b.id = r.knowledge_base_id
        where b.knowledge_type = #{request.type}
        and r.deleted = 0
        <if test="request.name !=null and request.name!=''">
            and r.client_name like concat('%',#{request.name},'%')
        </if>
        <if test="request.phone !=null and request.phone!=''">
            and r.mobile like concat('%',#{request.phone},'%')
        </if>
        <if test="request.startTime !=null">
            and r.push_time BETWEEN #{request.startTime} and #{request.endTime}
        </if>
        <if test="request.idList != null  and request.idList.size() > 0">
            AND r.`knowledge_base_id` IN
            <foreach collection="request.idList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="request.storeIdList !=null and request.storeIdList.size() > 0">
            AND r.`store_id` IN
            <foreach collection="request.storeIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="request.pushUserIdList !=null and request.pushUserIdList.size() > 0">
            AND r.`push_user_id` IN
            <foreach collection="request.pushUserIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY r.order_no, r.push_state
    </select>

</mapper>
