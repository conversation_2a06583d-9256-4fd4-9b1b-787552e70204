<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerQuestionnaireMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerQuestionnairePO">
        <id column="id" property="id" />
        <result column="questionnaire_id" property="questionnaireId" />
        <result column="questionnaire_name" property="questionnaireName" />
        <result column="questionnaire_write" property="questionnaireWrite" />
        <result column="questionnaire_create_date" property="questionnaireCreateDate" />
        <result column="questionnaire_write_date" property="questionnaireWriteDate" />
        <result column="brand_type" property="brandType" />
        <result column="biz_id" property="bizId" />
        <result column="basic_id" property="basicId" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, questionnaire_id, questionnaire_name, questionnaire_write, questionnaire_create_date, questionnaire_write_date, brand_type, biz_id, basic_id, create_by, create_by_name, gmt_create, gmt_modified, deleted
    </sql>

</mapper>
