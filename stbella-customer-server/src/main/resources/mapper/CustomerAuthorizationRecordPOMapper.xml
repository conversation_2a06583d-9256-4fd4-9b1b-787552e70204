<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAuthorizationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAuthorizationRecordPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="biz_type" property="bizType" />
        <result column="order_no" property="orderNo" />
        <result column="order_type" property="orderType" />
        <result column="store_id" property="storeId" />
        <result column="status" property="status" />
        <result column="confirm_date" property="confirmDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        biz_type, order_no, order_type, store_id, status, confirm_date
    </sql>

</mapper>
