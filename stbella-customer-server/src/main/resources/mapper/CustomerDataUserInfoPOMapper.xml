<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataUserInfoPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="basic_id" property="basicId" />
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="phone" property="phone" />
        <result column="openid" property="openid" />
        <result column="nick_name" property="nickName" />
        <result column="source" property="source" />
        <result column="scene" property="scene" />
        <result column="scene_key" property="sceneKey" />
        <result column="status" property="status" />
        <result column="setting_time" property="settingTime" />
        <result column="brand_type" property="brandType" />
        <result column="register_time" property="registerTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        basic_id, client_id, client_name, phone, openid, nick_name, source, scene, scene_key, status, setting_time, brand_type, register_time
    </sql>

</mapper>
