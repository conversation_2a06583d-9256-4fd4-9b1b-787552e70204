<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerWechatFansTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerWechatFansTempPO">
        <result column="id" property="id" />
        <result column="brand_type" property="brandType" />
        <result column="openid" property="openid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        brand_type, openid
    </sql>

    <!-- 更新之前关注，现在取消关注的用户 -->
    <update id="updateUnSubscribeUserStatus">
        UPDATE `customer_wechat_fans` SET `subscribe` = 0
        WHERE `open_id` NOT IN (
            SELECT `openid` FROM `customer_wechat_fans_temp` WHERE `brand_type` = #{brandType}
        ) AND `subscribe` = 1 AND `source` = #{source}
    </update>

    <!-- 更新之前取消关注，现在又关注了的用户 -->
    <update id="updateSubscribeUserStatus">
        UPDATE `customer_wechat_fans` SET `subscribe` = 1
        WHERE `open_id` IN (
            SELECT `openid` FROM `customer_wechat_fans_temp` WHERE `brand_type` = #{brandType}
        ) AND `subscribe` = 0 AND `source` = #{source}
    </update>

    <!-- 清空中间表的数据 -->
    <delete id="clearTemp">
        DELETE FROM `customer_wechat_fans_temp` WHERE `brand_type` = #{brandType}
    </delete>

    <!-- 查找本次新增关注的用户列表 -->
    <select id="queryNewSubscribeUserOpenId" resultType="java.lang.String">
        SELECT `openid` FROM `customer_wechat_fans_temp` WHERE `brand_type` = #{brandType} AND `openid` NOT IN (
            SELECT `open_id` FROM `customer_wechat_fans` WHERE `subscribe` = 1 AND `source` = #{source}
        )
    </select>

</mapper>
