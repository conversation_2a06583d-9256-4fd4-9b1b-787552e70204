<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingPagePathMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingPagePathPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="page_name" property="pageName" />
        <result column="page_path" property="pagePath" />
        <result column="enable" property="enable" />
        <result column="advert" property="advert" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type, page_name, page_path, enable, advert
    </sql>

</mapper>
