<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.cts.mapper.CustomerInfoCtsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.cts.entity.CustomerInfoCtsPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="info_id" property="infoId"/>
        <result column="cts_site_id" property="ctsSiteId"/>
        <result column="customer_name" property="customerName"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="serve_province_id" property="serveProvinceId"/>
        <result column="serve_city_id" property="serveCityId"/>
        <result column="id_card_type" property="idCardType"/>
        <result column="id_card_no" property="idCardNo"/>
        <result column="birthday" property="birthday"/>
        <result column="national" property="national"/>
        <result column="education" property="education"/>
        <result column="live_province_address_id" property="liveProvinceAddressId"/>
        <result column="live_city_address_id" property="liveCityAddressId"/>
        <result column="live_area_address_id" property="liveAreaAddressId"/>
        <result column="live_address" property="liveAddress"/>
        <result column="births" property="births"/>
        <result column="population" property="population"/>
        <result column="living_area" property="livingArea"/>
        <result column="emergency_contact_name" property="emergencyContactName"/>
        <result column="emergency_contact_relation" property="emergencyContactRelation"/>
        <result column="emergency_contact_mobile" property="emergencyContactMobile"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="channel" property="channel"/>
        <result column="store_id" property="storeId"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        info_id, cts_site_id, customer_name, phone_number, serve_province_id, serve_city_id, id_card_type, id_card_no, birthday, national, education, live_province_address_id, live_city_address_id, live_area_address_id, live_address, births, population, living_area, emergency_contact_name, emergency_contact_relation, emergency_contact_mobile, remark, create_by, create_by_name,channel,store_id,status, update_by, update_by_name
    </sql>



    <select id="pageCtsList" resultType="com.stbella.customer.server.cts.vo.CustomerInfoCtsListVO">
        SELECT
        cic.id,
        cic.customer_name,
        cic.phone_number,
        cis.sales_id,
        cis.sales_name,
        cic.cts_site_id,
        cic.serve_province_id,
        cic.serve_city_id,
        cic.channel,
        cic.store_id,
        cic.status,
        cic.create_by,
        cic.create_by_name,
        cic.gmt_create,
        cic.update_by,
        cic.update_by_name,
        cic.gmt_modified
        FROM
        customer_info_cts AS cic
        LEFT JOIN customer_cts_sales AS cis ON cis.customer_info_cts_id = cic.id and cis.deleted = 0
        WHERE
        cic.deleted = 0

        <if test="query.ctsSiteId != null">
            AND cic.cts_site_id = #{query.ctsSiteId}
        </if>


        <if test="query.customerName != null and query.customerName != '' ">
            AND cic.customer_name = #{query.customerName}
        </if>
        <if test="query.phoneNumber != null and query.phoneNumber != ''">
            AND cic.phone_number = #{query.phoneNumber}
        </if>
        <if test="query.serveProvinceId != null">
            AND cic.serve_province_id = #{query.serveProvinceId}
        </if>
        <if test="query.serveCityId != null">
            AND cic.serve_city_id = #{query.serveCityId}
        </if>
        <if test="query.gmtCreateStart != null and query.gmtCreateEnd != null">
            AND cic.gmt_create >= #{query.gmtCreateStart}
            AND cic.gmt_create &lt;= #{query.gmtCreateEnd}
        </if>
        <if test="query.createByName != null and query.createByName != '' ">
            AND cic.create_by_name = #{query.createByName}
        </if>
        <if test="query.channel != null">
            AND cic.channel = #{query.channel}
        </if>
        <if test="query.status != null">
            AND cic.status = #{query.status}
        </if>

        <choose>
            <when test="query.ctsSiteIds != null and query.ctsSiteIds.size() > 0 and query.ids != null and query.ids.size() > 0 ">
                AND(
                cic.cts_site_id IN
                <foreach collection="query.ctsSiteIds" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
                OR cic.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
                )
            </when>
            <otherwise>
                <if test="query.ctsSiteIds != null and query.ctsSiteIds.size() > 0 ">
                    AND cic.cts_site_id IN
                    <foreach collection="query.ctsSiteIds" item="item" index="index" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="query.ids != null and query.ids.size() > 0 ">
                    AND cic.id IN
                    <foreach collection="query.ids" item="item" index="index" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>

        ORDER BY cic.gmt_create DESC
    </select>


    <select id="pageCtsListForSales" resultType="com.stbella.customer.server.cts.vo.CustomerInfoCtsListVO">
        SELECT
        cic.id,
        cic.customer_name,
        cic.phone_number,
        cis.sales_id,
        cis.sales_name,
        cic.cts_site_id,
        cic.serve_province_id,
        cic.serve_city_id,
        cic.channel,
        cic.store_id,
        cic.status,
        cic.create_by,
        cic.create_by_name,
        cic.gmt_create,
        cic.update_by,
        cic.update_by_name,
        cic.gmt_modified
        FROM
        customer_info_cts AS cic
        LEFT JOIN customer_cts_sales AS cis ON cis.customer_info_cts_id = cic.id and cis.deleted = 0
        WHERE
        cic.deleted = 0

        <if test="query.ctsSiteId != null">
            AND cic.cts_site_id = #{query.ctsSiteId}
        </if>

        <if test="query.customerName != null and query.customerName != '' ">
            AND cic.customer_name = #{query.customerName}
        </if>
        <if test="query.phoneNumber != null and query.phoneNumber != ''">
            AND cic.phone_number = #{query.phoneNumber}
        </if>
        <if test="query.serveProvinceId != null">
            AND cic.serve_province_id = #{query.serveProvinceId}
        </if>
        <if test="query.serveCityId != null">
            AND cic.serve_city_id = #{query.serveCityId}
        </if>
        <if test="query.gmtCreateStart != null and query.gmtCreateEnd != null">
            AND cic.gmt_create >= #{query.gmtCreateStart}
            AND cic.gmt_create &lt;= #{query.gmtCreateEnd}
        </if>
        <if test="query.createByName != null and query.createByName != '' ">
            AND cic.create_by_name = #{query.createByName}
        </if>
        <if test="query.channel != null">
            AND cic.channel = #{query.channel}
        </if>
        <if test="query.status != null">
            AND cic.status = #{query.status}
        </if>

        <if test="query.ctsSiteIds != null and query.ctsSiteIds.size() > 0 ">
            AND cic.cts_site_id IN
            <foreach collection="query.ctsSiteIds" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.ids != null and query.ids.size() > 0 ">
            OR cic.id IN
            <foreach collection="query.ids" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        ORDER BY cic.gmt_create DESC
    </select>

    <select id="getCtsDetail" resultType="com.stbella.customer.server.cts.vo.CustomerInfoCtsDetailVO">
     SELECT
        cic.id,
        cic.customer_name,
        cic.phone_number,
        cis.sales_id,
        cis.sales_name,
        cic.cts_site_id,
        cic.phone_number,
        cic.serve_province_id,
        cic.serve_city_id,
        cic.id_card_type,
        cic.id_card_no,
        cic.birthday,
        cic.national,
        cic.education,
        cic.live_province_address_id,
        cic.live_city_address_id,
        cic.live_area_address_id,
        cic.live_address,
        cic.births,
        cic.population,
        cic.living_area,
        cic.emergency_contact_name,
        cic.emergency_contact_relation,
        cic.emergency_contact_mobile,
        cic.remark,
        cic.channel,
        cic.store_id,
        cic.status
    FROM
        customer_info_cts AS cic
        LEFT JOIN customer_cts_sales AS cis ON cis.customer_info_cts_id = cic.id
        AND cis.deleted = 0
    WHERE
	    cic.deleted = 0
        AND cic.id = #{query}


        ORDER BY cic.gmt_create DESC
    </select>

</mapper>
