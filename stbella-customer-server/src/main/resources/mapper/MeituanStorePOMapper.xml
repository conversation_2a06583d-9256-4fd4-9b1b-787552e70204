<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.MeituanStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.MeituanStorePO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="shop_name" property="shopName" />
        <result column="shop_op_code" property="shopOpCode" />
        <result column="shop_id" property="shopId" />
        <result column="store_id" property="storeId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        shop_name, shop_op_code, shop_id, store_id
    </sql>

</mapper>
