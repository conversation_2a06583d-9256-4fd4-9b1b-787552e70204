<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.activity.mapper.CustomerActivityAppointmentSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.activity.entity.CustomerActivityAppointmentSummaryPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="basic_uid" property="basicUid" />
        <result column="scene_id" property="sceneId" />
        <result column="appointment_id" property="appointmentId" />
        <result column="brand_type" property="brandType" />
        <result column="appoint_customer_name" property="appointCustomerName" />
        <result column="appoint_phone" property="appointPhone" />
        <result column="appoint_project_name" property="appointProjectName" />
        <result column="appoint_project_image" property="appointProjectImage" />
        <result column="activity_address" property="activityAddress" />
        <result column="appoint_date_start" property="appointDateStart" />
        <result column="appoint_date_end" property="appointDateEnd" />
        <result column="status" property="status" />
        <result column="verify_time" property="verifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_uid, scene_id, appointment_id, brand_type, appoint_customer_name, appoint_phone, appoint_project_name, appoint_project_image, activity_address, appoint_date_start, appoint_date_end, status, verify_time
    </sql>

</mapper>
