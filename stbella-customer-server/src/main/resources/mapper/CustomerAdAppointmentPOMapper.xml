<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAdAppointmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAdAppointmentPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="phone_fill_type" property="phoneFillType" />
        <result column="phone" property="phone" />
        <result column="predict_born_date" property="predictBornDate" />
        <result column="prepare_pregnancy" property="preparePregnancy" />
        <result column="mini_openid" property="miniOpenid" />
        <result column="uninterested" property="uninterested" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type, phone_fill_type, phone, predict_born_date, prepare_pregnancy, mini_openid, uninterested
    </sql>

</mapper>
