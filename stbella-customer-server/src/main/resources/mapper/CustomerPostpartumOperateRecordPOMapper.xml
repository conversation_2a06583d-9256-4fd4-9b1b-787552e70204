<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerPostpartumOperateRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerPostpartumOperateRecordPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_id" property="customerId" />
        <result column="postpartum_id" property="postpartumId" />
        <result column="original_content" property="originalContent" />
        <result column="operate_content" property="operateContent" />
        <result column="create_id" property="createId" />
        <result column="create_name" property="createName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_id, postpartum_id, original_content, operate_content, create_id, create_name
    </sql>

</mapper>
