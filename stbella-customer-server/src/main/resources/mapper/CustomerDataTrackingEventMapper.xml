<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingEventMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingEventPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="basic_id" property="basicId"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName" />
        <result column="phone" property="phone" />
        <result column="openid" property="openid"/>
        <result column="sessionid" property="sessionid"/>
        <result column="source" property="source" />
        <result column="event_id" property="eventId"/>
        <result column="event_type" property="eventType"/>
        <result column="event_name" property="eventName"/>
        <result column="event_content" property="eventContent"/>
        <result column="event_query" property="eventQuery"/>
        <result column="page_name" property="pageName"/>
        <result column="page_path" property="pagePath"/>
        <result column="start_time" property="startTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type,
        basic_id, client_id, client_name, phone, openid, sessionid, source, event_id, event_type, event_name, event_content, event_query, page_name, page_path, start_time
    </sql>

    <!-- 页面按钮点击排名 -->
    <select id="tapRankList"
            resultType="com.stbella.customer.server.tracking.vo.TapRankVO">
        SELECT
            path.`page_name`,
            e.`event_name`,
            e.`event_content`,
            e.`event_type`,
            e.`event_id`,
            COUNT(DISTINCT e.`openid`) AS 'user_view',
            COUNT(*) AS 'page_view'
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        LEFT JOIN
            `customer_data_tracking_page_path` AS path ON e.`page_path` = path.`page_path` AND path.`brand_type` = #{brandType}
        WHERE
            e.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            AND e.`deleted` = 0
            AND e.`event_type` = 'tap'
            AND e.`openid` != ''
            AND u.`deleted` = 0
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="brandType != null">
                AND e.`brand_type` = #{brandType}
            </if>
        GROUP BY e.`event_id`
        ORDER BY user_view DESC
    </select>

    <!-- 页面分享排名 -->
    <select id="pageShareRankList"
            resultType="com.stbella.customer.server.tracking.vo.PageShareRankVO">
        SELECT
            path.`page_name`,
            e.`page_path`,
            COUNT(DISTINCT e.`openid`) AS 'share_user'
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        LEFT JOIN
            `customer_data_tracking_page_path` AS path ON e.`page_path` = path.`page_path`
        WHERE
            e.`start_time` BETWEEN #{dateStart} AND #{dateEnd}
            AND e.`deleted` = 0
            AND e.`event_type` = 'share'
            AND e.`openid` != ''
            AND u.`deleted` = 0
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="brandType != null">
                AND e.`brand_type` = #{brandType}
            </if>
        GROUP BY e.`page_path`
        ORDER BY share_user DESC
    </select>

    <!-- 事件统计列表 -->
    <select id="eventStatList"
            resultType="com.stbella.customer.server.tracking.vo.EventStatVO">
        SELECT
            e.`event_id`,
            e.`event_name`,
            e.`event_content`,
            COUNT(DISTINCT e.`openid`) AS 'user_view',
            COUNT(*) AS 'page_view'
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        WHERE
            e.`start_time` BETWEEN #{dateStart} AND #{dateEnd}

            <if test="eventName != null and eventName != ''">
                AND e.`event_name` = #{eventName}
            </if>

            <if test="eventId != null and eventId != ''">
                AND e.`event_id` = #{eventId}
            </if>

            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="brandType != null">
                AND e.`brand_type` = #{brandType}
            </if>

            AND e.`deleted` = 0
            AND e.`openid` != ''
            AND u.`deleted` = 0
        GROUP BY e.`event_id`
        ORDER BY page_view DESC
    </select>

    <!-- 事件趋势 -->
    <select id="eventTrendList"
            resultType="com.stbella.customer.server.tracking.vo.EventTrendVO$DataStatDetailVO">
        SELECT
            DATE_FORMAT(e.`start_time`, '%Y-%m-%d') AS stat_date,
            COUNT(DISTINCT e.`openid`) AS 'user_view',
            COUNT(*) AS 'page_view'
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        WHERE
            e.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
            AND e.`event_id` = #{request.eventId}
            AND e.`deleted` = 0
            AND e.`openid` != ''
            AND u.`deleted` = 0
            <if test="userStatus != null  and userStatus.size() > 0">
                AND u.`status` IN
                <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="request.brandType != null">
                AND e.`brand_type` = #{request.brandType}
            </if>
        GROUP BY stat_date
        ORDER BY stat_date ASC
    </select>

    <!-- 事件详情 -->
    <select id="eventDetailList"
            resultType="com.stbella.customer.server.tracking.vo.EventDetailVO">
        SELECT
            u.`basic_id`,
            u.`client_id`,
            u.`client_name`,
            u.`phone`,
            e.`openid`,
            e.`sessionid`,
            e.`event_id`,
            e.`event_type`,
            e.`event_name`,
            e.`event_content`,
            e.`event_query`,
            path.`page_name`,
            e.`page_path`,
            e.`start_time`
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        LEFT JOIN
            `customer_data_tracking_page_path` AS path ON e.`page_path` = path.`page_path`
        WHERE
           <include refid="eventDetailWhere" />
        order by e.`start_time` desc
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 事件详情总记录数 -->
    <select id="eventDetailCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            `customer_data_tracking_event` AS e
        LEFT JOIN
            `customer_data_user_info` AS u ON e.`openid` = u.`openid`
        WHERE
            <include refid="eventDetailWhere" />
    </select>

    <sql id="eventDetailWhere">
        e.`deleted` = 0
        AND e.`openid` != ''
        AND u.`deleted` = 0
        <if test="request.dateStart != null and request.dateEnd != null">
            AND e.`start_time` BETWEEN #{request.dateStart} AND #{request.dateEnd}
        </if>

        <if test="request.eventId != null and request.eventId != ''">
            AND e.`event_id` = #{request.eventId}
        </if>

        <if test="request.nameOrPhone != null and request.nameOrPhone != ''">
            AND (u.`client_name` like concat('%',#{request.nameOrPhone},'%') or u.`phone` like concat('%',#{request.nameOrPhone},'%') or u.`nick_name` like concat('%',#{request.nameOrPhone},'%'))
        </if>

        <if test="userStatus != null  and userStatus.size() > 0">
            AND u.`status` IN
            <foreach collection="userStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="request.brandType != null">
            AND e.`brand_type` = #{request.brandType}
        </if>
    </sql>

</mapper>
