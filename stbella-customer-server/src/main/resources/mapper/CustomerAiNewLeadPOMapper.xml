<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAiNewLeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAiNewLeadPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="biz_type" property="bizType" />
        <result column="brand_type" property="brandType" />
        <result column="from_type" property="fromType" />
        <result column="customer_id" property="customerId" />
        <result column="phone" property="phone" />
        <result column="predict_born_date" property="predictBornDate" />
        <result column="service_name" property="serviceName" />
        <result column="city_name" property="cityName" />
        <result column="intended_store" property="intendedStore" />
        <result column="package_name" property="packageName" />
        <result column="budget" property="budget" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        biz_type, brand_type, from_type, customer_id, phone, predict_born_date, service_name, city_name, intended_store, package_name, budget, remark
    </sql>

</mapper>
