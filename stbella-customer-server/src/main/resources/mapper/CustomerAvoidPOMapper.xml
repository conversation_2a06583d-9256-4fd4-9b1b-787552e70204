<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerAvoidMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerAvoidPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="avoid_type" property="avoidType" />
        <result column="basic_id" property="basicId" />
        <result column="order_no" property="orderNo" />
        <result column="food_id" property="foodId" />
        <result column="tag_cls" property="tagCls" />
        <result column="content" property="content" />
        <result column="created_by" property="createdBy" />
        <result column="created_by_name" property="createdByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        avoid_type, basic_id, order_no, food_id, tag_cls, content, created_by, created_by_name
    </sql>

</mapper>
