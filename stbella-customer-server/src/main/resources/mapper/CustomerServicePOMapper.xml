<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerServicePO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="store_id" property="storeId" />
        <result column="client_id" property="clientId" />
        <result column="basic_id" property="basicId" />
        <result column="type_sign" property="typeSign" />
        <result column="service_date" property="serviceDate" />
        <result column="belong_date" property="belongDate" />
        <result column="biz_id" property="bizId" />
        <result column="push_record_id" property="pushRecordId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        store_id, client_id, basic_id, type_sign, service_date, belong_date, biz_id, push_record_id
    </sql>

</mapper>
