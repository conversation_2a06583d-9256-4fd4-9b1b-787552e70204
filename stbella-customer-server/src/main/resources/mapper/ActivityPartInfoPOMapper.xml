<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.activity.mapper.ActivityPartInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.activity.entity.ActivityPartInfoPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="create_id" property="createId" />
        <result column="modify_id" property="modifyId" />

        <result column="activity_id" property="activityId" />
        <result column="name" property="name" />
        <result column="city_name" property="cityName" />
        <result column="address_detail" property="addressDetail" />
        <result column="region" property="region" />
        <result column="city" property="city" />
        <result column="province" property="province" />
        <result column="country" property="country" />
        <result column="location_name" property="locationName" />
        <result column="lat" property="lat" />
        <result column="lng" property="lng" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="sign_start_time" property="signStartTime" />
        <result column="sign_end_time" property="signEndTime" />
        <result column="max_signup_num" property="maxSignupNum" />
        <result column="store_id" property="storeId" />
        <result column="organizer_id" property="organizerId" />
        <result column="organizer_name" property="organizerName" />
        <result column="activity_status" property="activityStatus" />
        <result column="poster_image_url" property="posterImageUrl" />
        <result column="secondary_image_url" property="secondaryImageUrl" />
        <result column="moment_image_url" property="momentImageUrl" />
        <result column="condition_json" property="conditionJson" />
        <result column="signup_fields" property="signupFields" />
    </resultMap>

    <sql id="Base_Column_List">
        id, gmt_create, gmt_modified, deleted, create_id, modify_id, activity_id, name, address_detail, region, city, province, country, location_name, lat, lng, start_time, end_time, sign_start_time, sign_end_time, max_signup_num, store_id, organizer_id, organizer_name
    </sql>
    <select id="selectByActivityIdAndPartId"
            resultType="com.stbella.customer.server.activity.entity.ActivityPartInfoPO">
        select
        <include refid="Base_Column_List"/>
        from customer_activity_part_info
        where activity_id = #{activityId}
        and part_id = #{partId}
        and deleted = 0
        limit 1
    </select>
    <select id="selectByActivityId"
            resultType="com.stbella.customer.server.activity.entity.ActivityPartInfoPO">
        select
        <include refid="Base_Column_List"/>
        from customer_activity_part_info
        where activity_id = #{activityId}
        and deleted = 0
    </select>
    <select id="selectByActivityIdAndPartName"
            resultType="com.stbella.customer.server.activity.entity.ActivityPartInfoPO">
        select
        <include refid="Base_Column_List"/>
        from customer_activity_part_info
        where activity_id = #{activityId}
        and name = #{partName}
        and deleted = 0
        limit 1
    </select>

    <select id="getActivityCityList" resultType="com.stbella.customer.server.activity.vo.ActiveCityListVOV2">
        select count(1) as count,b.city,b.city_name as cityName
        from customer_activity_info a
        left join customer_activity_part_info b on b.activity_id = a.id
        where a.brand_type = #{request.brandType} and a.publish_status = 1
        and a.deleted =0 and b.deleted =0
        and b.city is not null
        <if test="request.activityId!=null">
            and b.activity_id = #{request.activityId}
        </if>
        group by b.city
        order by count(1) desc;
    </select>

    <select id="getAllSortPartList" resultType="com.stbella.customer.server.activity.vo.SortPartListVO">
        select id,activity_id,city,lat,lng,name,activity_status as activityStatus,
        `gmt_create` as gmtCreate,
        CASE
        WHEN NOW() > #{request.activityEndTime} THEN 0
        WHEN NOW() &lt; #{request.activityStartTime} THEN
        CASE
        WHEN NOW() &lt; a.sign_start_time THEN 0
        WHEN NOW() >= a.sign_start_time AND NOW() &lt;= a.sign_end_time THEN 1
        WHEN NOW() > a.sign_end_time THEN 0
        END
        WHEN NOW() >= #{request.activityStartTime} AND NOW() &lt;= #{request.activityEndTime} THEN
        CASE
        WHEN NOW() &lt; a.sign_start_time THEN 0
        WHEN NOW() >= a.sign_start_time AND NOW() &lt;= a.sign_end_time THEN 1
        WHEN NOW() > a.sign_end_time THEN 0
        END
        END AS can_appointment_status,
        CASE
        WHEN NOW() &lt; start_time THEN 1
        WHEN NOW() >= start_time AND NOW() &lt;= end_time THEN 0
        WHEN NOW() > end_time THEN 2
        END AS activity_part_status,
        IF(#{request.lat} IS NULL OR #{request.lng} IS NULL, 0,
        6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI()
        / 180 - a.lat * PI() / 180) / 2), 2) +
        COS(#{request.lat} *
        PI() / 180) *
        COS(a.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - a.lng * PI() / 180) / 2), 2))) *1000)
        AS distance,
        city_name,start_time,end_time,sign_start_time,sign_end_time
        from customer_activity_part_info a
        where activity_id = #{request.activityId} and a.deleted =0
        <if test="request.city!=null">
            and a.city = #{request.city}
        </if>
    </select>

    <update id="jobUpdatePartStatus">
        UPDATE customer_activity_part_info SET activity_status = CASE
        WHEN start_time > NOW() THEN 0
        WHEN start_time &lt;= NOW() AND end_time >= NOW() THEN 1
        WHEN end_time &lt; NOW() THEN 2
        ELSE activity_status
        END;
    </update>

    <select id="jobLevelsExpiredTips"
            resultType="com.stbella.customer.server.activity.vo.JobLevelExpiredVO">
        SELECT
        ca.id,
        ca.basic_id,
        ca.biz_type,
        ca.balance,
        ca.growth_level_id,
        ca.growth_date,
        DATE_ADD(ca.growth_date, INTERVAL
        CASE ca.growth_level_id
        WHEN 1 THEN 0 WHEN 2 THEN 365 WHEN 3 THEN 365 WHEN 4 THEN 365
        WHEN 5 THEN 0 WHEN 6 THEN 0 WHEN 7 THEN 365 WHEN 8 THEN 365
        WHEN 9 THEN 365 WHEN 10 THEN 0 WHEN 12 THEN 0 WHEN 13 THEN 365
        WHEN 14 THEN 365 WHEN 15 THEN 365 WHEN 16 THEN 0 ELSE 0
        END DAY) AS end_date,
        CASE growth_level_id WHEN 1 THEN 'FANS'WHEN 2 THEN '悦己'WHEN 3 THEN '金卡'WHEN 4 THEN '铂金'WHEN 5 THEN '蓝钻'WHEN 6 THEN 'FANS'WHEN 7 THEN '皓白'WHEN 8 THEN '梦境'WHEN 9 THEN '星夜'WHEN 10 THEN '铂金'WHEN 12 THEN 'FANS'WHEN 13 THEN '蔚蓝'WHEN 14 THEN '岛屿'WHEN 15 THEN '星海'WHEN 16 THEN '铂金'ELSE ''END AS current,
        IF(DATE_ADD(ca.growth_date, INTERVAL
        CASE ca.growth_level_id
        WHEN 1 THEN 0 WHEN 2 THEN 365 WHEN 3 THEN 365 WHEN 4 THEN 365
        WHEN 5 THEN 0 WHEN 6 THEN 0 WHEN 7 THEN 365 WHEN 8 THEN 365
        WHEN 9 THEN 365 WHEN 10 THEN 0 WHEN 12 THEN 0 WHEN 13 THEN 365
        WHEN 14 THEN 365 WHEN 15 THEN 365 WHEN 16 THEN 0 ELSE 0
        END DAY) &lt;= NOW(), '已过期', '未过期') AS is_expired,
        DATEDIFF(DATE_ADD(ca.growth_date, INTERVAL
        CASE ca.growth_level_id
        WHEN 1 THEN 0 WHEN 2 THEN 365 WHEN 3 THEN 365 WHEN 4 THEN 365
        WHEN 5 THEN 0 WHEN 6 THEN 0 WHEN 7 THEN 365 WHEN 8 THEN 365
        WHEN 9 THEN 365 WHEN 10 THEN 0 WHEN 12 THEN 0 WHEN 13 THEN 365
        WHEN 14 THEN 365 WHEN 15 THEN 365 WHEN 16 THEN 0 ELSE 0
        END DAY), NOW()) +1 AS left_date,
        CASE growth_level_id-1 WHEN 1 THEN 'FANS'WHEN 2 THEN '悦己'WHEN 3 THEN '金卡'WHEN 4 THEN '铂金'WHEN 5 THEN '蓝钻'WHEN 6 THEN 'FANS'WHEN 7 THEN '皓白'WHEN 8 THEN '梦境'WHEN 9 THEN '星夜'WHEN 10 THEN '铂金'WHEN 12 THEN 'FANS'WHEN 13 THEN '蔚蓝'WHEN 14 THEN '岛屿'WHEN 15 THEN '星海'WHEN 16 THEN '铂金'ELSE ''END AS next
        FROM customer_assets ca
        WHERE deleted = 0
        AND growth_level_id NOT IN (1, 5, 6, 10, 12, 16)
        AND DATEDIFF(DATE_ADD(ca.growth_date, INTERVAL
        CASE ca.growth_level_id
        WHEN 1 THEN 0 WHEN 2 THEN 365 WHEN 3 THEN 365 WHEN 4 THEN 365
        WHEN 5 THEN 0 WHEN 6 THEN 0 WHEN 7 THEN 365 WHEN 8 THEN 365
        WHEN 9 THEN 365 WHEN 10 THEN 0 WHEN 12 THEN 0 WHEN 13 THEN 365
        WHEN 14 THEN 365 WHEN 15 THEN 365 WHEN 16 THEN 0 ELSE 0
        END DAY), NOW()) &lt;= #{request.daysIn}
        having is_expired ='未过期'
           and end_date > '2024-10-01 23:59:59'
        order by end_date asc
    </select>

</mapper>
