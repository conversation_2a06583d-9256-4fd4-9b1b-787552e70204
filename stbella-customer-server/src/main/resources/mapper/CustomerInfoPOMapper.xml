<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerInfoPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_name" property="customerName" />
        <result column="nick_name" property="nickName" />
        <result column="label" property="label"/>
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="nation_code" property="nationCode"/>
        <result column="phone_number" property="phoneNumber" />
        <result column="wechat_id" property="wechatId" />
        <result column="channel_source" property="channelSource" />
        <result column="input_source" property="inputSource" />
        <result column="order_num" property="orderNum"/>
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_name, nick_name, label, store_id, store_name, nation_code,phone_number, wechat_id, channel_source, input_source, remark, create_by, create_by_name, update_by, update_by_name, order_num
    </sql>

    <select id="list" resultType="com.stbella.customer.server.customer.vo.CustomerInfoListVO">

        select
            i.id customerId,
            i.gmt_create gmtCreate,
            i.gmt_modified gmtModified,
            (case i.customer_name when null then i.nick_name else i.customer_name end ) customerName,
            i.phone_number phoneNumber,
            i.wechat_id wechatId,
            i.channel_source channelSource,
            i.input_source inputSource,
            i.customer_status customerStatus,
            dtime.allocationDate,
            allocation.storeIds  allocationStoreIds,
            sign.storeIds signStoreIds

        from  customer_info i

              left join
              (select customer_id,max(gmt_create) allocationDate from customer_dynamic_record where
              dynamic_code = 200 group by customer_id) dtime on i.id = dtime.customer_id

              left join
              (select customer_id,GROUP_CONCAT(distinct store_id ) storeIds from customer_dynamic_record
              where dynamic_code = 200 group by customer_id) allocation on i.id = allocation.customer_id

              left join
              (select customer_id,GROUP_CONCAT(distinct store_id ) storeIds from customer_dynamic_record
              where dynamic_code = 400 group by customer_id) sign on i.id = sign.customer_id

        where i.deleted = 0

        <if test="request.allocationStoreId != null  ">
            and find_in_set(#{request.allocationStoreId},allocation.storeIds)
        </if>
        <if test="request.signStoreId != null  ">
            and find_in_set(#{request.signStoreId},sign.storeIds)
        </if>

        <if test="request.customerName != null and request.customerName != '' ">

            and (i.customer_name like concat(#{request.customerName},'%') or i.nick_name like concat(#{request.customerName},'%'))
        </if>
        <if test="request.phoneNumber != null and request.phoneNumber != '' ">
            and i.phone_number = #{request.phoneNumber}
        </if>
        <if test="request.channelSource != null ">
            and i.channel_source = #{request.channelSource}
        </if>
        <if test="request.inputSource != null ">
            and i.input_source = #{request.inputSource}
        </if>
        <if test="request.customerStatus != null ">
            and i.customer_status = #{request.customerStatus}
        </if>
        <if test="request.cDateStartStr!=null  and request.cDateEndStr!=null ">
            AND i.gmt_modified &gt;= #{request.cDateStartStr}
            AND i.gmt_modified &lt;= #{request.cDateEndStr}
        </if>
        <if test="request.cDateStartStr != null  and request.cDateEndStr==null">
            AND i.gmt_modified &gt;= #{request.cDateStartStr}
        </if>
        <if test="request.cDateStartStr == null  and request.cDateEndStr!=null  ">
            AND i.gmt_modified &lt;= #{request.cDateEndStr}
        </if>
        order by i.gmt_modified desc

    </select>

    <select id="customerList" resultType="com.stbella.customer.server.customer.vo.CustomerStoreListVO">

        select i.id customerId,
               csr.store_id storeId,
               i.customer_name customerName,
               i.customer_status customerStatus,
               i.phone_number phoneNumber,
               i.channel_source channelSource,
               i.update_by_name updateByName
        from
        customer_info i left join customer_store_relation csr on i.id = csr.customer_id

        where  i.deleted = 0

        <if test="request.customerStatus != null">
            and i.customer_status = #{request.customerStatus}
        </if>
        <if test="request.storeId != null and request.storeId != ''">
            and csr.store_id = #{request.storeId}
        </if>

        <if test="request.keyword != null and request.keyword != ''  ">
            and (i.customer_name  like concat(#{request.keyword},'%')   or i.phone_number like concat(#{request.keyword},'%') )
        </if>

        order by i.gmt_modified desc

    </select>

    <select id="customerEmpList" resultType="com.stbella.customer.server.customer.vo.CustomerStoreListVO">

        select  i.id customerId,
                csr.store_id storeId,
                i.customer_name customerName,
                i.customer_status customerStatus,
                i.phone_number phoneNumber,
                i.channel_source channelSource,
                i.update_by_name updateByName
        from
        customer_info i join
        (select customer_id,emp_id,store_id from customer_dynamic_record  where emp_id = #{request.empId} group by customer_id,emp_id,store_id) csr on i.id=csr.customer_id

        where  i.deleted = 0

        <if test="request.customerStatus != null">
            and i.customer_status = #{request.customerStatus}
        </if>
        <if test="request.storeId != null and request.storeId != ''">
            and csr.store_id = #{request.storeId}
        </if>

        <if test="request.keyword != null and request.keyword != ''  ">
            and (i.customer_name  like concat(#{request.keyword},'%')   or i.phone_number like concat(#{request.keyword},'%') )
        </if>
        order by i.gmt_modified desc

    </select>

    <select id="visitorList" resultType="com.stbella.customer.server.customer.vo.CustomerVisitorListVO">
        select
            i.id customerId,
            cvr.id visitorId,
            i.customer_name customerName,
            if(cvr.id is null,0,1)  flag,
            i.phone_number phoneNumber,
            cvr.reception_id receptionId,
            cvr.reception_name receptionName,
            cvr.visitor_date visitorDate
        from
            customer_info i left join customer_visitor_record cvr on i.id = cvr.customer_id and cvr.store_id = #{request.storeId} and cvr.deleted = 0
        where i.deleted = 0
        <if test="request.phoneNumber != null and request.phoneNumber != ''  ">
            and i.phone_number  like concat(#{request.phoneNumber},'%')
        </if>
        order by i.gmt_modified desc
    </select>


    <select id="searchList" resultType="com.stbella.customer.server.customer.vo.CustomerSearchVO">

        select
            i.id customerId,
            i.customer_name customerName
        from
            customer_info i
        where   i.deleted = 0

        <if test="request.customerName != null and request.customerName != ''">
            and   i.customer_name  like concat(#{request.customerName},'%')
        </if>
        <if test="request.phoneNumber != null and request.phoneNumber != ''">
            and   i.phone_number  like concat(#{request.phoneNumber},'%')
        </if>
        <if test="request.keyword != null and request.keyword != ''  ">
            and (i.customer_name  like concat(#{request.keyword},'%')   or i.phone_number like concat(#{request.keyword},'%') )
        </if>
        <if test="request.entryLeaveFlag != null and request.entryLeaveFlag ==0 ">
            and   i.customer_status not in (500,600)
        </if>
        <if test="request.signStoreId != null">
            and exists(select 1 from customer_dynamic_record where dynamic_code = 400 and deleted = 0  and customer_id= i.id and store_id = #{request.signStoreId})
        </if>






    </select>


</mapper>
