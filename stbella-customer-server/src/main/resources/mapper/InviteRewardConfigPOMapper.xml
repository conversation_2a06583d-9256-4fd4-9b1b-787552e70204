<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.invite.mapper.InviteRewardConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.invite.entity.InviteRewardConfigPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="biz_type" property="bizType"/>
        <result column="brand_type" property="brandType"/>
        <result column="scene" property="scene"/>
        <result column="reward_type" property="rewardType"/>
        <result column="ticket_id" property="ticketId"/>
        <result column="number_type" property="numberType" />
        <result column="reward_number" property="rewardNumber"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        biz_type, brand_type, scene, reward_type, ticket_id, reward_number
    </sql>
    <select id="getRewardStatistics" resultType="com.stbella.customer.server.invite.vo.MyRewardInfoVO">
        SELECT SUM(CASE WHEN reward_type = 0 THEN reward_number ELSE 0 END) AS totalPoints,
               SUM(CASE WHEN reward_type = 2 THEN reward_number ELSE 0 END) AS totalProductionAmount,
               SUM(CASE WHEN reward_type = 1 THEN reward_number ELSE 0 END) AS totalTickets,
               COUNT(DISTINCT invitee_basic_id)                             AS totalInviters
        FROM invite_reward_record
        WHERE deleted = 0 AND invitee_basic_id = #{basicId}
    </select>

</mapper>
