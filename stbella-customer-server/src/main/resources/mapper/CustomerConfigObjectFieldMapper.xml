<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.pageConfig.CustomerConfigObjectFieldMapper">

     <!-- 定义结果映射 -->
    <resultMap id="CustomerConfigObjectResultMap" type="com.stbella.customer.server.customer.entity.pageConfig.CustomerConfigObjectFieldPO">
    </resultMap>

    <update id="updateField" parameterType="com.stbella.customer.server.customer.entity.pageConfig.CustomerConfigObjectFieldPO">
        update customer_config_object_field
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="code != null and code != ''">
                `code` = #{code},
            </if>
            <if test="desc != null and desc != ''">
                `desc` = #{desc},
            </if>
                pic_width = #{picWidth},
                pic_height = #{picHeight},
                pic_type = #{picType},
                file_type = #{fileType},
                bind_key = #{bindKey}
        </set>
        where id = #{id} and deleted = 0
    </update>

</mapper>
