<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmCustomerPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="staff_phone" property="staffPhone"/>
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="cert_type" property="certType" />
        <result column="id_card" property="idCard" />
        <result column="id_card_front" property="idCardFront" />
        <result column="id_card_back" property="idCardBack" />
        <result column="wechat" property="wechat" />
        <result column="nation" property="nation" />
        <result column="birthdate" property="birthdate" />
        <result column="lunar_birthday" property="lunarBirthday" />
        <result column="constellation_type" property="constellationType" />
        <result column="constellation" property="constellation" />
        <result column="sex" property="sex" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="region" property="region" />
        <result column="address" property="address" />
        <result column="predict_born_date" property="predictBornDate" />
        <result column="customer_stage" property="customerStage"/>
        <result column="customer_status" property="customerStatus"/>
        <result column="pregnancy" property="pregnancy" />
        <result column="born_num" property="bornNum" />
        <result column="fetus_num" property="fetusNum" />
        <result column="gestation_week_now" property="gestationWeekNow" />
        <result column="hospital" property="hospital" />
        <result column="production_mode" property="productionMode" />
        <result column="born_time" property="bornTime" />
        <result column="urgent_name" property="urgentName" />
        <result column="urgent_phone" property="urgentPhone" />
        <result column="relation_with_client" property="relationWithClient" />
        <result column="profession" property="profession" />
        <result column="remark" property="remark" />
        <result column="age" property="age" />
        <result column="blood_type" property="bloodType" />
        <result column="from_type" property="fromType" />
        <result column="tags" property="tags" />
        <result column="scrm_customer_id" property="scrmCustomerId"/>
        <result column="scrm_owner_id" property="scrmOwnerId" />
        <result column="scrm_owner_phone" property="scrmOwnerPhone" />
        <result column="food_prohibition" property="foodProhibition" />
        <result column="other_food_requirement" property="otherFoodRequirement" />
        <result column="room_prohibition" property="roomProhibition" />
        <result column="other_room_prohibition" property="otherRoomProhibition" />
        <result column="gestational_diabetes" property="gestationalDiabetes" />
        <result column="high_blood_pressure" property="highBloodPressure" />
        <result column="Infectious_disease_examination" property="infectiousDiseaseExamination" />
        <result column="history_of_drug_allergy" property="historyOfDrugAllergy" />
        <result column="other_medical_precautions" property="otherMedicalPrecautions" />
        <result column="check_in_date" property="checkInDate" />
        <result column="check_out_date" property="checkOutDate" />
        <result column="in_days" property="inDays" />
        <result column="sign_status" property="signStatus" />
        <result column="sign_num" property="signNum" />
        <result column="month_care_mission_time" property="monthCareMissionTime" />
        <result column="morning_music_healing_time" property="morningMusicHealingTime" />
        <result column="other_sever_notes" property="otherSeverNotes" />
        <result column="smell" property="smell" />
        <result column="most_concerned_about_recovery_sites" property="mostConcernedAboutRecoverySites" />
        <result column="massage_intensity" property="massageIntensity" />
        <result column="desired_time_to_participate" property="desiredTimeToParticipate" />
        <result column="like_type" property="likeType" />
        <result column="entry_reminder" property="entryReminder" />
        <result column="invitation_mode" property="invitationMode" />
        <result column="butler_care_frequency" property="butlerCareFrequency" />
        <result column="do_not_disturb_service" property="doNotDisturbService" />
        <result column="other_service" property="otherService" />
        <result column="room_preparation" property="roomPreparation" />
        <result column="other_preparation" property="otherPreparation" />
        <result column="special_identity" property="specialIdentity" />
        <result column="vip_level" property="vipLevel" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
        <result column="win_sale" property="winSale" />
        <result column="budget" property="budget" />
        <result column="wechat_moment_source_extend" property="wechatMomentSourceExtend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        name, phone, email, cert_type, id_card, id_card_front, id_card_back, wechat, nation, birthdate, lunar_birthday, constellation_type, constellation, sex, province, city, region, address, predict_born_date, customer_stage,customer_status, pregnancy, born_num, fetus_num, gestation_week_now, hospital, production_mode, born_time, urgent_name, urgent_phone, relation_with_client, profession, remark, age, blood_type, from_type, tags, scrm_customer_id, scrm_owner_id, scrm_owner_phone, food_prohibition, other_food_requirement, room_prohibition, other_room_prohibition, gestational_diabetes, high_blood_pressure, Infectious_disease_examination, history_of_drug_allergy, other_medical_precautions, check_in_date, check_out_date, in_days, sign_status, sign_num, month_care_mission_time, morning_music_healing_time, other_sever_notes, smell, most_concerned_about_recovery_sites, massage_intensity, desired_time_to_participate, like_type, entry_reminder, invitation_mode, butler_care_frequency, do_not_disturb_service, other_service, room_preparation, other_preparation, special_identity, vip_level, create_by, create_by_name, update_by, update_by_name, win_sale, budget, wechat_moment_source_extend
    </sql>

</mapper>
