<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.pageConfig.CustomerConfigObjectMapper">

     <!-- 定义结果映射 -->
    <resultMap id="CustomerConfigObjectResultMap" type="com.stbella.customer.server.customer.entity.pageConfig.CustomerConfigObjectPO">
        <id property="id" column="id" />
        <result property="parentId" column="parent_id" />
        <result property="name" column="name" />
        <result property="code" column="code" />
        <result property="level" column="level" />
        <result property="type" column="type" />
        <result property="sort" column="sort" />
        <result property="gmtCreate" column="gmt_create" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="modifyId" column="modify_id" />
        <result property="createId" column="create_id" />
        <result property="creatorName" column="creator_name" />
        <result property="modifierName" column="modifier_name" />
        <result property="deleted" column="deleted" />
    </resultMap>

    <select id="search" resultType="com.stbella.customer.server.customer.request.pageConfig.TreeNodeResult">
        SELECT
            id,
            parent_id,
            name,
            code,
            level,
            type,
            sub_type,
            gmt_create,
        deleted
        FROM customer_config_object
        WHERE deleted =0 and (sub_type is null or sub_type!=32)
        and `show` = 1
        <if test="keyword != null or keyword != ''">
            and name LIKE CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <select id="findChildrenById" resultType="com.stbella.customer.server.customer.request.pageConfig.TreeNodeResult">
        SELECT
            id,
            parent_id,
            name,
            code,
            level,
            type,
            sub_type,
        gmt_create,
            deleted
        FROM customer_config_object
        WHERE deleted =0 and (sub_type is null or sub_type!=32)
          and `show` = 1
        and parent_id = #{parentId}
    </select>


</mapper>
