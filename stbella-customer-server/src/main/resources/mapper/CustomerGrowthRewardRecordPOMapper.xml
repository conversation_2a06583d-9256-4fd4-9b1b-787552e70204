<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerGrowthRewardRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.vip.CustomerGrowthRewardRecordPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="brand_type" property="brandType" />
        <result column="reward_id" property="rewardId" />
        <result column="reward_type" property="rewardType" />
        <result column="reward_number" property="rewardNumber" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="order_sn" property="orderSn" />
        <result column="coupon_id" property="couponId" />
        <result column="recovery" property="recovery" />
        <result column="verify_store_id" property="verifyStoreId" />
        <result column="verify_staff_id" property="verifyStaffId" />
        <result column="verify_staff_name" property="verifyStaffName" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_remark" property="verifyRemark" />
        <result column="extend" property="extend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_id, brand_type, reward_id, reward_type, reward_number, status, start_time, end_time, order_sn, coupon_id, recovery, verify_store_id, verify_staff_id, verify_staff_name, verify_time, verify_remark, extend
    </sql>

</mapper>
