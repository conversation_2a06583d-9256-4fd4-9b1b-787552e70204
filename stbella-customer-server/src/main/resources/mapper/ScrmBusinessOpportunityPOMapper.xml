<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmBusinessOpportunityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmBusinessOpportunityPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="scrm_id" property="scrmId" />
        <result column="entity_type" property="entityType" />
        <result column="owner_id" property="ownerId" />
        <result column="opportunity_name" property="opportunityName" />
        <result column="customer_id" property="customerId" />
        <result column="price_id" property="priceId" />
        <result column="opportunity_type" property="opportunityType" />
        <result column="money" property="money" />
        <result column="sale_stage_id" property="saleStageId" />
        <result column="lost_stage_id" property="lostStageId" />
        <result column="win_rate" property="winRate" />
        <result column="reason" property="reason" />
        <result column="reason_desc" property="reasonDesc" />
        <result column="close_date" property="closeDate" />
        <result column="commitment_flg" property="commitmentFlg" />
        <result column="source_id" property="sourceId" />
        <result column="project_budget" property="projectBudget" />
        <result column="actual_cost" property="actualCost" />
        <result column="product" property="product" />
        <result column="stage_updated_at" property="stageUpdatedAt" />
        <result column="recent_activity_record_time" property="recentActivityRecordTime" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="comment" property="comment" />
        <result column="dim_depart" property="dimDepart" />
        <result column="territory_id" property="territoryId" />
        <result column="lock_status" property="lockStatus" />
        <result column="campaign_id" property="campaignId" />
        <result column="approval_status" property="approvalStatus" />
        <result column="status" property="status" />
        <result column="lead_id" property="leadId" />
        <result column="opportunity_score" property="opportunityScore" />
        <result column="territory_high_sea_status" property="territoryHighSeaStatus" />
        <result column="territory_expire_time" property="territoryExpireTime" />
        <result column="territory_high_sea_id" property="territoryHighSeaId" />
        <result column="fcast_money" property="fcastMoney" />
        <result column="forecast_category" property="forecastCategory" />
        <result column="duplicate_flg" property="duplicateFlg" />
        <result column="win_reason" property="winReason" />
        <result column="win_reason_desc" property="winReasonDesc" />
        <result column="applicant_id" property="applicantId" />
        <result column="reason_return" property="reasonReturn" />
        <result column="reason_comment" property="reasonComment" />
        <result column="delay_reason" property="delayReason" />
        <result column="delay_comment" property="delayComment" />
        <result column="allocate_time" property="allocateTime" />
        <result column="sign_in_information" property="signInInformation" />
        <result column="consultation_time" property="consultationTime" />
        <result column="arrival_time" property="arrivalTime" />
        <result column="month_order" property="monthOrder" />
        <result column="non_month_order" property="nonMonthOrder" />
        <result column="owned_store" property="ownedStore" />
        <result column="contracted_store" property="contractedStore" />

        <result column="intended_province" property="intendedProvince" />
        <result column="intended_city" property="intendedCity" />
        <result column="intended_area" property="intendedArea" />
        <result column="intended_store" property="intendedStore" />
        <result column="intended_brand" property="intendedBrand" />
        <result column="customer_budget" property="customerBudget" />
        <result column="intended_country" property="intendedCountry" />

        <result column="old_tag" property="oldTag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        scrm_id, entity_type, owner_id, opportunity_name, customer_id, price_id, opportunity_type, money, sale_stage_id, lost_stage_id, win_rate, reason, reason_desc, close_date, commitment_flg, source_id, project_budget, actual_cost, product, stage_updated_at, recent_activity_record_time, created_at, created_by, updated_at, updated_by, comment, dim_depart, territory_id, lock_status, campaign_id, approval_status, status, lead_id, opportunity_score, territory_high_sea_status, territory_expire_time, territory_high_sea_id, fcast_money, forecast_category, duplicate_flg, win_reason, win_reason_desc, applicant_id, reason_return, reason_comment, delay_reason, delay_comment, allocate_time, sign_in_information, consultation_time, arrival_time, month_order, non_month_order,owned_store,contracted_store,
        intended_province, intended_city, intended_area, intended_store, intended_brand, customer_budget, intended_country, old_tag
    </sql>

    <!-- 获取指定时间段内每个门店的打卡信息 -->
    <select id="queryCheckinUsersByDate"
            resultType="com.stbella.customer.server.scrm.dto.ScrmCheckinUserInfoDTO">
        SELECT
            store.`store_id`,
            record.`real_finish_time` AS checkin_time,
            staff.`name` AS staff_name
        FROM `scrm_opportunity_status_record` AS record
        INNER JOIN `scrm_business_opportunity` AS opportunity ON record.`opportunity_scrm_id` = opportunity.`scrm_id`
        INNER JOIN `scrm_user_store_config` AS store ON opportunity.`contracted_store` = store.`scrm_record_id`
        INNER JOIN `scrm_user` AS staff ON opportunity.`owner_id` = staff.`scrm_id` AND staff.`deleted` = 0
        WHERE
            record.`sale_stage_id` = 2
          AND record.`deleted` = 0
          AND ( opportunity.`reason` NOT IN ( 1, 6 ) OR opportunity.`reason` IS NULL )
          AND opportunity.`contracted_store` IS NOT NULL
          AND opportunity.`owned_store` IS NULL
          AND opportunity.entity_type = 11010000300001
          AND record.`real_finish_time` BETWEEN #{dateStart} AND #{dateEnd}
    </select>

    <!-- 获取指定时间段内每个门店的分配信息 -->
    <select id="queryDistributeUsersByDate"
            resultType="com.stbella.customer.server.scrm.dto.ScrmCheckinUserInfoDTO">
        SELECT
            store.`store_id`,
            record.`real_finish_time` AS checkin_time,
            staff.`name` AS staff_name
        FROM `scrm_opportunity_status_record` AS record
        INNER JOIN `scrm_business_opportunity` AS opportunity ON record.`opportunity_scrm_id` = opportunity.`scrm_id`
        INNER JOIN `scrm_user_store_config` AS store ON opportunity.`contracted_store` = store.`scrm_record_id`
        INNER JOIN `scrm_user` AS staff ON opportunity.`owner_id` = staff.`scrm_id` AND staff.`deleted` = 0
        WHERE
            record.`sale_stage_id` = 1
            AND record.`deleted` = 0
            AND ( opportunity.`reason` NOT IN ( 1, 6 ) OR opportunity.`reason` IS NULL )
            AND ( opportunity.`old_tag` NOT IN (1, 2) OR opportunity.`old_tag` IS NULL )
            AND opportunity.`contracted_store` IS NOT NULL
            AND opportunity.`owned_store` IS NULL
            AND opportunity.entity_type = 11010000300001
            AND record.`real_finish_time` BETWEEN #{dateStart} AND #{dateEnd}
            AND store.store_id NOT IN (1001,1020,1011,1041,1042)
    </select>

    <!-- 获取指定时间段内每个门店的首签信息 -->
    <select id="queryFirstSignUsersByDate"
            resultType="com.stbella.customer.server.scrm.dto.ScrmCheckinUserInfoDTO">
        SELECT
            t.store_id,
            t.sign_time AS checkin_time,
            t.staff_name
        FROM (
                 SELECT
                     store.`store_id`,
                     record.`real_finish_time` AS sign_time,
                     staff.`name` AS staff_name,
                     IF(checkin.`real_finish_time` IS NULL, 99999999, UNIX_TIMESTAMP(record.`real_finish_time`) - UNIX_TIMESTAMP(checkin.`real_finish_time`)) AS checkin_time
                 FROM `scrm_opportunity_status_record` AS record
                          INNER JOIN `scrm_business_opportunity` AS opportunity ON record.`opportunity_scrm_id` = opportunity.`scrm_id`
                          INNER JOIN `scrm_user_store_config` AS store ON opportunity.`contracted_store` = store.`scrm_record_id`
                          INNER JOIN `scrm_user` AS staff ON opportunity.`owner_id` = staff.`scrm_id` AND staff.`deleted` = 0
                          INNER JOIN `scrm_customer_order` AS o ON opportunity.`month_order` = o.`scrm_id`
                          INNER JOIN `stbella-customer`.`scrm_opportunity_status_record` AS checkin ON record.opportunity_scrm_id = checkin.opportunity_scrm_id AND checkin.sale_stage_id = 2 AND checkin.deleted = 0
                 WHERE
                     record.`sale_stage_id` = 3
                   AND record.`deleted` = 0
                   AND ( opportunity.`reason` NOT IN ( 1, 6 ) OR opportunity.`reason` IS NULL )
                   AND opportunity.`contracted_store` IS NOT NULL
                   AND opportunity.entity_type = 11010000300001
                   AND o.performance_status != 2
                   AND record.`real_finish_time` BETWEEN #{dateStart} AND #{dateEnd}
             ) AS t
        WHERE
            (t.checkin_time BETWEEN 0 AND 86400)
    </select>

</mapper>
