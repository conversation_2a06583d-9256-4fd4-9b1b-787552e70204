<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingApiPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="basic_id" property="basicId"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName" />
        <result column="phone" property="phone" />
        <result column="openid" property="openid"/>
        <result column="sessionid" property="sessionid"/>
        <result column="source" property="source"/>
        <result column="param" property="param"/>
        <result column="url" property="url"/>
        <result column="method" property="method"/>
        <result column="page_name" property="pageName"/>
        <result column="page_path" property="pagePath"/>
        <result column="start_time" property="startTime"/>
        <result column="duration_time" property="durationTime" />
        <result column="status" property="status"/>
        <result column="code" property="code"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type,
        basic_id, client_id, client_name, phone, openid, sessionid, source, param, url, method, page_name, page_path, start_time, duration_time,status,code
    </sql>


    <select id="queryApiList" resultType="com.stbella.customer.server.tracking.vo.ApiListVO">
        select
        page_name,
        url,
        count(url) as requestNum,
        (SUM(duration_time) / COUNT(url)) AS avgDurationTime

        from
        customer_data_tracking_api
        where
        deleted = 0
        and gmt_create BETWEEN #{req.dateStart} AND #{req.dateEnd}
        and brand_type = #{req.brandType}
        <if test="req.url !=null and req.url !='' ">
            and url = #{req.url}
        </if>
        group by url
        order by requestNum desc
        <if test="isPage == 1">
            limit #{req.pageNum},#{req.pageSize}
        </if>
    </select>

</mapper>
