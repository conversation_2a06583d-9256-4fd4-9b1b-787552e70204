<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.tracking.mapper.CustomerDataTrackingSourceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.tracking.entity.CustomerDataTrackingSourceConfigPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="source_key" property="sourceKey" />
        <result column="source_name" property="sourceName" />
        <result column="source_content" property="sourceContent" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        source_key, source_name, source_content, sort
    </sql>

</mapper>
