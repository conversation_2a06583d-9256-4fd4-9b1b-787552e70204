<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerVisitorRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerVisitorRecordPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_id" property="customerId" />
        <result column="store_id" property="storeId" />
        <result column="reception_id" property="receptionId" />
        <result column="reception_name" property="receptionName" />
        <result column="visitor_date" property="visitorDate" />
        <result column="customer_name" property="customerName" />
        <result column="anticipate_date" property="anticipateDate" />
        <result column="nation_code" property="nationCode"/>
        <result column="phone_number" property="phoneNumber" />
        <result column="age" property="age" />
        <result column="parity" property="parity" />
        <result column="channel_source" property="channelSource" />
        <result column="referral_code_flag" property="referralCodeFlag" />
        <result column="referral_code" property="referralCode" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="district_id" property="districtId" />
        <result column="province_name" property="provinceName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="address" property="address" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_id, store_id, reception_id, reception_name, visitor_date, customer_name,anticipate_date, nation_code,phone_number, age, parity, channel_source, referral_code_flag, referral_code, province_id, city_id, district_id, province_name, city_name, district_name, address, create_by, create_by_name
    </sql>

</mapper>
