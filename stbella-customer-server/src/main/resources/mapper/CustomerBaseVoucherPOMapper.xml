<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerBaseVoucherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerBaseVoucherPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_uid" property="basicUid" />
        <result column="biz_type" property="bizType" />
        <result column="coupon_type" property="couponType" />
        <result column="coupon_id" property="couponId" />
        <result column="title" property="title" />
        <result column="coupon_image" property="couponImage" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="verify_staff_name" property="verifyStaffName" />
        <result column="verify_time" property="verifyTime" />
        <result column="extend" property="extend" />
    </resultMap>

    <resultMap id="CouponFullDTOResultMap" type="com.stbella.customer.server.coupon.dto.CouponDTO">
        <!-- Mapping fields from customer_base_voucher table -->
        <id property="id" column="cbv_id"/>
        <result property="basicUid" column="cbv_basic_uid"/>
        <result property="bizType" column="cbv_biz_type"/>
        <result property="couponType" column="cbv_coupon_type"/>
        <result property="couponId" column="cbv_coupon_id"/>
        <result property="title" column="cbv_title"/>
        <result property="couponImage" column="cbv_coupon_image"/>
        <result property="status" column="cbv_status"/>
        <result property="startTime" column="cbv_start_time"/>
        <result property="endTime" column="cbv_end_time"/>
        <result property="verifyStaffName" column="cbv_verify_staff_name"/>
        <result property="verifyTime" column="cbv_verify_time"/>
        <result property="extend" column="cbv_extend"/>
        <result property="gmtCreate" column="cbv_gmt_create"/>
        <result property="gmtModified" column="cbv_gmt_modified"/>
        <result property="deleted" column="cbv_deleted"/>

        <!-- Association to InviteRewardRecordDTO -->
        <association property="inviteRewardRecord" javaType="com.stbella.customer.server.invite.dto.InviteRewardRecordDTO">
            <id property="id" column="irr_id"/>
            <result property="basicId" column="irr_basic_id"/>
            <result property="rewardId" column="irr_reward_id"/>
            <result property="rewardType" column="irr_reward_type"/>
            <result property="rewardNumber" column="irr_reward_number"/>
            <result property="status" column="irr_status"/>
            <result property="startTime" column="irr_start_time"/>
            <result property="endTime" column="irr_end_time"/>
            <result property="inviteeBasicId" column="irr_invitee_basic_id"/>
            <result property="stageTime" column="irr_stage_time"/>
            <result property="verifyStoreId" column="irr_verify_store_id"/>
            <result property="verifyStaffId" column="irr_verify_staff_id"/>
            <result property="verifyStaffName" column="irr_verify_staff_name"/>
            <result property="verifyTime" column="irr_verify_time"/>
            <result property="verifyRemark" column="irr_verify_remark"/>
            <result property="extend" column="irr_extend"/>
            <result property="gmtCreate" column="irr_gmt_create"/>
            <result property="gmtModified" column="irr_gmt_modified"/>
            <result property="deleted" column="irr_deleted"/>
            <!-- Nested association to InviteRewardConfigDTO -->
            <association property="rewardConfig" javaType="com.stbella.customer.server.invite.dto.InviteRewardConfigDTO">
                <id property="id" column="irc_id"/>
                <result property="bizType" column="irc_biz_type"/>
                <result property="brandType" column="irc_brand_type"/>
                <result property="scene" column="irc_scene"/>
                <result property="rewardType" column="irc_reward_type"/>
                <result property="ticketId" column="irc_ticket_id"/>
                <result property="rewardNumber" column="irc_reward_number"/>
                <result property="active" column="irc_active"/>
                <result property="gmtCreate" column="irc_gmt_create"/>
                <result property="gmtModified" column="irc_gmt_modified"/>
                <result property="deleted" column="irc_deleted"/>

                <!-- Nested association to InviteTicketConfigDTO -->
                <association property="ticketConfig" javaType="com.stbella.customer.server.invite.dto.InviteTicketConfigDTO">
                    <id property="id" column="itc_id"/>
                    <result property="rewardTitle" column="itc_reward_title"/>
                    <result property="rewardImage" column="itc_reward_image"/>
                    <result property="validityType" column="itc_validity_type"/>
                    <result property="validityNumber" column="itc_validity_number"/>
                    <result property="activeType" column="itc_active_type"/>
                    <result property="rewardType" column="itc_reward_type"/>
                    <result property="ruleContent" column="itc_rule_content"/>
                    <result property="gmtCreate" column="itc_gmt_create"/>
                    <result property="gmtModified" column="itc_gmt_modified"/>
                    <result property="deleted" column="itc_deleted"/>
                </association>
            </association>
        </association>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_uid, biz_type, coupon_type, coupon_id, title, coupon_image, status, start_time, end_time, verify_staff_name, verify_time, extend
    </sql>


    <select id="selectCouponsByFilters" resultMap="CouponFullDTOResultMap">
        SELECT
            cbv.id AS cbv_id,
            cbv.basic_uid AS cbv_basic_uid,
            cbv.biz_type AS cbv_biz_type,
            cbv.coupon_type AS cbv_coupon_type,
            cbv.coupon_id AS cbv_coupon_id,
            cbv.title AS cbv_title,
            cbv.coupon_image AS cbv_coupon_image,
            cbv.status AS cbv_status,
            cbv.start_time AS cbv_start_time,
            cbv.end_time AS cbv_end_time,
            cbv.verify_staff_name AS cbv_verify_staff_name,
            cbv.verify_time AS cbv_verify_time,
            cbv.extend AS cbv_extend,
            cbv.gmt_create AS cbv_gmt_create,
            cbv.gmt_modified AS cbv_gmt_modified,
            cbv.deleted AS cbv_deleted,

            -- Fields from invite_reward_record
            irr.id AS irr_id,
            irr.basic_id AS irr_basic_id,
            irr.reward_id AS irr_reward_id,
            irr.reward_type AS irr_reward_type,
            irr.reward_number AS irr_reward_number,
            irr.status AS irr_status,
            irr.start_time AS irr_start_time,
            irr.end_time AS irr_end_time,
            irr.invitee_basic_id AS irr_invitee_basic_id,
            irr.stage_time AS irr_stage_time,
            irr.verify_store_id AS irr_verify_store_id,
            irr.verify_staff_id AS irr_verify_staff_id,
            irr.verify_staff_name AS irr_verify_staff_name,
            irr.verify_time AS irr_verify_time,
            irr.verify_remark AS irr_verify_remark,
            irr.extend AS irr_extend,
            irr.gmt_create AS irr_gmt_create,
            irr.gmt_modified AS irr_gmt_modified,
            irr.deleted AS irr_deleted,

            -- Fields from invite_reward_config
            irc.id AS irc_id,
            irc.biz_type AS irc_biz_type,
            irc.brand_type AS irc_brand_type,
            irc.scene AS irc_scene,
            irc.reward_type AS irc_reward_type,
            irc.ticket_id AS irc_ticket_id,
            irc.reward_number AS irc_reward_number,
            irc.active AS irc_active,
            irc.gmt_create AS irc_gmt_create,
            irc.gmt_modified AS irc_gmt_modified,
            irc.deleted AS irc_deleted,

            -- Fields from invite_ticket_config
            itc.id AS itc_id,
            itc.reward_title AS itc_reward_title,
            itc.reward_image AS itc_reward_image,
            itc.validity_type AS itc_validity_type,
            itc.validity_number AS itc_validity_number,
            itc.active_type AS itc_active_type,
            itc.reward_type AS itc_reward_type,
            itc.rule_content AS itc_rule_content,
            itc.gmt_create AS itc_gmt_create,
            itc.gmt_modified AS itc_gmt_modified,
            itc.deleted AS itc_deleted
        FROM
            customer_base_voucher cbv
                LEFT JOIN
            invite_reward_record irr ON cbv.coupon_id = irr.id
                LEFT JOIN
            invite_reward_config irc ON irr.reward_id = irc.id
                LEFT JOIN
            invite_ticket_config itc ON irc.ticket_id = itc.id

        WHERE
            cbv.deleted = 0
        <if test="request.status != null">
            AND cbv.status = #{request.status}
        </if>
        <if test="request.statusList != null and request.statusList.size > 0">
            AND cbv.status IN
            <foreach item="status" index="index" collection="request.statusList"
                     open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
          AND cbv.basic_uid = #{request.basicId}
        order by cbv.gmt_create desc
    </select>

    <select id="getCouponCount" resultType="java.lang.Integer">
        SELECT
        SUM(CASE
        WHEN irr.reward_number is null
        THEN 1
        ELSE irr.reward_number
        END) AS total
        FROM
        customer_base_voucher cbv
        LEFT JOIN
        invite_reward_record irr ON cbv.coupon_id = irr.id
        LEFT JOIN
        invite_reward_config irc ON irr.reward_id = irc.id
        LEFT JOIN
        invite_ticket_config itc ON irc.ticket_id = itc.id
        WHERE cbv.deleted = 0
        AND cbv.basic_uid = #{basicId}
        <if test="statusList != null and statusList.size > 0">
            AND cbv.status IN
            <foreach item="status" index="index" collection="statusList"
                     open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>


    <update id="jobUpdateStatus">
        UPDATE customer_base_voucher
        SET status =
                CASE
                    WHEN start_time > NOW() THEN 0
                    WHEN start_time &lt;= NOW() AND end_time >= NOW() THEN 1
                    WHEN end_time &lt; NOW() THEN 3
                    ELSE status
                    END
        where biz_type in (100003,100004) and deleted=0
    </update>


</mapper>
