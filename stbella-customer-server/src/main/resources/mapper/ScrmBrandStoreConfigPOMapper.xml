<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmBrandStoreConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmBrandStoreConfigPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="scrm_id" property="scrmId" />
        <result column="name" property="name" />
        <result column="scrm_store_id" property="scrmStoreId" />
        <result column="store_id" property="storeId" />
        <result column="scrm_brand_id" property="scrmBrandId" />
        <result column="scrm_area_id" property="scrmAreaId" />
        <result column="store_status" property="storeStatus" />
        <result column="scrm_staff_id" property="scrmStaffId" />
        <result column="open_date" property="openDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        scrm_id, name, scrm_store_id, store_id, scrm_brand_id, scrm_area_id, store_status, scrm_staff_id, open_date
    </sql>

</mapper>
