<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerDeliveryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerDeliveryPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="customer_info_id" property="customerInfoId" />
        <result column="parity" property="parity" />
        <result column="hospital" property="hospital" />
        <result column="gestation_week" property="gestationWeek"/>
        <result column="gestation_day" property="gestationDay"/>
        <result column="anticipate_date" property="anticipateDate" />
        <result column="hospital_address" property="hospitalAddress" />
        <result column="nation" property="nation" />
        <result column="arrival_day" property="arrivalDay" />
        <result column="entry_date" property="entryDate" />
        <result column="room_type_id" property="roomTypeId" />
        <result column="room_type_name" property="roomTypeName" />
        <result column="need_car" property="needCar" />
        <result column="production_date" property="productionDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_info_id, parity, hospital,gestation_week,gestation_day, anticipate_date, hospital_address, nation, arrival_day, entry_date, room_type_id, room_type_name, need_car,production_date
    </sql>

</mapper>
