<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.activity.mapper.ActivityInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.activity.entity.ActivityInfoPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="create_id" property="createId" />
        <result column="modify_id" property="modifyId" />
        <result column="activity_name" property="activityName" />
        <result column="link_url" property="linkUrl" />
        <result column="link_type" property="linkType" />
        <result column="link_app_id" property="linkAppId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="activity_status" property="activityStatus" />
        <result column="publish_status" property="publishStatus" />
        <result column="cancel_before_minutes" property="cancelBeforeMinutes" />
        <result column="share_text" property="shareText" />
        <result column="cover_image_url" property="coverImageUrl" />
        <result column="ticket_image_url" property="ticketImageUrl" />
        <result column="poster_image_url" property="posterImageUrl" />
        <result column="category" property="category" />
        <result column="brand_type" property="brandType" />
    </resultMap>
    <sql id="Base_Column_List">
        id, gmt_create, gmt_modified, deleted, create_id, modify_id, activity_name, link_url, link_type, link_app_id, start_time, end_time, activity_status, publish_status, cancel_before_minutes, share_text, cover_image_url, ticket_image_url, poster_image_url, category, brand_type
    </sql>
    <select id="getActivityMiniList"
            resultType="com.stbella.customer.server.activity.vo.mini.ActivityListMiniVO">
        SELECT
        a.id AS activityId,
        a.biz_id as bizId,
        b.`city`,
        GROUP_CONCAT(distinct
        CASE
        WHEN b.city_name IS NOT NULL AND b.city_name !='' THEN b.city_name
        END
        ) AS cityNames,
        a.gmt_create as gmtCreate,
        a.activity_source as activitySource,
        a.activity_name AS activityName,
        a.cover_image_url AS coverImageUrl,
        a.brand_type AS brandType,
        a.brand_logo AS brandLogo,
        a.category AS category,
        a.activity_status AS activityStatus,
        a.publish_status AS publishStatus,
        a.link_type AS linkType,
        a.link_url AS linkUrl,
        a.link_app_id AS linkAppId,
        DATE_FORMAT(a.start_time, '%Y-%m-%d %H:%i') AS startTime,
        DATE_FORMAT(a.end_time, '%Y-%m-%d %H:%i') AS endTime,
        DATE_FORMAT(b.start_time, '%Y-%m-%d %H:%i') AS partStartTime,
        DATE_FORMAT(b.end_time, '%Y-%m-%d %H:%i') AS partEndTime,
        b.sign_start_time AS signStartTime,
        b.sign_end_time AS signEndTime,
        GROUP_CONCAT(distinct
        CASE
        WHEN b.store_id IS NOT NULL AND b.store_id !='' THEN b.store_id
        END
        ) AS storeId,
        max(
        CASE
        WHEN NOW() > a.end_time THEN NULL
        WHEN NOW() &lt; a.start_time THEN
        CASE
        WHEN NOW() &lt; b.sign_start_time THEN NULL
        WHEN NOW() >= b.sign_start_time AND NOW() &lt;= b.sign_end_time THEN 1
        WHEN NOW() > b.sign_end_time THEN NULL
        END
        WHEN NOW() >= a.start_time AND NOW() &lt;= a.end_time THEN
        CASE
        WHEN NOW() &lt; b.sign_start_time THEN NULL
        WHEN NOW() >= b.sign_start_time AND NOW() &lt;= b.sign_end_time THEN 1
        WHEN NOW() > b.sign_end_time THEN NULL
        END
        END
        ) AS canAppointmentStatus
        <if test="request.city !=null and request.city==0">
            <if test="request.lat!=null and request.lng!=null">
                ,6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI() / 180 - b.lat * PI() / 180) / 2), 2) +
                COS(#{request.lat} *
                PI() / 180) *
                COS(b.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - b.lng * PI() / 180) / 2), 2))) *1000 AS
                distance
            </if>
        </if>
        from customer_activity_info a
        left join customer_activity_part_info b on a.id = b.activity_id AND b.deleted = 0
        where a.deleted =0 and a.publish_status = 1
        <if test ="request.brandType!=null">
            and a.brand_type = #{request.brandType}
        </if>
        <if test ="request.activityStatus!=null">
            and b.activity_status =#{request.activityStatus}
        </if>
        <if test="request.city !=null">
            <if test="request.city>0">
                and b.city = #{request.city}
            </if>
        </if>
        <if test ="request.category!=null and request.category!= 0">
            and a.category = #{request.category}
        </if>
        <if test="request.city !=null">
            <if test="request.city==0 and request.lat!=null and request.lng!=null">
                group by b.id
                having distance &lt;= 100000
                order by a.gmt_create desc
            </if>
            <if test="request.city!=0">
                group by a.id
                order by a.gmt_create desc,a.biz_id desc
            </if>
        </if>
    </select>

    <sql id="sql_getActivityNearMiniList">
        select * from(
        SELECT
        a.id AS activityId,
        b.`city`,
        GROUP_CONCAT(distinct
        CASE
        WHEN b.city_name IS NOT NULL AND b.city_name !='' THEN b.city_name
        END
        ) AS cityNames,
        a.gmt_create as gmtCreate,
        a.biz_id as bizId,
        a.activity_source as activitySource,
        a.activity_name AS activityName,
        a.cover_image_url AS coverImageUrl,
        a.brand_type AS brandType,
        a.brand_logo AS brandLogo,
        a.category AS category,
        a.activity_status AS activityStatus,
        a.publish_status AS publishStatus,
        a.link_type AS linkType,
        a.link_url AS linkUrl,
        a.link_app_id AS linkAppId,
        DATE_FORMAT(a.start_time, '%Y-%m-%d %H:%i') AS startTime,
        DATE_FORMAT(a.end_time, '%Y-%m-%d %H:%i') AS endTime,
        DATE_FORMAT(b.start_time, '%Y-%m-%d %H:%i') AS partStartTime,
        DATE_FORMAT(b.end_time, '%Y-%m-%d %H:%i') AS partEndTime,
        b.sign_start_time AS signStartTime,
        b.sign_end_time AS signEndTime,
        GROUP_CONCAT(distinct
        CASE
        WHEN b.store_id IS NOT NULL AND b.store_id !='' THEN b.store_id
        END
        ) AS storeId,
        max(
        CASE
        WHEN NOW() > a.end_time THEN NULL
        WHEN NOW() &lt; a.start_time THEN
        CASE
        WHEN NOW() &lt; b.sign_start_time THEN NULL
        WHEN NOW() >= b.sign_start_time AND NOW() &lt;= b.sign_end_time THEN 1
        WHEN NOW() > b.sign_end_time THEN NULL
        END
        WHEN NOW() >= a.start_time AND NOW() &lt;= a.end_time THEN
        CASE
        WHEN NOW() &lt; b.sign_start_time THEN NULL
        WHEN NOW() >= b.sign_start_time AND NOW() &lt;= b.sign_end_time THEN 1
        WHEN NOW() > b.sign_end_time THEN NULL
        END
        END
        ) AS canAppointmentStatus
        <if test="request.city !=null and request.city==0">
            <if test="request.lat!=null and request.lng!=null">
                ,6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI() / 180 - b.lat * PI() / 180) / 2), 2) +
                COS(#{request.lat} *
                PI() / 180) *
                COS(b.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - b.lng * PI() / 180) / 2), 2))) *1000 AS
                distance
            </if>
        </if>
        from customer_activity_info a
        left join customer_activity_part_info b on a.id = b.activity_id AND b.deleted = 0
        where a.deleted =0 and a.publish_status = 1
        <if test="request.brandType!=null">
            and a.brand_type = #{request.brandType}
        </if>
        <if test="request.activityStatus!=null">
            and b.activity_status =#{request.activityStatus}
        </if>
        <if test="request.city !=null">
            <if test="request.city>0">
                and b.city = #{request.city}
            </if>
        </if>
        <if test="request.category!=null and request.category!= 0">
            and a.category = #{request.category}
        </if>
        GROUP BY b.id
        HAVING distance &lt;= 100000
        ORDER BY a.gmt_create DESC
        )m
    </sql>
    <select id="getActivityNearMiniList"
            resultType="com.stbella.customer.server.activity.vo.mini.ActivityListMiniVO">
        <include refid="sql_getActivityNearMiniList"/>
        GROUP BY m.activityId
        HAVING m.distance &lt;= 100000
        ORDER BY m.gmtCreate DESC
    </select>
    <select id="getActivityNearMiniListCount" resultType="java.lang.Integer">
        select count(1) from (
        <include refid="sql_getActivityNearMiniList"/>
        GROUP BY m.activityId
        HAVING m.distance &lt;= 100000
        ORDER BY m.gmtCreate DESC
        )cnt
    </select>
    <select id="getActivityMiniListCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1) AS total_count
        FROM (
        SELECT
        a.id AS activityId,
        a.activity_name AS activityName
        <if test="request.city !=null and request.city==0">
            <if test="request.lat!=null and request.lng!=null">
                ,6378.138 * 2 * ASIN(SQRT(POW(SIN((#{request.lat} * PI() / 180 - b.lat * PI() / 180) / 2), 2) +
                COS(#{request.lat} *
                PI() / 180) *
                COS(b.lat * PI() / 180) * POW(SIN((#{request.lng} * PI() / 180 - b.lng * PI() / 180) / 2), 2))) *1000 AS
                distance
            </if>
        </if>
        from customer_activity_info a
        left join customer_activity_part_info b on a.id = b.activity_id AND b.deleted = 0
        where a.deleted =0 and a.publish_status = 1
        <if test ="request.brandType!=null">
            and a.brand_type = #{request.brandType}
        </if>
        <if test ="request.activityStatus!=null">
            and b.activity_status =#{request.activityStatus}
        </if>
        <if test="request.city !=null">
            <if test="request.city>0">
                and b.city = #{request.city}
            </if>
        </if>
        <if test ="request.category!=null and request.category!= 0">
            and a.category = #{request.category}
        </if>
        <if test="request.city !=null">
            <if test="request.city==0 and request.lat!=null and request.lng!=null">
                group by a.id
                having distance &lt;= 100000
                order by a.gmt_create desc
            </if>
            <if test="request.city!=0">
                group by a.id
                order by a.gmt_create desc
            </if>
        </if>
             ) AS c;
    </select>

    <!-- 管理后台 -->
    <select id="getActivityListPage" resultType="com.stbella.customer.server.activity.vo.ActivityListVO">
        select
        a.id as id,
        a.activity_name as activityName,
        a.brand_type as brandType,
        a.category as category,
        a.activity_status as activityStatus,
        a.publish_status as publishStatus,
        a.gmt_create as gmtCreate,
        a.create_id as createId,
        a.gmt_modified as gmtModified,
        a.modify_id as modifyId,
        a.creator_name as creatorName,
        a.modifier_name as modifierName,
        a.start_time as startTime,
        a.end_time as endTime,
        a.link_type AS linkType,
        a.link_url AS linkUrl,
        a.link_app_id AS linkAppId,
        GROUP_CONCAT(distinct
        CASE
        WHEN b.store_id IS NOT NULL AND b.store_id !='' THEN b.store_id
        END
        ) AS storeId,
        GROUP_CONCAT(b.id) AS partsId,
        GROUP_CONCAT(distinct b.city_name) AS cityNames
        from customer_activity_info a
        left join customer_activity_part_info b on a.id = b.activity_id
        where a.deleted =0 and a.activity_source is null
        and a.biz_id is null
        <if test="request.activityName != null">
            and a.activity_name  like concat('%',#{request.activityName},'%')
        </if>

        <if test="request.activityStatus != null  and request.activityStatus.size() > 0">
            and b.activity_status in
            <foreach collection="request.activityStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="request.publishStatus != null">
            and a.publish_status =   #{request.publishStatus}
        </if>
        <if test="request.brandType != null">
            and a.brand_type = #{request.brandType}
        </if>
        group by a.id
        order by a.gmt_create desc
    </select>

    <update id="jobUpdateActivityStatusAndTime">
        UPDATE customer_activity_info a
        JOIN (
        SELECT
        activity_id,
        MIN(start_time) AS min_startTime,
        MAX(end_time) AS max_endTime
        FROM customer_activity_part_info
        WHERE deleted = 0
        GROUP BY activity_id
        ) b ON a.id = b.activity_id
        SET a.activity_status =
        CASE
        WHEN NOW() &lt; b.min_startTime THEN 0
        WHEN NOW() >= b.min_startTime AND NOW() &lt;= b.max_endTime THEN 1
        ELSE 2
        END,
        a.start_time = b.min_startTime,
        a.end_time = b.max_endTime

    </update>

    <select id="getLastestActivity" resultType="com.stbella.customer.server.activity.vo.mini.ActivityListMiniVO">
        select * from customer_activity_info a  where
        <if test="brandType !=null">
            a.brand_type = #{brandType}
        </if>
        and a.publish_status = 1 and a.deleted=0
        order by gmt_create desc
        limit 1
    </select>
</mapper>
