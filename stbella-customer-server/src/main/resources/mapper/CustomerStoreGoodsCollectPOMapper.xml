<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerStoreGoodsCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerStoreGoodsCollectPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="basic_uid" property="basicUid" />
        <result column="store_id" property="storeId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_image" property="goodsImage" />
        <result column="room_type_name" property="roomTypeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_uid, store_id, goods_id, goods_name, goods_image, room_type_name
    </sql>

</mapper>
