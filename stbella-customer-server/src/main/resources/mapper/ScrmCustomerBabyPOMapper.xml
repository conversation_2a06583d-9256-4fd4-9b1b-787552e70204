<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmCustomerBabyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmCustomerBabyPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="baby_id" property="babyId"/>
        <result column="scrm_customer_id" property="scrmCustomerId"/>
        <result column="scrm_id" property="scrmId"/>
        <result column="order_no" property="orderNo"/>
        <result column="sex" property="sex"/>
        <result column="nickname" property="nickname"/>
        <result column="name" property="name"/>
        <result column="birthdate" property="birthdate"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="entity_type" property="entityType" />
        <result column="type" property="type" />
        <result column="owner_id" property="ownerId" />
        <result column="depart" property="depart" />
        <result column="post" property="post" />
        <result column="phone" property="phone" />
        <result column="mobile" property="mobile" />
        <result column="email" property="email" />
        <result column="zip_code" property="zipCode" />
        <result column="contact_birthday" property="contactBirthday" />
        <result column="wx_union_id" property="wxUnionId" />
        <result column="wx_user_type" property="wxUserType" />
        <result column="recent_activity_record_time" property="recentActivityRecordTime" />
        <result column="loss" property="loss" />
        <result column="share_tags" property="shareTags" />
        <result column="external_user_id" property="externalUserId" />
        <result column="corp_id" property="corpId" />
        <result column="wx_position" property="wxPosition" />
        <result column="wx_avatar_url" property="wxAvatarUrl" />
        <result column="wx_user_name" property="wxUserName" />
        <result column="contact_channel" property="contactChannel" />
        <result column="dim_depart" property="dimDepart" />
        <result column="contact_role" property="contactRole" />
        <result column="qw_contact_id" property="qwContactId" />
        <result column="contact_score" property="contactScore" />
        <result column="relation_with_client" property="relationWithClient" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, baby_id, scrm_customer_id, scrm_id, order_no, sex, nickname, name, birthdate, remark, gmt_create, gmt_modified, deleted, create_by, create_by_name, update_by, update_by_name, entity_type, type, owner_id, depart, post, phone, mobile, email, zip_code, contact_birthday, wx_union_id, wx_user_type, recent_activity_record_time, loss, share_tags, external_user_id, corp_id, wx_position, wx_avatar_url, wx_user_name, contact_channel, dim_depart, contact_role, qw_contact_id, contact_score, relation_with_client
    </sql>

</mapper>
