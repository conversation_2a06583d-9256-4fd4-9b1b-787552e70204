<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerMediaLearnedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerMediaLearnedPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="duration" property="duration" />
        <result column="basic_uid" property="basicUid" />
        <result column="mobile" property="mobile" />
        <result column="learn_status" property="learnStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        tenant_id, duration, basic_uid, mobile, learn_status
    </sql>

</mapper>
