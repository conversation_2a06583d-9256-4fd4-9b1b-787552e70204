<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerGrowthLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerGrowthLevelPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="type" property="type" />
        <result column="level_name" property="levelName" />
        <result column="growth_val_start" property="growthValStart" />
        <result column="growth_val_end" property="growthValEnd" />
        <result column="relegation_type" property="relegationType" />
        <result column="relegation_days" property="relegationDays" />
        <result column="multiplier_integral" property="multiplierIntegral" />
        <result column="level_rights" property="levelRights" />
        <result column="pre_level" property="preLevel" />
        <result column="next_level" property="nextLevel" />
        <result column="is_top" property="isTop" />
        <result column="sort" property="sort" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        type, level_name, growth_val_start, growth_val_end, relegation_type, relegation_days, multiplier_integral, level_rights, pre_level, next_level, is_top, sort, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>
