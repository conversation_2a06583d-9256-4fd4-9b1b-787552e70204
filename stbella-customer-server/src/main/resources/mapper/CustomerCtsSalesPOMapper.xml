<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.cts.mapper.CustomerCtsSalesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.cts.entity.CustomerCtsSalesPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="customer_info_cts_id" property="customerInfoCtsId"/>
        <result column="sales_id" property="salesId"/>
        <result column="sales_name" property="salesName"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_modified,
        gmt_create,
        customer_info_cts_id, sales_id, sales_name, created_by, updated_by
    </sql>


    <select id="getSalesList"
            resultType="com.stbella.customer.server.cts.vo.CustomerInfoCtsSalesVO">
        SELECT
          DISTINCT(cis.sales_id)  AS id ,
          cis.sales_name AS name
        FROM
        customer_cts_sales AS cis
        WHERE
        cis.deleted = 0

        ORDER BY cis.gmt_create DESC
    </select>



    <select id="getCustomerListBySalesIds" resultType="com.stbella.customer.server.cts.dto.CtsCustomerSalesDTO">
        SELECT
            cic.id,cis.sales_id,cic.cts_site_id
        FROM
            customer_cts_sales AS cis LEFT JOIN customer_info_cts AS cic ON cic.id = cis.customer_info_cts_id
        WHERE
            cis.deleted = 0
        AND cic.deleted = 0
        <if test="salesIdList != null  and salesIdList.size() > 0">
            AND cis.sales_id IN
            <foreach collection="salesIdList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY cis.gmt_create DESC
    </select>


</mapper>
