<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.CustomerAdSubscribeListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.CustomerAdSubscribeListPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="trace_id" property="traceId" />
        <result column="wechat_openid" property="wechatOpenid"/>
        <result column="adgroup_id" property="adgroupId"/>
        <result column="campaign_id" property="campaignId" />
        <result column="wechat_account_id" property="wechatAccountId" />
        <result column="position_id" property="positionId"/>
        <result column="ad_id" property="adId"/>
        <result column="act_time" property="actTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        trace_id, wechat_openid, adgroup_id, campaign_id, wechat_account_id, position_id, ad_id, act_time
    </sql>

</mapper>
