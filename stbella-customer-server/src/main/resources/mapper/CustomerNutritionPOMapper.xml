<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerNutritionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.nutuition.entity.CustomerNutritionPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="info_id" property="infoId"/>
        <result column="head_portrait" property="headPortrait"/>
        <result column="age" property="age"/>
        <result column="address_province" property="addressProvince"/>
        <result column="address_city" property="addressCity"/>
        <result column="address_area" property="addressArea"/>
        <result column="address_details" property="addressDetails"/>
        <result column="anticipate_date" property="anticipateDate"/>
        <result column="delivery_mode" property="deliveryMode"/>
        <result column="birthday" property="birthday"/>
        <result column="email" property="email"/>
        <result column="emergency_contact" property="emergencyContact"/>
        <result column="emergency_contact_phone" property="emergencyContactPhone"/>
        <result column="social_relations" property="socialRelations"/>
        <result column="source" property="source"/>
        <result column="sell_id" property="sellId"/>
        <result column="sell_name" property="sellName"/>
        <result column="lebel_id" property="lebelId"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        info_id, head_portrait, age, address_province, address_city, address_area, address_details, anticipate_date, delivery_mode, birthday, email, emergency_contact, emergency_contact_phone, social_relations, source, sell_id, sell_name, lebel_id, contract_status, create_by, create_by_name, update_by, update_by_name
    </sql>

    <resultMap id="VOResultMap" type="com.stbella.customer.server.nutuition.response.CustomerSearchDTO">
        <result column="id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="phone_number" property="customerPhone" />
        <result column="lebel_id" property="lebelId" />
        <result column="contract_status" property="contractStatus" />
        <result column="sell_id" property="saleId" />
    </resultMap>

    <select id="getCustomerListByNameOrPhone" parameterType="String" resultMap="VOResultMap">
        select n.id, c.customer_name, c.phone_number, n.lebel_id, n.contract_status, n.sell_id
        from `customer_info` as `c`
        left join `customer_info_nutrition` as `n` on `n`.`info_id` = `c`.`id`
        where
         `n`.`deleted` = '0'
        AND `c`.`deleted` = '0'
        AND (`c`.`customer_name` like concat('%', #{text,jdbcType=VARCHAR}, '%')
        OR `c`.`phone_number` like concat('%', #{text,jdbcType=VARCHAR}, '%'))
    </select>

</mapper>
