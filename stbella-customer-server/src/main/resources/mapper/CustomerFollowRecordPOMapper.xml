<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerFollowRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerFollowRecordPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="store_id" property="storeId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_age" property="customerAge" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="district_id" property="districtId" />
        <result column="province_name" property="provinceName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="hospital" property="hospital" />
        <result column="customer_source" property="customerSource" />
        <result column="customer_job" property="customerJob" />
        <result column="cultural_attribute" property="culturalAttribute" />
        <result column="customer_income" property="customerIncome" />
        <result column="consumption_capacity" property="consumptionCapacity" />
        <result column="brand_like" property="brandLike" />
        <result column="customer_influence" property="customerInfluence" />
        <result column="pay_type" property="payType" />
        <result column="spouse_age" property="spouseAge" />
        <result column="spouse_job" property="spouseJob" />
        <result column="club_quality" property="clubQuality" />
        <result column="next_fllow_date" property="nextFllowDate" />
        <result column="remark" property="remark" />
        <result column="emp_id" property="empId" />
        <result column="emp_name" property="empName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        store_id, customer_id, customer_age, province_id, city_id, district_id, province_name, city_name, district_name, hospital, customer_source, customer_job, cultural_attribute, customer_income, consumption_capacity, brand_like, customer_influence, pay_type, spouse_age, spouse_job, club_quality, next_fllow_date, remark, emp_id, emp_name
    </sql>

</mapper>
