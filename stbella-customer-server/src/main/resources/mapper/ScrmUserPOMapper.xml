<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmUserPO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="scrm_id" property="scrmId" />
        <result column="dim_depart" property="dimDepart" />
        <result column="employee_id" property="employeeId" />
        <result column="entity_type" property="entityType" />
        <result column="enable" property="enable" />
        <result column="sales" property="sales" />
        <result column="scrm_modified" property="scrmModified" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, phone, scrm_id, dim_depart, employee_id, entity_type, enable, sales, scrm_modified, tenant_id, deleted, gmt_create, gmt_modified
    </sql>

</mapper>
