<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmUserAssignAccountTempRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmUserAssignAccountTempRecordPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="scrm_id" property="scrmId" />
        <result column="owner_id" property="ownerId" />
        <result column="dim_depart" property="dimDepart" />
        <result column="customer_id" property="customerId" />
        <result column="sale_id" property="saleId" />
        <result column="account_id" property="accountId" />
        <result column="distribution_time" property="distributionTime" />
        <result column="opportunity_scrm_id" property="opportunityScrmId" />
        <result column="scrm_user_store_config_id" property="scrmUserStoreConfigId" />
        <result column="picp_store_id" property="picpStoreId" />
        <result column="entity_type" property="entityType" />
        <result column="lock_status" property="lockStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        scrm_id, owner_id, dim_depart, customer_id, sale_id, account_id, distribution_time, opportunity_scrm_id, scrm_user_store_config_id, picp_store_id, entity_type, lock_status
    </sql>

</mapper>
