<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerStoreInvitationProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerStoreInvitationProjectPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="title" property="title" />
        <result column="duration" property="duration" />
        <result column="project_image" property="projectImage" />
        <result column="project_remark" property="projectRemark" />
        <result column="invitation_image" property="invitationImage" />
        <result column="appointment_image" property="appointmentImage" />
        <result column="visiting_process_image" property="visitingProcessImage" />
        <result column="share_image" property="shareImage" />
        <result column="active" property="active" />
        <result column="sort_num" property="sortNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type, title, duration, project_image, project_remark, invitation_image, appointment_image, visiting_process_image, share_image, active, sort_num
    </sql>

</mapper>
