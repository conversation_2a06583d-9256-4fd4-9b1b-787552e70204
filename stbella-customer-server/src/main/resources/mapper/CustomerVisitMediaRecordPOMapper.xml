<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerVisitMediaRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerVisitMediaRecordPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="course_id" property="courseId" />
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="from_type" property="fromType" />
        <result column="visit_time_number" property="visitTimeNumber" />
        <result column="statistical_time" property="statisticalTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        tenant_id, course_id, client_id, client_name, store_id, store_name, from_type, visit_time_number, statistical_time
    </sql>

</mapper>
