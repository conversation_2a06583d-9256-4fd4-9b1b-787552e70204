<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerStoreInvitationAppointmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerStoreInvitationAppointmentPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="brand_type" property="brandType" />
        <result column="basic_id" property="basicId" />
        <result column="client_phone" property="clientPhone" />
        <result column="client_name" property="clientName" />
        <result column="client_gender" property="clientGender" />
        <result column="checkin_store_time" property="checkinStoreTime" />
        <result column="store_id" property="storeId" />
        <result column="project_id" property="projectId" />
        <result column="qw_qrcode" property="qwQrcode" />
        <result column="status" property="status" />
        <result column="staff_id" property="staffId" />
        <result column="staff_name" property="staffName" />
        <result column="appointment_type" property="appointmentType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        brand_type, basic_id, client_phone, client_name, client_gender, checkin_store_time, store_id, project_id, qw_qrcode, status, staff_id, staff_name, appointment_type
    </sql>

</mapper>
