<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmOpportunityStatusRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmOpportunityStatusRecordPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="owner_id" property="ownerId" />
        <result column="dim_depart" property="dimDepart" />
        <result column="scrm_id" property="scrmId" />
        <result column="opportunity_name" property="opportunityName" />
        <result column="sale_stage_id" property="saleStageId" />
        <result column="scrm_user_store_config_id" property="scrmUserStoreConfigId" />
        <result column="entity_type" property="entityType" />
        <result column="lock_status" property="lockStatus" />
        <result column="real_finish_time" property="realFinishTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        owner_id, dim_depart, scrm_id, opportunity_name, sale_stage_id, scrm_user_store_config_id, entity_type, lock_status, real_finish_time
    </sql>

    <select id="pageExport" resultType="com.stbella.customer.server.customer.dto.ExportIslaHealthyOftenDataDTO">
        SELECT
        customer.`name` AS 'client_name',
        customer.phone AS 'client_phone',
        record.real_finish_time AS 'distribution_time',
        scrm_store.store_id,
        CASE opportunity.owned_store
        WHEN 1 THEN '疑似探子'
        WHEN 2 THEN '广告合作'
        WHEN 3 THEN '应聘'
        WHEN 4 THEN '无月子需求'
        WHEN 5 THEN '联系不上'
        WHEN 6 THEN '产康需求'
        WHEN 7 THEN '月子餐服务'
        WHEN 8 THEN '其他'
        ELSE '有效客资'
        END 'invalid_tag',
        CASE opportunity.old_tag
        WHEN 1 THEN '历史未签约客户'
        WHEN 2 THEN '历史已签约客户'
        ELSE '无'
        END 'old_tag',
        checkin_store.real_finish_time AS 'checkin_time',
        staff.`name` AS 'staff_name'
        FROM `stbella-customer`.scrm_opportunity_status_record AS record
        LEFT JOIN `stbella-customer`.scrm_business_opportunity AS opportunity ON record.opportunity_scrm_id =
        opportunity.scrm_id
        LEFT JOIN `stbella-customer`.scrm_user_store_config AS scrm_store ON opportunity.contracted_store =
        scrm_store.scrm_record_id
        LEFT JOIN `stbella-customer`.scrm_customer AS customer ON customer.deleted = 0 AND opportunity.customer_id =
        customer.scrm_customer_id
        LEFT JOIN `stbella-customer`.scrm_opportunity_status_record AS checkin_store ON record.opportunity_scrm_id =
        checkin_store.opportunity_scrm_id AND checkin_store.sale_stage_id=2 AND checkin_store.deleted = 0
        LEFT JOIN `stbella-customer`.scrm_user AS staff ON record.owner_id = staff.scrm_id
        WHERE
        record.deleted = 0
        AND record.sale_stage_id = 1
        AND (opportunity.reason NOT IN (1,6) OR opportunity.reason IS NULL)
        AND opportunity.entity_type = 11010000300001
        AND (record.real_finish_time BETWEEN #{req.customerBeginDate} AND #{req.customerEndDate})
        AND scrm_store.store_id in
        <foreach collection="req.storeIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
