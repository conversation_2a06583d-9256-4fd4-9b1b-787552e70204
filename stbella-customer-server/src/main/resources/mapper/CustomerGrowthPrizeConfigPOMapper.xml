<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerGrowthPrizeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.vip.CustomerGrowthPrizeConfigPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="sub_title" property="subTitle" />
        <result column="yz_activity_id" property="yzActivityId" />
        <result column="rule_content" property="ruleContent" />
        <result column="image" property="image" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        type, title, sub_title, yz_activity_id, rule_content, image
    </sql>

</mapper>
