<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmOpportunityActivityRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmOpportunityActivityRecordPO">
        <result column="id" property="id" />
        <result column="content" property="content" />
        <result column="scrm_id" property="scrmId" />
        <result column="contact_id" property="contactId" />
        <result column="dim_depart" property="dimDepart" />
        <result column="owner_id" property="ownerId" />
        <result column="created_at" property="createdAt" />
        <result column="start_time" property="startTime" />
        <result column="activity_ecordFrom_compound" property="activityEcordfromCompound" />
        <result column="entity_type" property="entityType" />
        <result column="sale_stage_id" property="saleStageId" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="intentional_degree" property="intentionalDegree" />
        <result column="need_follow" property="needFollow" />
        <result column="next_call_time" property="nextCallTime" />
        <result column="location" property="location" />
        <result column="location_detail" property="locationDetail" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="updated_at" property="updatedAt" />
        <result column="dbc_relation26" property="dbcRelation26" />
        <result column="qw_event" property="qwEvent" />
        <result column="qw_rvent_type" property="qwRventType" />
        <result column="content_pic" property="contentPic" />
        <result column="content_file" property="contentFile" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        content,scrm_id,contact_id, dim_depart, owner_id, created_at, start_time, activity_ecordFrom_compound, entity_type, sale_stage_id, contact_name, contact_phone, intentional_degree, need_follow, next_call_time, location, location_detail, longitude, latitude, updated_at, dbc_relation26, qw_event, qw_rvent_type, content_pic, content_file
    </sql>

</mapper>
