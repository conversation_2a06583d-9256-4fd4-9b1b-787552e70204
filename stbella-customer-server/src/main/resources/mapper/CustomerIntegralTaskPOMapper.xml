<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerIntegralTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerIntegralTaskPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="title" property="title" />
        <result column="task_desc" property="taskDesc" />
        <result column="task_rule" property="taskRule" />
        <result column="task_icon" property="taskIcon" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="brand_type" property="brandType" />
        <result column="sence" property="sence" />
        <result column="sort" property="sort" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        title, task_desc, task_rule, task_icon, type, status, brand_type, sence, sort, created_by, updated_by
    </sql>

</mapper>
