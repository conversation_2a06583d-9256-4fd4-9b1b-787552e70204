<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.activity.mapper.CustomerGenericActivityAppointmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.activity.entity.CustomerGenericActivityAppointmentPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="basic_uid" property="basicUid" />
        <result column="activity_id" property="activityId" />
        <result column="activity_part_id" property="activityPartId" />
        <result column="appoint_fields" property="appointFields" />
        <result column="appoint_date_start" property="appointDateStart" />
        <result column="appoint_date_end" property="appointDateEnd" />
        <result column="status" property="status" />
        <result column="verify_staff_id" property="verifyStaffId" />
        <result column="verify_staff_name" property="verifyStaffName" />
        <result column="verify_time" property="verifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_uid, activity_id, activity_part_id, appoint_fields, appoint_date_start, appoint_date_end, status, verify_staff_id, verify_staff_name, verify_time
    </sql>

    <!-- 查询指定日期内过期但实际是待使用的预约记录id -->
    <select id="queryExpiredAppointmentIdList" resultType="java.lang.Long">
        SELECT appointment.id FROM `customer_generic_activity_appointment` AS appointment
        LEFT JOIN `customer_activity_info` AS ai ON appointment.`activity_id` = ai.`id`
        WHERE
        appointment.`status` = 0
        AND ai.end_time &lt;= #{date}
    </select>

</mapper>
