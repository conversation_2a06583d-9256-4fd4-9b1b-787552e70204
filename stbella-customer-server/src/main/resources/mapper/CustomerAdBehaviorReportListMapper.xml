<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.CustomerAdBehaviorReportListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.CustomerAdBehaviorReportListPO">
        <result column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="openid" property="openid" />
        <result column="ad_subscribe_id" property="adSubscribeId" />
        <result column="behavior_type" property="behaviorType" />
        <result column="customer_status" property="customerStatus" />
        <result column="report_time" property="reportTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        openid, ad_subscribe_id, behavior_type, customer_status, report_time
    </sql>

</mapper>
