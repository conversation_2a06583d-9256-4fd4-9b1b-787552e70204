<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerUserIncreaseMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a
        .
        gmt_create
        ,a.gmt_modified,a.id as id, a.basic_id, a.parent_basic_id,
        a.integral, a.qr_code, a.qr_id, a.deleted
    </sql>
    <!--基础用户信息查询结果列-->
    <sql id="Basic_User_Column_List">
        b
        .
        id
        as id,b.name,b.phone
    </sql>

    <select id="inviteRelationList"
            resultType="com.stbella.customer.server.customer.vo.InvitedUserListVO$InviteRelationVO">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Basic_User_Column_List"/>
        from
        `sbl-saas-dev`.`he_invite_relation` a
        left join `sbl-saas-dev`.`he_user_basic` b on a.`basic_id` = b.`id`
        where
        a.`parent_basic_id` = #{basicId} and a.`deleted` = 0
        order by a.`gmt_create` desc
    </select>

    <select id="countMyShare" resultType="int">
        select COALESCE(sum(`integral`), 0)
        from `sbl-saas-dev`.`he_invite_relation`
        where `parent_basic_id` = #{basicId}
    </select>
</mapper>