<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerGrowthRightsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerGrowthRightsPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="type" property="type" />
        <result column="rights_name" property="rightsName" />
        <result column="rights_shown_name" property="rightsShownName" />
        <result column="rights_icon" property="rightsIcon" />
        <result column="rights_shown_icon" property="rightsShownIcon" />
        <result column="rights_desc" property="rightsDesc" />
        <result column="sort" property="sort" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        type, rights_name, rights_shown_name, rights_icon, rights_shown_icon, rights_desc, sort, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>
