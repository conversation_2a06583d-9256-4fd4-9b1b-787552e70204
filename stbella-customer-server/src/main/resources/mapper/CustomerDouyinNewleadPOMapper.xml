<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerDouyinNewleadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerDouyinNewleadPO">
        <result column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="clue_id" property="clueId" />
        <result column="local_account_id" property="localAccountId" />
        <result column="promotion_id" property="promotionId" />
        <result column="create_time_detail" property="createTimeDetail" />
        <result column="effective_state" property="effectiveState" />
        <result column="effective_state_name" property="effectiveStateName" />
        <result column="req_id" property="reqId" />
        <result column="intention_store_name" property="intentionStoreName" />
        <result column="message" property="message" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        name, phone, clue_id, local_account_id, promotion_id, create_time_detail, effective_state, effective_state_name, req_id, intention_store_name, message
    </sql>

</mapper>
