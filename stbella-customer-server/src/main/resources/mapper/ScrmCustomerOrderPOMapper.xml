<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.scrm.mapper.ScrmCustomerOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.scrm.entity.ScrmCustomerOrderPO">
        <id column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="deleted" property="deleted" />
        <result column="order_id" property="orderId" />
        <result column="scrm_id" property="scrmId" />
        <result column="scrm_customer_id" property="scrmCustomerId" />
        <result column="scrm_owner_id" property="scrmOwnerId" />
        <result column="scrm_dim_depart" property="scrmDimDepart" />
        <result column="goods_name" property="goodsName" />
        <result column="production_name" property="productionName" />
        <result column="order_sn" property="orderSn" />
        <result column="order_type" property="orderType" />
        <result column="staff_id" property="staffId" />
        <result column="store_id" property="storeId" />
        <result column="order_amount" property="orderAmount" />
        <result column="goods_amount" property="goodsAmount" />
        <result column="pay_amount" property="payAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="goods_days" property="goodsDays" />
        <result column="pay_status" property="payStatus" />
        <result column="discount_margin" property="discountMargin" />
        <result column="net_margin" property="netMargin" />
        <result column="extra_gift" property="extraGift" />
        <result column="remark" property="remark" />
        <result column="first_pay_time" property="firstPayTime" />
        <result column="percent_first_time" property="percentFirstTime" />
        <result column="order_h5" property="orderH5" />
        <result column="gross_margin" property="grossMargin" />
        <result column="remaining_amount" property="remainingAmount" />
        <result column="review_amount" property="reviewAmount" />
        <result column="real_amount" property="realAmount" />
        <result column="refunding_amount" property="refundingAmount" />
        <result column="order_status" property="orderStatus" />
        <result column="refund_status" property="refundStatus" />
        <result column="month_order" property="monthOrder" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gmt_create,
        gmt_modified,
        deleted,
        id, order_id, scrm_id,store_id, scrm_customer_id,scrm_owner_id,scrm_dim_depart, goods_name, production_name, order_sn, order_type, staff_id, order_amount, goods_amount, pay_amount, paid_amount, refund_amount, goods_days, pay_status, discount_margin, net_margin, extra_gift, remark, first_pay_time, percent_first_time, order_h5, gross_margin, remaining_amount, review_amount, real_amount, refunding_amount, order_status, refund_status, month_order, version, create_by, create_by_name, update_by, update_by_name
    </sql>
</mapper>
