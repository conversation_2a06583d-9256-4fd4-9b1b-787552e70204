<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerServiceContextMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerServiceContextPO">
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="customer_service_id" property="customerServiceId" />
        <result column="type_sign" property="typeSign" />
        <result column="context_key" property="contextKey" />
        <result column="context_key_name" property="contextKeyName" />
        <result column="context_val" property="contextVal" />
        <result column="is_show" property="isShow" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        deleted,
        gmt_create,
        gmt_modified,
        customer_service_id, type_sign, context_key, context_key_name, context_val, is_show
    </sql>

</mapper>
