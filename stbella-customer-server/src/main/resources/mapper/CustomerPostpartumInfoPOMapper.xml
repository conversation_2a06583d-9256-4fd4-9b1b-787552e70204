<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerPostpartumInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerPostpartumInfoPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_id" property="customerId" />
        <result column="record_date" property="recordDate" />
        <result column="weight" property="weight" />
        <result column="breast_circumference" property="breastCircumference" />
        <result column="hip_circumference" property="hipCircumference" />
        <result column="abdomen_circumference" property="abdomenCircumference" />
        <result column="thigh_circumference_left" property="thighCircumferenceLeft" />
        <result column="thigh_circumference_right" property="thighCircumferenceRight" />
        <result column="armpit_circumference" property="armpitCircumference" />
        <result column="waist_circumference" property="waistCircumference" />
        <result column="create_id" property="createId" />
        <result column="create_name" property="createName" />
        <result column="update_id" property="updateId" />
        <result column="update_name" property="updateName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_id, record_date, weight, breast_circumference, hip_circumference, abdomen_circumference, thigh_circumference_left, thigh_circumference_right, armpit_circumference, waist_circumference, create_id, create_name, update_id, update_name
    </sql>

    <update id="updateByPrimaryKey" parameterType="com.stbella.customer.server.customer.entity.CustomerPostpartumInfoPO">
        update customer_postpartum_info
        set weight = #{weight},
            breast_circumference = #{breastCircumference},
            hip_circumference = #{hipCircumference},
            abdomen_circumference = #{abdomenCircumference},
            thigh_circumference_left = #{thighCircumferenceLeft},
            thigh_circumference_right = #{thighCircumferenceRight},
            armpit_circumference = #{armpitCircumference},
            waist_circumference = #{waistCircumference},
            update_id = #{updateId},
            update_name = #{updateName},
            <if test="intentionProjectFlag != null">
                intention_project_flag = #{intentionProjectFlag},
            </if>
            <if test="intentionProject != null">
                intention_project = #{intentionProject,jdbcType=VARCHAR,typeHandler=com.stbella.customer.server.customer.handler.ListConfigHandler},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="taskNode != null">
                task_node = #{taskNode},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo},
            </if>
            gmt_modified = now()
        where id = #{id}
    </update>

</mapper>
