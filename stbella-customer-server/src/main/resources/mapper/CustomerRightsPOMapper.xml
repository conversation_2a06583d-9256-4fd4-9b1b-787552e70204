<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerRightsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerRightsPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="basic_id" property="basicId" />
        <result column="rights_code" property="rightsCode" />
        <result column="collect_date" property="collectDate" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="order_date" property="orderDate" />
        <result column="property_id" property="propertyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        basic_id, rights_code, collect_date, order_id, order_no, order_date, property_id
    </sql>

</mapper>
