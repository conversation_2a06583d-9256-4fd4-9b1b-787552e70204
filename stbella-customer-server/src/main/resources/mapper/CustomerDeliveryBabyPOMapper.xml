<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.customer.server.customer.mapper.CustomerDeliveryBabyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.customer.server.customer.entity.CustomerDeliveryBabyPO">
    <result column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="deleted" property="deleted" />
        <result column="customer_delivery_id" property="customerDeliveryId" />
        <result column="sex" property="sex" />
        <result column="baby_name" property="babyName" />
        <result column="jaundice_value" property="jaundiceValue" />
        <result column="birth_weight" property="birthWeight" />
        <result column="nation" property="nation" />
        <result column="lactation_type" property="lactationType" />
        <result column="lactation_remark" property="lactationRemark"/>
        <result column="delivery_mode" property="deliveryMode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        customer_delivery_id, sex, baby_name, jaundice_value, birth_weight,nation, lactation_type,lactation_remark,delivery_mode
    </sql>

</mapper>
