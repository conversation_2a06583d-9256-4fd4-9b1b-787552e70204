<snowballFlow name="new_year_prize_list_query" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="新春活动奖品列表" identifier="Year:Prize:List:Query" bizActivity="NEW_YEAR_PRIZE_LIST_QUERY">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.customer.server.newYear.component.query.PrizeListQueryAssembler"/>
            </stream>
        </step>

    </flow>

</snowballFlow>
