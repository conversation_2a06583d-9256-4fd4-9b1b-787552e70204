<snowballFlow name="invite_reward_send" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="老带新奖励发放流程" identifier="Invite:Reward:Send" bizActivity="INVITE_REWARD_SEND">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.invite.component.validator.InviteRelationValidator"/>
                <node type="com.stbella.customer.server.invite.component.validator.InviteSendRewardValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--保存奖励记录-->
                 <node type="com.stbella.customer.server.invite.component.assembler.InviteSendRewardRecordAssembler"/>
                 <node type="com.stbella.customer.server.invite.component.assembler.VoucherSaveAssembler"/>
                 <node type="com.stbella.customer.server.invite.component.assembler.InviteStageUpdateAssembler"/>
             </stream>
        </step>

        <step name="transaction" priority="3" check="true">
            <stream priority="1">
                <!--发放奖励-->
                <node type="com.stbella.customer.server.invite.component.assembler.InviteSendAssetAssembler"/>
                <node type="com.stbella.customer.server.invite.component.assembler.YouzanCouponSendAssembler"/>
            </stream>
        </step>

        <step name="afterTransaction" priority="4" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
