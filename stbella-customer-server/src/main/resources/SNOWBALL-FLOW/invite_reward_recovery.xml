<snowballFlow name="invite_reward_recovery" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="老带新奖励退款回收流程" identifier="Invite:Reward:Recovery" bizActivity="INVITE_REWARD_REFUND_RECOVERY">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.invite.component.validator.RewardRefundRecoverValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--回收奖励记录-->
                 <node type="com.stbella.customer.server.invite.component.assembler.RewardRefundRecoveryAssembler"/>
                 <node type="com.stbella.customer.server.invite.component.assembler.VoucherUpdateAssembler"/>
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
