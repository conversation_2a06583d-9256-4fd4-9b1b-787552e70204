<snowballFlow name="flow_demo" version="1"
  xmlns="http://www.guanghetang.cn/schema/snowball/flow"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.guanghetang.cn/schema/snowball/flow/snowballFlow.xsd">

    <flow name="商机分配流程" identifier="CARE_CENTER:opportunity:assign"  >
        <step name="PARAM_VALID" priority="0" check="true" >
            <stream priority="1">
                <node executableAtom="opportunityValidator"/>
            </stream>

        </step>
        <step name="BIZ_PROCESSOR" priority="1" transaction="false">
            <stream priority="1" >
                <node executableAtom="opportunityAssignTeamProcessor"/>
                <node executableAtom="opportunityAssignMemberProcessor"/>
                <node executableAtom="finishOpportunityAssignProcessor"/>
                <node executableAtom="opportunityAssignSuccessNoticeProcessor"/>
            </stream>
        </step>

    </flow>


</snowballFlow>
