<snowballFlow name="customer_growth_reward_birthday_gift_send" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="会员权益发放" identifier="GROWTH:REWARD:USER:BRAND:QUERY" bizActivity="CUSTOMER_GROWTH_REWARD_USER_BRAND_QUERY">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
                <node type="com.stbella.customer.server.customer.component.query.CustomerGrowthUserBrandQuery"/>
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
