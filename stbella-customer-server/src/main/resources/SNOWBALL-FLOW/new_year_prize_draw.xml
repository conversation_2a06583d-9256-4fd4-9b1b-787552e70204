<snowballFlow name="new_year_prize_draw" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="新春活动抽奖" identifier="Year:Prize:Draw" bizActivity="NEW_YEAR_PRIZE_DRAW">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.newYear.component.validator.LotteryEligibilityValidator"/>
                <node type="com.stbella.customer.server.newYear.component.validator.CompanyStaffValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--保存奖励记录-->
                 <node type="com.stbella.customer.server.newYear.component.assembler.PrizePoolAssembler"/>
             </stream>
        </step>

        <step name="transaction" priority="3" check="true">
            <stream priority="1">
                <!--发放奖励-->
                <node type="com.stbella.customer.server.newYear.component.assembler.PrizeResultUpdateAssembler"/>
                <node type="com.stbella.customer.server.newYear.component.assembler.PrizeSendAssembler"/>
            </stream>
        </step>

        <step name="afterTransaction" priority="4" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
                <node type="com.stbella.customer.server.newYear.component.assembler.PrizeMessageAssembler"/>
            </stream>
        </step>


    </flow>

</snowballFlow>
