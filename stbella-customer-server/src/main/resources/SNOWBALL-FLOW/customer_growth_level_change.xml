<snowballFlow name="customer_growth_level_change" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="会员等级变化" identifier="GROWTH:LEVEL:CHANGE" bizActivity="CUSTOMER_GROWTH_LEVEL_CHANGE">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--激活奖励记录-->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthLevelChangeAssembler"/>
                 <!-- 虚拟券处理 -->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthVirtualCouponAssembler"/>
                 <!-- 有赞券处理 -->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthYouzanCouponAssembler"/>
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
