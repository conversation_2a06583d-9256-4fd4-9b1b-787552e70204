<snowballFlow name="customer_growth_reward_refund_recover" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="订单全额退款回收会员权益" identifier="GROWTH:REWARD:REFUND" bizActivity="CUSTOMER_GROWTH_REWARD_REFUND_RECOVER">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.customer.component.validator.CustomerGrowthRewardRefundRecoverValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--激活奖励记录-->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthRewardRefundRecoverAssembler"/>
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthYouzanCouponAssembler"/>
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
