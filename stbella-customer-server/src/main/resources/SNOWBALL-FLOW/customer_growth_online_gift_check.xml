<snowballFlow name="customer_growth_online_gift_check" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="会员等级变化" identifier="GROWTH:ONLINE:GIFT:CHECK" bizActivity="CUSTOMER_GROWTH_ONLINE_GIFT_CHECK">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.customer.component.validator.CustomerOnlineGiftSendValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
