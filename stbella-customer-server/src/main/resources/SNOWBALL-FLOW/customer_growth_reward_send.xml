<snowballFlow name="customer_growth_reward_send" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="会员权益发放" identifier="GROWTH:REWARD:SEND" bizActivity="CUSTOMER_GROWTH_REWARD_SEND">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <!--校验前查询/组装-->
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <!--各种校验-->
                <node type="com.stbella.customer.server.customer.component.validator.CustomerGrowthRewardSendValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <!--激活奖励记录-->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthRewardSendAssembler"/>
                 <!-- 积分发放 -->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthRewardIntegralSendAssembler"/>
                 <!-- 虚拟券处理 -->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthVirtualCouponAssembler"/>
                 <!-- 有赞券处理 -->
                 <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthYouzanCouponAssembler"/>
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <!--入库以后发送通知等-->
            </stream>
        </step>


    </flow>

</snowballFlow>
