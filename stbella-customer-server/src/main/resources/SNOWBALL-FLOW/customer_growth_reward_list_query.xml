<snowballFlow name="customer_growth_reward_list_query" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="会员权益列表" identifier="GROWTH:REWARD:LIST:QUERY" bizActivity="CUSTOMER_GROWTH_REWARD_LIST_QUERY">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.customer.server.customer.component.query.CustomerGrowthRewardListQuery"/>
                <node type="com.stbella.customer.server.customer.component.assembler.CustomerGrowthRewardListResultAssembler"/>
            </stream>
        </step>

    </flow>

</snowballFlow>
