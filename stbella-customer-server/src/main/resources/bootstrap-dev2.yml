server:
  port: 8080 #服务端口

spring:
  application:
    name: customer-center
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  cloud:
    nacos:
      discovery:
        server-addr: https://nacos-test.primecare.top
        namespace: 5c413e61-cbec-493b-b16f-e0aca5c90ac3
        username: test
        password: test@primecare
        group: DEFAULT_GROUP
      config:
        server-addr: https://nacos-test.primecare.top
        namespace: 5c413e61-cbec-493b-b16f-e0aca5c90ac3
        username: test
        password: test@primecare
        prefix: customer-config
        file-extension: yml
        shared-configs:
          - { data-id: customer-config.yml, refresh: true }
        group: DEFAULT_GROUP
