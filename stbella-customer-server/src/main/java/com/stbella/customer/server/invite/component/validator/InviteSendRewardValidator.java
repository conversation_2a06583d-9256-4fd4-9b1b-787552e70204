package com.stbella.customer.server.invite.component.validator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.req.StreamQueryReq;
import com.stbella.asset.api.res.AccountStreamDto;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.constant.FlowConstant;
import com.stbella.customer.server.ecp.vo.InviteInfoVO;
import com.stbella.customer.server.invite.entity.InviteRewardRecordPO;
import com.stbella.customer.server.invite.enums.InviteRewardSceneEnum;
import com.stbella.customer.server.invite.req.InviteRewardRecordRequest;
import com.stbella.customer.server.invite.service.InviteRewardRecordService;
import com.stbella.customer.server.manager.AssetManager;
import com.stbella.customer.server.manager.OrderManager;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.order.month.res.GoodsNewVO;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.store.core.enums.BrandEnum;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Slf4j
@Component
@SnowballComponent(name = "InviteSendRewardValidator", desc = "邀请奖励发放校验")
public class InviteSendRewardValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private InviteRewardRecordService inviteRewardRecordService;

    @Resource
    private OrderManager orderManager;

    @Resource
    private AssetManager assetManager;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(FlowConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        InviteInfoVO inviteInfoVO = bizContext.getAttribute(InviteInfoVO.class);
        if (Objects.isNull(inviteInfoVO.getBrandType())) {
            bizContext.setAttribute(FlowConstant.CONTINUE, false);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "老的绑定关系，不符合本次奖励发放");
        }

        // 查看奖励是否发放过
        InviteRewardRecordRequest request = bizContext.getAttribute(InviteRewardRecordRequest.class);

        InviteRewardRecordRequest query = InviteRewardRecordRequest.builder()
            .basicId(request.getBasicId())
            .inviteeBasicId(request.getInviteeBasicId())
            .status(request.getStatus())
            .bizType(request.getBizType())
            .scene(request.getScene())
            .build();

        List<InviteRewardRecordPO> inviteRewardRecordPOList = inviteRewardRecordService.queryRewardRecordList(query);

        if (CollectionUtil.isNotEmpty(inviteRewardRecordPOList)) {
            bizContext.setAttribute(FlowConstant.CONTINUE, false);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "该类型奖励已发放，不能重复发放");
        }

        // 校验打卡
        if (request.getScene().equals(InviteRewardSceneEnum.INVITE_TO_STORE.code())) {
            try {
                checkSignStore(inviteInfoVO.getBasicId());
            } catch (BusinessException e) {
                bizContext.setAttribute(FlowConstant.CONTINUE, false);
                throw e;
            }
        }

        // 校验订单
        if (request.getScene().equals(InviteRewardSceneEnum.INVITE_SIGN.code())) {
            try {
                checkAddInviteReward(inviteInfoVO.getBasicId(), inviteInfoVO.getParentBasicId());
                checkOrder(request.getExtend());
            } catch (BusinessException e) {
                bizContext.setAttribute(FlowConstant.CONTINUE, false);
                throw e;
            }
        }
    }

    private void checkOrder(String orderSn) {
        if (StringUtils.isBlank(orderSn)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单不存在，无法发放奖励");
        }

        boolean flag = false;
        OrderMainInfo orderMainInfo = orderManager.queryOrderMainInfoByOrderSn(orderSn);
        if (Objects.isNull(orderMainInfo)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单不存在，无法发放奖励");
        }

        // 判断订单类型, 只有标准月子订单/小月子订单/小月龄订单才可以发放奖励
        if (orderMainInfo.getVersion().compareTo(new BigDecimal(3)) >= 0) {
            // 新的订单
            List<GoodsNewVO> orderGoodsList = orderManager.queryOrderGoodsListByOrderId(
                orderMainInfo.getOrderId());
            if (CollectionUtil.isNotEmpty(orderGoodsList)) {
                List<Integer> goodsTypeList = Arrays.asList(0, 1, 28);
                long count = orderGoodsList.stream()
                    .filter(i -> goodsTypeList.contains(i.getGoodsType()))
                    .count();

                if (count > 0L) {
                    flag = true;
                }
            }
        } else {
            List<Integer> orderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(),
                OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode());
            if (orderTypeList.contains(orderMainInfo.getOrderType())) {
                flag = true;
            }
        }

        if (!flag) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单类型不符合，无法发放奖励");
        }
    }

    private void checkSignStore(Long basicId) {
        StreamQueryReq req = new StreamQueryReq();
        req.setUserId(basicId.toString());
        req.setTradeTypes(Collections.singleton(TradeType.AVAILABLE_STORE_SIGN.getCode()));

        AccountStreamDto assetDto = assetManager.getAsset(req);
        if (Objects.nonNull(assetDto)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "被邀请人打卡奖励已发放过，无法再次发放奖励");
        }
    }

    private void checkAddInviteReward(Long basicId, Long parentBasicId) {
        StreamQueryReq req = new StreamQueryReq();
        req.setUserId(parentBasicId.toString());
        req.setTradeTypes(Collections.singleton(TradeType.AVAILABLE_RECOMMEND_ORDER_PAYED.getCode()));
        req.setPageNum(1);
        req.setPageSize(10000);

        PageVO<AccountStreamDto> accountStreamDtoPageVO = assetManager.getAssets(req);
        if (Objects.nonNull(accountStreamDtoPageVO)) {
            for (AccountStreamDto dto : accountStreamDtoPageVO.getList()) {
                if (StringUtils.isNotBlank(dto.getExt())) {
                    JSONObject ext = JSONUtil.parseObj(dto.getExt());
                    if (basicId.equals(ext.getLong("basic_uid"))) {
                        throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "被邀请人已获得过签约奖励，无法再次发放");
                    }
                }
            }
        }
    }
}
