package com.stbella.customer.server.scrm.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.customer.server.activity.request.ActivitySignupEmailRequest;
import com.stbella.customer.server.activity.service.AppointmentBaseService;
import com.stbella.customer.server.cts.request.SCRMClockStoreRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.customer.request.growth.CustomerProductionGiftRequest;
import com.stbella.customer.server.scrm.dto.ScrmCustomerBirthDTO;
import com.stbella.customer.server.scrm.dto.ScrmDistributionCustomerDTO;
import com.stbella.customer.server.scrm.dto.ScrmPulsarMessageDTO;
import com.stbella.customer.server.scrm.enums.ScrmPulsarMessageStatusEnum;
import com.stbella.customer.server.scrm.enums.ScrmPulsarMessageTypeEnum;
import com.stbella.customer.server.scrm.manager.ScrmApiConfig;
import com.stbella.customer.server.scrm.manager.ScrmManager;
import com.stbella.customer.server.scrm.request.AccountInfoRequest;
import com.stbella.customer.server.scrm.request.ContactInfoRequest;
import com.stbella.customer.server.scrm.request.CustomerStatusChangeEventRequest;
import com.stbella.customer.server.scrm.request.OpportunityTempRequest;
import com.stbella.customer.server.scrm.request.ScrmCheckinStoreRequest;
import com.stbella.customer.server.scrm.request.ScrmCustomerCreateRequest;
import com.stbella.customer.server.scrm.request.ScrmOpportunityActivityRecordRequest;
import com.stbella.customer.server.scrm.request.ScrmUserStoreConfigOperateRequest;
import com.stbella.customer.server.scrm.request.ServiceOpportunityWinRequest;
import com.stbella.customer.server.scrm.service.AdsMarketingService;
import com.stbella.customer.server.scrm.service.ScrmBusinessOpportunityService;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.scrm.service.ScrmOpportunityStatusRecordService;
import com.stbella.customer.server.scrm.service.ScrmUserService;
import com.stbella.customer.server.scrm.service.ScrmUserStoreConfigService;
import com.stbella.customer.server.scrm.vo.ScrmUserInfoVO;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import com.stbella.redis.service.RedisService;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ScrmConsumer {

    // scrm消费者消息id锁
    private static final String SCRM_CONSUMER_MESSAGE_ID_REDIS_LOCK  = "scrm_consumer:message_id:%s";

    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private ScrmUserStoreConfigService scrmUserStoreConfigService;

    @Resource
    private ScrmUserService scrmUserService;

    @Resource
    private RedisService redisService;

    @Resource
    private ScrmOpportunityStatusRecordService scrmOpportunityStatusRecordService;

    @Resource
    @Lazy
    private ScrmManager scrmManager;

    @Resource
    private AppointmentBaseService appointmentBaseService;

    @Resource
    private AdsMarketingService adsMarketingService;

    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/scrm_notify", subscriptionName = "scrm_notify", subscriptionType = SubscriptionType.Shared)
    public void revicer(Message message) {
        String messageData = new String(message.getData());
        String messageId = String.valueOf(message.getMessageId());

        if (StringUtils.isBlank(messageData)) {
            log.error("scrmPulsarConsumer接收到空白消息, 消息id:{}", messageId);
        }

        ScrmPulsarMessageDTO pulsarMessageDTO = JSONUtil.toBean(messageData, ScrmPulsarMessageDTO.class);
        if (ObjectUtil.isEmpty(pulsarMessageDTO)) {
            log.error("scrmPulsarConsumer接收到空白消息, 消息id:{}", messageId);
        }

        String redisKey = String.format(SCRM_CONSUMER_MESSAGE_ID_REDIS_LOCK, messageId);

        try {
            ScrmPulsarMessageTypeEnum messageTypeEnum = ScrmPulsarMessageTypeEnum.of(
                pulsarMessageDTO.getType());
            log.info("scrmPulsarConsumer:消息id:{}, 消息类型id:{}, 消息类型名称:{}, 消息内容:{}",
                messageId,
                pulsarMessageDTO.getType(), messageTypeEnum.getDesc(),
                pulsarMessageDTO.getMessage());

            // 先进行加锁，防止消息执行时间过长，系统自动重试
            Integer messageStatus = redisService.getCacheObject(redisKey);
            if (ObjectUtil.isNotNull(messageStatus)) {
                if (messageStatus.equals(ScrmPulsarMessageStatusEnum.PROCESSING.getCode())) {
                    log.error("scrmPulsarConsumer消息正在处理中，无需进行重试, 消息id:{}", messageId);
                    throw new BusinessException("消息正在处理中，无需进行重试, 消息id:" + messageId);
                }

                if (messageStatus.equals(ScrmPulsarMessageStatusEnum.FINISHED.getCode())) {
                    log.error("scrmPulsarConsumer消息已经处理完毕，无需重试, 消息id:{}", messageId);
                    return;
                }
            }

            redisService.setCacheObject(redisKey, ScrmPulsarMessageStatusEnum.PROCESSING.getCode(), 30L, TimeUnit.MINUTES);

            switch (messageTypeEnum) {
                case ACCOUNT_INFO_UPDATE:
                    // 客户信息变更
                    AccountInfoRequest accountInfoRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), AccountInfoRequest.class);
                    Result<Integer> accountInfoResult = scrmCustomerService.scrm2CustomPhp(
                        accountInfoRequest);
                    log.info("scrmPulsarConsumer:客户信息变更执行结果:{}", accountInfoResult);
                    break;
                case FOLLOW_UP_RECORD_UPDATE:
                    // 跟进记录变更
                    ScrmOpportunityActivityRecordRequest activityRecordRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ScrmOpportunityActivityRecordRequest.class);
                    scrmBusinessOpportunityService.updateFollowUpRecord(activityRecordRequest);
                    log.info("scrmPulsarConsumer:跟进记录变更执行完成");
                    break;
                case CHECKIN_STORE_CHANGE:
                    // scrm新增/编辑补卡记录
                    ScrmCheckinStoreRequest checkinStoreRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ScrmCheckinStoreRequest.class);
                    scrmCustomerService.editScrmCheckinStoreRecord(checkinStoreRequest);
                    log.info("scrmPulsarConsumer:scrm新增/编辑补卡记录执行完成");
                    break;
                case CONTACT_INFO_UPDATE:
                    // 联系人信息同步
                    List<ContactInfoRequest> contactInfoRequests = JSONUtil.toList(
                        pulsarMessageDTO.getMessage(), ContactInfoRequest.class);
                    scrmCustomerService.contactUpdate(contactInfoRequests);
                    log.info("scrmPulsarConsumer:联系人信息同步执行完成");
                    break;
                case OPPORTUNITY_ADD:
                    // 创建商机
                    SCRMOpportunityRequest opportunityAddRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMOpportunityRequest.class);
                    scrmBusinessOpportunityService.add(opportunityAddRequest);
                    log.info("scrmPulsarConsumer:创建商机执行完成");
                    break;
                case OPPORTUNITY_DEL:
                    // 删除商机
                    SCRMOpportunityRequest opportunityDelRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMOpportunityRequest.class);
                    scrmBusinessOpportunityService.del(opportunityDelRequest);
                    log.info("scrmPulsarConsumer:删除商机执行完成");
                    break;
                case OPPORTUNITY_UPDATE:
                    SCRMOpportunityRequest opportunityUpdateRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMOpportunityRequest.class);
                    scrmBusinessOpportunityService.updateOpportunity(opportunityUpdateRequest);
                    log.info("scrmPulsarConsumer:更新商机执行完成");
                    break;
                case DISTRIBUTION_CUSTOMER_TEAM_MEMBER:
                    // 客户分配团队成员
                    List<ScrmDistributionCustomerDTO> distributionCustomerRequest = JSONUtil.toList(
                        pulsarMessageDTO.getMessage(), ScrmDistributionCustomerDTO.class);
                    Result<Long> distributionCustomerResult = scrmCustomerService.distributionCustomer(
                        distributionCustomerRequest);
                    log.info("scrmPulsarConsumer:客户分配团队成员执行结果:{}", distributionCustomerResult);
                    break;
                case SALE_STORE_CONFIG_UPDATE:
                    // 销售与门店编辑
                    ScrmUserStoreConfigOperateRequest scrmUserStoreConfigOperateRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ScrmUserStoreConfigOperateRequest.class);
                    scrmUserStoreConfigService.operateScrmUserStoreConfig(
                        scrmUserStoreConfigOperateRequest);
                    log.info("scrmPulsarConsumer:销售与门店编辑执行完毕");
                    break;
                case USER_INFO_UPDATE:
                    ScrmUserInfoVO scrmUserInfoVO = JSONUtil.toBean(pulsarMessageDTO.getMessage(),
                        ScrmUserInfoVO.class);
                    scrmUserService.updateUserInfoByScrmId(scrmUserInfoVO);
                    log.info("scrmPulsarConsumer:员工信息编辑执行完毕");
                    break;
                case CUSTOMER_ADD_OPPORTUNITY:
                    SCRMOpportunityRequest scrmOpportunityRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMOpportunityRequest.class);
                    scrmBusinessOpportunityService.customerAddOpportunity(scrmOpportunityRequest);
                    log.info("scrmPulsarConsumer:400新增商机同步执行完毕");
                    break;
                case CREATE_CUSTOMER_TO_SCRM:
                    ScrmCustomerCreateRequest scrmCustomerCreateRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ScrmCustomerCreateRequest.class);
                    scrmCustomerService.createCustomer2Scrm(scrmCustomerCreateRequest);
                    log.info("scrmPulsarConsumer:创建客户到scrm执行完毕");
                    break;
                case OCC_CLOCK_STORE_SIGNIN:
                    SCRMClockStoreRequest scrmClockStoreRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMClockStoreRequest.class);
                    scrmCustomerService.clockStore(scrmClockStoreRequest);
                    log.info("scrmPulsarConsumer:同步occ客户探店打卡至scrm执行完毕");
                    break;
                case OPPORTUNITY_RECORD_TEMP_UPDATE:
                    OpportunityTempRequest opportunityTempRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), OpportunityTempRequest.class);
                    scrmOpportunityStatusRecordService.opportunityStatusRecordSync(opportunityTempRequest);
                    log.info("scrmPulsarConsumer:更新商机中间表执行完毕");
                    break;
                case MEMBER_BIRTHDAY_PRODUCTION_GIFT:
                    CustomerProductionGiftRequest customerProductionGiftRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), CustomerProductionGiftRequest.class);
                    scrmManager.handleScrmCommonPost(customerProductionGiftRequest, ScrmApiConfig.SCRM_ISLA_MEMBER_PRODUCT_GIVE);
                    log.info("scrmPulsarConsumer:会员赠送产康执行完毕");
                    break;
                case SYNC_CUSTOMER_CHILDBIRTH_ROOM:
                    ScrmCustomerBirthDTO scrmCustomerBirthDTO = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ScrmCustomerBirthDTO.class);
                    scrmCustomerService.synCustomerChildbirthRoom(scrmCustomerBirthDTO);
                    log.info("scrmPulsarConsumer:同步分娩喜报执行完毕");
                    break;
                case ACTIVITY_SIGNUP_EMAIL:
                    ActivitySignupEmailRequest activitySignupEmailRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ActivitySignupEmailRequest.class);
                    appointmentBaseService.sendEmailActivityPartSignupDetailList(activitySignupEmailRequest);
                    log.info("scrmPulsarConsumer:发送活动报名邮件执行完毕");
                    break;
                case CUSTOMER_OPPORTUNITY_STATUS_CHANGE:
                    CustomerStatusChangeEventRequest customerStatusChangeEventRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), CustomerStatusChangeEventRequest.class);
                    adsMarketingService.customerBehaviorReport(customerStatusChangeEventRequest);
                    log.info("接收到客户商机状态变更消息:{}", pulsarMessageDTO.getMessage());
                    break;
                case OPPORTUNITY_400_UPDATE:
                    SCRMOpportunityRequest opportunity400Request = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), SCRMOpportunityRequest.class);
                    scrmBusinessOpportunityService.opportunityFor400Update(opportunity400Request);
                    break;
                case OPPORTUNITY_TEAM_MEMBER_UPDATE:
                    List<ScrmDistributionCustomerDTO> opportunityTeamMemberUpdateList = JSONUtil.toList(
                        pulsarMessageDTO.getMessage(), ScrmDistributionCustomerDTO.class);
                    scrmBusinessOpportunityService.opportunityTeamMemberHandle(opportunityTeamMemberUpdateList);
                    break;
                case OPPORTUNITY_400_WIN:
                    ServiceOpportunityWinRequest serviceOpportunityWinRequest = JSONUtil.toBean(
                        pulsarMessageDTO.getMessage(), ServiceOpportunityWinRequest.class);
                    scrmBusinessOpportunityService.serviceOpportunityWinHandle(serviceOpportunityWinRequest);
                    break;
                default:
                    log.error("未知的scrm消息类型:{}", messageTypeEnum.getCode());
                    break;
            }

            log.info("scrmPulsarConsumer执行消息成功,消息id:{}, 消息内容:{}, ", messageId, messageData);
            redisService.setCacheObject(redisKey, ScrmPulsarMessageStatusEnum.FINISHED.getCode(), 30L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("执行消息失败，消息id:{}, 消息内容:{}, 失败原因:{}", messageId, messageData, e);
            redisService.setCacheObject(redisKey, ScrmPulsarMessageStatusEnum.FAILED.getCode(), 30L, TimeUnit.MINUTES);
        }
    }
}
