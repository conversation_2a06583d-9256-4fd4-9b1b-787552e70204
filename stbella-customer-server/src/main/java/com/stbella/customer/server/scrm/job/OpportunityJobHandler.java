package com.stbella.customer.server.scrm.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.customer.server.scrm.entity.ScrmBusinessOpportunityPO;
import com.stbella.customer.server.scrm.entity.ScrmConfigPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.enums.ScrmConfigBizTypeEnum;
import com.stbella.customer.server.scrm.manager.SmsManager;
import com.stbella.customer.server.scrm.request.LocalQwMsgRequest;
import com.stbella.customer.server.scrm.request.OpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmTeamMemberRecordRequest;
import com.stbella.customer.server.scrm.request.ServiceOpportunityQuery;
import com.stbella.customer.server.scrm.service.ScrmBusinessOpportunityService;
import com.stbella.customer.server.scrm.service.ScrmConfigService;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.scrm.service.ScrmStaffPhoneConfigService;
import com.stbella.customer.server.scrm.service.ScrmTeamMemberRecordService;
import com.stbella.customer.server.scrm.service.ScrmUserService;
import com.stbella.customer.server.scrm.service.XsyScrmService;
import com.stbella.customer.server.scrm.vo.ScrmUserVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OpportunityJobHandler {

    @Resource
    private ScrmConfigService scrmConfigService;

    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;

    @Resource
    private ScrmTeamMemberRecordService scrmTeamMemberRecordService;

    @Resource
    private ScrmUserService scrmUserService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private ScrmStaffPhoneConfigService scrmStaffPhoneConfigService;

    @Resource
    private XsyScrmService xsyScrmService;

    @Resource
    private SmsManager smsManager;

    /**
     * 400商机阶段推进
     */
    @XxlJob("serviceOpportunityStageChange")
    public void serviceOpportunityStageChange() {
        String params = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(params)) {
            return;
        }

        DateTime date = DateUtil.date();

        JSONObject paramsObject = JSONUtil.parseObj(params);
        Integer day = paramsObject.getInt("day");
        String dateStr = paramsObject.getStr("date");
        if (StringUtils.isNotBlank(dateStr)) {
            date = DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
        }

        ScrmConfigBizTypeEnum stageEnum = convertStageEnumByDay(day);
        if (Objects.isNull(stageEnum)) {
            return;
        }

        ScrmConfigPO stageConfig = scrmConfigService.queryConfigByBizType(stageEnum.getCode());
        if (Objects.isNull(stageConfig)) {
            return;
        }

        Long stageId = Long.valueOf(stageConfig.getBizContent());

        ServiceOpportunityQuery query = buildQuery(day, date);
        if (Objects.isNull(query)) {
            return;
        }

        List<ScrmBusinessOpportunityPO> opportunityList = scrmBusinessOpportunityService.queryServiceOpportunityList(query);
        if (CollectionUtil.isNotEmpty(opportunityList)) {
            opportunityList.forEach(opportunity -> {
                opportunity.setSaleStageId(stageId);

                scrmBusinessOpportunityService.updateById(opportunity);

                OpportunityRequest opportunityUpdateRequest = new OpportunityRequest();
                opportunityUpdateRequest.setId(opportunity.getScrmId());
                opportunityUpdateRequest.setAccountId(opportunity.getCustomerId());
                opportunityUpdateRequest.setOwnerId(opportunity.getOwnerId());
                opportunityUpdateRequest.setSaleStageId(opportunity.getSaleStageId());

                xsyScrmService.opportunityEdit(opportunityUpdateRequest);

                if (stageEnum.equals(ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_RELEASED)) {
                    // 释放400商机，需要移除客户的团队成员
                    removeCustomerTeamMember(opportunity);
                }
            });
        }
    }

    @XxlJob("serviceOpportunityRecoverRemind")
    public void serviceOpportunityRecoverRemind() {
        String params = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(params)) {
            return;
        }

        DateTime date = DateUtil.date();

        JSONObject paramsObject = JSONUtil.parseObj(params);
        String dateStr = paramsObject.getStr("date");
        if (StringUtils.isNotBlank(dateStr)) {
            date = DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
        }

        ScrmConfigBizTypeEnum stageEnum = ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_WAIT_3_DAYS;

        ScrmConfigPO stageConfig = scrmConfigService.queryConfigByBizType(stageEnum.getCode());
        if (Objects.isNull(stageConfig)) {
            return;
        }

        Long stageId = Long.valueOf(stageConfig.getBizContent());

        ServiceOpportunityQuery query = new ServiceOpportunityQuery();
        query.setStageId(stageId);
        query.setEndTime(DateUtil.offsetDay(date, -6));

        List<ScrmBusinessOpportunityPO> opportunityList = scrmBusinessOpportunityService.queryServiceOpportunityList(query);
        if (CollectionUtil.isNotEmpty(opportunityList)) {
            // 需要提醒销售，跟进客户
            opportunityList.forEach(this::remindSale);
        }
    }

    private ServiceOpportunityQuery buildQuery(Integer day, Date date) {
        ServiceOpportunityQuery query = new ServiceOpportunityQuery();

        ScrmConfigBizTypeEnum stageEnum;
        if (day.equals(1)) {
            // 标记等待1天的
            stageEnum = ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_INITIAL_COMMUNICATION;
            date = DateUtil.offsetDay(date, -1);
        } else if (day.equals(3)) {
            // 标记等待3天的
            stageEnum = ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_WAIT_1_DAY;
            date = DateUtil.offsetDay(date, -3);
        } else if (day.equals(7)) {
            // 标记等待7天的
            stageEnum = ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_WAIT_3_DAYS;
            date = DateUtil.offsetDay(date, -7);
        } else {
            return null;
        }

        ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(stageEnum.getCode());
        if (Objects.isNull(scrmConfigPO)) {
            return null;
        }

        query.setStageId(Long.valueOf(scrmConfigPO.getBizContent()));
        query.setEndTime(date);

        return query;
    }

    private ScrmConfigBizTypeEnum convertStageEnumByDay(Integer day) {
        if (day.equals(1)) {
            // 标记等待1天的
            return ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_WAIT_1_DAY;
        } else if (day.equals(3)) {
            // 标记等待3天的
            return ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_WAIT_3_DAYS;
        } else if (day.equals(7)) {
            // 标记等待7天的
            return ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_RELEASED;
        } else {
            return null;
        }
    }

    private void removeCustomerTeamMember(ScrmBusinessOpportunityPO opportunity) {
        List<ScrmTeamMemberRecordPO> opportunityTeamMemberRecordList = scrmTeamMemberRecordService.queryTeamMemberRecordList(opportunity.getScrmId(), 3);
        if (CollectionUtil.isEmpty(opportunityTeamMemberRecordList)) {
            return;
        }

        // 找出销售人员
        Result<List<ScrmUserVO>> scrmUserResult = scrmUserService.queryByScrmIds(opportunityTeamMemberRecordList.stream().map(ScrmTeamMemberRecordPO::getUserId).collect(Collectors.toList()));
        if (!scrmUserResult.getSuccess()) {
            XxlJobHelper.log("未找到销售人员, 商机id:{}", opportunity.getScrmId());
            return;
        }

        Set<Long> scrmUserIdList = scrmUserResult.getData().stream()
            .filter(i -> i.getSales().equals(1))
            .map(ScrmUserVO::getScrmId)
            .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(scrmUserIdList)) {
            XxlJobHelper.log("未找到销售人员, 商机id:{}", opportunity.getScrmId());
            return;
        }

        scrmUserIdList.forEach(scrmUserId -> {
            xsyScrmService.removeTeamMember(ScrmTeamMemberRecordRequest.builder()
                .type(1)
                .userId(scrmUserId)
                .recordId(opportunity.getCustomerId())
                .build());
        });

        List<ScrmTeamMemberRecordPO> accountTeamMemberRecordList = scrmTeamMemberRecordService.queryTeamMemberRecordList(opportunity.getCustomerId(), 1);
        List<ScrmTeamMemberRecordPO> needRemoveList = accountTeamMemberRecordList.stream()
            .filter(i -> scrmUserIdList.contains(i.getUserId()))
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(needRemoveList)) {
            needRemoveList.forEach(po -> {
                scrmTeamMemberRecordService.removeById(po.getId());
            });
        }
    }

    private void remindSale(ScrmBusinessOpportunityPO opportunity) {
        List<ScrmTeamMemberRecordPO> opportunityTeamMemberRecordList = scrmTeamMemberRecordService.queryTeamMemberRecordList(opportunity.getScrmId(), 3);
        if (CollectionUtil.isEmpty(opportunityTeamMemberRecordList)) {
            return;
        }

        // 找出销售人员
        Result<List<ScrmUserVO>> scrmUserResult = scrmUserService.queryByScrmIds(opportunityTeamMemberRecordList.stream().map(ScrmTeamMemberRecordPO::getUserId).collect(Collectors.toList()));
        if (!scrmUserResult.getSuccess()) {
            XxlJobHelper.log("未找到销售人员, 商机id:{}", opportunity.getScrmId());
            return;
        }

        List<ScrmUserVO> scrmUserList = scrmUserResult.getData().stream()
            .filter(i -> i.getSales().equals(1))
            .collect(Collectors.toList());

        scrmUserList.forEach(scrmUser -> {
            ScrmCustomerPO scrmCustomerPO = scrmCustomerService.getByScrmCustomerId(opportunity.getCustomerId());
            if (Objects.nonNull(scrmCustomerPO)) {
                LocalQwMsgRequest localQwMsgRequest = new LocalQwMsgRequest();
                localQwMsgRequest.setCustomerName("");
                localQwMsgRequest.setTouser(Collections.singletonList(scrmUser.getPhone()));
                localQwMsgRequest.setContent(String.format("您好，您的客资【%s】月子商机还没有创建，该客户线索将在明天系统视为放弃，您无创建权限，请尽快创建~", StringUtils.isNotBlank(scrmCustomerPO.getName()) ? scrmCustomerPO.getName() : scrmCustomerPO.getPhone()));
                smsManager.localBatchSendMsg(localQwMsgRequest);
            }
        });
    }
}
