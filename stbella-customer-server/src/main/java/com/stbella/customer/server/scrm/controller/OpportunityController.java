package com.stbella.customer.server.scrm.controller;

import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.customer.server.cts.request.BatchOpportunityRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunitiesWinOrderRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.scrm.dto.ScrmDistributionCustomerDTO;
import com.stbella.customer.server.scrm.producer.ScrmProducer;
import com.stbella.customer.server.scrm.request.OpportunityTempRequest;
import com.stbella.customer.server.scrm.request.ScrmCrateOpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmCrateWinOpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmOpportunityActivityRecordRequest;
import com.stbella.customer.server.scrm.request.ScrmUserAssignAccountTempQuery;
import com.stbella.customer.server.scrm.request.ServiceOpportunityWinRequest;
import com.stbella.customer.server.scrm.service.ScrmBusinessOpportunityService;
import com.stbella.customer.server.scrm.service.ScrmUserAssignAccountTempRecordService;
import com.stbella.customer.server.scrm.vo.ScrmTeamUserOpportunityStatisticsVO;
import com.stbella.customer.server.scrm.vo.ScrmUserOpportunityStatisticsVO;
import com.stbella.redisson.DistributedLocker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

@Validated
@Api(tags = "商机")
@RestController
@RequestMapping("/customer/scrm/opportunity")
@Slf4j
public class OpportunityController {

    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;
    @Resource
    private DistributedLocker redisson;
    @Resource
    private ScrmProducer scrmProducer;
    @Resource
    private ScrmUserAssignAccountTempRecordService scrmUserAssignAccountTempRecordService;

    /**
     * 创建/更新商机
     * @return
     */
    @PostMapping("/register")
    @ApiOperation(value = "更新商机")
    public Result registerOpportunity(@RequestBody SCRMOpportunityRequest scrmOpportunityRequest){
        //scrmBusinessOpportunityService.updateOpportunity(scrmOpportunityRequest);
        scrmProducer.opportunityUpdate(scrmOpportunityRequest);
        return Result.success();
    }

    /**
     * 创建/更新商机
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "创建商机")
    public Result add(@RequestBody SCRMOpportunityRequest scrmOpportunityRequest){
        //scrmBusinessOpportunityService.add(scrmOpportunityRequest);
        scrmProducer.opportunityAdd(scrmOpportunityRequest);
        return Result.success();
    }


    /**
     * 删除商机
     * @return
     */
    @PostMapping("/del")
    @ApiOperation(value = "删除商机")
    public Result del(@RequestBody SCRMOpportunityRequest scrmOpportunityRequest){
        //scrmBusinessOpportunityService.del(scrmOpportunityRequest);
        scrmProducer.opportunityDelete(scrmOpportunityRequest);
        return Result.success();
    }



    /**
     * 商机赢单
     * @return
     */
    @PostMapping("/opportunitiesAutoWinOrders")
    @ApiOperation(value = "商机自动赢单（订单支付金额>=50%）")
    public Result opportunitiesAutoWinOrders(@RequestBody SCRMOpportunitiesWinOrderRequest scrmOpportunitiesWinOrderRequest){
        scrmBusinessOpportunityService.opportunitiesAutoWinOrders(scrmOpportunitiesWinOrderRequest);
        return Result.success();
    }

    /**
     * 保存跟进记录
     * @return
     */
    @PostMapping("/updateFollowUpRecord")
    @ApiOperation(value = "保存跟进记录")
    public Result updateFollowUpRecord(@RequestBody ScrmOpportunityActivityRecordRequest scrmOpportunityActivityRecordRequest){
        //return scrmBusinessOpportunityService.updateFollowUpRecord(scrmOpportunityActivityRecordRequest);
        scrmProducer.followUpRecordUpdate(scrmOpportunityActivityRecordRequest);
        return Result.success();
    }


    /**
     * 数据同步-创建标准月子商机
     * @return
     */
    @PostMapping("/createOpportunity")
    @ApiOperation(value = "数据同步-创建标准月子商机")
    public Result createOpportunity(@RequestBody ScrmCrateOpportunityRequest scrmCrateOpportunityRequest){
        scrmBusinessOpportunityService.createOpportunity(scrmCrateOpportunityRequest,null);
        return Result.success();
    }

    /**
     * 数据同步-创建标准月子商机并赢单
     * @return
     */
    @PostMapping("/createWinOpportunity")
    @ApiOperation(value = "数据同步-创建标准月子商机并赢单")
    public Result createWinOpportunity(@RequestBody ScrmCrateWinOpportunityRequest scrmCrateWinOpportunityRequest){
        scrmBusinessOpportunityService.createWinOpportunity(scrmCrateWinOpportunityRequest);
        return Result.success();
    }

    /**
     * 创建/更新商机
     * @return
     */
    @PostMapping("/batchInsertOpportunity")
    @ApiOperation(value = "批量插入商机")
    public Result batchInsertOpportunity(@RequestBody List<BatchOpportunityRequest> scrmOpportunityRequests){
        scrmBusinessOpportunityService.batchInsertOpportunity(scrmOpportunityRequests);
        return Result.success();
    }

    /**
     * 创建/更新商机
     * @return
     */
    @GetMapping("/updateAllOpportunityIntermediateTable")
    @ApiOperation(value = "判断目前所有的商机阶段是否完整以及新增字段是否完成")
    public Result updateAllOpportunityIntermediateTable(){
        scrmBusinessOpportunityService.updateAllOpportunityIntermediateTable();
        return Result.success();
    }

    /**
     *
     * @param scrmId
     * @return
     */
    @GetMapping("/updateOpportunityByScrmId")
    @ApiOperation(value = "通过scrm商机id新增或修改商机信息")
    public Result updateOpportunityByScrmId(@RequestParam("scrmId") Long scrmId) {
        scrmBusinessOpportunityService.updateOpportunityByScrmId(scrmId);
        return Result.success();
    }

    @PostMapping("/queryUserByDistributionTime")
    @ApiOperation(value = "销售个人商机统计")
    public Result<PageVO<ScrmUserOpportunityStatisticsVO>> queryUserByDistributionTime(@Valid  @RequestBody ScrmUserAssignAccountTempQuery scrmUserAssignAccountTempQuery) {
        return scrmUserAssignAccountTempRecordService.queryUserByDistributionTime(scrmUserAssignAccountTempQuery);
    }

    @PostMapping("/queryTeamUserByDistributionTime")
    @ApiOperation(value = "销售团队销售商机统计")
    public Result<PageVO<ScrmTeamUserOpportunityStatisticsVO>> queryTeamUserByDistributionTime(@Valid @RequestBody ScrmUserAssignAccountTempQuery scrmUserAssignAccountTempQuery) {
        return scrmUserAssignAccountTempRecordService.queryTeamUserByDistributionTime(scrmUserAssignAccountTempQuery);
    }

    @ApiOperation(value = "400新增商机同步")
    @PostMapping("/customerAddOpportunity")
    public Result customerAddOpportunity(@RequestBody SCRMOpportunityRequest request) {
        scrmProducer.customerAddOpportunity(request);
        return Result.success();
    }

    @ApiOperation(value = "商机跟进记录临时表同步")
    @PostMapping("/opportunityRecordTempRsync")
    public Result opportunityRecordTempRsync(@RequestBody OpportunityTempRequest request) {
        scrmProducer.opportunityRecordTempUpdate(request);
        return Result.success();
    }

    @ApiOperation(value = "400商机变更")
    @PostMapping("/opportunityFor400Update")
    public Result opportunityFor400Update(@RequestBody SCRMOpportunityRequest request) {
        scrmProducer.opportunityFor400Update(request);
        return Result.success();
    }

    @ApiOperation(value = "商机团队成员变更")
    @PostMapping("/opportunityTeamMemberHandle")
    public Result opportunityTeamMemberHandle(@RequestBody List<ScrmDistributionCustomerDTO> request) {
        scrmProducer.opportunityTeamMemberHandle(request);
        return Result.success();
    }

    @ApiOperation(value = "400商机赢单处理")
    @PostMapping("/serviceOpportunityWinHandle")
    public Result serviceOpportunityWinHandle(@RequestBody ServiceOpportunityWinRequest request) {
        scrmProducer.serviceOpportunityWinHandle(request);
        return Result.success();
    }
}
