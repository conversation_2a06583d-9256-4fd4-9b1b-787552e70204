package com.stbella.customer.server.ecp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.core.result.Result;
import com.stbella.customer.server.activity.entity.CustomerGenericActivityAppointmentPO;
import com.stbella.customer.server.activity.service.CustomerGenericActivityAppointmentService;
import com.stbella.customer.server.client.RuleLinkClient;
import com.stbella.customer.server.ecp.entity.EcpStorePO;
import com.stbella.customer.server.ecp.entity.TabMiniProgramSignInPO;
import com.stbella.customer.server.ecp.enums.StoreTypeEnum;
import com.stbella.customer.server.ecp.service.CfgStoreService;
import com.stbella.customer.server.ecp.service.HeOrderService;
import com.stbella.customer.server.ecp.service.TabMiniProgramSignInService;
import com.stbella.customer.server.ecp.service.dubbo.FirstCheckInContractService;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首签首访判断服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
public class FirstCheckInContractServiceImpl implements FirstCheckInContractService {

    @Autowired
    private TabMiniProgramSignInService signInService;

    @Autowired
    private HeOrderService heOrderService;

    @Autowired
    private CfgStoreService storeService;

    @Autowired
    private CustomerGenericActivityAppointmentService customerGenericActivityAppointmentService;

    @Resource
    private RuleLinkClient ruleLinkClient;


    @Override
    public Result<Boolean> checkFirstCheckInContract(Integer basicUid) {
        long startTime = System.currentTimeMillis();
        log.info("开始检查首签首访条件, basicUid: {}", basicUid);

        // 参数校验
        if (basicUid == null) {
            log.warn("参数basicUid为null，返回false");
            return Result.success(false);
        }

        try {
            // 获取用户所有ECP订单记录，如果有订单直接返回false
            log.debug("查询用户订单记录, basicUid: {}", basicUid);
            Integer count = heOrderService.queryAllOrderByBasicId(basicUid.longValue()).size();

            log.info("用户订单数量: {}, basicUid: {}", count, basicUid);
            if (count > 0) {
                log.info("用户已有订单，不符合首签首访条件, basicUid: {}, 订单数量: {}", basicUid, count);
                return Result.success(false);
            }

            // 获取用户所有打卡记录
            log.debug("查询用户打卡记录, basicUid: {}", basicUid);
            List<TabMiniProgramSignInPO> signInRecords = signInService.list(
                    new LambdaQueryWrapper<TabMiniProgramSignInPO>()
                            .eq(TabMiniProgramSignInPO::getBasicUid, basicUid)
                            .orderByAsc(TabMiniProgramSignInPO::getSignInTime)
            );

            log.info("用户打卡记录数量: {}, basicUid: {}", signInRecords.size(), basicUid);
            if (signInRecords.isEmpty()) {
                log.info("用户无打卡记录，不符合首签首访条件, basicUid: {}", basicUid);
                return Result.success(false);
            }

            // 获取涉及的门店信息
            Set<Integer> storeIds = signInRecords.stream()
                    .map(TabMiniProgramSignInPO::getStoreId)
                    .collect(Collectors.toSet());

            log.debug("涉及的门店ID: {}, basicUid: {}", storeIds, basicUid);

            List<EcpStorePO> stores = storeService.list(
                    new LambdaQueryWrapper<EcpStorePO>()
                            .in(EcpStorePO::getStoreId, storeIds)
            );

            log.debug("查询到的门店数量: {}, basicUid: {}", stores.size(), basicUid);

            // 构建门店类型映射
            Map<Integer, Integer> storeTypeMap = stores.stream()
                    .collect(Collectors.toMap(EcpStorePO::getStoreId, EcpStorePO::getType));

            log.debug("门店类型映射: {}, basicUid: {}", storeTypeMap, basicUid);

            // 情况1：首次在圣贝拉品牌门店打卡
            log.debug("检查情况1：首次在圣贝拉品牌门店打卡, basicUid: {}", basicUid);
            if (checkScenario1(signInRecords, storeTypeMap)) {
                long endTime = System.currentTimeMillis();
                log.info("符合情况1：首次在圣贝拉品牌门店打卡, basicUid: {}, 耗时: {}ms",
                        basicUid, (endTime - startTime));
                return Result.success(true);
            }

            // 情况2：用户在兄弟品牌门店有打卡记录但从未在圣贝拉打卡
            log.debug("检查情况2：用户在兄弟品牌门店有打卡记录但从未在圣贝拉打卡, basicUid: {}", basicUid);
            boolean scenario2Result = checkScenario2(signInRecords, storeTypeMap);

            if (scenario2Result) {
                long endTime = System.currentTimeMillis();
                log.info("符合情况2：用户在兄弟品牌门店有打卡记录但从未在圣贝拉打卡, basicUid: {}, 耗时: {}ms",
                        basicUid, (endTime - startTime));
                return Result.success(true);
            }

            // 情况3：客户在CustomerGenericActivityAppointmentPO表中有当天的核销记录
            log.debug("检查情况3：客户在活动预约表中有当天的核销记录, basicUid: {}", basicUid);
            boolean scenario3Result = checkScenario3(basicUid.longValue());

            if (scenario3Result) {
                long endTime = System.currentTimeMillis();
                log.info("符合情况3：客户在活动预约表中有当天的核销记录, basicUid: {}, 耗时: {}ms",
                        basicUid, (endTime - startTime));
                return Result.success(true);
            }

            log.info("不符合任何首签首访条件, basicUid: {}", basicUid);
            long endTime = System.currentTimeMillis();
            log.info("首签首访条件检查完成, basicUid: {}, 结果: false, 耗时: {}ms",
                    basicUid, (endTime - startTime));

            return Result.success(false);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("检查首签首访条件时发生异常, basicUid: {}, 耗时: {}ms",
                    basicUid, (endTime - startTime), e);
            return Result.success(false);
        }
    }

    /**
     * 检查情况1：首次在圣贝拉品牌门店打卡
     */
    private boolean checkScenario1(List<TabMiniProgramSignInPO> signInRecords,
                                   Map<Integer, Integer> storeTypeMap) {

        log.debug("情况1检查：开始查找圣贝拉门店打卡记录");

        // 找到首次在圣贝拉门店的打卡记录
        TabMiniProgramSignInPO firstSaintBellaSignIn = signInRecords.stream()
                .filter(record -> {
                    Integer storeType = storeTypeMap.get(record.getStoreId());
                    boolean isSaintBella = storeType != null && storeType.equals(StoreTypeEnum.SAINT_BELLA.code());
                    if (isSaintBella) {
                        log.debug("找到圣贝拉门店打卡记录: storeId={}, signInTime={}",
                                record.getStoreId(), record.getSignInTime());
                    }
                    return isSaintBella;
                })
                .findFirst()
                .orElse(null);

        if (firstSaintBellaSignIn == null) {
            log.debug("情况1检查：未找到圣贝拉门店打卡记录");
            return false;
        }

        log.info("情况1检查：找到首次圣贝拉门店打卡记录, storeId={}, signInTime={}",
                firstSaintBellaSignIn.getStoreId(), firstSaintBellaSignIn.getSignInTime());

        // 检查首次圣贝拉打卡时间是否为当天
        if (!isToday(firstSaintBellaSignIn.getSignInTime())) {
            log.info("情况1检查：首次圣贝拉门店打卡时间不是当天, signInTime={}", firstSaintBellaSignIn.getSignInTime());
            return false;
        }


        log.info("情况1检查：确认为首次在圣贝拉门店打卡");
        return true;
    }

    /**
     * 检查情况2：用户在兄弟品牌门店有打卡记录但从未在圣贝拉打卡
     */
    private boolean checkScenario2(List<TabMiniProgramSignInPO> signInRecords,
                                   Map<Integer, Integer> storeTypeMap) {

        log.debug("情况2检查：开始检查兄弟品牌门店打卡记录");

        // 检查是否有小贝拉/艾屿品牌门店的打卡记录
        List<TabMiniProgramSignInPO> brotherBrandSignIns = signInRecords.stream()
                .filter(record -> {
                    Integer storeType = storeTypeMap.get(record.getStoreId());
                    boolean isBrotherBrand = storeType != null &&
                            (storeType.equals(StoreTypeEnum.BABY_BELLA.code()) ||
                                    storeType.equals(StoreTypeEnum.ISLA_BELLA.code()));

                    if (isBrotherBrand) {
                        String brandName = storeType.equals(StoreTypeEnum.BABY_BELLA.code()) ? "小贝拉" : "艾屿";
                        log.debug("找到兄弟品牌门店打卡记录: storeId={}, 品牌={}, signInTime={}",
                                record.getStoreId(), brandName, record.getSignInTime());
                    }
                    return isBrotherBrand;
                })
                .collect(Collectors.toList());

        if (brotherBrandSignIns.isEmpty()) {
            log.debug("情况2检查：未找到兄弟品牌门店打卡记录");
            return false;
        }

        log.info("情况2检查：找到{}条兄弟品牌门店打卡记录", brotherBrandSignIns.size());

        // 检查是否在圣贝拉品牌门店从未打卡
        List<TabMiniProgramSignInPO> saintBellaSignIns = signInRecords.stream()
                .filter(record -> {
                    Integer storeType = storeTypeMap.get(record.getStoreId());
                    boolean isSaintBella = storeType != null && storeType.equals(StoreTypeEnum.SAINT_BELLA.code());

                    if (isSaintBella) {
                        log.debug("发现圣贝拉门店打卡记录: storeId={}, signInTime={}",
                                record.getStoreId(), record.getSignInTime());
                    }
                    return isSaintBella;
                })
                .collect(Collectors.toList());

        if (!saintBellaSignIns.isEmpty()) {
            log.info("情况2检查：发现{}条圣贝拉门店打卡记录，不符合条件", saintBellaSignIns.size());
            return false;
        }

        log.info("情况2检查：确认有兄弟品牌打卡记录且从未在圣贝拉打卡");
        return true;
    }

    /**
     * 检查情况3：客户在CustomerGenericActivityAppointmentPO表中有当天的核销记录
     */
    private boolean checkScenario3(Long basicUid) {
        log.info("情况3检查：开始查找客户活动预约核销记录, basicUid: {}", basicUid);
        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode("systemConfig");
        Map<String, Object> factObj = new HashMap<>();
        factObj.put("configCode", "firstCheckInContract");
        req.setFactObj(factObj);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(req);
        List<Integer> activityIds = new ArrayList<>();
        if (Objects.nonNull(hitRuleVo) && StringUtils.isNotBlank(hitRuleVo.getSimpleRuleValue())) {
            activityIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } else {
            return false;
        }
        try {
            // 查询该客户的所有活动预约记录
            List<CustomerGenericActivityAppointmentPO> appointmentRecords = customerGenericActivityAppointmentService.list(
                    new LambdaQueryWrapper<CustomerGenericActivityAppointmentPO>()
                            .eq(CustomerGenericActivityAppointmentPO::getBasicUid, basicUid)
                            .isNotNull(CustomerGenericActivityAppointmentPO::getVerifyTime)
                            .in(CustomerGenericActivityAppointmentPO::getActivityId,activityIds)
                            .orderByDesc(CustomerGenericActivityAppointmentPO::getVerifyTime)
            );

            log.info("情况3检查：找到{}条活动预约核销记录, basicUid: {}", appointmentRecords.size(), basicUid);

            if (appointmentRecords.isEmpty()) {
                log.info("情况3检查：未找到活动预约核销记录, basicUid: {}", basicUid);
                return false;
            }

            // 检查是否有当天的核销记录
            boolean hasTodayVerifyRecord = appointmentRecords.stream()
                    .anyMatch(record -> {
                        boolean isTodayVerify = isToday(record.getVerifyTime());
                        if (isTodayVerify) {
                            log.info("情况3检查：找到当天核销记录, basicUid: {}, verifyTime: {}, appointmentId: {}",
                                    basicUid, record.getVerifyTime(), record.getId());
                        }
                        return isTodayVerify;
                    });

            if (hasTodayVerifyRecord) {
                log.info("情况3检查：确认客户在活动预约表中有当天的核销记录, basicUid: {}", basicUid);
                return true;
            } else {
                log.info("情况3检查：客户活动预约核销记录均不是当天, basicUid: {}", basicUid);
                return false;
            }

        } catch (Exception e) {
            log.info("情况3检查：查询客户活动预约核销记录时发生异常, basicUid: {}", basicUid, e);
            return false;
        }
    }

    /**
     * 检查打卡时间是否为当天
     */
    private boolean isToday(int signInTime) {
        LocalDate signInDate = Instant.ofEpochSecond(signInTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();
        return signInDate.equals(today);
    }

    /**
     * 检查Date类型的时间是否为当天
     */
    private boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        LocalDate dateLocalDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();
        return dateLocalDate.equals(today);
    }

}