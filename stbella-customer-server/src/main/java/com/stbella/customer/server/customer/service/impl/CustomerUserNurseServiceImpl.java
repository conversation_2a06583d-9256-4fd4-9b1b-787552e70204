package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.care.server.care.enmu.RoomStateCheckInEnum;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.service.RoomStateCheckInInfoService;
import com.stbella.care.server.care.vo.room.RoomStateCheckInInfoVO;
import com.stbella.customer.server.brand.strategy.BrandStrategyService;
import com.stbella.customer.server.client.OrderClient;
import com.stbella.customer.server.client.StoreCoreClient;
import com.stbella.customer.server.customer.entity.CustomerServicePO;
import com.stbella.customer.server.customer.request.CustomerCheckOutSummaryRequest;
import com.stbella.customer.server.customer.request.CustomerUserNurseInfoRequest;
import com.stbella.customer.server.customer.service.CustomerServiceService;
import com.stbella.customer.server.customer.service.CustomerSummaryService;
import com.stbella.customer.server.customer.service.CustomerUserNurseService;
import com.stbella.customer.server.customer.vo.ClientRoomOrderVO;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO;
import com.stbella.customer.server.customer.vo.CustomerUserNurseInfoVO;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.enums.StoreTypeEnum;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.scrm.enums.BrandTypeBusinessTypeEnum;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.store.core.enums.BrandTypeEnum;
import com.stbella.store.core.vo.req.brand.StoreBrandListReq;
import com.stbella.store.core.vo.res.brand.StoreBrandListVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * customer user nurse service impl
 *
 * <AUTHOR>
 * @date 2023/11/07 10:13:34
 */
@Service
@Slf4j
public class CustomerUserNurseServiceImpl implements CustomerUserNurseService {


    private static final String PRODUCTION_APPOINTMENT_STORE_LIST = "PRODUCTION_APPOINTMENT_STORE_LIST";

    @Resource
    private TabClientService tabClientService;//ecp-tab_client

    @Resource
    private CfgStoreService cfgStoreService;//ecp-store

    @Resource
    private TabOrderService tabOrderService;//Ecp-order

    @Resource
    private TabRoomService tabRoomService;//Ecp-room

    @Resource
    private SettingPackageService settingPackageService;//ecp settingPackage

    @Resource
    private CustomerSummaryService customerSummaryService;//客户离馆小结

    @DubboReference
    private RoomStateCheckInInfoService roomStateCheckInInfoService;//房态信息

    @Resource
    private HeClientExpandPeriodService heClientExpandPeriodService;//saas -he_client_expand_period

    @Resource
    private HeUserBasicService heUserBasicService;//saas-HeUserBasic

    @Resource
    private HeOrderService heOrderService;//saas-he_order

    @Resource
    private TabWechatUserService tabWechatUserService;//Ecp-WechatUser

    @Resource
    private HeSystemConfigService heSystemConfigService;//系统配置

    @Autowired
    private BrandStrategyService brandStrategyService;
    @Resource
    private OrderClient orderClient;
    @Resource
    private CustomerServiceService customerServiceService;
    @Resource
    private StoreCoreClient storeCoreClient;


    @Override
    public CustomerUserNurseInfoVO nurseInfo(CustomerUserNurseInfoRequest req) {
        //兼容老版本
        processReq(req);
        log.info("打印req:{}",JSON.toJSONString(req));
        if (ObjectUtil.equals(req.getDamonStatus(), 1)) {
            return getExampleInfo(req);
        }
        CustomerUserNurseInfoVO nurseInfo = getNurseInfo(req);
        // 增加小月龄标识
        nurseInfo.setSmallAgeOrder(orderClient.isSmallAgeOrder(nurseInfo.getOrderNo()));
        nurseInfo.setCustomerServiceNum(this.customerServiceNum(nurseInfo.getBasicId(), req.getBrandType()));
        return nurseInfo;
    }

    @Override
    public CustomerUserNurseInfoVO nurseInfoOld(CustomerUserNurseInfoRequest req) {
        //兼容老版本
        processReq(req);
        if (ObjectUtil.equals(req.getDamonStatus(), 1)) {
            return getExampleInfo(req);
        }
        CustomerUserNurseInfoVO nurseInfoOld = getNurseInfoOld(req);
        // 增加小月龄标识
        nurseInfoOld.setSmallAgeOrder(orderClient.isSmallAgeOrder(nurseInfoOld.getOrderNo()));
        nurseInfoOld.setCustomerServiceNum(this.customerServiceNum(nurseInfoOld.getBasicId(), req.getBrandType()));
        return nurseInfoOld;
    }

    /**
     * 获取客户服务数量
     */
    @Override
    public Integer customerServiceNum(Integer basicId, Integer brandType) {
        if (Objects.isNull(basicId) || Objects.isNull(brandType)) return 0;

        // 通过品牌值获取目标门店
        StoreBrandListReq storeBrandListReq = new StoreBrandListReq();
        storeBrandListReq.setEnable(1);
        storeBrandListReq.setActive(1);
        storeBrandListReq.setBrandType(BrandTypeEnum.MAIN_BRAND.getCode());
        storeBrandListReq.setBrandVals(Collections.singletonList(brandType));

        List<StoreBrandListVO> storeBrandList = storeCoreClient.listBrandStore(storeBrandListReq);
        if (CollectionUtil.isEmpty(storeBrandList)) return 0;

        // 该品牌下所有门店
        List<StoreBrandListVO.StoreVO> stores = storeBrandList.stream()
                .filter(o -> CollectionUtil.isNotEmpty(o.getStores()))
                .flatMap(o -> o.getStores().stream())
                .collect(Collectors.toList());
        List<Long> storeIds = stores.stream().map(StoreBrandListVO.StoreVO::getStoreId).collect(Collectors.toList());
        log.info("customerServiceNum brandType:{}, storeSize:{}", brandType, storeIds.size());
        if (CollectionUtil.isEmpty(storeIds)) return 0;

        return customerServiceService.count(
                new LambdaQueryWrapper<CustomerServicePO>()
                        .eq(CustomerServicePO::getBasicId, basicId.longValue())
                        .in(CustomerServicePO::getStoreId, storeIds)
        );
    }

    /**
     * 处理参数
     *
     * @param req
     */
    private void processReq(CustomerUserNurseInfoRequest req) {
        //老版本brandType不会传
        if(req.getBrandType()==null) {
            req.setBrandType(req.getStoreType());
        }else{
            //新版本storeType不会传
            req.setStoreType(req.getBrandType());
        }
        log.info("查询会员护理信息:{}", JSON.toJSONString(req));
        if (ObjectUtil.isNull(req.getDamonStatus())) {
            req.setDamonStatus(0);
        }
        if (ObjectUtil.isNull(req.getIsOrder())) {
            req.setIsOrder(0);
        }
    }

    /**
     * 演示数据
     *
     * @return customerusernurseinfovo
     * <AUTHOR>
     * @date 2023/10/23 05:20:41
     * @since 1.0.0
     */
    private CustomerUserNurseInfoVO getExampleInfo(CustomerUserNurseInfoRequest req) {
        log.info("演示模式-request:{}",JSON.toJSONString(req));
        CustomerUserNurseInfoVO customerUserNurseInfoVO = new CustomerUserNurseInfoVO();
        customerUserNurseInfoVO.setCheckDay(10);

        CustomerUserNurseInfoVO.RoomInfo roomInfo = new CustomerUserNurseInfoVO.RoomInfo();
        roomInfo.setClientInTime(com.stbella.customer.server.util.DateUtils.addDayOrMonthOrYear(new Date(), Calendar.DAY_OF_MONTH, -10, "yyyy-MM-dd"));
        roomInfo.setClientOutTime(com.stbella.customer.server.util.DateUtils.addDayOrMonthOrYear(new Date(), Calendar.DAY_OF_MONTH, 20, "yyyy-MM-dd"));
        customerUserNurseInfoVO.setRoom(roomInfo);

        customerUserNurseInfoVO.setIsOrder(req.getIsOrder());
        if(req.getStoreType() == 0){
            customerUserNurseInfoVO.setStoreType("圣贝拉");
            customerUserNurseInfoVO.setPackageName("经典套餐28天");
            customerUserNurseInfoVO.setStoreName("杭州柏悦圣贝拉");
        }
        if(req.getStoreType() == 1){
            customerUserNurseInfoVO.setStoreType("小贝拉");
            customerUserNurseInfoVO.setPackageName("星夜摩天轮套餐56天");
            customerUserNurseInfoVO.setStoreName("杭州凯宾斯基小贝拉");
        }
        if(req.getStoreType() == 100){
            customerUserNurseInfoVO.setStoreType("艾屿");
            customerUserNurseInfoVO.setPackageName("星夜摩天轮套餐56天");
            customerUserNurseInfoVO.setStoreName("杭州西溪悦榕庄");
        }

        customerUserNurseInfoVO.setStoreId(2);
        customerUserNurseInfoVO.setAppointmentStatus(1);
        customerUserNurseInfoVO.setClientId(0);
        customerUserNurseInfoVO.setClientName("");
        customerUserNurseInfoVO.setClientPhone("");
        customerUserNurseInfoVO.setEncourage("即使进入生活的战壕，从不缴械投降");
        customerUserNurseInfoVO.setCheckoutSummaryPop(req.getStoreType() == 1 ? 1L : 2L);
        customerUserNurseInfoVO.setClientCurrentCheckinStatus(req.getIsOrder() == 1 ? 1 : (req.getIsOrder() == 2 ? 0 : 2));
        customerUserNurseInfoVO.setHasCheckoutSummary(req.getIsOrder() == 3 ? 1 : 0);
        customerUserNurseInfoVO.setOrderNo("HEM" + (new Date().getTime() / 1000));
        customerUserNurseInfoVO.setOrderType(0);
        log.info("演示模式-response:{}",JSON.toJSONString(customerUserNurseInfoVO));
        return customerUserNurseInfoVO;
    }


    /**
     * get nurse info
     *
     * @return customerusernurseinfovo
     * <AUTHOR>
     * @date 2023/10/23 05:21:18
     * @since 1.0.0
     */
    private CustomerUserNurseInfoVO getNurseInfo(CustomerUserNurseInfoRequest req) {
        //校验 & 赋值
        NurseInfoCheckDTO checkDto = new NurseInfoCheckDTO(req);
        if(!checkDto.check()){
            return new CustomerUserNurseInfoVO();
        }
        //init
        CustomerUserNurseInfoVO vo = init(checkDto);
        //build
        return buildVO(checkDto, vo);
    }

    /**
     * get nurse info
     *
     * @return customerusernurseinfovo
     * <AUTHOR>
     * @date 2023/10/23 05:21:18
     * @since 1.0.0
     */
    private CustomerUserNurseInfoVO getNurseInfoOld(CustomerUserNurseInfoRequest req) {

        List<EcpStorePO> ecpStorePOList = cfgStoreService.list(new LambdaQueryWrapper<EcpStorePO>()
//                .eq(EcpStorePO::getType, req.getBrandType())
                .eq(EcpStorePO::getType, brandStrategyService.processBrandType(req.getBrandType(),BrandTypeBusinessTypeEnum.STORE))
                .eq(EcpStorePO::getActive, 1));

        if (CollUtil.isEmpty(ecpStorePOList)) {
            log.info("根据品牌未查询到门店信息");
            return new CustomerUserNurseInfoVO();
        }
        List<Integer> storeIdList = ecpStorePOList.stream().map(EcpStorePO::getStoreId).collect(Collectors.toList());
        if (CollUtil.isEmpty(storeIdList)) {
            log.info("未查询到门店id信息");
            return new CustomerUserNurseInfoVO();
        }

        List<TabClientPO> tabClientList = tabClientService.list(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getPhone, req.getPhone())
                .in(TabClientPO::getStoreId, storeIdList)
                .eq(TabClientPO::getActive, 1));

        if (CollUtil.isEmpty(tabClientList)) {
            log.info("未查询到用户client信息");
            return new CustomerUserNurseInfoVO();
        }

        List<Integer> clientIdList = tabClientList.stream().map(item -> item.getId().intValue()).collect(Collectors.toList());

        if (CollUtil.isEmpty(clientIdList)) {
            log.info("未查询到用户clientId信息");
            return new CustomerUserNurseInfoVO();
        }

        HeUserBasicPO basicPO = heUserBasicService.getOne(new LambdaQueryWrapper<HeUserBasicPO>()
                .eq(HeUserBasicPO::getPhone, req.getPhone())
                .eq(HeUserBasicPO::getIsDelete, 0)
                .last("limit 1"));

        if (ObjectUtil.isNull(basicPO)) {
            log.info("未找到用户basic信息");
            return new CustomerUserNurseInfoVO();
        }

        Long basicId = basicPO.getId();
        CustomerUserNurseInfoVO nurseInfoVO = new CustomerUserNurseInfoVO();
        nurseInfoVO.setIsOrder(0);
        nurseInfoVO.setBasicId(Objects.nonNull(basicId) ? basicId.intValue() : 0);
        nurseInfoVO.setClientName("");
        nurseInfoVO.setClientPhone(req.getPhone());
        nurseInfoVO.setNickName("");
        nurseInfoVO.setRoom(null);
        nurseInfoVO.setCheckoutSummaryPop(0L);
        nurseInfoVO.setClientCurrentCheckinStatus(0);
        nurseInfoVO.setHasCheckoutSummary(0);
        nurseInfoVO.setPredictBornTime(null);
        nurseInfoVO.setBornTimeRemark("");


        Long dueDate = basicPO.getManualPredictBornTime();
        Long initDueDate = null;
        //品牌类型为圣贝拉
        if (ObjectUtil.equals(req.getStoreType(), StoreTypeEnum.SAINT_BELLA.code())
                ||ObjectUtil.equals(req.getStoreType(), StoreTypeEnum.ISLA_BELLA.code())) {
            // 获取我的预产期  先获取userBasic中的预产期时间戳，不存在根据是否有订单来找填写的预产期
            if (ObjectUtil.isNotNull(basicPO.getManualPredictBornTime()) && basicPO.getManualPredictBornTime() != 0) {

                initDueDate = basicPO.getManualPredictBornTime();
            } else {
                initDueDate = getPredictBornTime(clientIdList);
            }
            nurseInfoVO.setPredictBornTime(initDueDate.intValue());
        }

        TabWechatUserPO tabWechatUserPO = tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
                .eq(TabWechatUserPO::getPhoneNumber, req.getPhone())
                .eq(TabWechatUserPO::getFromType, brandStrategyService.processBrandType(req.getStoreType(), BrandTypeBusinessTypeEnum.WECHAT_USER))
                .isNull(TabWechatUserPO::getDeletedAt));

        if (ObjectUtil.isNotNull(tabWechatUserPO)) {
            String name = StringUtils.isBlank(tabWechatUserPO.getNickname()) ? "会员" + tabWechatUserPO.getPhoneNumber().substring(tabWechatUserPO.getPhoneNumber().length() - 4) : tabWechatUserPO.getNickname();
            nurseInfoVO.setNickName(name);
        }

        CustomerCheckOutSummaryRequest request = new CustomerCheckOutSummaryRequest();
        request.setBasicId(basicId);
        request.setStoreType(req.getStoreType());

        // 查询客户的离馆小结和当前入住状态
        CustomerCheckOutSummaryVO summaryVO = customerSummaryService.checkOutSummaryInfo(request);

        if (ObjectUtil.isNotNull(summaryVO)) {
            nurseInfoVO.setCheckoutSummaryPop(ObjectUtil.isNotNull(summaryVO.getPopNotice()) && ObjectUtil.equals(summaryVO.getPopNotice(), 0) ? summaryVO.getId() : 0L);
            nurseInfoVO.setHasCheckoutSummary(1);
        }
        // 查询客户的当前入住状态
        log.info("排查入参问题:{},入参为:{}",request.getStoreType(),null);
        Integer currentStatus = customerSummaryService.queryCustomerCheckInStatus(basicId.intValue(), null);
        if (Objects.nonNull(currentStatus)) {
            nurseInfoVO.setClientCurrentCheckinStatus(currentStatus);
        }

        //查询最近一笔已预订待入住/入住中/已离店的订单
        ClientRoomOrderVO roomOrderVO = getClientLastRoomOrder(clientIdList);
        if (ObjectUtil.isNotNull(roomOrderVO.getOrder())) {
            TabOrderPO tabOrderPO = roomOrderVO.getOrder();
            nurseInfoVO.setIsOrder(1);
            //查询入住房间信息记录
            if (ObjectUtil.equals(tabOrderPO.getRoomRecordSource(), 2)) {
                //查询csp系统
                RoomStateCheckInInfoVO roomStateCheckInInfoVO = queryRoomStateByOrderNo(tabOrderPO.getOrderNumber());

                if (ObjectUtil.isNotNull(roomStateCheckInInfoVO)) {
                    CustomerUserNurseInfoVO.RoomInfo room = new CustomerUserNurseInfoVO.RoomInfo();
                    room.setClientInTime(ObjectUtil.isNotNull(roomStateCheckInInfoVO.getCheckInDate()) ? roomStateCheckInInfoVO.getCheckInDate() : "");
                    room.setClientOutTime(ObjectUtil.isNotNull(roomStateCheckInInfoVO.getCheckOutDate()) ? roomStateCheckInInfoVO.getCheckOutDate() : "");
                    nurseInfoVO.setRoom(room);
                }
            } else {
                TabRoomPO roomPO = roomOrderVO.getRoom();
                CustomerUserNurseInfoVO.RoomInfo room = new CustomerUserNurseInfoVO.RoomInfo();
                room.setClientInTime(ObjectUtil.isNotNull(roomPO.getClientInTime()) ? DateUtils.format(roomPO.getClientInTime(), DateUtils.YYYY_MM_DD) : "");
                room.setClientOutTime(ObjectUtil.isNotNull(roomPO.getClientInTime()) ? DateUtils.format(roomPO.getClientOutTime(), DateUtils.YYYY_MM_DD) : "");
                nurseInfoVO.setRoom(room);
            }
            return setNurseInfoVO(req.getStoreType(), dueDate, initDueDate, tabOrderPO, nurseInfoVO, ecpStorePOList, tabClientList);
        }
        TabOrderPO clientBeforeRoomOrder = getClientBeforeRoomOrder(clientIdList);
        if (ObjectUtil.isNotNull(clientBeforeRoomOrder)) {
            nurseInfoVO.setIsOrder(2);
            return setNurseInfoVO(req.getStoreType(), dueDate, initDueDate, clientBeforeRoomOrder, nurseInfoVO, ecpStorePOList, tabClientList);
        }

        TabOrderPO clientLastOrder = getClientLastOrder(3, clientIdList);
        if (ObjectUtil.isNotNull(clientLastOrder)) {
            nurseInfoVO.setIsOrder(3);
            return setNurseInfoVO(req.getStoreType(), dueDate, initDueDate, clientLastOrder, nurseInfoVO, ecpStorePOList, tabClientList);
        }
        nurseInfoVO.setOrderType(0);
        return setNurseInfoVO(req.getStoreType(), dueDate, initDueDate, null, nurseInfoVO, ecpStorePOList, tabClientList);
    }


    private @NotNull CustomerUserNurseInfoVO buildVO(NurseInfoCheckDTO checkDto, CustomerUserNurseInfoVO vo) {
        Long initDueDate = getInitDueDate(checkDto.req, checkDto);
        log.info("打印initDueDate:{}",initDueDate);
        if (initDueDate != null) {
            vo.setPredictBornTime(initDueDate.intValue());
        }
        vo.setNickName(getNickName(checkDto));

        CustomerCheckOutSummaryVO summaryVO = getCustomerCheckOutSummaryVO(checkDto);
        if (ObjectUtil.isNotNull(summaryVO)) {
            vo.setCheckoutSummaryPop(ObjectUtil.isNotNull(summaryVO.getPopNotice()) && ObjectUtil.equals(summaryVO.getPopNotice(), 0) ? summaryVO.getId() : 0L);
            vo.setHasCheckoutSummary(1);
        }
        // 查询客户的当前入住状态
        log.info("排查入参问题:{},入参为:{}",checkDto.req.getStoreType(),null);
        Integer currentStatus = customerSummaryService.queryCustomerCheckInStatus(checkDto.basicPO.getId().intValue(), null);
        if (Objects.nonNull(currentStatus)) {
            vo.setClientCurrentCheckinStatus(currentStatus);
        }

        //查询最近一笔已预订待入住/入住中/已离店的订单
        ClientRoomOrderVO roomOrderVO = getClientLastRoomOrder(checkDto.clientIdList);
        if (ObjectUtil.isNotNull(roomOrderVO.getOrder())) {
            TabOrderPO tabOrderPO = roomOrderVO.getOrder();
            vo.setIsOrder(1);
            //查询入住房间信息记录
            if (ObjectUtil.equals(tabOrderPO.getRoomRecordSource(), 2)) {
                //查询csp系统
                RoomStateCheckInInfoVO roomStateCheckInInfoVO = queryRoomStateByOrderNo(tabOrderPO.getOrderNumber());
                if (ObjectUtil.isNotNull(roomStateCheckInInfoVO)) {
                    CustomerUserNurseInfoVO.RoomInfo room = new CustomerUserNurseInfoVO.RoomInfo();
                    room.setClientInTime(ObjectUtil.isNotNull(roomStateCheckInInfoVO.getCheckInDate()) ? roomStateCheckInInfoVO.getCheckInDate() : "");
                    room.setClientOutTime(ObjectUtil.isNotNull(roomStateCheckInInfoVO.getCheckOutDate()) ? roomStateCheckInInfoVO.getCheckOutDate() : "");
                    vo.setRoom(room);
                }
            } else {
                TabRoomPO roomPO = roomOrderVO.getRoom();
                CustomerUserNurseInfoVO.RoomInfo room = new CustomerUserNurseInfoVO.RoomInfo();
                room.setClientInTime(ObjectUtil.isNotNull(roomPO.getClientInTime()) ? DateUtils.format(roomPO.getClientInTime(), DateUtils.YYYY_MM_DD) : "");
                room.setClientOutTime(ObjectUtil.isNotNull(roomPO.getClientInTime()) ? DateUtils.format(roomPO.getClientOutTime(), DateUtils.YYYY_MM_DD) : "");
                vo.setRoom(room);
            }
            log.info("打印 checkDto.req:{}",JSON.toJSONString(checkDto.req));
            return setNurseInfoVO(checkDto.req.getStoreType(), checkDto.basicPO.getManualPredictBornTime(), initDueDate, tabOrderPO, vo, checkDto.ecpStorePOList, checkDto.tabClientList);
        }
        TabOrderPO clientBeforeRoomOrder = getClientBeforeRoomOrder(checkDto.clientIdList);
        if (ObjectUtil.isNotNull(clientBeforeRoomOrder)) {
            vo.setIsOrder(2);
            return setNurseInfoVO(checkDto.req.getStoreType(), checkDto.basicPO.getManualPredictBornTime(), initDueDate, clientBeforeRoomOrder, vo, checkDto.ecpStorePOList, checkDto.tabClientList);
        }

        TabOrderPO clientLastOrder = getClientLastOrder(3, checkDto.clientIdList);
        if (ObjectUtil.isNotNull(clientLastOrder)) {
            vo.setIsOrder(3);
            return setNurseInfoVO(checkDto.req.getStoreType(), checkDto.basicPO.getManualPredictBornTime(), initDueDate, clientLastOrder, vo, checkDto.ecpStorePOList, checkDto.tabClientList);
        }
        vo.setIsOrder(0);
        return setNurseInfoVO(checkDto.req.getStoreType(), checkDto.basicPO.getManualPredictBornTime(), initDueDate, null, vo, checkDto.ecpStorePOList, checkDto.tabClientList);
    }

    private CustomerCheckOutSummaryVO getCustomerCheckOutSummaryVO(NurseInfoCheckDTO checkDto) {
        CustomerCheckOutSummaryRequest request = new CustomerCheckOutSummaryRequest();
        request.setBasicId(checkDto.basicPO.getId());
        request.setStoreType(checkDto.req.getStoreType());
        // 查询客户的离馆小结和当前入住状态
        CustomerCheckOutSummaryVO summaryVO = customerSummaryService.checkOutSummaryInfo(request);
        return summaryVO;
    }

    private String getNickName(NurseInfoCheckDTO checkDto) {
        TabWechatUserPO tabWechatUserPO = checkDto.getTabWechatUserPO(checkDto.req);
        if (ObjectUtil.isNotNull(tabWechatUserPO)) {
            return StringUtils.isBlank(tabWechatUserPO.getNickname()) ? "会员" + tabWechatUserPO.getPhoneNumber().substring(tabWechatUserPO.getPhoneNumber().length() - 4) : tabWechatUserPO.getNickname();
        }
        return "";
    }

    private @Nullable Long getInitDueDate(CustomerUserNurseInfoRequest req, NurseInfoCheckDTO checkDto) {
        Long initDueDate = null;
        //品牌类型为圣贝拉
        if (ObjectUtil.equals(req.getStoreType(), StoreTypeEnum.SAINT_BELLA.code())
                ||ObjectUtil.equals(req.getStoreType(), StoreTypeEnum.ISLA_BELLA.code())) {
            // 获取我的预产期  先获取userBasic中的预产期时间戳，不存在根据是否有订单来找填写的预产期
            if (ObjectUtil.isNotNull(checkDto.basicPO.getManualPredictBornTime()) && checkDto.basicPO.getManualPredictBornTime() != 0) {
                initDueDate = checkDto.basicPO.getManualPredictBornTime();
            } else {
                initDueDate = getPredictBornTime(checkDto.clientIdList);
            }
        }
        return initDueDate;
    }

    private static CustomerUserNurseInfoVO init(NurseInfoCheckDTO checkDto) {
        CustomerUserNurseInfoVO vo= new CustomerUserNurseInfoVO();
        vo.setIsOrder(0);
        vo.setBasicId(Objects.nonNull(checkDto.basicPO.getId()) ? checkDto.basicPO.getId().intValue() : 0);
        vo.setClientName("");
        vo.setClientPhone(checkDto.req.getPhone());
        vo.setNickName("");
        vo.setRoom(null);
        vo.setCheckoutSummaryPop(0L);
        vo.setClientCurrentCheckinStatus(0);
        vo.setHasCheckoutSummary(0);
        vo.setPredictBornTime(null);
        vo.setBornTimeRemark("");;
        return vo;
    }

    @Data
    private class NurseInfoCheckDTO {
        public List<EcpStorePO> ecpStorePOList;
        public List<TabClientPO> tabClientList;
        public List<Integer> clientIdList;
        private HeUserBasicPO basicPO;
        CustomerUserNurseInfoRequest req;
        public NurseInfoCheckDTO(CustomerUserNurseInfoRequest req) {
            this.req = req;
        }

        private boolean check(){
            List<EcpStorePO> ecpStorePOList = getEcpStorePOS(req);
            if (CollUtil.isEmpty(ecpStorePOList)) {
                log.info("根据品牌未查询到门店信息");
                return false;
            }
            List<Integer> storeIdList = ecpStorePOList.stream().map(EcpStorePO::getStoreId).collect(Collectors.toList());
            if (CollUtil.isEmpty(storeIdList)) {
                log.info("未查询到门店id信息");
                return false;
            }
            List<TabClientPO> tabClientList = tabClientService.list(new LambdaQueryWrapper<TabClientPO>()
                    .eq(TabClientPO::getPhone, req.getPhone())
                    .in(TabClientPO::getStoreId, storeIdList)
                    .eq(TabClientPO::getActive, 1));

            if (CollUtil.isEmpty(tabClientList)) {
                log.info("未查询到用户client信息");
                return false;
            }
            List<Integer> clientIdList = tabClientList.stream().map(item -> item.getId().intValue()).collect(Collectors.toList());

            if (CollUtil.isEmpty(clientIdList)) {
                log.info("未查询到用户clientId信息");
                return false;
            }
            HeUserBasicPO basicPO = heUserBasicService.getOne(new LambdaQueryWrapper<HeUserBasicPO>()
                    .eq(HeUserBasicPO::getPhone, req.getPhone())
                    .eq(HeUserBasicPO::getIsDelete, 0)
                    .last("limit 1"));

            if (ObjectUtil.isNull(basicPO)) {
                log.info("未找到用户basic信息");
                return false;
            }

            this.ecpStorePOList = ecpStorePOList;
            this.tabClientList = tabClientList;
            this.clientIdList = clientIdList;
            this.basicPO=basicPO;
            return true;
        }
        private TabWechatUserPO getTabWechatUserPO(CustomerUserNurseInfoRequest req) {
            return tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
                    .eq(TabWechatUserPO::getPhoneNumber, req.getPhone())
                    .eq(TabWechatUserPO::getFromType, brandStrategyService.processBrandType(req.getStoreType(), BrandTypeBusinessTypeEnum.WECHAT_USER))
                    .isNull(TabWechatUserPO::getDeletedAt));
        }
    }

    private List<EcpStorePO> getEcpStorePOS(CustomerUserNurseInfoRequest req) {
        List<EcpStorePO> ecpStorePOList = cfgStoreService.list(new LambdaQueryWrapper<EcpStorePO>()
//                .eq(EcpStorePO::getType, req.getBrandType())
                .eq(EcpStorePO::getType, brandStrategyService.processBrandType(req.getBrandType(),BrandTypeBusinessTypeEnum.STORE))
                .eq(EcpStorePO::getActive, 1));
        return ecpStorePOList;
    }

    /**
     * 将查询的订单信息和预产信息放入vo
     *
     * @return customerusernurseinfovo
     * <AUTHOR>
     * @date 2023/10/25 01:54:00
     * @since 1.0.0
     */
    private CustomerUserNurseInfoVO setNurseInfoVO(Integer storeType, Long dueDate, Long initDueDate, TabOrderPO orderPO, CustomerUserNurseInfoVO vo, List<EcpStorePO> ecpStorePOList, List<TabClientPO> tabClientList) {
        log.info("打印 storeTypee:{}",storeType);
        if (ObjectUtil.isNotNull(orderPO)) {
            getOrder(orderPO, vo, ecpStorePOList, tabClientList);
        }
        //品牌类型为圣贝拉
        if (ObjectUtil.equals(storeType, StoreTypeEnum.SAINT_BELLA.code()) ||ObjectUtil.equals(storeType, StoreTypeEnum.ISLA_BELLA.code())) {
            // 获取设置的预产期计算周数
            Map<String, Object> map = getPregnantWeeksByCustomerStatus(dueDate, initDueDate, vo);
            if (ObjectUtil.isNotEmpty(map) && StringUtils.isNotBlank(map.get("remark").toString())) {
                vo.setBornTimeRemark(map.get("remark").toString());
            }
        }
        return vo;
    }

    /**
     * 查询最近一笔已预订待入住/入住中/已离店的订单
     *
     * @param clientIds client ids
     * <AUTHOR>
     * @date 2023/10/24 01:50:39
     * @since 1.0.0
     */
    private ClientRoomOrderVO getClientLastRoomOrder(List<Integer> clientIds) {
        ClientRoomOrderVO clientRoomOrderVO = new ClientRoomOrderVO();
        //todo 1.优先查csp 订单入住中的
        //2.再查ecp已入住csp房态信息

        TabOrderPO order = tabOrderService.getOne(new LambdaQueryWrapper<TabOrderPO>()
                .eq(TabOrderPO::getActive, 1)
                .eq(TabOrderPO::getCheckInStatus, RoomStateCheckInEnum.ROOM_STATE_CHECK_IN.getCode())
                //房态录入来源：1=ecp系统录入；2=csp系统录入；3=ecp sync csp ; 4=推送过csp但有异常手动处理
                .eq(TabOrderPO::getRoomRecordSource, 2)
                .in(TabOrderPO::getClientId, clientIds)
                .in(TabOrderPO::getOrderType, Arrays.asList(1, 6))
                .orderByDesc(TabOrderPO::getRecordTime)
                .last("limit 1"));

        if (ObjectUtil.isNull(order)) {
            TabRoomPO tabRoomPO = tabRoomService.getOne(new LambdaQueryWrapper<TabRoomPO>()
                    .in(TabRoomPO::getClientId, clientIds)
                    .in(TabRoomPO::getRoomRecordSource, Arrays.asList(1, 3, 4))
                    .eq(TabRoomPO::getStatus, 3)
                    .orderByDesc(TabRoomPO::getRecordTime)
                    .last("limit 1"));

            if (ObjectUtil.isNotNull(tabRoomPO)) {
                clientRoomOrderVO.setRoom(tabRoomPO);

                TabOrderPO tabOrderPO = tabOrderService.getOne(new LambdaQueryWrapper<TabOrderPO>()
                        .eq(TabOrderPO::getActive, 1)
                        .eq(TabOrderPO::getCheckInStatus, 2)
                        .eq(TabOrderPO::getClientId, tabRoomPO.getClientId())
                        .in(TabOrderPO::getOrderType, Arrays.asList(1, 6))
                        .orderByDesc(TabOrderPO::getRecordTime)
                        .last("limit 1"));

                if (ObjectUtil.isNotNull(tabOrderPO)) {
                    clientRoomOrderVO.setOrder(tabOrderPO);
                }
            }

            return clientRoomOrderVO;
        }

        clientRoomOrderVO.setOrder(order);
        return clientRoomOrderVO;
    }

    /**
     * 查询最近一笔待入住/已离店/的订单(未入馆)
     *
     * @param clientIds client ids
     * @return tab order po
     * <AUTHOR>
     * @date 2023/10/24 06:17:03
     * @since 1.0.0
     */
    private TabOrderPO getClientBeforeRoomOrder(List<Integer> clientIds) {

        return tabOrderService.getOne(new LambdaQueryWrapper<TabOrderPO>()
                .eq(TabOrderPO::getActive, 1)
                .in(TabOrderPO::getCheckInStatus, Arrays.asList(0, 1))
                .in(TabOrderPO::getClientId, clientIds)
                .in(TabOrderPO::getOrderType, Arrays.asList(1, 6))
                .orderByDesc(TabOrderPO::getRecordTime)
                .last("limit 1"));
    }

    /**
     * 查询最近一笔已预订待入住/入住中/已离店的订单
     *
     * @param checkInStatus check in status
     * @param clientIds     client ids
     * @return tab order po
     * <AUTHOR>
     * @date 2023/10/24 06:33:16
     * @since 1.0.0
     */
    private TabOrderPO getClientLastOrder(Integer checkInStatus, List<Integer> clientIds) {

        if (ObjectUtil.equals(checkInStatus, 2)) {
            //查询最近一笔入住中的订单
            ClientRoomOrderVO roomOrderVO = getClientLastRoomOrder(clientIds);
            if (ObjectUtil.isNotNull(roomOrderVO) && ObjectUtil.isNotNull(roomOrderVO.getOrder())) {
                return roomOrderVO.getOrder();
            } else {
                return null;
            }
        }

        //查询最近一笔待入住/已离店/的订单
        return tabOrderService.getOne(new LambdaQueryWrapper<TabOrderPO>()
                .eq(TabOrderPO::getActive, 1)
                .eq(TabOrderPO::getCheckInStatus, checkInStatus)
                .in(TabOrderPO::getClientId, clientIds)
                .in(TabOrderPO::getOrderType, Arrays.asList(1, 6))
                .orderByDesc(TabOrderPO::getRecordTime)
                .last("limit 1"));
    }

    /**
     * 将查询的信息 放入 CustomerUserNurseInfoVO
     *
     * @param orderPO        order po
     * @param vo      nurse info
     * @param ecpStorePOList ecp store polist
     * @param tabClientList  tab client list
     * <AUTHOR>
     * @date 2023/10/24 05:44:33
     * @since 1.0.0
     */
    private void getOrder(TabOrderPO orderPO, CustomerUserNurseInfoVO vo, List<EcpStorePO> ecpStorePOList, List<TabClientPO> tabClientList) {
        vo.setClientId(orderPO.getClientId());
        vo.setOrderNo(orderPO.getOrderNumber());
        //1月子对应 小助手0
        vo.setOrderType(orderPO.getOrderType() == 1 ? 0 : 1);
        vo.setProductionAppoinmentShow(0);

        if (ObjectUtil.isNotNull(orderPO.getCheckInDate())) {
            vo.setCheckDay(DateUtils.dateDifDay(orderPO.getCheckInDate(), new Date()).intValue() + 1);
        } else {
            vo.setCheckDay(0);
        }

        //改为本地
        SettingPackagePO settingPackagePO = settingPackageService.getById(orderPO.getPackageId());
        if (ObjectUtil.isNotNull(settingPackagePO)) {
            String name = settingPackagePO.getName().replace("\r\n", "");
            name = name.replace("\r", "");
            name = name.replace("\n", "");
            vo.setPackageName(name);
        }

        Optional<EcpStorePO> ecpStorePO = ecpStorePOList.stream()
                .filter(item -> ObjectUtil.equals(orderPO.getStoreId(), item.getStoreId())).findFirst();

        if (ecpStorePO.isPresent()) {
            EcpStorePO store = ecpStorePO.get();
            vo.setStoreId(store.getStoreId());
            vo.setStoreName(store.getStoreName());
            vo.setStoreType(StoreTypeEnum.getValueByCode(store.getType()));
            vo.setAppointmentStatus(store.getAppointmentStatus());
        }

        Optional<TabClientPO> tabClientPO = tabClientList.stream()
                .filter(item -> ObjectUtil.equals(orderPO.getClientId().longValue(), item.getId())).findFirst();

        if (tabClientPO.isPresent()) {
            TabClientPO client = tabClientPO.get();
            vo.setClientName(client.getName());
            vo.setClientPhone(client.getPhone());
        }

        HeSystemConfigPO configPO = heSystemConfigService.queryOneByKey(PRODUCTION_APPOINTMENT_STORE_LIST);

        //配置不为空
        if (Objects.isNull(configPO) || StringUtils.isBlank(configPO.getValue())) {
            return;
        }
        List<String> configArr = JSONUtil.toList(configPO.getValue(), String.class);

        boolean productionAppoinmentShow = false;
        // 判断门店是否在产康预约的白名单中
//        if (ObjectUtil.equals(vo.getIsOrder(), 1) || ObjectUtil.equals(vo.getIsOrder(), 3)) {
//            if (configArr.contains("all") || configArr.contains(String.valueOf(orderPO.getStoreId()))) {
//                productionAppoinmentShow = true;
//            }
//        }
        if (configArr.contains("all") || configArr.contains(String.valueOf(orderPO.getStoreId()))) {
            productionAppoinmentShow = true;
        }
        if (productionAppoinmentShow) {
            vo.setProductionAppoinmentShow(1);
        }

    }


    /**
     * 查询房态信息
     *
     * @param orderNo order no
     * @return room state check in info vo
     * <AUTHOR>
     * @date 2023/10/24 04:15:24
     * @since 1.0.0
     */
    private RoomStateCheckInInfoVO queryRoomStateByOrderNo(String orderNo) {
        final RoomStateCheckInInfoPO one = roomStateCheckInInfoService.getCheckInfoByOrderNo(orderNo);
        RoomStateCheckInInfoVO copy = new RoomStateCheckInInfoVO();
        if (Objects.nonNull(one)) {
            BeanUtil.copyProperties(one, copy);
            copy.setRoomStateCheckInInfoId(one.getId());
            copy.setCheckInDate(DateUtils.format(one.getCheckInDate()));
            copy.setCheckOutDate(DateUtils.format(one.getCheckOutDate()));
        }
        return copy;
    }

    /**
     * 通过预产期获取孕周V2版本
     *
     * @param dueDate     due date
     * @param initDueDate init due date
     * @param vo nurse info vo
     * <AUTHOR>
     * @date 2023/10/24 04:22:29
     * @since 1.0.0
     */
    public Map<String, Object> getPregnantWeeksByCustomerStatus(Long dueDate, Long initDueDate, CustomerUserNurseInfoVO vo) {


        Map<String, Object> map = new HashMap<>();
        // 初始值
        if (ObjectUtil.equals(dueDate, 0L) && ObjectUtil.equals(vo.getIsOrder(), 1)) {
            if (ObjectUtil.isNotNull(vo.getOrderType())) {
                if (ObjectUtil.equals(vo.getOrderType(), 0)) {
                    map.put("week", 0);
                    map.put("remainder", 0);
                    map.put("remark", "育儿中");
                    return map;
                } else {
                    map.put("week", 0);
                    map.put("remainder", 0);
                    map.put("remark", "去设置");
                    return map;
                }
            }
        }

        if ((ObjectUtil.equals(dueDate, 0L) && ObjectUtil.equals(vo.getIsOrder(), 0))
                || (ObjectUtil.isNotNull(vo.getOrderType()) && ObjectUtil.equals(vo.getOrderType(), 1) && ObjectUtil.isNotNull(dueDate))) {
            map.put("week", 0);
            map.put("remainder", 0);
            map.put("remark", "去设置");
            return map;
        }

        dueDate = initDueDate;
        // 除在馆需要默认值之外，其他通过订单/设置的预产期拿计算状态
        if (ObjectUtil.equals(dueDate, -1L)) {
            map.put("week", 0);
            map.put("remainder", 0);
            map.put("remark", "备孕中");
            return map;
        }

        long day = 280L;
        // 怀孕周期根据预产期按照280天倒推计算孕周（即40周）计算，超过280天后在“我的孕期”回显“已过预产期X天”
        // 先获取怀孕期
        long pregnancyTime = dueDate - (day * 86400);

        long newDate = new Date().getTime() / 1000;

        //怀孕期大于当前时间
        if (pregnancyTime > newDate) {
            map.put("week", 0);
            map.put("remainder", 0);
            map.put("remark", "怀孕 0 周 0 天");
            return map;
        }

        day = DateUtils.dateDifDay(new Date(pregnancyTime * 1000), new Date());
        int week = Math.toIntExact(day / 7);
        int remainder = Math.toIntExact(day % 7);

        int status = 0;
        String remake = "怀孕" + week + " 周" + remainder + " 天";

        if (week <= 12) {
            status = 2;
        } else if (week <= 24) {
            status = 3;
        } else {
            status = 4;
            if (week >= 40 && remainder > 0) {
                status = 5;
                remake = "已过预产期" + ((week - 40) * 7 + remainder) + "天";
            }
        }

        map.put("week", week);
        map.put("remainder", remainder);
        map.put("remark", remake);
        map.put("status", status);
        return map;
    }

    /**
     * get predict born time
     *
     * @param clientIdList client id list
     * @return long
     * <AUTHOR>
     * @date 2023/10/24 06:14:24
     * @since 1.0.0
     */
    public Long getPredictBornTime(List<Integer> clientIdList) {
        // 1. 客户已下过单，则获取下单时的预产期
        // 2. 设置过日期的，默认展示上次设置的日期
        // 3. 备孕期为备孕中

        HeOrderPO orderPO = heOrderService.getOne(new LambdaQueryWrapper<HeOrderPO>()
                .in(HeOrderPO::getClientUid, clientIdList)
                .eq(HeOrderPO::getOrderType, 0)
                .eq(HeOrderPO::getIsDelete, 0)
                .orderByDesc(HeOrderPO::getOrderId)
                .last("limit 1"));


        long predictBornTime = 0L;

        if (ObjectUtil.isNotNull(orderPO) && ObjectUtil.isNotNull(orderPO.getClientUid())) {
            HeClientExpandPeriodPO clientExpandPeriod = heClientExpandPeriodService.getOne(new LambdaQueryWrapper<HeClientExpandPeriodPO>()
                    .eq(HeClientExpandPeriodPO::getEcpCid, orderPO.getClientUid())
                    .eq(HeClientExpandPeriodPO::getIsDelete, 0)
                    .last("limit 1"));

            if (ObjectUtil.isNotNull(clientExpandPeriod)) {
                predictBornTime = ObjectUtil.isNotNull(clientExpandPeriod.getPredictBornTime()) ? clientExpandPeriod.getPredictBornTime() : 0;
            }
        }
        return predictBornTime;
    }


}
