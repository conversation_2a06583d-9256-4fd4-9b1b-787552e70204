package com.stbella.customer.server.invite.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.customer.server.brand.strategy.BrandStrategyService;
import com.stbella.customer.server.config.BizConfig;
import com.stbella.customer.server.config.ErrorMsgConfig;
import com.stbella.customer.server.customer.entity.CustomerBaseVoucherPO;
import com.stbella.customer.server.customer.service.CustomerBaseVoucherService;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.ecp.vo.InviteInfoVO;
import com.stbella.customer.server.invite.component.InviteSnowballEngine;
import com.stbella.customer.server.invite.dto.InviteRewardRecordFullDTO;
import com.stbella.customer.server.invite.entity.InviteRewardConfigPO;
import com.stbella.customer.server.invite.entity.InviteRewardRecordPO;
import com.stbella.customer.server.invite.entity.InviteTicketConfigPO;
import com.stbella.customer.server.invite.enums.*;
import com.stbella.customer.server.invite.req.*;
import com.stbella.customer.server.invite.service.InviteRewardConfigService;
import com.stbella.customer.server.invite.service.InviteRewardRecordService;
import com.stbella.customer.server.invite.service.InviteService;
import com.stbella.customer.server.invite.service.InviteTicketConfigService;
import com.stbella.customer.server.invite.vo.*;
import com.stbella.customer.server.manager.OrderManager;
import com.stbella.customer.server.scrm.component.assembler.NoticeMessageAssembler;
import com.stbella.customer.server.scrm.enums.BrandTypeBusinessTypeEnum;
import com.stbella.customer.server.util.UMSUtils;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.order.month.res.GoodsNewVO;
import com.stbella.store.core.enums.BrandEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 老带新 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-12-04
 */
@Service
@Slf4j
public class InviteServiceImpl implements InviteService {
    public static final String INVITEE_BIND_ERROR_MSG = "您暂不符合活动要求，详情请查看活动规则";

    public static final String SAINTBELLA_DEFAULT_AVATAR = "https://devop.primecare.top/sbl-xcx/master/images/user/appointment-detail-v2/avatar.webp";
    public static final String BABYBELLA_DEFAULT_AVATAR = "https://devop.primecare.top/xbl-xcx/master/images/user/appointment-detail-v2/avatar.webp";
    public static final String BELLAISLA_DEFAULT_AVATAR = "https://devop.primecare.top/isla-xcx/master/images/user/appointment-detail-v2/avatar.webp";

    public static final Map<Integer, String> BRAND_AVATAR_MAP;

    public static final String CAN_NOT_BIND_SELF = "您不能绑定自己";
    public static final String CAN_NOT_FIND_INVITTER = "邀请人信息不存在";
    public static final String CAN_NOT_FIND_INVITTEE = "您暂不符合活动要求，详情请查看活动规则";
    public static final String BIND_SUCCESS = "您已接受邀请，您的奖励已发放至您的账户，更多福利请到店咨询";

    public static final String SAINTBELLA_BIND_SUCCESS = "成功推荐好友首次到店即签约，推荐人和被推荐人均可获得5000元产康金、1000元广禾堂内调配货卡等，更多优惠请到店咨询…";
    public static final String BABYBELLA_BIND_SUCCESS = "成功推荐好友首次到店即签约，推荐人和被推荐人均可获得3000元产康金、1000元广禾堂内调配货卡等，更多优惠请到店咨询…";
    public static final String BELLAISLA_BIND_SUCCESS = "成功推荐好友首次到店即签约，推荐人和被推荐人均可获得5000元产康金、1000元广禾堂内调配货卡等，更多优惠请到店咨询…";

    public static final Map<Integer, String> BRAND_BIND_SUCCESS_MAP;


    @Resource
    private InviteRewardRecordService inviteRewardRecordService;

    @Resource
    private CustomerBaseVoucherService customerBaseVoucherService;

    @Resource
    private HeUserBasicService heUserBasicService;

    @Resource
    private HeInviteQrCodeService heInviteQrCodeService;

    @Resource
    private InviteTicketConfigService ticketConfigService;

    @Resource
    private TabWechatUserService tabWechatUserService;//Ecp-WechatUser

    @Resource
    private InviteRewardConfigService rewardConfigService;
    @Resource
    private HeOrderService heOrderService;
    @Resource
    private HeInviteRelationService heInviteRelationService;

    @Resource
    private BrandStrategyService brandStrategyService;

    @Resource
    private CfgStoreService storeService;

    @Resource
    private InviteSnowballEngine inviteSnowballEngine;

    @Resource
    private BizConfig bizConfig;

    @Resource
    private ErrorMsgConfig errorMsgConfig;

    @Resource
    private OrderManager orderManager;

    @Resource
    private NoticeMessageAssembler noticeMessageAssembler;

    private static final ThreadLocal<SimpleDateFormat> DATE_FORMATTER_mmss = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMATTER = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    static {
        BRAND_AVATAR_MAP = new HashMap<>();
        BRAND_AVATAR_MAP.put(BrandEnum.SAN_BELLA.getCode(), SAINTBELLA_DEFAULT_AVATAR);
        BRAND_AVATAR_MAP.put(BrandEnum.BABY_BELLA.getCode(), BABYBELLA_DEFAULT_AVATAR);
        BRAND_AVATAR_MAP.put(BrandEnum.ISLA.getCode(), BELLAISLA_DEFAULT_AVATAR);

        BRAND_BIND_SUCCESS_MAP = new HashMap<>();
        BRAND_BIND_SUCCESS_MAP.put(BrandEnum.SAN_BELLA.getCode(), SAINTBELLA_BIND_SUCCESS);
        BRAND_BIND_SUCCESS_MAP.put(BrandEnum.BABY_BELLA.getCode(), BABYBELLA_BIND_SUCCESS);
        BRAND_BIND_SUCCESS_MAP.put(BrandEnum.ISLA.getCode(), BELLAISLA_BIND_SUCCESS);
    }

    @Override
    public MyRewardInfoVO info(MyRewardInfoRequest request) {
        log.info("统计接口参数信息:{}", JSONUtil.toJsonStr(request));
        Long basicId = request.getBasicId();
        int totalInviters = 0;
        int totalProductionAmount = 0;
        int totalTickets = 0;
        int totalPoints = 0;
        MyRewardInfoVO data = new MyRewardInfoVO();
        //获取邀请人数
        totalInviters = heInviteRelationService.count(new LambdaQueryWrapper<HeInviteRelationPO>()
                .eq(HeInviteRelationPO::getParentBasicId, basicId)
                .eq(HeInviteRelationPO::getDeleted, 0)
        );
        //获取本人获取的产康数
        totalProductionAmount = getTotalByRewardType(basicId, InviteRewardTypeEnum.PRODUCTION_AMOUNT.code(),null);
        //获取本人获取的礼券数
        totalTickets = getTotalByRewardType(basicId, InviteRewardTypeEnum.TICKET.code(),null);
        //积分
        totalPoints = getTotalByRewardType(basicId, InviteRewardTypeEnum.INTEGRAL.code(),null);
        data.setTotalInviters(totalInviters);
        data.setTotalProductionAmount(totalProductionAmount);
        data.setTotalTickets(totalTickets);
        data.setTotalPoints(totalPoints);
        return data;
    }

    @Override
    public Integer getTotalByRewardType(Long basicId,Integer rewardType,List<Integer> statusList) {
        return inviteRewardRecordService.getTotalByRewardType(basicId, rewardType, statusList);
    }


    @Override
    public PageVO<RewardDetailVO> rewardList(RewardDetailRequest request) {
        if (Objects.isNull(request)) {
            return new PageVO<>(Lists.newArrayList(), 0, 0,0);
        }
        log.info("rewardList参数信息:{}", JSONUtil.toJsonStr(request));
        Page<InviteRewardRecordFullDTO> page = getInviteRewardRecordDTOPage(request);
        if (page == null || CollectionUtil.isEmpty(page.getRecords())) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        List<InviteRewardRecordFullDTO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        try {
            //所有用户id
            Set<Integer> basicIds = records.stream().map(InviteRewardRecordFullDTO::getBasicId).collect(Collectors.toSet());
            //所有被邀请人
            Set<Integer> inviteeBasicIds = records.stream().map(InviteRewardRecordFullDTO::getInviteeBasicId).filter(Objects::nonNull).collect(Collectors.toSet());
            basicIds.addAll(inviteeBasicIds);
            //查出邀请人
            InviteInfoVO relationVO = heInviteRelationService.queryUserBindInfoByBasicId(request.getBasicId());
            if(Objects.nonNull(relationVO) && Objects.nonNull(relationVO.getParentBasicId())){
                basicIds.add(relationVO.getParentBasicId().intValue());
            }
            if (CollectionUtils.isEmpty(basicIds)) {
                return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
            }

            Integer wechatUserType = brandStrategyService.processBrandType(request.getBrandType(), BrandTypeBusinessTypeEnum.WECHAT_USER);

            Map<Integer, TabWechatUserPO> wechatUserMap = tabWechatUserService.list(new LambdaQueryWrapper<TabWechatUserPO>()
                .in(TabWechatUserPO::getBasicUid, new ArrayList<>(basicIds))
                .eq(TabWechatUserPO::getFromType, wechatUserType)
                .orderByDesc(TabWechatUserPO::getUpdatedAt)
            ).stream().collect(Collectors.toMap(TabWechatUserPO::getBasicUid, Function.identity(), (o1, o2) -> o1));

            List<HeUserBasicPO> heUserBasicPOS = heUserBasicService.queryUserBasicInfoByIdList(basicIds.stream().map(Integer::longValue).collect(Collectors.toList()));
            Map<Long, HeUserBasicPO> heUserBasicPOMap = heUserBasicPOS.stream().collect(Collectors.toMap(HeUserBasicPO::getId, Function.identity()));

            List<RewardDetailVO> vos = new ArrayList<>();
            for (InviteRewardRecordFullDTO record : records) {
                RewardDetailVO vo = new RewardDetailVO();
                if (Objects.nonNull(record.getInviteeBasicId())) {
                    //邀请人展示被邀请的用户的信息
                    setInfo(record.getInviteeBasicId(),
                        (setter, avatar, nickname) -> {
                            setter.setAvatar(avatar);
                            setter.setName(nickname);
                        }, wechatUserMap, request.getBrandType(), heUserBasicPOMap, vo);
                } else {
                    if(Objects.isNull(relationVO) || Objects.isNull(relationVO.getParentBasicId())){
                        continue;
                    }
                    setInfo(relationVO.getParentBasicId().intValue(),
                        (setter, avatar, nickname) -> {
                            setter.setAvatar(avatar);
                            setter.setName(nickname);
                        }, wechatUserMap, request.getBrandType(), heUserBasicPOMap, vo);
                }
                vo.setId(record.getId());
                vo.setBasicId(record.getBasicId());
                vo.setStatus(record.getStatus());
                InviteRewardRecordStatusEnum byCode = InviteRewardRecordStatusEnum.getByCode(record.getStatus());
                vo.setStatusStr(byCode==null?"":byCode.desc());
                //礼券名
                vo.setCouponName(record.getRewardTitle());
                vo.setRewardType(record.getRewardType());
                vo.setRewardTypeStr(Objects.requireNonNull(InviteRewardTypeEnum.getByCode(record.getRewardType())).desc());
                vo.setRuleText(record.getRuleContent());
                vo.setRewardNumber(record.getRewardNumber());
                vo.setStageStr(Objects.requireNonNull(InviteRewardSceneEnum.getByCode(record.getScene())).desc());
                vo.setStage(record.getScene());
                vo.setGmtCreate(record.getGmtCreate());
                vo.setList(getNodes(record));
                vo.setInviteeBasicId(record.getInviteeBasicId());
                vos.add(vo);
            }
            return new PageVO<>(vos, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
        } catch (Exception e) {
            log.error("获取奖励列表异常", e);
        }
        return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * 获取建立列表
     *
     * @param request
     * @return
     */
    private Page<InviteRewardRecordPO> getInviteRewardRecordPOPage(RewardDetailRequest request) {
        Long basicId = request.getBasicId();
        Integer rewardTypeFilter = request.getRewardType();
        Integer status = request.getStatus();
        LambdaQueryWrapper<InviteRewardRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteRewardRecordPO::getBasicId, basicId);
        queryWrapper.eq(InviteRewardRecordPO::getDeleted, 0);
        if (rewardTypeFilter != null) {
            queryWrapper.eq(InviteRewardRecordPO::getRewardType, rewardTypeFilter);
        }
        if (status != null) {
            queryWrapper.eq(InviteRewardRecordPO::getStatus, status);
        }
        // 按照奖励获得时间（gmtCreate字段）由近到远排序
        queryWrapper.orderByDesc(InviteRewardRecordPO::getGmtCreate);
        Page<InviteRewardRecordPO> page = inviteRewardRecordService.page(new Page<>(request.getPageNum(), request.getPageSize()),
                queryWrapper
        );
        return page;
    }

    public Page<InviteRewardRecordFullDTO> getInviteRewardRecordDTOPage(RewardDetailRequest request) {
        Long basicId = request.getBasicId();
        Integer rewardTypeFilter = request.getRewardType();
        Integer status = request.getStatus();
        List<Integer> statusList = request.getStatusList();
        Page<InviteRewardRecordPO> recordPage = null;
//        if(!request.getUnion()) {
            //主表的查询条件
            LambdaQueryWrapper<InviteRewardRecordPO> recordQueryWrapper = getRecordQueryWrapper(request, basicId, rewardTypeFilter, status, statusList);
            recordPage = inviteRewardRecordService.page(new Page<>(request.getPageNum(), request.getPageSize()), recordQueryWrapper);
//        }else {
//            //关联查询邀请人和被邀请人的逻辑
//            Integer byUnionCount = inviteRewardRecordService.getInviteRewardRecordsByUnionCount(request);
//            if (byUnionCount == null || byUnionCount.equals(0)) {
//                return new Page<InviteRewardRecordFullDTO>(request.getPageNum(), 0, 0);
//            }
//            recordPage = inviteRewardRecordService.getInviteRewardRecords(new Page(request.getPageNum(), request.getPageSize()),request);
//        }
        if (recordPage == null || CollectionUtils.isEmpty(recordPage.getRecords())) {
            return new Page<>(request.getPageNum(), 0, 0);
        }
        //获取查询结果中的reward_id列表，用于关联查询配置表
        List<Integer> rewardIdList = recordPage.getRecords().stream()
                .map(InviteRewardRecordPO::getRewardId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<InviteRewardConfigPO> configQueryWrapper = new LambdaQueryWrapper<>();
        configQueryWrapper.in(InviteRewardConfigPO::getId, rewardIdList);
        configQueryWrapper.eq(InviteRewardConfigPO::getDeleted, 0);
        List<InviteRewardConfigPO> configList = rewardConfigService.list(configQueryWrapper);
        List<Integer> ticketIdList = configList.stream()
                .map(InviteRewardConfigPO::getTicketId)
                .collect(Collectors.toList());

        //构建奖券配置表的查询条件，关联配置表的ticket_id
        LambdaQueryWrapper<InviteTicketConfigPO> ticketQueryWrapper = new LambdaQueryWrapper<>();
        ticketQueryWrapper.in(InviteTicketConfigPO::getId, ticketIdList);
        ticketQueryWrapper.eq(InviteTicketConfigPO::getDeleted, 0);
        List<InviteTicketConfigPO> ticketList = ticketConfigService.list(ticketQueryWrapper);
        //合并查询结果
        List<InviteRewardRecordFullDTO> dtoList = new ArrayList<>();
        for (InviteRewardRecordPO recordPO : recordPage.getRecords()) {
            InviteRewardRecordFullDTO dto = new InviteRewardRecordFullDTO();
            //使用beancopy 填充invite_reward_record表的字段
            BeanUtils.copyProperties(recordPO, dto);
            // 根据reward_id查找对应的invite_reward_config表记录并填充字段
            Optional<InviteRewardConfigPO> configOptional = configList.stream()
                    .filter(config -> config.getId().equals(recordPO.getRewardId()))
                    .findFirst();
            if (configOptional.isPresent()) {
                InviteRewardConfigPO configPO = configOptional.get();
                BeanUtils.copyProperties(configPO, dto);
                dto.setConfigId(configPO.getId());
                dto.setConfigRewardNumber(configPO.getRewardNumber());
            }
            dto.setRewardNumber(recordPO.getRewardNumber());
            // 根据ticket_id查找对应的invite_ticket_config表记录并填充字段
            Optional<InviteTicketConfigPO> ticketOptional = ticketList.stream().filter(ticket -> ticket.getId().equals(dto.getTicketId()))
                    .findFirst();
            if (ticketOptional.isPresent()) {
                InviteTicketConfigPO ticketPO = ticketOptional.get();
                BeanUtils.copyProperties(ticketPO, dto);
                dto.setTicketConfigId(ticketPO.getId());
            }
            dtoList.add(dto);
        }
        Page<InviteRewardRecordFullDTO> page = new Page<>(request.getPageNum(), request.getPageSize(), recordPage.getTotal());
        page.setRecords(dtoList);
        return page;
    }

    private static @NotNull LambdaQueryWrapper<InviteRewardRecordPO> getRecordQueryWrapper(RewardDetailRequest request, Long basicId, Integer rewardTypeFilter, Integer status, List<Integer> statusList) {
        LambdaQueryWrapper<InviteRewardRecordPO> recordQueryWrapper = new LambdaQueryWrapper<>();
        recordQueryWrapper.eq(InviteRewardRecordPO::getBasicId, basicId);
        recordQueryWrapper.eq(InviteRewardRecordPO::getDeleted, 0);
        if (rewardTypeFilter != null) {
            if (rewardTypeFilter.equals(1)) {
                recordQueryWrapper.in(InviteRewardRecordPO::getRewardType, Arrays.asList(InviteRewardTypeEnum.TICKET.code(), InviteRewardTypeEnum.YOUZAN_COUPON.code()));
            } else {
                recordQueryWrapper.eq(InviteRewardRecordPO::getRewardType, rewardTypeFilter);
            }

        }
        if (status != null) {
            recordQueryWrapper.eq(InviteRewardRecordPO::getStatus, status);
        }else{
            if(CollectionUtil.isNotEmpty(statusList)){
                recordQueryWrapper.in(InviteRewardRecordPO::getStatus, statusList);
            }
        }
        if(request.getOrderByRewardList()){
            recordQueryWrapper.orderByDesc(InviteRewardRecordPO::getGmtCreate)
                    .orderByAsc(InviteRewardRecordPO::getRewardType);
        }else {
            recordQueryWrapper.orderByDesc(InviteRewardRecordPO::getGmtCreate);
        }
        return recordQueryWrapper;
    }

    public InviteRewardRecordFullDTO getInviteRewardRecordFullDTOById(Long rewardRecordId) {
        // 主表的查询条件
        LambdaQueryWrapper<InviteRewardRecordPO> recordQueryWrapper = new LambdaQueryWrapper<>();
        recordQueryWrapper.eq(InviteRewardRecordPO::getId, rewardRecordId);
        recordQueryWrapper.eq(InviteRewardRecordPO::getDeleted, 0);

        // 查询主表记录
        InviteRewardRecordPO recordPO = inviteRewardRecordService.getOne(recordQueryWrapper);
        if (recordPO == null) {
            return null;
        }
        // 构建配置表的查询条件
        LambdaQueryWrapper<InviteRewardConfigPO> configQueryWrapper = new LambdaQueryWrapper<>();
        configQueryWrapper.eq(InviteRewardConfigPO::getId, recordPO.getRewardId());
        configQueryWrapper.eq(InviteRewardConfigPO::getDeleted, 0);
        List<InviteRewardConfigPO> configList = rewardConfigService.list(configQueryWrapper);

        // 获取 ticket_id 列表，用于关联查询奖券配置表
        List<Integer> ticketIdList = configList.stream()
                .map(InviteRewardConfigPO::getTicketId)
                .collect(Collectors.toList());

        // 构建奖券配置表的查询条件
        LambdaQueryWrapper<InviteTicketConfigPO> ticketQueryWrapper = new LambdaQueryWrapper<>();
        ticketQueryWrapper.in(InviteTicketConfigPO::getId, ticketIdList);
        ticketQueryWrapper.eq(InviteTicketConfigPO::getDeleted, 0);
        List<InviteTicketConfigPO> ticketList = ticketConfigService.list(ticketQueryWrapper);

        // 构建 InviteRewardRecordFullDTO 对象
        InviteRewardRecordFullDTO dto = new InviteRewardRecordFullDTO();
        BeanUtils.copyProperties(recordPO, dto);

        // 根据 reward_id 查找对应的 InviteRewardConfigPO 记录并填充字段
        Optional<InviteRewardConfigPO> configOptional = configList.stream()
                .filter(config -> config.getId().equals(recordPO.getRewardId()))
                .findFirst();
        if (configOptional.isPresent()) {
            InviteRewardConfigPO configPO = configOptional.get();
            BeanUtils.copyProperties(configPO, dto);
            dto.setConfigId(configPO.getId());
            dto.setConfigRewardNumber(configPO.getRewardNumber());
        }

        // 根据 ticket_id 查找对应的 InviteTicketConfigPO 记录并填充字段
        Optional<InviteTicketConfigPO> ticketOptional = ticketList.stream()
                .filter(ticket -> ticket.getId().equals(dto.getTicketId()))
                .findFirst();
        if (ticketOptional.isPresent()) {
            InviteTicketConfigPO ticketPO = ticketOptional.get();
            BeanUtils.copyProperties(ticketPO, dto);
            dto.setTicketConfigId(ticketPO.getId());
        }

        return dto;
    }

    private static @NotNull List<Node> getNodes(InviteRewardRecordFullDTO record) {
        List<Node> list = new ArrayList<>();
        //0-积分奖励 1-奖券 2-产康金奖励
//        switch (record.getRewardType()) {
//            case 2:
//            case 0:
                Node node = new Node();
                if (record.getStageTime() != null) {
                    node.setTime(DATE_FORMATTER_mmss.get().format(record.getStageTime()));
                }
                if (record.getScene() != null) {
                    String prefix = "";
                    if (record.getInviteeBasicId() == null) {
                        prefix = "被";
                    }
                    node.setTitle(prefix + InviteRewardSceneEnum.getByCode(record.getScene()).desc());
                }
                list.add(node);
//                break;
//            case 1:
//                //计算券的有效期
//                Node valid = getTimeNode(record.getStartTime(), record.getEndTime(), record);
//                list.add(valid);
//                break;
//        }
        return list;
    }

    @Override
    public Node getTimeNode(Date startTime, Date endTime, Integer activeType) {
        Node valid = new Node();
        String startTimeStr = "";
        String endTimeStr = "";
        String title = "有效期";
        if(activeType.equals(InviteTicketActiveTypeEnum.CHECK_IN.code())){
            if (startTime == null && endTime == null) {
                valid.setTime("入住后生效（有效期一年）");
            }else{
                if (startTime != null) {
                    startTimeStr = DATE_FORMATTER.get().format(startTime);
                }
                if (endTime != null) {
                    endTimeStr = DATE_FORMATTER.get().format(endTime);
                }
                valid.setTime(startTimeStr + "~" + endTimeStr );
            }
        }else{
            if (startTime == null && endTime == null) {
                valid.setTime("");
            }else{
                if (startTime != null) {
                    startTimeStr = DATE_FORMATTER.get().format(startTime);
                }
                if (endTime != null) {
                    endTimeStr = DATE_FORMATTER.get().format(endTime);
                }
                valid.setTime(startTimeStr + "~" + endTimeStr + "可用");
            }
        }
        valid.setTitle(title);
        return valid;
    }

    private @NotNull Map<Integer, InviteRewardConfigPO> getInviteRewardConfigPOMap(Set<Integer> rewardIds) {
        List<InviteRewardConfigPO> rewardConfigPOS = rewardConfigService.list(new LambdaQueryWrapper<InviteRewardConfigPO>()
                .in(InviteRewardConfigPO::getId, new ArrayList<>(rewardIds))
                .eq(InviteRewardConfigPO::getDeleted, 0)
        );
        Map<Integer, InviteRewardConfigPO> rewardConfigMap = rewardConfigPOS.stream().collect(Collectors.toMap(InviteRewardConfigPO::getId, config -> config));
        return rewardConfigMap;
    }

    @Override
    public Result<Integer> testMessage(MessageTestRequest request) {
        sendQiYeWxMessage(request.getPhone(), request.getParentName());
        return Result.success();
    }

    private void sendQiYeWxMessage(String phone, String parentName) {
        try {
            if(!bizConfig.getInviteSendQwMsg()){
                return ;
            }
            String content = String.format("会员邀请客户来啦\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\uD83C\uDF8A\n"
                            + "手机号：%s\n"
                            + "渠道：%s\n"
                            + "门店：%s\n"
                            + "推荐会员：%s\n"
                            + "请及时联系客户~",
                    phone,
                    "微信小程序",
                    "暂无",
                    parentName//推荐人
                    );

            //企微群通知
            noticeMessageAssembler.pushMsgScope(content, bizConfig.getMsgBizType(),bizConfig.getInviteQwMsgNoticePhone(),0);
            log.info("老带新v2.0绑定-发企微通知成功:通知内容:{}",content );
        }catch (Exception e){
            log.error("老带新v2.0绑定-发企微通知失败", e);
        }
    }

    @Override
    public RewardCouponDetailVO couponDetail(RewardCouponDetailByIdRequest request) {
        log.info("couponDetail参数信息:{}", JSONUtil.toJsonStr(request));
        if (Objects.isNull(request)) {
            return new RewardCouponDetailVO();
        }
        InviteRewardRecordFullDTO fullDTOById = getInviteRewardRecordFullDTOById(request.getId());
        return RewardCouponDetailVO.builder()
                .id(fullDTOById.getId())
                .basicId(fullDTOById.getBasicId())
                .couponName(fullDTOById.getRewardTitle())
                .rewardType(fullDTOById.getRewardType())
                .rewardTypeStr(InviteRewardTypeEnum.getByCode(fullDTOById.getRewardType()).desc())
                .status(fullDTOById.getStatus())
                .statusStr(InviteRewardRecordStatusEnum.getByCode(fullDTOById.getStatus()).desc())
//                .list(getNodes(fullDTOById))
                .ruleText(fullDTOById.getRuleContent())
                .gmtCreate(fullDTOById.getGmtCreate())
                .build();
    }

    @Override
    public PageVO<RewardCouponDetailVO> couponList(RewardCouponDetailRequest request) {
        if (Objects.isNull(request)) {
            return new PageVO<>(Lists.newArrayList(), 0, 0,0);
        }
        log.info("couponList参数信息:{}", JSONUtil.toJsonStr(request));
        RewardDetailRequest req = new RewardDetailRequest();
        BeanUtils.copyProperties(request, req);
        req.setRewardType(1);
        if(request.getStatus()!=null) {
            if (request.getStatus().equals(1)) {
                req.setStatus(null);
                req.setStatusList(Arrays.asList(0, 1));
            } else if (request.getStatus().equals(4)) {
                req.setStatus(null);
                req.setStatusList(Arrays.asList(3, 4));
            } else {
                req.setStatus(request.getStatus());
            }
        }
        PageVO<RewardDetailVO> page = rewardList(req);
        if (page == null || CollectionUtil.isEmpty(page.getList())) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        List<RewardDetailVO> list = page.getList();
        List<RewardCouponDetailVO> vos = new ArrayList<>();
        for (RewardDetailVO rewardDetailVO : list) {
            RewardCouponDetailVO vo = new RewardCouponDetailVO();
            BeanUtils.copyProperties(rewardDetailVO, vo);
            List<Node> timeNode = rewardDetailVO.getList();
            vo.setList(Arrays.asList(timeNode.get(0).getTitle(), timeNode.get(0).getTime()));
            vo.setCouponName(rewardDetailVO.getCouponName());
            vos.add(vo);
        }
        return new PageVO<>(vos, page.getTotalCount(), request.getPageSize(), request.getPageNum());
    }

    @Override
    public PageVO<InviteRecordVO> inviteList(GetInviteRecordsRequest request) {
        if (Objects.isNull(request)) {
            return new PageVO<>(Lists.newArrayList(), 0, 0, 0);
        }
        log.info("inviteRecordList参数信息:{}", JSONUtil.toJsonStr(request));
        List<InviteRecordVO> list = new ArrayList<>();
        //获取所有被邀请人信息
        PageVO<HeInviteRelationPO> page = heInviteRelationService.pageListByParentBasicIds(request.getPageNum(), request.getPageSize(), Collections.singletonList(request.getParentBasicId()));
        if (page == null || CollectionUtil.isEmpty(page.getList())) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        List<HeInviteRelationPO> inviteRelations = page.getList();
        Set<Long> basicIds = inviteRelations.stream().map(HeInviteRelationPO::getBasicId).collect(Collectors.toSet());

        Integer wechatUserType = brandStrategyService.processBrandType(request.getBrandType(), BrandTypeBusinessTypeEnum.WECHAT_USER);

        List<TabWechatUserPO> tabWechatUserPOS = tabWechatUserService.list(new LambdaQueryWrapper<TabWechatUserPO>()
            .in(TabWechatUserPO::getBasicUid, new ArrayList<>(basicIds))
            .eq(TabWechatUserPO::getFromType, wechatUserType)
            .orderByDesc(TabWechatUserPO::getUpdatedAt));
        Map<Integer, TabWechatUserPO> wechatUserMap = tabWechatUserPOS.stream().collect(Collectors.toMap(TabWechatUserPO::getBasicUid, Function.identity(), (o1, o2) -> o1));

        List<HeUserBasicPO> heUserBasicPOS = heUserBasicService.queryUserBasicInfoByIdList(new ArrayList<>(basicIds));
        Map<Long, HeUserBasicPO> heUserBasicPOMap = heUserBasicPOS.stream().collect(Collectors.toMap(HeUserBasicPO::getId, Function.identity()));

        if (CollectionUtil.isNotEmpty(inviteRelations)) {
            //构建InviteRecordVO
            for (HeInviteRelationPO inviteRelation : inviteRelations) {
                InviteRecordVO vo = new InviteRecordVO();
                vo.setBasicId(inviteRelation.getBasicId());
                setInfo(inviteRelation.getBasicId().intValue(),
                        (setter, avatar, nickname) -> {
                            setter.setAvatar(avatar);
                            setter.setNickname(nickname);
                        }
                        , wechatUserMap, request.getBrandType(), heUserBasicPOMap, vo);
                vo.setList(new ArrayList<>());
                setNodes(vo, inviteRelation, HeInviteRelationPO::getGmtCreate, "邀请绑定");
                setNodes(vo, inviteRelation, HeInviteRelationPO::getCheckinStoreTime, "邀请到店");
                setNodes(vo, inviteRelation, HeInviteRelationPO::getSignOrderTime, "邀请签单");
                list.add(vo);
            }
        }
        return new PageVO<>(list, page.getTotalCount(), page.getPageSize(), page.getPageNo());
    }

    private static <T> void setInfo(Integer basicId, InviteServiceImpl.SetUserWechatInfo<T> setter,
        Map<Integer, TabWechatUserPO> wechatUserMap, Integer brandType, Map<Long, HeUserBasicPO> heUserBasicPOMap,T targetObject) {
        try {
            String avatar = "";
            String nickName = "";
            if (basicId == null) {
                return;
            }
            TabWechatUserPO wechatUserPO = wechatUserMap.get(basicId);
            if (wechatUserPO != null) {
                avatar = wechatUserPO.getAvatarUrl();
                if (StringUtils.isBlank(wechatUserPO.getNickname())) {
                    String phoneSub = "";
                    String phoneNumber = wechatUserPO.getPhoneNumber();
                    if (StringUtils.isNotBlank(phoneNumber)) {
                        phoneSub = phoneNumber.substring(phoneNumber.length() - 4);
                    }
                    nickName = StringUtils.isBlank(wechatUserPO.getNickname()) ? "会员" + phoneSub : wechatUserPO.getNickname();
                } else {
                    nickName = wechatUserPO.getNickname();
                }
            } else {
                HeUserBasicPO userBasicPO = heUserBasicPOMap.get(basicId.longValue());
                if (Objects.nonNull(userBasicPO)) {
                    String phoneSub = "";
                    String phoneNumber = userBasicPO.getPhone();
                    if (StringUtils.isNotBlank(phoneNumber)) {
                        if (phoneNumber.length() >= 4) {
                            phoneSub = phoneNumber.substring(phoneNumber.length() - 4);
                        } else {
                            phoneSub = phoneNumber;
                        }
                    }
                    nickName = "会员" + phoneSub;
                }
                avatar = BRAND_AVATAR_MAP.get(brandType);
            }
            setter.set(targetObject, avatar, nickName);
        }catch (Exception e){
            log.error("设置头像 error",e);
        }
    }

    @FunctionalInterface
    interface SetUserWechatInfo<T> {
        void set(T vo, String avatar, String nickname);
    }

    public void processInviteRelation(HeInviteRelationPO po, List<Node> timeList) {
        if (po == null) {
            return;
        }

        addNodeIfNotNull(timeList, po.getGmtCreate());
        addNodeIfNotNull(timeList, po.getCheckinStoreTime());
        addNodeIfNotNull(timeList, po.getSignOrderTime());
    }

    private void setNodes(InviteRecordVO vo, HeInviteRelationPO inviteRelation, Function<HeInviteRelationPO, Date> dateFunction, String description) {
        Date date = dateFunction.apply(inviteRelation);
        if (date != null) {
            Node node = new Node();
            node.setTime(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
            node.setTitle(description);
            vo.getList().add(node);
        }
    }

    private void addNodeIfNotNull(List<Node> timeList, Date date) {
        if (date != null) {
            Node node = new Node();
            node.setTime(DATE_FORMATTER_mmss.get().format(date));
            timeList.add(node);
        }
    }

    @Override
    public Integer checkInvite(CanInviteRequest request) {
        log.info("checkInvite参数信息:{}", JSONUtil.toJsonStr(request));
        if(Objects.isNull(request.getBasicId())){
            return 0;
        }
        //在邀请人白名单中查找用户 && 邀请人能邀请总开关
        if(bizConfig.getParentInviteFlag() && findInWhiteList(bizConfig.getInviteParentIds(),request.getBasicId().intValue(),false)){
            return 1;
        }
        //true:符合条件
        boolean flag = false;
        List<Integer> orderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(),
            OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(),
            OmniOrderTypeEnum.PRODUCTION_ORDER.getCode());

        List<HeOrderPO> oldOrders = heOrderService.queryOrderByBasicIds(
            Collections.singletonList(request.getBasicId()), orderTypeList, null,
            Collections.singletonList(3),1,true);
        if(CollectionUtil.isNotEmpty(oldOrders)){
            log.info("邀请人{}，有老订单，数量:{},flag:{}", request.getBasicId(), 0,flag);
            //循环老订单,
            for (HeOrderPO oldOrder : oldOrders) {
                if(oldOrder.getPercentFirstTime()!=null &&
                        (oldOrder.getPercentFirstTime()>0 || oldOrder.getPercentFirstTime().equals(-1))){
                    flag = true;
                }
            }
        }
        if(!flag){
            log.info("邀请人{}，没有老订单,查询新订单", request.getBasicId());
            //查询新订单
            List<HeOrderPO> newOrders = heOrderService.queryOrderByBasicIds(
                Collections.singletonList(request.getBasicId()), orderTypeList, 1,
                Collections.singletonList(3),1,true);
            if(CollectionUtil.isNotEmpty(newOrders)) {
                log.info("邀请人{}，新订单数量:{}", request.getBasicId(), newOrders.size());
            }
            //查询订单的goodslist
            for (HeOrderPO newOrder : newOrders) {
                if (newOrder.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())
                    && (newOrder.getPercentFirstTime()>0 || newOrder.getPercentFirstTime().equals(-1))) {
                    flag = true;
                    break;
                }
                List<GoodsNewVO> orderGoodsList = orderManager.queryOrderGoodsListByOrderId(newOrder.getOrderId());
                if (CollectionUtil.isNotEmpty(orderGoodsList)) {
                    List<Integer> goodsTypeList = Arrays.asList(0, 1, 28);
                    long count = orderGoodsList.stream()
                            .filter(i -> goodsTypeList.contains(i.getGoodsType())
                                    && (newOrder.getPercentFirstTime()>0||newOrder.getPercentFirstTime().equals(-1)))
                            .count();
                    if (count > 0L) {
                        flag = true;
                        break;
                    }
                }
            }
        }
        if(!flag){
            return 0;
        }
        return 1;
    }

    /**
     * 0:否;1:可以被邀请
     *
     * @param request
     * @return
     */
    @Override
    public Integer checkBind(CanInviteRequest request) {
        log.info("判断被邀请人-参数信息:{}", JSONUtil.toJsonStr(request));
        if(Objects.isNull(request.getBasicId())){
            return 0;
        }
        //在被邀请人白名单中查找用户 && 被邀请总开关
        if(bizConfig.getInviteFlag() && findInWhiteList(bizConfig.getInviteBasicIds(),request.getBasicId().intValue(),false)){
            return 1;
        }
        //查询是否被绑过
        InviteInfoVO inviteInfoVO = heInviteRelationService.queryUserBindInfoByBasicId(request.getBasicId());
        //有邀请记录=不能被邀请
        if (ObjectUtil.isNotNull(inviteInfoVO)) {
            log.info("判断被邀请人-{}存在邀请记录信息={}", request.getBasicId(),JSONUtil.toJsonStr(inviteInfoVO));
            return 0;
        }
        //true:符合条件
        List<Integer> orderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(),
            OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(),
            OmniOrderTypeEnum.PRODUCTION_ORDER.getCode());

        List<HeOrderPO> oldOrders = heOrderService.queryOrderByBasicIds(Collections.singletonList(request.getBasicId()), orderTypeList, null,null,null,false);
        //被邀请人存在老订单直接返回不符合条件
        if(CollectionUtil.isNotEmpty(oldOrders)){
            log.info("判断被邀请人-被邀请人{}存在老订单,直接返回:flag:{}", request.getBasicId(),0);
            return 0;
        }
        //todo 判断老订单的条件，付款，报单
        //查询新订单
        List<HeOrderPO> newOrders = heOrderService.queryOrderByBasicIds(Collections.singletonList(request.getBasicId()), orderTypeList, 1,null,null,false);
        for (HeOrderPO newOrder : newOrders) {
            if (newOrder.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
                log.info("判断被邀请人-被邀请人{}新订单存在不符合条件的类目,直接返回:flag:{}", request.getBasicId(),0);
                return 0;
            }
            List<GoodsNewVO> orderGoodsList = orderManager.queryOrderGoodsListByOrderId(newOrder.getOrderId());
            if (CollectionUtil.isNotEmpty(orderGoodsList)) {
                List<Integer> goodsTypeList = Arrays.asList(0, 1, 28);
                long count = orderGoodsList.stream()
                        .filter(i -> goodsTypeList.contains(i.getGoodsType()))
                        .count();
                if (count > 0L) {
                    log.info("判断被邀请人-被邀请人{}新订单存在不符合条件的类目,直接返回:flag:{}", request.getBasicId(),0);
                    return 0;
                }
            }
        }
        return 1;
    }

    @Override
    public CheckRewardResponse verify(CheckRewardRequest request) {
        log.info("verify参数信息:{}", JSONUtil.toJsonStr(request));
        CheckRewardResponse vo = new CheckRewardResponse();
        EcpStorePO storePO = storeService.queryStoreByStoreId(request.getStoreId().intValue());
        log.info("verify参数信息-storePO:{}", JSONUtil.toJsonStr(storePO));
        InviteRewardRecordFullDTO dto = getInviteRewardRecordFullDTOById(request.getRewardRecordId());
        log.info("verify参数信息-dto is null?:{}", ObjectUtil.isNull(dto));
        CheckRewardResponse checkVerify = checkVerify(request, storePO, vo, dto);
        if (checkVerify != null) return checkVerify;
        HeUserBasicPO userBasicPO = heUserBasicService.getById(dto.getBasicId());
        //成功,修改奖励记录状态为已核销
        InviteRewardRecordPO rewardRecord = inviteRewardRecordService.getOne(new LambdaQueryWrapper<InviteRewardRecordPO>()
                .eq(InviteRewardRecordPO::getId, request.getRewardRecordId())
        );
        rewardRecord.setStatus(InviteRewardRecordStatusEnum.USED.code());
        rewardRecord.setGmtModified(new Date());
        rewardRecord.setVerifyStoreId(request.getStoreId().intValue());
        rewardRecord.setVerifyStaffId(request.getOperator().getOperatorGuid());
        rewardRecord.setVerifyTime(new Date());
        rewardRecord.setVerifyStaffName(request.getOperator().getOperatorName());
        rewardRecord.setVerifyRemark(request.getRemark());
        inviteRewardRecordService.updateById(rewardRecord);

        //修改customer_base_voucher表的状态
        updateBaseVoucher(rewardRecord,request);
        buildVO(request, vo, userBasicPO, dto, storePO,rewardRecord);
        return vo;
    }
    private void updateBaseVoucher(InviteRewardRecordPO rewardRecord, CheckRewardRequest request) {
        CustomerBaseVoucherPO customerVoucherPO = customerBaseVoucherService.getOne(new LambdaQueryWrapper<CustomerBaseVoucherPO>()
                .eq(CustomerBaseVoucherPO::getCouponId, rewardRecord.getId())
        );
        if(ObjectUtil.isNotNull(customerVoucherPO)){
            customerVoucherPO.setStatus(InviteRewardRecordStatusEnum.USED.code());
            customerVoucherPO.setGmtModified(new Date());
            customerVoucherPO.setVerifyTime(new Date());
            customerVoucherPO.setVerifyStaffName(request.getOperator().getOperatorName());
            customerBaseVoucherService.updateById(customerVoucherPO);
        }
    }
    private @Nullable CheckRewardResponse checkVerify(CheckRewardRequest request, EcpStorePO storePO, CheckRewardResponse vo, InviteRewardRecordFullDTO dto) {
        HeUserBasicPO userBasicPO = null;
        if (ObjectUtil.isNull(dto)) {
            handleErrorResponse(request, vo, "核销失败:奖励不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (ObjectUtil.isNull(storePO)) {
            // 查询用户信息
            if (ObjectUtil.isNotNull(dto.getBasicId())) {
                userBasicPO = heUserBasicService.getById(dto.getBasicId());
            }
            handleErrorResponse(request, vo, "核销失败:门店不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        // 查询用户信息
        if (ObjectUtil.isNull(dto.getBasicId())) {
            handleErrorResponse(request, vo, "核销失败:用户不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        userBasicPO = heUserBasicService.getById(dto.getBasicId());
        //获取用户
        if (ObjectUtil.isNull(userBasicPO)) {
            handleErrorResponse(request, vo, "核销失败:用户不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        // 券信息
        if (ObjectUtil.isNull(dto.getRewardId())) {
            handleErrorResponse(request, vo, "核销失败:礼券不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (dto.getDeleted().equals(1)) {
            handleErrorResponse(request, vo, "核销失败:礼券不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (dto.getStatus().equals(InviteRewardRecordStatusEnum.PENDING_ACTIVATION.code())) {
            handleErrorResponse(request, vo, "核销失败:礼券未生效", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (dto.getStatus().equals(InviteRewardRecordStatusEnum.USED.code())) {
            handleErrorResponse(request, vo, "核销失败:礼券已使用", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (dto.getStatus().equals(InviteRewardRecordStatusEnum.EXPIRED.code())) {
            handleErrorResponse(request, vo, "核销失败:礼券已过期", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (dto.getStatus().equals(InviteRewardRecordStatusEnum.INVALID.code())) {
            handleErrorResponse(request, vo, "核销失败:礼券已失效", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (ObjectUtil.isNull(dto.getConfigId())) {
            handleErrorResponse(request, vo, "核销失败:礼券不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (ObjectUtil.isNull(dto.getTicketId())) {
            handleErrorResponse(request, vo, "核销失败:礼券不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        if (ObjectUtil.isNull(ObjectUtil.isNull(dto.getTicketConfigId()))) {
            handleErrorResponse(request, vo, "核销失败:礼券不存在", dto, userBasicPO, storePO,null);
            return vo;
        }
        return null;
    }

    private static void handleErrorResponse(CheckRewardRequest request,
                                            CheckRewardResponse vo,
                                            String errorMsg,
                                            InviteRewardRecordFullDTO dto,
                                            HeUserBasicPO userBasicPO,
                                            EcpStorePO storePO,InviteRewardRecordPO rewardRecord) {
        vo.setErrorMsg(errorMsg);
        buildVO(request, vo, userBasicPO, dto, storePO, rewardRecord);
    }

    private static void buildVO(CheckRewardRequest request, CheckRewardResponse vo,
                                HeUserBasicPO userBasicPO,
                                InviteRewardRecordFullDTO dto,
                                EcpStorePO storePO, InviteRewardRecordPO rewardRecord) {
        // 返回奖励信息
        if (userBasicPO != null) {
            vo.setOwnerName(userBasicPO.getName());
            vo.setOwnerPhone(userBasicPO.getPhone());
        }
        if (dto != null) {
            vo.setRedemptionTime(DateUtil.format(dto.getVerifyTime(), "yyyy-MM-dd HH:mm:ss"));
            vo.setVerifyName(dto.getVerifyStaffName());
            vo.setCouponName(dto.getRewardTitle());
            vo.setCouponDesc(dto.getRuleContent());
        }
        if (storePO != null) {
            vo.setRedemptionStore(storePO.getStoreName());
            vo.setStoreId(storePO.getStoreId());
        }
        if(rewardRecord!=null){
            vo.setRedemptionTime(DateUtil.format(rewardRecord.getVerifyTime(), "yyyy-MM-dd HH:mm:ss"));
            vo.setVerifyName(rewardRecord.getVerifyStaffName());
        }
        vo.setRemark(request.getRemark());
    }

    private String findValueByConfig(String keyPrefix,String defaultValue){
        try {
            String value="";
            List<String> errorMsg = errorMsgConfig.getInviteErrorMsg();
            log.info("errorMsg nacos配置:{}", JSON.toJSONString(errorMsg));
            if (CollectionUtil.isNotEmpty(errorMsg)) {
                for (String string : errorMsg) {
                    if (string.startsWith(keyPrefix+defaultValue)) {
                        String[] split = string.split(":");
                        value = split[1];
                    }
                }
            }
            return StringUtils.isNotBlank(value)?value:defaultValue;
        }catch (Exception e){
            log.error("findValueByConfig error",e);
        }
        return "";
    }
    private Result<Integer> bindCheckBasic(BindRequest request) {
        if (ObjectUtil.isNull(request.getBasicId()) || request.getBasicId() <= 0) {
            return returnData(false, findValueByConfig("bindCheck", CAN_NOT_FIND_INVITTEE));
        }
        if (ObjectUtil.isNull(request.getParentBasicId()) || request.getParentBasicId() <= 0) {
            return returnData(false, findValueByConfig("bindCheck", CAN_NOT_FIND_INVITTER));
        }

        // 不能邀请自己
        if (request.getBasicId().equals(request.getParentBasicId())) {
            return returnData(false, findValueByConfig("bindCheck", CAN_NOT_BIND_SELF));
        }

        // 获取被邀请人信息
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(request.getParentBasicId());
        if (ObjectUtil.isNull(userBasicPO) || userBasicPO.getId() == null) {
            return returnData(false, findValueByConfig("bindCheck", CAN_NOT_FIND_INVITTER));
        }

        // 获取邀请人信息
        HeUserBasicPO userBasicPO2 = heUserBasicService.queryUserBasicInfoById(request.getBasicId());
        if (ObjectUtil.isNull(userBasicPO2) || userBasicPO2.getId() == null) {
            return returnData(false, findValueByConfig("bindCheck", CAN_NOT_FIND_INVITTEE));
        }

        // 查询是否被绑过
        InviteInfoVO inviteInfoVO = heInviteRelationService.queryUserBindInfoByBasicId(request.getBasicId());
        log.info("查询是否被绑过,客户的邀请人信息:{}", JSONUtil.toJsonStr(inviteInfoVO));
        if (ObjectUtil.isNotNull(inviteInfoVO)) {
            if (inviteInfoVO.getParentBasicId().equals(request.getParentBasicId())) {
                return returnData(true, BIND_SUCCESS);
            } else {
                return returnData(false, INVITEE_BIND_ERROR_MSG);
            }
        }

        // 所有检查通过
        return returnData(true, "");
    }

    @Override
    public Result<Integer> bind(BindRequest request) {
        log.info("bind参数信息:{}", JSONUtil.toJsonStr(request));
        Result<Integer> checkResult = bindCheck(request);
        if (checkResult != null) return checkResult;
        if (doBind(request)) {
            return returnData(true, BRAND_BIND_SUCCESS_MAP.get(request.getBrandType()));
        }
        return returnData(false, "绑定失败");
    }

    private @Nullable Result<Integer> bindCheck(BindRequest request) {
        //check
        Result<Integer> checkResult = bindCheckBasic(request);
        if (!checkResult.getSuccess()) {
            return checkResult;
        }
        if (BIND_SUCCESS.equals(checkResult.getMsg())) {
            return checkResult;
        }
        Integer bindCheckOrder = bindCheckOrder(request);
        if (bindCheckOrder == 0) {
            return returnData(false, INVITEE_BIND_ERROR_MSG);
        }
        return null;
    }

    /**
     * 0:否;1:是
     *
     * @param request
     */
    private Integer bindCheckOrder(BindRequest request) {
        CanInviteRequest canBindRequest = new CanInviteRequest();
        canBindRequest.setBasicId(request.getBasicId());
        return checkBind(canBindRequest);
    }

    private boolean doBind(BindRequest request) {
        String qrCode = UMSUtils.generateQrCode(6);
        if (StringUtils.isBlank(qrCode)) {
            throw new BusinessException("生成二维码失败");
        }
        HeInviteQrCodePO inviteQrCodePO = getInviteQrCodePO(request, qrCode);
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(request.getBasicId());
        HeUserBasicPO parentUserBasicPO = heUserBasicService.queryUserBasicInfoById(request.getParentBasicId());
        HeInviteRelationPO inviteRelationPO = new HeInviteRelationPO();
        inviteRelationPO.setBasicId(request.getBasicId());
        inviteRelationPO.setParentBasicId(request.getParentBasicId());
        inviteRelationPO.setBrandType(request.getBrandType());
        inviteRelationPO.setQrCode(qrCode);
        inviteRelationPO.setQrId(inviteQrCodePO.getId());
        boolean saveResult = heInviteRelationService.save(inviteRelationPO);
        log.info("保存邀请关系结果:{}", saveResult);
        if (saveResult) {
            //发放各方奖励
            sendReward(request);
            //通知
            sendQiYeWxMessage(userBasicPO!=null?userBasicPO.getPhone():"",
                    parentUserBasicPO!=null?parentUserBasicPO.getPhone():"");
            return true;
        }
        return false;
    }

    private @NotNull HeInviteQrCodePO getInviteQrCodePO(BindRequest request, String qrCode) {
        HeInviteQrCodePO inviteQrCodePO = new HeInviteQrCodePO();
        inviteQrCodePO.setQrCode(qrCode);

        Integer source = brandStrategyService.processBrandType(request.getBrandType(), BrandTypeBusinessTypeEnum.WECHAT_USER);
        inviteQrCodePO.setQrSource(source);

        inviteQrCodePO.setBasicId(request.getParentBasicId());
        inviteQrCodePO.setQrState(0);
        inviteQrCodePO.setGmtCreate(new Date());
        inviteQrCodePO.setExpireTime(0L);
        inviteQrCodePO.setGmtModified(new Date());
        inviteQrCodePO.setDeleted(0);
        heInviteQrCodeService.save(inviteQrCodePO);
        return inviteQrCodePO;
    }

    private static @NotNull Result<Integer> returnData(Boolean success, String message) {
        Result<Integer> result = new Result<>();
        result.setCode(Result.success().getCode());
        result.setData(success ? 1 : 0);
        result.setMsg(message);
        result.setSuccess(success);
        return result;
    }

    private void sendReward(BindRequest request) {
        if (bizConfig.getInviteSendReward()) {
            try {
                InviteRewardRecordRequest sendRewardRequest = InviteRewardRecordRequest.builder()
                        .basicId(request.getBasicId().intValue())
                        .scene(InviteRewardSceneEnum.INVITE_BIND.code())
                        .bizType(InviteBizTypeEnum.INVITEE_REWARD.code())
                        .stageTime(new Date())
                        .build();
                log.info("第一次接受邀请，发送奖励:{}", JSONUtil.toJsonStr(sendRewardRequest));
                inviteSnowballEngine.sendInviteReward(sendRewardRequest);
            } catch (Exception e) {
                log.error("第一次接受邀请，发送奖励失败", e);
            }
        }
    }

    private void sendWXMessage(BindRequest request) {
    }

    @Override
    public UserInfoVO userInfo(UserInfoRequest request) {
        log.info("参数信息:{}", JSONUtil.toJsonStr(request));
        UserInfoVO userInfo = new UserInfoVO();
        // 获取邀请人信息
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(request.getBasicId());
        if (ObjectUtil.isNull(userBasicPO)) {
            return userInfo;
        }

        //查询微信信息
        Integer wechatUserType = brandStrategyService.processBrandType(request.getBrandType(), BrandTypeBusinessTypeEnum.WECHAT_USER);
        TabWechatUserPO wechatUser = tabWechatUserService.queryWechatInfoByBasicUidAndBrandType(request.getBasicId().intValue(), wechatUserType);
        if (wechatUser == null) {
            //登出状态，数据会被删除
            userInfo.setBasicId(userBasicPO.getId());
            userInfo.setAvatar(BRAND_AVATAR_MAP.get(request.getBrandType()));
            String phoneSub = "";
            if (StringUtils.isNotBlank(userBasicPO.getPhone())) {
                phoneSub = userBasicPO.getPhone().substring(userBasicPO.getPhone().length() - 4);
            }
            userInfo.setNickname("会员" + phoneSub);
            userInfo.setText("");
            return userInfo;
        }
        userInfo.setBasicId(userBasicPO.getId());
        userInfo.setNickname(wechatUser.getNickname());
        userInfo.setAvatar(wechatUser.getAvatarUrl());
        userInfo.setText("");
        return userInfo;
    }
    /**
     * 判断用户是否在名单中
     *
     * @param list
     * @param basicId
     * @param notFoundReturnDefault
     * @return
     */
    private static boolean findInWhiteList(String list, Integer basicId,boolean notFoundReturnDefault) {
        if (Optional.ofNullable(list).isPresent()) {
            String[] whiteIds = list.split(";");
            boolean hasMinusSign = true;
            String cleanId = "";
            for (String id : whiteIds) {
                hasMinusSign = !id.startsWith("-");
                cleanId = id.replace("-", "");
                boolean isMatch = cleanId.equals(basicId.toString());
                if(isMatch){
                    return hasMinusSign;
                }
            }
            return false;
        }
        return notFoundReturnDefault; // 默认返回false
    }
    @Override
    public void jobUpdateStatus() {
        inviteRewardRecordService.jobUpdateStatus();
    }

    @Override
    public Integer getInviteStatus(CanInviteRequest request) {
        log.info("判断被邀请人是否可以被绑定-参数信息:{}", JSONUtil.toJsonStr(request));
        if(Objects.isNull(request.getBasicId())){
            return 0;
        }
        //查询是否被绑过
        InviteInfoVO inviteInfoVO = heInviteRelationService.queryUserBindInfoByBasicId(request.getBasicId());
        //有邀请记录=不能被邀请
        if (ObjectUtil.isNotNull(inviteInfoVO)) {
            log.info("判断被邀请人-{}存在邀请记录信息={}", request.getBasicId(),JSONUtil.toJsonStr(inviteInfoVO));
            return 1;
        }
        return 0;
    }
}
