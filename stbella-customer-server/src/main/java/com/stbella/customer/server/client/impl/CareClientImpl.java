package com.stbella.customer.server.client.impl;

import com.stbella.base.server.dto.SysEmployeeDTO;
import com.stbella.care.server.care.entity.food.FoodRepoPO;
import com.stbella.care.server.care.request.api.CareBoardRequest;
import cn.hutool.core.collection.CollectionUtil;
import com.stbella.care.server.care.entity.QuestionnairePO;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.entity.RoomStateCheckInRoomInfoPO;
import com.stbella.care.server.care.entity.comment.CommentResultPO;
import com.stbella.care.server.care.request.api.CareBoardRequest;
import com.stbella.care.server.care.request.api.CheckedInRoomInfoBasicReq;
import com.stbella.care.server.care.request.comment.CommentEntranceReq;
import com.stbella.care.server.care.service.*;
import com.stbella.care.server.care.service.comment.CommentCommandService;
import com.stbella.care.server.care.service.comment.CommentQueryService;
import com.stbella.care.server.care.service.food.FoodRepoService;
import com.stbella.care.server.care.service.questionnaire.islahealthy.IsLaHealthyService;
import com.stbella.care.server.care.vo.CareGuideInfoVO;
import com.stbella.care.server.care.vo.CustomerPostpartumVO;
import com.stbella.care.server.care.vo.api.CareBoardBaseVO;
import com.stbella.care.server.care.vo.mom.BodyIndexVO;
import com.stbella.care.server.care.vo.questionnaire.islahealthy.IsLaHealthyAnswerBaseResultVO;
import com.stbella.care.server.care.vo.survey.QuestionnaireInfoVO;
import com.stbella.care.server.ecp.TabClientBirthService;
import com.stbella.core.base.PageVO;
import com.stbella.care.server.ecp.UserService;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.client.CareClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CareClientImpl implements CareClient {

    @DubboReference(timeout = 5000)
    private CareService careService;

    @DubboReference(timeout = 5000)
    private CareLeaveGuideQueryService careLeaveGuideQueryService;
    @DubboReference(timeout = 5000, check = false)
    private TabClientBirthService tabClientBirthService;

    @DubboReference(timeout = 5000)
    private UserService userService;

    @DubboReference(timeout = 5000)
    private QuestionnaireService questionnaireService;

    @DubboReference(timeout = 5000)
    private MemberCareService memberCareService;

    @DubboReference(timeout = 5000)
    private RoomStateCheckInInfoService roomStateCheckInInfoService;

    @DubboReference(timeout = 5000)
    private RoomStateCheckInRoomInfoService roomStateCheckInRoomInfoService;

    @DubboReference(timeout = 5000)
    private CommentCommandService commentCommandService;
    @DubboReference(timeout = 5000)
    private CommentQueryService commentQueryService;


    @DubboReference(timeout = 6000)
    private IsLaHealthyService isLaHealthyService;

    @DubboReference(timeout = 5000)
    private FoodRepoService foodRepoService;



    @Override
    public CustomerPostpartumVO getPostpartumData(Long customerId, Date date) {
        try {
            return careService.getPostpartumData(customerId,date);
        }catch (Exception e){
            log.error("CareClientImpl.getPostpartumData error ",e);
            return null;
        }
    }

    @Override
    public String getMaxLeaveOrderNo(Integer basicId,Integer brandType) {
        try {
            return careService.getMaxLeaveOrderNo(basicId,brandType);
        }catch (Exception e){
            log.error("CareClientImpl.getMaxLeaveOrderNo error ",e);
            return null;
        }
    }

    @Override
    public List<CareGuideInfoVO> careGuideByOrderNo(List<String> orderNoList) {
        try {
            return careLeaveGuideQueryService.listByOrderNo(orderNoList);
        }catch (Exception e){
            log.error("CareClientImpl.careLeaveGuideQueryService.listByOrderNo error ",e);
            return null;
        }
    }

    @Override
    public List<QuestionnaireInfoVO> getQuestionById(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)){
            return new ArrayList<>();
        }
        return questionnaireService.getQuestionById(idList);
    }

    @Override
    public Map<String, Integer> getGestationWeekType(List<String> orderNos) {
        try {
            return tabClientBirthService.getGestationWeekType(orderNos);
        } catch (Exception e) {
            log.error("CareClientImpl.getGestationWeekType error ", e);
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "查询订单孕产信息失败");
        }
    }

    @Override
    public CareBoardBaseVO queryCareBoard(CareBoardRequest request) {
        try {
            return careService.queryCareBoard(request);
        } catch (Exception e) {
            log.error("CareClientImpl.queryCareBoard error ", e);
            return null;
        }
    }

    @Override
    public List<SysEmployeeDTO> listEcpUserByStoreIdAndRoleCode(Long storeId, List<String> targetRoleCodes) {
        try {
            return userService.listEcpUserByStoreIdAndRoleCode(storeId,targetRoleCodes);
        } catch (Exception e) {
            log.error("userService.listEcpUserByStoreIdAndRoleCode error ", e);
            return null;
        }
    }

    @Override
    public List<BodyIndexVO> getMomBodyIndex(String orderNo, Long customerId) {
        try {
            return memberCareService.getMomBodyIndex(orderNo, customerId);
        } catch (Exception e) {
            log.error("memberCareService.getMomBodyIndex error ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<BodyIndexVO> getCheckInCustomerBodyIndex(Date now) {
        try {
            return memberCareService.getCheckInCustomerBodyIndex(now);
        } catch (Exception e) {
            log.error("memberCareService.getCheckInCustomerBodyIndex error ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public PageVO<RoomStateCheckInInfoPO> pageByCheckInStatus(Integer pageNum, Integer pageSize, List<Integer> checkInStatus) {
        return roomStateCheckInInfoService.pageByCheckInStatus(pageNum,pageSize,checkInStatus);
    }

    @Override
    public List<RoomStateCheckInInfoPO> getRoomInfoByOrderNoList(List<String> orderNoList) {
        try {
            return roomStateCheckInInfoService.getRoomInfoByOrderNoList(orderNoList);
        } catch (Exception e) {
            log.error("roomStateCheckInInfoService.getRoomInfoByOrderNoList error ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<RoomStateCheckInInfoPO> getRoomInfoByCustomerId(List<Long> customerIdList, List<Integer> checkInStatusList) {
        try {
            return roomStateCheckInInfoService.listByCustomerId(customerIdList, checkInStatusList);
        } catch (Exception e) {
            log.error("roomStateCheckInInfoService.getRoomInfoByOrderNoList error ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long genEntrance(CommentEntranceReq req) {
        try {
            return commentCommandService.genEntrance(req);
        } catch (Exception e) {
            log.error("genEntrance error ", e);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "评价入口生成失败");
        }
    }

    @Override
    public String genEntranceUrl(Long entranceId) {
        try {
            return commentQueryService.getCommentUrl(entranceId);
        } catch (Exception e) {
            log.error("genEntranceUrl error", e);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "评价入口生成失败");
        }
    }

    /**
     * 批量获取评价结果
     */
    @Override
    public List<CommentResultPO> listResultByBizIds(List<String> bizIds) {
        try {
            return commentQueryService.listResultByBizIds(bizIds);
        } catch (Exception e) {
            log.error("listResultByBizIds error", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public QuestionnairePO queryByBrandAndType(Integer brandType, Integer questionnaireType) {
        if (null == brandType || null == questionnaireType) {
            return null;
        }
        try {
            return questionnaireService.queryByBrandAndType(brandType, questionnaireType);
        } catch (Exception e) {
            log.error("queryByBrandAndType error", e);
            return null;
        }
    }


    @Override
    public List<IsLaHealthyAnswerBaseResultVO> listByQuestionAnswer(List<Integer> basicUids, Long questionnaireId) {
        if (CollectionUtil.isEmpty(basicUids) || null == questionnaireId) {
            return null;
        }
        try {
            return isLaHealthyService.listByQuestionAnswer(basicUids, questionnaireId);
        } catch (Exception e) {
            log.error("queryByBrandAndType error", e);
            return null;
        }
    }

    @Override
    public List<RoomStateCheckInRoomInfoPO> listByRoomIdList(List<Long> roomStateInfoList) {
        if (CollectionUtil.isEmpty(roomStateInfoList)) {
            return null;
        }
        try {
            return roomStateCheckInRoomInfoService.listByRoomIdList(roomStateInfoList);
        } catch (Exception e) {
            log.error("roomStateCheckInRoomInfoService.listByRoomIdList error", e);
            return null;
        }
    }

    @Override
    public List<FoodRepoPO> listByFoodIds(List<Long> foodIds) {
        try {
            return foodRepoService.listByFoodIds(foodIds);
        } catch (Exception e) {
            log.error("listByFoodIds error", e);
            return Lists.newArrayList();
        }
    }

	@Override
	public RoomStateCheckInInfoPO getLastRoomStateCheckByBasicReq(CheckedInRoomInfoBasicReq req) {
        try {
            return roomStateCheckInInfoService.getLastRoomStateCheckByBasicReq(req);
        } catch (Exception e) {
            log.error("调用客户房态失败", e);
            return null;
        }
	}
}
