package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.stbella.base.server.sms.Sms;
import com.stbella.base.server.sms.SmsService;
import com.stbella.care.api.php.PhpApiService;
import com.stbella.care.manager.RosterCustomerService;
import com.stbella.care.server.care.enmu.RoomStateCheckInEnum;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.entity.TabClientPO;
import com.stbella.care.server.care.service.RoomStateCheckInInfoService;
import com.stbella.care.server.care.service.ShiftService;
import com.stbella.care.server.care.vo.CustomerUnionidVO;
import com.stbella.care.server.care.vo.NurseWorkDayStatVO;
import com.stbella.care.server.care.vo.summary.BabySummaryVO;
import com.stbella.care.server.ecp.TabClientService;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.client.MessageClient;
import com.stbella.customer.server.cts.enums.CustomerAssetsBrandEnum;
import com.stbella.customer.server.customer.entity.CustomerAnniversaryActivitySitePO;
import com.stbella.customer.server.customer.entity.CustomerCheckOutSummaryPO;
import com.stbella.customer.server.customer.entity.CustomerWechatFansPO;
import com.stbella.customer.server.customer.enums.BrandTypeEnum;
import com.stbella.customer.server.customer.enums.CheckOutSummaryStatusEnum;
import com.stbella.customer.server.customer.enums.CustomerCheckInStatusEnum;
import com.stbella.customer.server.customer.enums.CustomerCheckOutSummaryTagEnum.BabybellaTagEnum;
import com.stbella.customer.server.customer.enums.CustomerCheckOutSummaryTagEnum.StbellaTagEnum;
import com.stbella.customer.server.customer.enums.CustomerWechatFansEnum;
import com.stbella.customer.server.customer.mapper.CustomerCheckOutSummaryMapper;
import com.stbella.customer.server.customer.request.CustomerCheckOutSummaryRequest;
import com.stbella.customer.server.customer.request.CustomerCheckoutSummaryListRequest;
import com.stbella.customer.server.customer.request.CustomerCheckoutSummaryStatusEditRequest;
import com.stbella.customer.server.customer.request.CustomerCheckoutSummaryWechatPushRequest;
import com.stbella.customer.server.customer.request.PhpNurseInfoRequest;
import com.stbella.customer.server.customer.service.CustomerSummaryService;
import com.stbella.customer.server.customer.service.CustomerWechatFansService;
import com.stbella.customer.server.customer.service.PushMessageService;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.BabyList;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Content;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Other;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Part1;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Part2;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Part3;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Part4;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Part5;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Tag;
import com.stbella.customer.server.customer.vo.CustomerCheckoutSummaryListVO;
import com.stbella.customer.server.customer.vo.CustomerSummaryIdVO;
import com.stbella.customer.server.customer.vo.PhpNurseInfoVO;
import com.stbella.customer.server.ecp.entity.TabWechatUserPO;
import com.stbella.customer.server.ecp.service.TabWechatUserService;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.customer.server.util.UMSUtils;
import com.stbella.customer.server.util.XcxMessageUtil;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.order.server.order.order.api.OrderInfoService;
import com.stbella.order.server.order.order.req.OrderBasicReq;
import com.stbella.order.server.order.order.res.OrderCreateInfoVo;
import com.stbella.order.server.order.production.api.UserPropertyQueryService;
import com.stbella.order.server.order.production.req.ProductionVerificationStatReq;
import com.stbella.order.server.order.production.res.ProductionVerificationStatVo;
import com.stbella.store.core.enums.BrandEnum;
import com.stbella.store.server.ecp.entity.CfgStore;
import com.stbella.store.server.ecp.service.CfgStoreService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 客户离馆小结 服务实现类
 * </p>
 *
 */
@DubboService
@Service
@Slf4j
public class CustomerSummaryServiceImpl extends ServiceImpl<CustomerCheckOutSummaryMapper, CustomerCheckOutSummaryPO> implements CustomerSummaryService {


    @DubboReference
    private RosterCustomerService rosterCustomerService;

    @DubboReference
    private OrderInfoService orderInfoService;

    @DubboReference
    private TabClientService tabClientService;

    @DubboReference
    private RoomStateCheckInInfoService roomStateCheckInInfoService;

    @DubboReference
    private ShiftService shiftService;

    @DubboReference
    private UserPropertyQueryService userPropertyQueryService;

    @DubboReference
    private PhpApiService phpApiService;

    @DubboReference
    private SmsService smsService;

    @DubboReference
    private CfgStoreService cfgStoreService;

    @Resource
    private CustomerCheckOutSummaryMapper customerCheckOutSummaryMapper;

    @Resource
    private PushMessageService pushMessageService;

    @Resource
    private CustomerWechatFansService customerWechatFansService;

    @Resource
    private TabWechatUserService tabWechatUserService;

    @Resource
    private MessageClient messageClient;

    /**
     * 添加离馆小结
     * @param roomStateCheckInInfoId
     * @param orderNo
     * @param customerId
     * @return
     */
    @Override
    public Boolean addCheckoutSummary(Long roomStateCheckInInfoId, String orderNo, Long customerId) {
        log.info("开始生成离馆小结: 房态ID:{}, 订单号:{}, 客户id:{}", roomStateCheckInInfoId, orderNo, customerId);

        /************** Part1 咨询下单 ***************/
        Part1 part1 = new Part1();
        OrderBasicReq orderBasicReq = new OrderBasicReq();
        orderBasicReq.setOrderSn(orderNo);;
        Result<OrderCreateInfoVo> orderCreateInfoVoResult = orderInfoService.queryOrderCreateInfo(orderBasicReq);

        log.info("离馆小结:订单号:{}, part1参数:{}, 结果:{}", orderNo, JSONUtil.toJsonStr(orderBasicReq), JSONUtil.toJsonStr(orderCreateInfoVoResult));

        if (orderCreateInfoVoResult.getCode().equals(ResultEnum.SUCCESS.getCode())) {
            if (ObjectUtil.isNotNull(orderCreateInfoVoResult.getData())) {
                OrderCreateInfoVo orderCreateInfoVo = orderCreateInfoVoResult.getData();
                part1.setOrderDate(DateUtil.format(orderCreateInfoVo.getCreatedAt(), "yyyy-MM-dd"));
                part1.setStoreName(orderCreateInfoVo.getStoreNameAlias());
                part1.setStoreId(orderCreateInfoVo.getStoreId());
                part1.setStoreType(orderCreateInfoVo.getStoreType());
                part1.setPackageName(orderCreateInfoVo.getGoodsName());
            }
        }

        TabClientPO tabClientPO = tabClientService.queryClientInfoById(customerId.intValue());
        if (ObjectUtil.isNotNull(tabClientPO)) {
            part1.setConsultationDate(DateUtil.format(tabClientPO.getRecordTime(), "yyyy-MM-dd"));
        }

        log.info("离馆小结:订单号:{}, part1内容:{}", orderNo, JSONUtil.toJsonStr(part1));

        /************** Part2 入住 ***************/
        Part2 part2 = new Part2();
        RoomStateCheckInInfoPO checkInfo = roomStateCheckInInfoService.getCheckInfoByOrderNo(orderNo);

        log.info("离馆小结:订单号:{}, part2房态信息结果:{}", orderNo, JSONUtil.toJsonStr(checkInfo));

        part2.setCheckInDate(checkInfo.getCheckInDate().toString());
        part2.setOvernightDays(checkInfo.getOvernightDays());
        part2.setStoreName(part1.getStoreName());

        // 护士工作时长
        Result<List<NurseWorkDayStatVO>> nurseWorkDayListResult = shiftService.queryNurseWorkDay(
            roomStateCheckInInfoId, orderNo, customerId);

        log.info("离馆小结:订单号:{}, part2护士工作时长结果:{}", orderNo, JSONUtil.toJsonStr(nurseWorkDayListResult));

        if (nurseWorkDayListResult.getCode().equals(ResultEnum.SUCCESS.getCode())) {
            if (ObjectUtil.isNotNull(nurseWorkDayListResult.getData())) {
                List<String> primaryNurseMaxList = new ArrayList<>();
                Integer primaryNurseServiceSum = 0;

                List<NurseWorkDayStatVO> nurseWorkDayList = nurseWorkDayListResult.getData();
                for (NurseWorkDayStatVO nurseWorkDayStatVO : nurseWorkDayList) {
                    if (primaryNurseMaxList.size() >= 2) {
                        break;
                    }

                    primaryNurseMaxList.add(nurseWorkDayStatVO.getNurseName());
                    primaryNurseServiceSum += nurseWorkDayStatVO.getWorkNum();
                }

                if (primaryNurseServiceSum > checkInfo.getOvernightDays()) {
                    primaryNurseServiceSum = checkInfo.getOvernightDays();
                }

                part2.setPrimaryNurseMax(primaryNurseMaxList);
                part2.setPrimaryNurseServiceSum(primaryNurseServiceSum);
            }
        }

        /************** Part3 宝宝护理服务列表 ***************/
        List<BabySummaryVO> babySummaryList = rosterCustomerService.getBabySummary(roomStateCheckInInfoId, orderNo, customerId);

        log.info("离馆小结:订单号:{}, part3宝宝护理服务结果:{}", orderNo, JSONUtil.toJsonStr(babySummaryList));

        List<BabyList> babyList = new ArrayList<>();
        babySummaryList.forEach(item -> {
            BabyList babyInfo = new BabyList();
            BeanUtils.copyProperties(item, babyInfo);
            babyList.add(babyInfo);
        });

        Part3 part3 = new Part3();
        part3.setBabyList(babyList);

        /************** Part4 宝妈护理服务 ***************/
        Integer spiritPleasureSmoothNum = rosterCustomerService.getSpiritPleasureSmoothNum(orderNo); // 精神状态 愉悦平稳次数
        Integer chineseCheckNum = rosterCustomerService.getChineseCheckNum(orderNo, customerId); // 中医查房次数
        Integer customerCheckNum = rosterCustomerService.getCustomerCheckNum(orderNo, customerId); // 产科查房次数
        Integer childCheckNum = rosterCustomerService.getPediatricsCheckNum(orderNo, customerId); // 儿科查房次数
        Integer maternalEducationNum = rosterCustomerService.getMaternalEducationNum(orderNo); // 产妇宣教次数
        BigDecimal checkInOutWeightDiff = rosterCustomerService.getCheckInOutWeightDiff(orderNo); // 产妇入离馆体重差值
        //产妇入离馆体重差值文案是否展示
        boolean checkInOutWeightDiffShow = rosterCustomerService.checkInOutWeightDiffShow(orderNo); // 产妇入离馆体重差值文案是否展示


        Part4 part4 = new Part4();
        part4.setSpiritPleasureSmoothNum(spiritPleasureSmoothNum);
        part4.setChineseCheckNum(chineseCheckNum);
        part4.setCustomerCheckNum(customerCheckNum);
        part4.setChildCheckNum(childCheckNum);
        part4.setMaternalEducationNum(maternalEducationNum);
        part4.setCheckInOutWeightDiff(checkInOutWeightDiff);
        part4.setCheckInOutWeightDiffShow(checkInOutWeightDiffShow);

        log.info("离馆小结:订单号:{}, part4产妇护理服务结果:{}", orderNo, JSONUtil.toJsonStr(part4));

        /************** Part5 产康服务 ***************/
        Part5 part5 = new Part5();
        ProductionVerificationStatReq productionVerificationStatReq = new ProductionVerificationStatReq();
        productionVerificationStatReq.setBasicUid(tabClientPO.getBasicUid());
        productionVerificationStatReq.setStartDate(DateUtil.parse(checkInfo.getCheckInDate().toString()));
        productionVerificationStatReq.setEndDate(DateUtil.parse(checkInfo.getCheckOutDate().toString()));

        Result<ProductionVerificationStatVo> productionVerificationStatVoResult = userPropertyQueryService.queryProductionVerificationStat(
            productionVerificationStatReq);

        log.info("离馆小结:订单号:{}, part5参数:{}, 结果:{}", orderNo, JSONUtil.toJsonStr(productionVerificationStatReq), JSONUtil.toJsonStr(productionVerificationStatVoResult));
        if (productionVerificationStatVoResult.getCode().equals(ResultEnum.SUCCESS.getCode())) {
            ProductionVerificationStatVo productionVerificationStatVo = productionVerificationStatVoResult.getData();
            if (ObjectUtil.isNotNull(productionVerificationStatVo)) {
                part5.setIsCancelAfterVerification(productionVerificationStatVo.getVerificationNum() > 0 ? 1 : 0);
                part5.setProductionServiceNum(productionVerificationStatVo.getVerificationNum());
                part5.setCancelAfterVerificationFirstDate(DateUtil.format(productionVerificationStatVo.getFirstVerificationDate(), "yyyy-MM-dd"));
                part5.setCancelAfterVerificationPackage(productionVerificationStatVo.getVerificationMostGoods());
            }
        }

        /************** 其他 ***************/
        Integer storeType = part1.getStoreType();
        Other other = new Other();
        Tag tag = checkSummaryTag(checkInOutWeightDiff, maternalEducationNum, storeType);
        other.setTag(tag);
        //组装
        Map<String, Object> map = new HashMap<>(4);
        map.put("part1", part1);
        map.put("part2", part2);
        map.put("part3", part3);
        map.put("part4", part4);
        map.put("part5", part5);
        map.put("other", other);

        // 先判断该订单是否生成过离馆小结，如果生成过，则删除
        update(new LambdaUpdateWrapper<CustomerCheckOutSummaryPO>()
            .eq(CustomerCheckOutSummaryPO::getOrderNo, orderNo)
            .set(CustomerCheckOutSummaryPO::getDeleted, 1));

        CustomerCheckOutSummaryPO customerCheckOutSummaryPO = new CustomerCheckOutSummaryPO();
        customerCheckOutSummaryPO.setCustomerId(customerId);
        customerCheckOutSummaryPO.setContent(JSON.toJSONString(map));
        customerCheckOutSummaryPO.setOrderNo(orderNo);
        customerCheckOutSummaryPO.setGoodsName(part1.getPackageName());
        customerCheckOutSummaryPO.setStoreId(part1.getStoreId().longValue());
        customerCheckOutSummaryPO.setStoreType(part1.getStoreType());
        customerCheckOutSummaryPO.setStoreName(part1.getStoreName());
        customerCheckOutSummaryPO.setCustomerName(tabClientPO.getName());
        customerCheckOutSummaryPO.setBasicId(tabClientPO.getBasicUid());
        customerCheckOutSummaryPO.setPopNotice(0);
        customerCheckOutSummaryPO.setStatus(CheckOutSummaryStatusEnum.NO_SEND.getCode());
        customerCheckOutSummaryPO.setShareContent(JSON.toJSONString(other));
        customerCheckOutSummaryPO.setCheckinDate(DateUtil.parse(checkInfo.getCheckInDate().toString()));
        customerCheckOutSummaryPO.setCheckoutDate(DateUtil.parse(checkInfo.getCheckOutDate().toString()));

        if (0 == baseMapper.insert(customerCheckOutSummaryPO)){
            throw new BusinessException(ResultEnum.DATABASE_ERROR.getCode(), "新增离馆小结失败");
        }

        // 推送微信模版消息
        CustomerCheckoutSummaryWechatPushRequest customerCheckoutSummaryWechatPushRequest = new CustomerCheckoutSummaryWechatPushRequest();
        customerCheckoutSummaryWechatPushRequest.setSummaryId(customerCheckOutSummaryPO.getId());
        customerCheckoutSummaryWechatPushRequest.setCheckinDate(checkInfo.getCheckInDate().toString());
        customerCheckoutSummaryWechatPushRequest.setCheckoutDate(checkInfo.getCheckOutDate().toString());
        customerCheckoutSummaryWechatPushRequest.setStoreType(part1.getStoreType());
        customerCheckoutSummaryWechatPushRequest.setStoreName(part1.getStoreName());
        customerCheckoutSummaryWechatPushRequest.setPhone(tabClientPO.getPhone());

        // TODO 艾屿的先不发短信
        if (!Objects.equals(part1.getStoreType(), BrandTypeEnum.BELLA_ISLA.code())) {
            // 发送微信公众号消息
            sendWechatMessage(tabClientPO.getPhone(), customerCheckoutSummaryWechatPushRequest);

            // 发送订阅号消息
            sendSubscriptionMessage(tabClientPO.getBasicUid(), part1.getStoreType(), part1.getStoreName(), checkInfo.getCheckOutDate());
        }

        // 发送短信
        //sendSmsMessage(tabClientPO.getPhone(), tabClientPO.getName(), part1.getStoreType());
        sendSmsMessage(tabClientPO.getId(), tabClientPO.getName(), part1.getStoreType());
        return true;
    }



    @Async("taskExecutor")
    public void sendSubscriptionMessage(Integer basicId, Integer storeType, String storeName, LocalDate checkoutDate){
        log.info("微信订阅消息推送 addCheckoutSummary.sendSubscriptionMessage basicId={},storeType={},storeName={},checkoutDate={} ", basicId, storeType, storeName, checkoutDate);
        if (Objects.isNull(basicId) || Objects.isNull(storeType) || Objects.isNull(storeName) || Objects.isNull(checkoutDate)){
            return;
        }
        String templateId = null;
        Integer storeParamType = null;
        String path = "";
        if (Objects.equals(CustomerAssetsBrandEnum.BRAND_SBL.getCode(),storeType)){
            templateId = "xOlRHcKhbCaNIb4qkf23W4DnLu5n40uU28Q6rK076CQ";
            storeParamType = 1;
            path = "pages/my/index";
        }else if (Objects.equals(CustomerAssetsBrandEnum.BRAND_XBL.getCode(),storeType)){
            templateId = "LNJ2G121wcOCg_HRTmXUQ7tJ3lysCYNlXnyLdnuaE6c";
            storeParamType = 2;
            path = "pages/my/my";
        }else {
            log.error("微信订阅消息推送 addCheckoutSummary.sendSubscriptionMessage 门店类型错误 storeType="+storeType);
            return;
        }

        List<TabWechatUserPO> wechatUserList = tabWechatUserService.list(new LambdaQueryWrapper<TabWechatUserPO>()
                .in(TabWechatUserPO::getBasicUid, Collections.singletonList(basicId))
                .eq(TabWechatUserPO::getFromType, storeParamType)
                .isNull(TabWechatUserPO::getDeletedAt));
        if (CollectionUtil.isEmpty(wechatUserList)){
            log.error("微信订阅消息推送 addCheckoutSummary.sendSubscriptionMessage 微信登录信息查询为空");
            return;
        }

        TabWechatUserPO tabWechatUser = wechatUserList.get(0);
        XcxMessageUtil.XcxMessageRequest request = new XcxMessageUtil.XcxMessageRequest();
        request.setAccessToken(UMSUtils.getWeChatToken(storeType));
        request.setTemplateId(templateId);
        request.setPage(path);
        request.setMiniprogramState("trial");
        request.setLang("zh_CN");
        request.setToUser(tabWechatUser.getOpenid());
        request.setData(getSummaryParam(storeName, checkoutDate));
        XcxMessageUtil.messageSendCommon(0, request);
    }


    private LinkedHashMap<String, Object> getSummaryParam(String storeName, LocalDate checkoutDate) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("value", storeName);
        map.put("thing2", valueMap);

        valueMap = new HashMap<>();
        valueMap.put("value", checkoutDate.toString());
        map.put("date3", valueMap);

        valueMap = new HashMap<>();
        valueMap.put("value", "您的月子报告已生成, 请点击查看哦");
        map.put("thing4", valueMap);
        return map;
    }


    @Override
    public CustomerCheckOutSummaryVO checkOutSummaryInfo(CustomerCheckOutSummaryRequest customerCheckOutSummaryRequest) {
        CustomerCheckOutSummaryPO customerCheckOutSummaryPO = customerCheckOutSummaryMapper.customerCheckOutSummaryInfo(customerCheckOutSummaryRequest);
        if (ObjectUtil.isEmpty(customerCheckOutSummaryPO)) {
            return null;
        }
        CustomerCheckOutSummaryVO customerCheckOutSummaryVO = new CustomerCheckOutSummaryVO();
        BeanUtils.copyProperties(customerCheckOutSummaryPO, customerCheckOutSummaryVO);
        Content content = JSON.parseObject(customerCheckOutSummaryPO.getContent(),Content.class);
        customerCheckOutSummaryVO.setContent(content);

        if (customerCheckOutSummaryPO.getPopNotice().equals(0)) {
            customerCheckOutSummaryPO.setPopNotice(1);
            updateById(customerCheckOutSummaryPO);
        }

        return customerCheckOutSummaryVO;
    }

    /**
     * 根据分享id获取分享内容
     *
     * @param shareId
     * @return
     */
    @Override
    public Other queryCheckoutSummaryShareInfo(Long shareId) {
        CustomerCheckOutSummaryRequest customerCheckOutSummaryRequest = new CustomerCheckOutSummaryRequest();
        customerCheckOutSummaryRequest.setId(shareId);
        CustomerCheckOutSummaryPO customerCheckOutSummaryPO = baseMapper.customerCheckOutSummaryInfo(customerCheckOutSummaryRequest);
        if (ObjectUtil.isNotNull(customerCheckOutSummaryPO)) {
            return JSONUtil.toBean(customerCheckOutSummaryPO.getShareContent(), Other.class);
        }
        return null;
    }

    /**
     * 根据客户id和品牌，获取客户的离馆小结列表
     *
     * @param request
     * @return
     */
    @Override
    public PageVO<CustomerCheckoutSummaryListVO> queryCheckoutSummaryList(
        CustomerCheckoutSummaryListRequest request) {
        log.info("查询客户离馆小结，req:{}", JSON.toJSONString(request));

        List<CustomerCheckoutSummaryListVO> dataList = new ArrayList<>();

        LambdaQueryWrapper<CustomerCheckOutSummaryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerCheckOutSummaryPO::getBasicId, request.getBasicId());
        queryWrapper.eq(CustomerCheckOutSummaryPO::getStoreType, request.getStoreType());
        queryWrapper.orderByDesc(CustomerCheckOutSummaryPO::getGmtCreate);

        Page<CustomerCheckOutSummaryPO> pageResult = page(
            new Page<>(request.getPageNum(), request.getPageSize()), queryWrapper);

        if (!CollectionUtil.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(po -> {
                CustomerCheckoutSummaryListVO vo = new CustomerCheckoutSummaryListVO();
                vo.setId(po.getId());
                vo.setStoreId(po.getStoreId());
                vo.setStoreType(po.getStoreType());
                vo.setStoreName(po.getStoreName());
                vo.setCheckinDate(po.getCheckinDate());
                vo.setCheckoutDate(po.getCheckoutDate());
                vo.setGoodsName(po.getGoodsName());
                vo.setStatus(po.getStatus());

                dataList.add(vo);
            });
        }

        return new PageVO<>(dataList, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());
    }

    /**
     * 根据离馆小结id更新弹框状态
     *
     * @param summaryId
     * @return
     */
    @Override
    public Boolean updateCheckoutSummaryPopStatus(Long summaryId) {
        CustomerCheckOutSummaryPO summaryPO = getById(summaryId);
        if (ObjectUtil.isNotEmpty(summaryPO)) {
            if (summaryPO.getPopNotice().equals(0)) {
                summaryPO.setPopNotice(1);
                updateById(summaryPO);
            }
        }
        return true;
    }

    /**
     * 根据basicId获取客户的入住状态
     *
     * @param basicId
     * @param storeType 0:圣贝拉 1:小贝拉
     * @return
     */
    @Override
    public Integer queryCustomerCheckInStatus(Integer basicId, Integer storeType) {
        log.info("打印根据basicId获取客户的入住状态:basicId:{},storeType:{}",basicId,storeType);
        List<CfgStore> storeList = cfgStoreService.getStoreList();
        List<Integer> storeIds = storeList.stream()
            .filter(i -> ObjectUtil.isNull(storeType) || i.getType().equals(storeType))
            .map(CfgStore::getStoreId)
            .collect(Collectors.toList());
        log.info("打印根据basicId获取客户的入住状态:storeIds:{}",storeIds);
        // 查询basicId下的所有customerId
        List<RoomStateCheckInInfoPO> checkInfoList = new ArrayList<>();
        try {
            checkInfoList = roomStateCheckInInfoService.getCheckInfoByCustomerIds(basicId, storeIds);
        }catch (Exception e){
            log.error("打印根据basicId获取客户的入住状态:error:{},checkInfoList:{}",e,checkInfoList);
        }
        if (CollectionUtil.isEmpty(checkInfoList)) {
            // 没有过房态记录，属于未入住
            return CustomerCheckInStatusEnum.no_checkin.getCode();
        }

        // 当前离馆的列表
        List<RoomStateCheckInInfoPO> checkInList = checkInfoList.stream().filter(
                o -> o.getCheckInStatus().equals(RoomStateCheckInEnum.ROOM_STATE_CHECK_OUT.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(checkInList)) {
            return CustomerCheckInStatusEnum.checkout.getCode();
        }

        return CustomerCheckInStatusEnum.checkin.getCode();
    }

    /**
     * php nurseinfo接口需要的信息
     *
     * @param request
     * @return
     */
    @Override
    public PhpNurseInfoVO phpNurseInfo(PhpNurseInfoRequest request) {
        PhpNurseInfoVO phpNurseInfoVO = new PhpNurseInfoVO();

        CustomerCheckOutSummaryRequest customerCheckOutSummaryRequest = new CustomerCheckOutSummaryRequest();
        BeanUtils.copyProperties(request, customerCheckOutSummaryRequest);

        CustomerCheckOutSummaryVO customerCheckOutSummaryVO = checkOutSummaryInfo(customerCheckOutSummaryRequest);
        phpNurseInfoVO.setCheckoutSummaryPopNotice(ObjectUtil.isNotNull(customerCheckOutSummaryVO) ? customerCheckOutSummaryVO.getPopNotice() : 0);
        phpNurseInfoVO.setCheckoutSummaryId(ObjectUtil.isNotNull(customerCheckOutSummaryVO) ? customerCheckOutSummaryVO.getId() : null);
        log.info("排查入参问题:{}",request.getStoreType());
        Integer checkInStatus = queryCustomerCheckInStatus(request.getBasicId().intValue(), request.getStoreType());
        phpNurseInfoVO.setCheckInStatus(checkInStatus);

        return phpNurseInfoVO;
    }


    /**
     * 通过basicId获取客户最新的一条月子报告
     */
    @Override
    public CustomerSummaryIdVO getSummaryByBasicId(Integer basicId,Integer brandVal) {
        CustomerSummaryIdVO re = new CustomerSummaryIdVO();
        List<CfgStore> storeList = cfgStoreService.getStoreList();
        List<Integer> storeIds = storeList.stream().filter(i -> Objects.equals(i.getType(), brandVal)).map(CfgStore::getStoreId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(storeIds)){
            return re;
        }


        List<CustomerCheckOutSummaryPO> summaries = customerCheckOutSummaryMapper.selectList(
                new LambdaQueryWrapper<CustomerCheckOutSummaryPO>()
                        .select(CustomerCheckOutSummaryPO::getId, CustomerCheckOutSummaryPO::getOrderNo, CustomerCheckOutSummaryPO::getGmtCreate)
                        .in(CustomerCheckOutSummaryPO::getStoreId,storeIds)
                        .eq(CustomerCheckOutSummaryPO::getBasicId, basicId)
                        .orderByDesc(CustomerCheckOutSummaryPO::getGmtCreate)
        );

        if (CollectionUtil.isEmpty(summaries)) return re;
        return re.genId(summaries.get(0).getId(),summaries.get(0).getOrderNo());
    }

    /**
     * 根据订单号查询离馆小结(月子报告)
     *
     * @param orderNoList
     * @return
     */
    @Override
    public List<CustomerCheckoutSummaryListVO> queryCheckoutSummaryList(List<String> orderNoList) {
        log.info("通过订单号查询客户离馆小结，req:{}", JSON.toJSONString(orderNoList));

        List<CustomerCheckOutSummaryPO> list = list(new LambdaQueryWrapper<CustomerCheckOutSummaryPO>()
                .in(CustomerCheckOutSummaryPO::getOrderNo, orderNoList));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        return list.stream().map(po -> {
            CustomerCheckoutSummaryListVO vo = new CustomerCheckoutSummaryListVO();
            vo.setId(po.getId());
            vo.setStoreId(po.getStoreId());
            vo.setStoreType(po.getStoreType());
            vo.setStoreName(po.getStoreName());
            vo.setCheckinDate(po.getCheckinDate());
            vo.setCheckoutDate(po.getCheckoutDate());
            vo.setGoodsName(po.getGoodsName());
            vo.setStatus(po.getStatus());
            vo.setOrderNo(po.getOrderNo());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据离馆小结id更新状态
     *
     * @param request
     * @return
     */
    @Override
    public Boolean updateCheckoutSummaryStatus(CustomerCheckoutSummaryStatusEditRequest request) {
        log.info("修改离馆小结的状态, request:{}", JSONUtil.toJsonStr(request));

        if (Objects.isNull(request.getId()) && StringUtils.isBlank(request.getOrderNo())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "未知的月子报告");
        }

        LambdaQueryWrapper<CustomerCheckOutSummaryPO> queryWrapper = new LambdaQueryWrapper<>();

        if (Objects.nonNull(request.getId())) {
            queryWrapper.eq(CustomerCheckOutSummaryPO::getId, request.getId());
        } else {
            if (StringUtils.isNotBlank(request.getOrderNo())) {
                queryWrapper.eq(CustomerCheckOutSummaryPO::getOrderNo, request.getOrderNo());
            }
        }

        queryWrapper.last("limit 1");
        CustomerCheckOutSummaryPO summaryPO = getOne(queryWrapper);

        if (ObjectUtil.isNotEmpty(summaryPO)
            && summaryPO.getStatus().compareTo(request.getStatus()) < 0) {
            summaryPO.setStatus(request.getStatus());
            updateById(summaryPO);
        }
        return true;
    }

    /**
     * 离馆标签
     * @param checkInOutWeightDiff
     * @param maternalEducationNum
     * @param storeType
     * @return
     */
    private Tag checkSummaryTag(BigDecimal checkInOutWeightDiff, Integer maternalEducationNum, Integer storeType) {;
        Tag tag = new Tag();
        int a = 23;
//        int b = 20;
        BigDecimal c = new BigDecimal("3");
        int code;
        String tagName;
        if (!ObjectUtils.isEmpty(checkInOutWeightDiff) && checkInOutWeightDiff.compareTo(BigDecimal.ZERO) == -1 && checkInOutWeightDiff.negate().compareTo(c) > -1) {
            //体重变化值<0 且 绝对值大于3kg时表示健康或瘦身
            code = 0 == storeType ? StbellaTagEnum.HEALTH.getCode() : BabybellaTagEnum.HEALTH.getCode();
            tagName = 0 == storeType ? StbellaTagEnum.HEALTH.getValue() : BabybellaTagEnum.HEALTH.getValue();

        } else {
            if (maternalEducationNum < a) {
                //照护技能小于平均值23表示精致
                code = 0 == storeType ? StbellaTagEnum.FINE.getCode() : BabybellaTagEnum.FINE.getCode();
                tagName = 0 == storeType ? StbellaTagEnum.FINE.getValue() : BabybellaTagEnum.FINE.getValue();
            } else {
                //照护技能小于平均值23表示上进或学术
                code = 0 == storeType ? StbellaTagEnum.ACADEMIC.getCode() : BabybellaTagEnum.ACADEMIC.getCode();
                tagName = 0 == storeType ? StbellaTagEnum.ACADEMIC.getValue() : BabybellaTagEnum.ACADEMIC.getValue();
            }
        }
        tag.setCode(code);
        tag.setName(tagName);
        return tag;
    }

    /**
     * 推送离馆消息到微信公众号
     *
     * @param phone 客户手机号
     * @param request 微信公众号推送请求
     * @return 是否推送成功
     */
    private Boolean sendWechatMessage(String phone, CustomerCheckoutSummaryWechatPushRequest request) {
        CustomerWechatFansEnum wechatSource = request.getStoreType().equals(0) ? CustomerWechatFansEnum.ST_BELLA : CustomerWechatFansEnum.BABY_BELLA;

        // 获取客户的unionid
        List<CustomerUnionidVO> customerUnionidVOList = phpApiService.queryCustomerUnionid(Arrays.asList(phone));
        if (CollectionUtil.isEmpty(customerUnionidVOList)) {
            log.info("推送离馆小结到微信公众号失败，手机号:{}, 离馆小结id:{},原因:客户未登录小程序", phone, request.getSummaryId());
            return false;
        }

        CustomerUnionidVO customerUnionidVO = customerUnionidVOList.get(0);
        CustomerWechatFansPO fansPO = customerWechatFansService.getOne(
            new LambdaQueryWrapper<CustomerWechatFansPO>()
                .eq(CustomerWechatFansPO::getUnionId, customerUnionidVO.getUnionid())
                .eq(CustomerWechatFansPO::getSource, wechatSource.getCode())
                .last("limit 1"));

        if (ObjectUtil.isEmpty(fansPO)) {
            log.info("推送离馆小结到微信公众号失败，手机号:{}, 离馆小结id:{},原因:客户关注{}公众号", phone, request.getSummaryId(), wechatSource.getValue());
            return false;
        }

        request.setOpenId(fansPO.getOpenId());
        return pushMessageService.checkoutSummarySuccess(request);
    }

    /**
     * 发送短信消息
     *
     * @param phone
     * @param clientName
     * @param storeType
     * @return
     */
    private Boolean sendSmsMessage(String phone, String clientName, Integer storeType) {
        Sms sms = Sms.start(phone).setMessageApp().babyBella();
        if (storeType.equals(0)) {
            // 圣贝拉
            sms.setMessageSign().salntBella().setMessageTemplate().saintCheckoutSummaryMsg(clientName);
        } else {
            sms.setMessageSign().babyBella().setMessageTemplate().babyCheckoutSummaryMsg(clientName);
        }

        return smsService.sendMessage(sms);
    }

    private void sendSmsMessage(Long clientId, String clientName, Integer storeType) {
        Long sceneId;
        if (BrandEnum.SAN_BELLA.getCode().equals(storeType)) {
            sceneId = 100600L;
        } else if (BrandEnum.BABY_BELLA.getCode().equals(storeType)) {
            sceneId = 100601L;
        } else if (BrandEnum.ISLA.getCode().equals(storeType)) {
            sceneId = 100602L;
        } else {
            return;
        }

        SceneTriggerReq messageRequest = new SceneTriggerReq();
        messageRequest.setSceneId(sceneId);
        messageRequest.setTargetList(Collections.singletonList(clientId.toString()));

        Map<String, String> contentData = new HashMap<>();
        contentData.put("customerName", clientName);
        messageRequest.setContextData(contentData);
        messageRequest.setRequestId(IdWorker.get32UUID());

        log.info("发送月子报告短信:{}", JSONUtil.toJsonStr(messageRequest));
        messageClient.triggerScene(messageRequest);
    }
}
