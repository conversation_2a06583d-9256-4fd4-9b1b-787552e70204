package com.stbella.customer.server.scrm.component.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.client.RuleLinkClient;
import com.stbella.customer.server.exception.OpportunityAssignException;
import com.stbella.customer.server.repository.ScrmUserRepository;
import com.stbella.customer.server.repository.TeamMemberRepository;
import com.stbella.customer.server.scrm.component.selector.Selectee;
import com.stbella.customer.server.scrm.constant.ScrmQwCustomerMessageConstant;
import com.stbella.customer.server.scrm.dto.OpportunityAssignAlertDTO;
import com.stbella.customer.server.scrm.dto.OpportunityFact;
import com.stbella.customer.server.scrm.dto.ScrmOppotrunityProcessorDTO;
import com.stbella.customer.server.scrm.entity.TeamMemberPO;
import com.stbella.customer.server.scrm.enums.AssignFailTypeEnum;
import com.stbella.customer.server.scrm.enums.CustomerFromTypeEnum;
import com.stbella.customer.server.scrm.enums.CustomerProductionFromTypeEnum;
import com.stbella.customer.server.scrm.enums.PicpOrderTpyeEnum;
import com.stbella.customer.server.scrm.vo.ScrmUserVO;
import com.stbella.notice.enums.NoticeTypeEnum;
import com.stbella.notice.server.NoticeService;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-12-13  16:17
 * @Description: 商机分配成功通知
 */
@SnowballComponent( name = "商机分配成功通知")
@Slf4j
public class OpportunityAssignSuccessNoticeProcessor implements IExecutableAtom<FlowContext> {

    @DubboReference
    private NoticeService noticeService;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    ScrmUserRepository userRepository;

    @Override
    public void run(FlowContext context) {
        log.info("商机分配成功通知");

        OpportunityFact opportunityFact = context.getAttribute(OpportunityFact.class);

        // 发送统一群
        try {
            String msg = buildMsg(opportunityFact, false);
            log.info("企微客资分配通知内容{}", msg);
            WechatNailNailRobotDeclarationRequest wechatNailNailRobotDeclarationRequest = new WechatNailNailRobotDeclarationRequest();
            wechatNailNailRobotDeclarationRequest.setType(1);
            // 产康商机发送至产康客资群
            Integer bizType = PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode().equals(opportunityFact.getOrderType()) ? 60002 : NoticeTypeEnum.SCRM_CUSTOMER_DISTRIBUTION_SUCCESS.getBizType();
            wechatNailNailRobotDeclarationRequest.setBizType(bizType);
            wechatNailNailRobotDeclarationRequest.setStoreId(0);
            wechatNailNailRobotDeclarationRequest.setContent(msg);
            Result<Boolean> notice = noticeService.notice(wechatNailNailRobotDeclarationRequest);
            log.info("发送统一群-企微客资分配通知结果：{}", JSONUtil.toJsonStr(notice));
        } catch (Exception e) {
            log.error("发送统一群-企微客资分配通知异常:{}", e.getMessage());
        }

        // 发送客资小群
        try {
            Integer storeId = opportunityFact.getStoreId();
            if (Objects.isNull(opportunityFact.getStoreId())) {
                ExecuteRuleV2Req ruleV2Req = new ExecuteRuleV2Req("sales_team_robot");
                Map<String, String> factMap = new HashMap<>();
                factMap.put("teamId", opportunityFact.getTeamId()+"");
                ruleV2Req.setFactObj(factMap);

                HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(ruleV2Req);
                if (Objects.isNull(hitRuleVo)) {
                    //未找到团队，告警
                    log.error("查询团队机器人失败，请检查配置，{}", JSONUtil.toJsonStr(factMap));
                }else {
                    storeId = Integer.parseInt(hitRuleVo.getSimpleRuleValue());
                }
            }
            if (Objects.nonNull(storeId)){

                String msg = buildMsg(opportunityFact, true);
                log.info("企微客资分配通知内容{}", msg);
                WechatNailNailRobotDeclarationRequest wechatNailNailRobotDeclarationRequest = new WechatNailNailRobotDeclarationRequest();
                wechatNailNailRobotDeclarationRequest.setType(1);
                wechatNailNailRobotDeclarationRequest.setBizType(NoticeTypeEnum.DIANPING_RESERVATION_STORE_DIMENSION.getBizType());
                wechatNailNailRobotDeclarationRequest.setStoreId(storeId);
                wechatNailNailRobotDeclarationRequest.setContent(msg);
                ScrmUserVO user = userRepository.getUserByScrmId(opportunityFact.getScrmMemberId());
                if (user!= null && StringUtils.isNotBlank(user.getPhone()) && user.getPhone().length() == 11) {
                    //@指定成员
                    wechatNailNailRobotDeclarationRequest.setMentionedMobileList(user.getPhone());
                }
                Result<Boolean> notice = noticeService.notice(wechatNailNailRobotDeclarationRequest);
                log.info("发送客资小群-企微客资分配通知结果：{}", JSONUtil.toJsonStr(notice));
            }
        } catch (Exception e) {
            log.error("发送客资小群-企微客资分配通知异常:{}", e.getMessage());
        }
    }

    /**
     * 构建通知消息，并机密手机号中间4位
     * @param opportunityFact
     * @param encryptPhone
     * @return
     */
    protected String buildMsg(OpportunityFact opportunityFact, boolean encryptPhone){

        String phone = opportunityFact.getClientPhone();
        if (encryptPhone && Strings.isNotBlank(phone)){
            phone = PhoneUtil.hideBetween(phone).toString();
        }

        String messageTemplate = "";
        String fromTypeDesc = "";
        if (PicpOrderTpyeEnum.MONTH_ORDER.getCode().equals(opportunityFact.getOrderType())) {
            messageTemplate = ScrmQwCustomerMessageConstant.CUSTOMER_DISTRIBUTION;
            fromTypeDesc = CustomerFromTypeEnum.getValueByCode(opportunityFact.getFromType());
        } else if (PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode().equals(opportunityFact.getOrderType())) {
            messageTemplate = ScrmQwCustomerMessageConstant.PRODUCTION_CUSTOMER_DISTRIBUTION;
            fromTypeDesc = CustomerProductionFromTypeEnum.getDescByScrmCode(opportunityFact.getProductionFromType());
        }

        if (StringUtils.isBlank(messageTemplate)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "订单类型为:" + opportunityFact.getOrderType() +",消息模版不存在");
        }
        String msg = MessageFormat.format(messageTemplate,
                AssignFailTypeEnum.MISS_SUCCESS.getDesc(),
                ObjectUtil.isNotNull(opportunityFact.getClientName()) ? opportunityFact.getClientName() : "",
                ObjectUtil.isNotNull(opportunityFact.getClientPhone()) ? phone : "",
                //ObjectUtil.isNotNull(opportunityFact.getCustomerBudget()) ? opportunityFact.getCustomerBudget() : "",
                fromTypeDesc,
                //ObjectUtil.isNotNull(opportunityFact.getBrandName()) ? opportunityFact.getBrandName() : "",
                //ObjectUtil.isNotNull(opportunityFact.getCountryName()) ? opportunityFact.getCountryName() : "",
                //ObjectUtil.isNotNull(opportunityFact.getProvinceName()) ? opportunityFact.getProvinceName() : "",
                //ObjectUtil.isNotNull(opportunityFact.getCityName()) ? opportunityFact.getCityName() : "",
                //ObjectUtil.isNotNull(opportunityFact.getDistrictName()) ? opportunityFact.getDistrictName() : "",
                ObjectUtil.isNotNull(opportunityFact.getStoreName()) ? opportunityFact.getStoreName() : "",
                ObjectUtil.isNotNull(opportunityFact.getContactWay()) ? opportunityFact.getContactWay() : "",
                //ObjectUtil.isNotNull(opportunityFact.getIntendedGoods()) ? opportunityFact.getIntendedGoods() : "",
                //ObjectUtil.isNotNull(opportunityFact.getIntendedNursingMode()) ? opportunityFact.getIntendedNursingMode() : "",
                "已分配成功给【"+opportunityFact.getSalesName()+"】"
        );

        return msg;
    }
}
