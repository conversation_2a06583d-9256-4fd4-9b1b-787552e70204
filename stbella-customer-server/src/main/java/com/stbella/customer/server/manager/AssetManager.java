package com.stbella.customer.server.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.asset.api.enums.AccountType;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.asset.api.facade.AccountDetailQueryService;
import com.stbella.asset.api.facade.AccountQueryService;
import com.stbella.asset.api.facade.AccountStreamQueryService;
import com.stbella.asset.api.facade.TradeCmdService;
import com.stbella.asset.api.facade.VoucherService;
import com.stbella.asset.api.req.CouponSendActivityReq;
import com.stbella.asset.api.req.DetailQueryReq;
import com.stbella.asset.api.req.StreamQueryReq;
import com.stbella.asset.api.req.YouzanCouponDeleteReq;
import com.stbella.asset.api.req.trade.MultiUserTradeReq;
import com.stbella.asset.api.req.trade.UserTradeReq;
import com.stbella.asset.api.res.AccountDetailDto;
import com.stbella.asset.api.res.AccountStreamDto;
import com.stbella.asset.api.res.AssetAccountDto;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.customer.server.util.DateUtils;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 资产中心
 * @date 2021/12/6 3:06 下午
 */
@Component
@Slf4j
public class AssetManager {
    @DubboReference
    private TradeCmdService tradeCmdService;
    @DubboReference
    private AccountStreamQueryService accountStreamQueryService;
    @DubboReference
    private AccountDetailQueryService accountDetailQueryService;
    @DubboReference
    private AccountQueryService accountQueryService;

    @DubboReference
    private VoucherService voucherService;


    public Result<List<Long>> syncAsset(UserTradeReq req) {
        log.info("同步资产中心参数={}", JSONUtil.parse(req));
        Result<List<Long>> result = tradeCmdService.execUserTrade(req);
        log.info("同步资产中心结果={}", JSONUtil.parse(result));
        return result;
    }


    public Result<List<Long>> batchSyncAsset(MultiUserTradeReq req) {
        log.info("批量同步资产中心参数={}", JSONUtil.parse(req));
        Result<List<Long>> result = tradeCmdService.execMultiUserTrade(req);
        log.info("批量同步资产中心结果={}", JSONUtil.parse(result));
        return result;
    }

    public AccountStreamDto getAsset(StreamQueryReq req) {
        log.info("查询资产中心参数={}", JSONUtil.parse(req));
        Result<PageVO<AccountStreamDto>> result = accountStreamQueryService.listBy(req);
        log.info("查询资产中心结果={}", JSONUtil.parse(result));
        if (result.getSuccess() && ObjectUtil.isNotEmpty(result.getData().getList()) && ObjectUtil.isNotEmpty(result.getData().getList().get(0))) {
            return result.getData().getList().get(0);
        }
        return null;
    }

    public PageVO<AccountStreamDto> getAssets(StreamQueryReq req) {
        Result<PageVO<AccountStreamDto>> result = accountStreamQueryService.listBy(req);
        if (result.getSuccess() && ObjectUtil.isNotEmpty(result.getData().getList()) && ObjectUtil.isNotEmpty(result.getData().getList().get(0))) {
            return result.getData();
        }
        return new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum());
    }

    public AccountDetailDto getAccountDetailDto(DetailQueryReq req) {
        log.info("查询账户参数={}", JSONUtil.parse(req));
        Result<PageVO<AccountDetailDto>> result = accountDetailQueryService.listBy(req);
        log.info("查询账号结果={}", JSONUtil.parse(result));
        if (result.getSuccess() && ObjectUtil.isNotEmpty(result.getData().getList()) && ObjectUtil.isNotEmpty(result.getData().getList().get(0))) {
            return result.getData().getList().get(0);
        }
        return null;
    }

    /**
     * 查询子账户信息
     *
     * @param userId
     * @param assetType
     * @return
     */
    public AssetAccountDto queryAccountAssetInfo(Integer userId, Integer assetType) {
        Result<AssetAccountDto> result = accountQueryService.getAccountByUserAndAssetType(userId.toString(), assetType.longValue());

        return result.getSuccess() ? result.getData() : null;
    }

    /**
     * 发送有赞优惠券
     *
     * @param req
     * @return 有赞优惠券id
     */
    public String sendYouzanCoupon(CouponSendActivityReq req)  {
        Result<String> result = voucherService.sendCoupon(req);
        return result.getSuccess() ? result.getData() : null;
    }

    /**
     * 删除有赞优惠券
     *
     * @param req
     * @return
     */
    public Boolean deleteYouzanCoupon(YouzanCouponDeleteReq req) {
        Result<Boolean> result = voucherService.deleteCoupon(req);
        return result.getSuccess() && result.getData();
    }

    /**
     * 获取最近要过期的积分
     *
     * @param accountId
     * @param startTime
     * @param endTime
     * @return
     */
    public Long getSoonExpiringIntegral (Long accountId, Date startTime, Date endTime) {
        DetailQueryReq req = new DetailQueryReq();
        req.setAccountId(accountId);
        req.setAccountType(AccountType.INTEGRAL.getCode().longValue());
        req.setAssetType(AssetType.AVAILABLE_INTEGRAL.getCode().longValue());
        req.setExpiredStartTime(startTime);
        req.setExpiredEndTime(endTime);
        req.setPageNum(1);
        req.setPageSize(9999999);

        Result<PageVO<AccountDetailDto>> result = accountDetailQueryService.listBy(req);
        if (!result.getSuccess() || CollectionUtil.isEmpty(result.getData().getList())) {
            return 0L;
        }

        return result.getData().getList().stream()
            .filter(dto -> DateUtils.between(dto.getExpiredTime(), startTime, endTime))
            .filter(dto -> Objects.nonNull(dto.getAmount()))
            .mapToLong(dto -> dto.getAmount() - (Objects.nonNull(dto.getUsedAmount()) ? dto.getUsedAmount() : 0L))
            .sum();
    }
}
