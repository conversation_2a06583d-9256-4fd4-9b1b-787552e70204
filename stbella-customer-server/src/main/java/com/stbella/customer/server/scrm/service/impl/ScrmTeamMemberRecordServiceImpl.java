package com.stbella.customer.server.scrm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.customer.server.customer.vo.ClientSearchMemberVO;
import com.stbella.customer.server.scrm.convert.SCRMConvert;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.mapper.ScrmTeamMemberRecordMapper;
import com.stbella.customer.server.scrm.request.ScrmAddTeamMemberRequest;
import com.stbella.customer.server.scrm.request.ScrmTeamMemberRecordRequest;
import com.stbella.customer.server.scrm.service.ScrmTeamMemberRecordService;
import com.stbella.customer.server.scrm.service.XsyScrmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * scrm各实体的团队成员记录 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-09-05
 */
@Service
@Slf4j
public class ScrmTeamMemberRecordServiceImpl extends ServiceImpl<ScrmTeamMemberRecordMapper, ScrmTeamMemberRecordPO> implements ScrmTeamMemberRecordService {

    @Resource
    private SCRMConvert scrmConvert;

    @Resource
    private XsyScrmService xsyScrmService;

    /**
     * 创建或更新scrm团队成员关系
     *
     * @param request
     * @return
     */
    @Override
    public ScrmTeamMemberRecordPO edit(ScrmTeamMemberRecordRequest request) {
        ScrmTeamMemberRecordPO recordPO = getOneByRequest(request);
        if (Objects.isNull(recordPO)) {
            recordPO = scrmConvert.teamMemberRecordRequest2PO(request);
            if (Objects.nonNull(recordPO)) {
                save(recordPO);
            }
        }

        return recordPO;
    }

    /**
     * 删除scrm团队成员关系
     *
     * @param request
     */
    @Override
    public void delete(ScrmTeamMemberRecordRequest request) {
        ScrmTeamMemberRecordPO recordPO = getOneByRequest(request);
        if (Objects.nonNull(recordPO)) {
            removeById(recordPO.getId());
        }
    }

    private ScrmTeamMemberRecordPO getOneByRequest(ScrmTeamMemberRecordRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }

        return getOne(new LambdaQueryWrapper<ScrmTeamMemberRecordPO>()
            .eq(ScrmTeamMemberRecordPO::getType, request.getType())
            .eq(ScrmTeamMemberRecordPO::getRecordId, request.getRecordId())
            .eq(ScrmTeamMemberRecordPO::getUserId, request.getUserId())
            .last("limit 1"));
    }

    @Override
    public Page<ClientSearchMemberVO> searchPageByKeyword(Page page, Long scrmId, String keyword) {
        return this.baseMapper.searchPageByKeyword(page,scrmId,keyword);
    }

    @Override
    public List<ClientSearchMemberVO> searchByKeyword(Long scrmId, String keyword) {
        return this.baseMapper.searchByKeyword(scrmId,keyword);
    }

    /**
     * 将销售拉到客户的团队成员中
     *
     * @param customerId
     * @param saleId
     * @return
     */
    @Override
    public ScrmTeamMemberRecordPO addCustomerTeamMember(Long customerId, Long saleId) {
        ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
            .recordId(customerId)
            .userId(saleId)
            .type(1)
            .build();

        ScrmTeamMemberRecordPO scrmTeamMemberRecordPO = getOneByRequest(recordRequest);
        if (Objects.nonNull(scrmTeamMemberRecordPO)) {
            return scrmTeamMemberRecordPO;
        }

        ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
        scrmAddTeamMemberRequest.setUserId(saleId);
        scrmAddTeamMemberRequest.setRecordFrom_data(customerId);
        scrmAddTeamMemberRequest.setRecordFrom(1L);
        scrmAddTeamMemberRequest.setOwnerFlag(1);

        xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);

        return edit(recordRequest);
    }

    @Override
    public List<ScrmTeamMemberRecordPO> queryTeamMemberRecordList(Long recordId, Integer type) {
        return list(new LambdaQueryWrapper<ScrmTeamMemberRecordPO>()
            .eq(ScrmTeamMemberRecordPO::getRecordId, recordId)
            .eq(ScrmTeamMemberRecordPO::getType, type));
    }
}
