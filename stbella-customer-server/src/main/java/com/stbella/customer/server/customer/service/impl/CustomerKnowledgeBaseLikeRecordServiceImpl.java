package com.stbella.customer.server.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBaseLikeRecordPO;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBasePushRecordPO;
import com.stbella.customer.server.customer.mapper.CustomerKnowledgeBaseLikeRecordMapper;
import com.stbella.customer.server.customer.service.CustomerKnowledgeBaseLikeRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <p>
 * C端知识库文章点赞记录 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-12-19
 */
@Service
public class CustomerKnowledgeBaseLikeRecordServiceImpl extends ServiceImpl<CustomerKnowledgeBaseLikeRecordMapper, CustomerKnowledgeBaseLikeRecordPO> implements CustomerKnowledgeBaseLikeRecordService {

	@Override
	public void updateDeleted(Set<Integer> basicUids, Set<Long> baseIds) {
		LambdaUpdateWrapper<CustomerKnowledgeBaseLikeRecordPO> updateWrapper = new LambdaUpdateWrapper<CustomerKnowledgeBaseLikeRecordPO>()
				.in(CustomerKnowledgeBaseLikeRecordPO::getBasicUid, basicUids)
				.in(CustomerKnowledgeBaseLikeRecordPO::getKnowledgeBaseId, baseIds)
				.set(CustomerKnowledgeBaseLikeRecordPO::getDeleted, 1);
		this.update(updateWrapper);
	}
}
