package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.stbella.care.server.care.enmu.RoomStateCheckInEnum;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.entity.RoomStateCheckInRoomInfoPO;
import com.stbella.care.server.care.vo.CustomerPostpartumVO;
import com.stbella.care.server.care.vo.mom.BodyIndexVO;
import com.stbella.core.base.PageVO;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.JwtUtil;
import com.stbella.customer.server.client.CareClient;
import com.stbella.customer.server.client.MessageClient;
import com.stbella.customer.server.client.SsoClient;
import com.stbella.customer.server.customer.constant.MsgConstant;
import com.stbella.customer.server.customer.entity.CustomerPostpartumInfoPO;
import com.stbella.customer.server.customer.entity.CustomerPostpartumOperateRecordPO;
import com.stbella.customer.server.customer.enums.postpartum.PostpartumTaskNodeEnum;
import com.stbella.customer.server.customer.mapper.CustomerPostpartumInfoMapper;
import com.stbella.customer.server.customer.request.CustomerPostpartumSaveRequest;
import com.stbella.customer.server.customer.request.HistoryCustomerRequest;
import com.stbella.customer.server.customer.request.postpartum.TaskNodeRequest;
import com.stbella.customer.server.customer.service.CustomerPostpartumInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.customer.server.customer.service.CustomerPostpartumOperateRecordService;
import com.stbella.customer.server.customer.service.impl.postpartum.TaskNodeManager;
import com.stbella.customer.server.customer.service.impl.postpartum.task.TaskNode;
import com.stbella.customer.server.customer.vo.PostpartumCalendarVO;
import com.stbella.customer.server.customer.vo.PostpartumCustomerVO;
import com.stbella.customer.server.customer.vo.PostpartumDetailVO;
import com.stbella.customer.server.customer.vo.postpartum.TaskNodeVO;
import com.stbella.customer.server.ecp.entity.EcpStorePO;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.service.CfgStoreService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.sso.therapist.res.TherapistVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户产后修复数据 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-09
 */
@DubboService
@Service
@Slf4j
public class CustomerPostpartumInfoServiceImpl extends ServiceImpl<CustomerPostpartumInfoMapper, CustomerPostpartumInfoPO> implements CustomerPostpartumInfoService {


    @Resource
    private CustomerPostpartumOperateRecordService customerPostpartumOperateRecordService;

    @Resource
    private TabClientService tabClientService;

    @Resource
    private CfgStoreService cfgStoreService;

    @Resource
    private CareClient careClient;

    @Resource
    private TaskNodeManager taskNodeManager;

    @Resource
    private SsoClient ssoClient;

    @Resource
    private MessageClient messageClient;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> savePostpartum(CustomerPostpartumSaveRequest request) {
        if (! CustomerPostpartumSaveRequest.verifyNullData(request)){
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "身体指标数据不能都为空");
        }
        request.verifyIntentionParam();
        CustomerPostpartumOperateRecordPO operateRecord = new CustomerPostpartumOperateRecordPO();
        UserTokenInfoDTO userInfo = JwtUtil.getJwtTokenUserInfo();
        Long operatorGuid = userInfo.getUserId();
        String operatorName = userInfo.getUserName();

        List<CustomerPostpartumInfoPO> customerPostpartumList = this.listByCustomerIdAndDate(Collections.singletonList(request.getRecordDate()), request.getCustomerId());
        CustomerPostpartumInfoPO newCustomerPostpartumInfo;
        if (CollectionUtil.isEmpty(customerPostpartumList)){
            newCustomerPostpartumInfo = new CustomerPostpartumInfoPO();
            BeanUtils.copyProperties(request,newCustomerPostpartumInfo);
            newCustomerPostpartumInfo.setCreateId(operatorGuid);
            newCustomerPostpartumInfo.setCreateName(operatorName);
            this.save(newCustomerPostpartumInfo);
        }else{
            newCustomerPostpartumInfo = customerPostpartumList.get(0);
            operateRecord.setOriginalContent(JSON.toJSONString(customerPostpartumList.get(0)));
            setNewPartum(request, operatorGuid, operatorName, newCustomerPostpartumInfo);
            this.baseMapper.updateByPrimaryKey(newCustomerPostpartumInfo);
        }
        operateRecord.setCustomerId(request.getCustomerId());
        operateRecord.setPostpartumId(newCustomerPostpartumInfo.getId());
        operateRecord.setCreateId(operatorGuid);
        operateRecord.setCreateName(operatorName);
        operateRecord.setOperateContent(JSON.toJSONString(newCustomerPostpartumInfo));
        customerPostpartumOperateRecordService.save(operateRecord);
        return Result.success();
    }




    private void setNewPartum(CustomerPostpartumSaveRequest request, Long operatorGuid, String operatorName, CustomerPostpartumInfoPO newCustomerPostpartumInfo) {
        newCustomerPostpartumInfo.setWeight(request.getWeight());
        newCustomerPostpartumInfo.setBreastCircumference(request.getBreastCircumference());
        newCustomerPostpartumInfo.setHipCircumference(request.getHipCircumference());
        newCustomerPostpartumInfo.setAbdomenCircumference(request.getAbdomenCircumference());
        newCustomerPostpartumInfo.setThighCircumferenceLeft(request.getThighCircumferenceLeft());
        newCustomerPostpartumInfo.setThighCircumferenceRight(request.getThighCircumferenceRight());
        newCustomerPostpartumInfo.setArmpitCircumference(request.getArmpitCircumference());
        newCustomerPostpartumInfo.setWaistCircumference(request.getWaistCircumference());
        newCustomerPostpartumInfo.setUpdateId(operatorGuid);
        newCustomerPostpartumInfo.setUpdateName(operatorName);
        newCustomerPostpartumInfo.setIntentionProjectFlag(request.getIntentionProjectFlag());
        newCustomerPostpartumInfo.setIntentionProject(request.getIntentionProject());
        newCustomerPostpartumInfo.setRemark(request.getRemark());
        newCustomerPostpartumInfo.setTaskNode(request.getTaskNode());
        newCustomerPostpartumInfo.setOrderNo(request.getOrderNo());
    }


    @Override
    public List<CustomerPostpartumInfoPO> listByCustomerIdAndDate(List<Date> recordDateList, Long customerId) {
        if (CollectionUtil.isEmpty(recordDateList) || Objects.isNull(customerId)){
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                .in(CustomerPostpartumInfoPO::getRecordDate,recordDateList)
                .eq(CustomerPostpartumInfoPO::getCustomerId,customerId));
    }

    @Override
    public Result<PostpartumDetailVO> detail(Date recordDate, Long customerId) {
        List<CustomerPostpartumInfoPO> customerPostpartumInfoList = this.listByCustomerIdAndDate(Collections.singletonList(recordDate), customerId);
        PostpartumDetailVO postpartumDetail = new PostpartumDetailVO();
        if (CollectionUtil.isNotEmpty(customerPostpartumInfoList)){
            BeanUtils.copyProperties(customerPostpartumInfoList.get(0),postpartumDetail);
        }else{
            // 体重为空取护理
            CustomerPostpartumVO postpartumData = careClient.getPostpartumData(customerId, new Date(recordDate.getTime()));
            if (Objects.nonNull(postpartumData)){
                postpartumDetail.setWeight(postpartumData.getWeight());
            }
            // 意向项目放最近一次的记录
            CustomerPostpartumInfoPO  lastIntentionInfo = this.getOne(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                    .eq(CustomerPostpartumInfoPO::getCustomerId, customerId)
                    .isNotNull(CustomerPostpartumInfoPO::getIntentionProject)
                    .orderByDesc(CustomerPostpartumInfoPO::getRecordDate)
                    .last(" limit 1")
            );
            if (Objects.nonNull(lastIntentionInfo)){
                postpartumDetail.setIntentionProject(lastIntentionInfo.getIntentionProject());
                postpartumDetail.setIntentionProjectFlag(lastIntentionInfo.getIntentionProjectFlag());
            }
        }
        return Result.success(postpartumDetail);
    }

    @Override
    public Result<PostpartumCalendarVO> calendar(Long customerId) {
        PostpartumCalendarVO postpartumCalendar = new PostpartumCalendarVO();
        List<CustomerPostpartumInfoPO> list = this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                .select(CustomerPostpartumInfoPO::getRecordDate)
                .eq(CustomerPostpartumInfoPO::getCustomerId, customerId));
        if (CollectionUtil.isEmpty(list)){
            return Result.success(postpartumCalendar);
        }
        List<Date> dateList = list.stream()
                .sorted(Comparator.comparing(CustomerPostpartumInfoPO::getRecordDate))
                .map(CustomerPostpartumInfoPO::getRecordDate)
                .collect(Collectors.toList());
        postpartumCalendar.setExistDateList(dateList);
        return Result.success(postpartumCalendar);
    }

    @Override
    public Result<List<PostpartumCustomerVO>> historyCustomer(HistoryCustomerRequest request) {
        if (StringUtils.isBlank(request.getOperator().getOperatorGuid())){
            return Result.success(Collections.emptyList());
        }
        Long createId = Long.valueOf(request.getOperator().getOperatorGuid());
        List<CustomerPostpartumOperateRecordPO> list = customerPostpartumOperateRecordService.list(new LambdaQueryWrapper<CustomerPostpartumOperateRecordPO>()
                .select(CustomerPostpartumOperateRecordPO::getCustomerId, CustomerPostpartumOperateRecordPO::getGmtCreate)
                .eq(CustomerPostpartumOperateRecordPO::getCreateId, createId));
        if (CollectionUtil.isEmpty(list)){
            return Result.success(Collections.emptyList());
        }

        Map<Long, CustomerPostpartumOperateRecordPO> postpartumOperateRecord = list.stream()
                .collect(Collectors.toMap(CustomerPostpartumOperateRecordPO::getCustomerId, Function.identity(),
                (t1, t2) -> t1.getGmtCreate().getTime() > t2.getGmtCreate().getTime() ? t1 : t2));
        List<Long> customerIdList = postpartumOperateRecord.values().stream().map(CustomerPostpartumOperateRecordPO::getCustomerId).collect(Collectors.toList());
        List<TabClientPO> tabClientList = tabClientService.listByCustomerIdList(customerIdList);
        List<EcpStorePO> ecpStoreList = cfgStoreService.queryByStoreIdList(tabClientList.stream().map(TabClientPO::getStoreId).collect(Collectors.toList()));
        Map<Integer, String> storeMap = ecpStoreList.stream().collect(Collectors.toMap(EcpStorePO::getStoreId, EcpStorePO::getStoreName));

        List<PostpartumCustomerVO> resultData = postpartumOperateRecord.values()
                .stream()
                .sorted(Comparator.comparing(CustomerPostpartumOperateRecordPO::getGmtCreate).reversed())
                .map(i -> {
                    Optional<TabClientPO> first = tabClientList.stream().filter(tab -> Objects.equals(tab.getId(), i.getCustomerId())).findFirst();
                    if (!first.isPresent()) {
                        return null;
                    }
                    TabClientPO tabClient = first.get();
                    PostpartumCustomerVO postpartumCustomer = new PostpartumCustomerVO();
                    postpartumCustomer.setId(tabClient.getId());
                    postpartumCustomer.setName(tabClient.getName());
                    postpartumCustomer.setPhone(DesensitizedUtil.mobilePhone(tabClient.getPhone()));
                    postpartumCustomer.setStoreId(tabClient.getStoreId());
                    postpartumCustomer.setStoreName(storeMap.get(tabClient.getStoreId()));
                    postpartumCustomer.setBasicId(tabClient.getBasicUid());
                    return postpartumCustomer;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        return Result.success(resultData);
    }



    @Override
    public List<TaskNodeVO> taskNodes(TaskNodeRequest request) {
        log.info("CustomerPostpartumInfoService.taskNodes req={}",JSON.toJSONString(request));
        RoomStateCheckInInfoPO roomInfo = null;
        if (StringUtils.isBlank(request.getOrderNo())){
            List<RoomStateCheckInInfoPO> roomInfoByCustomerId = careClient.getRoomInfoByCustomerId(ListUtil.toList(request.getCustomerId()), RoomStateCheckInEnum.ROOM_IN_STATES);
            if (CollectionUtil.isEmpty(roomInfoByCustomerId)){
                log.info("taskNodes 房态不存在clientId={}",request.getCustomerId());
                return Collections.emptyList();
            }
            roomInfo = roomInfoByCustomerId.stream().max(Comparator.comparing(RoomStateCheckInInfoPO::getCheckInDate)).get();
        }else {
            List<RoomStateCheckInInfoPO> roomInfoList = careClient.getRoomInfoByOrderNoList(ListUtil.toList(request.getOrderNo()));
            if (CollectionUtil.isEmpty(roomInfoList)){
                log.info("taskNodes 房态不存在订单号={}",request.getOrderNo());
                return Collections.emptyList();
            }
            roomInfo = roomInfoList.get(0);
        }
        if (! Objects.equals(roomInfo.getCheckInStatus(),RoomStateCheckInEnum.ROOM_STATE_CHECK_IN.getCode())){
            return new ArrayList<>();
        }


        // 已经提交过的数据
        List<CustomerPostpartumInfoPO> list = this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                .select(CustomerPostpartumInfoPO::getTaskNode)
                .eq(CustomerPostpartumInfoPO::getOrderNo, roomInfo.getOrderNo())
                .eq(CustomerPostpartumInfoPO::getCustomerId, request.getCustomerId())
                .isNotNull(CustomerPostpartumInfoPO::getTaskNode)
        );

        Map<String, Object> context = new HashMap<>();
        context.put(TaskNode.KEY_ORDER_NO, roomInfo.getOrderNo());
        context.put(TaskNode.KEY_CUSTOMER_ID, request.getCustomerId());
        context.put(TaskNode.KEY_ROOM_STATE_INFO, roomInfo);
        context.put(TaskNode.KEY_RECORD_DATE,request.getRecordDate());
        context.put(TaskNode.KEY_EXIST_TASK_NODE,list.stream().map(CustomerPostpartumInfoPO::getTaskNode).collect(Collectors.toList()));
        return taskNodeManager.execute(PostpartumTaskNodeEnum.executeCodes, context);
    }

    @Override
    public List<CustomerPostpartumInfoPO> listByOrderNo(List<String> orderNoList,List<Integer> taskNodeList) {
         return this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                .in(CustomerPostpartumInfoPO::getOrderNo, orderNoList)
                .in(CollectionUtil.isNotEmpty(taskNodeList), CustomerPostpartumInfoPO::getTaskNode, taskNodeList)
        );
    }

    @Override
    public void remindSeventhDayAdmission() {

        int pageNum = 1;
        int pageSize = 500;
        Date now = DateUtils.toDate(LocalDate.now());

        while (true) {
            PageVO<RoomStateCheckInInfoPO> roomInfoPage = careClient.pageByCheckInStatus(pageNum, pageSize, ListUtil.toList(RoomStateCheckInEnum.ROOM_STATE_CHECK_IN.getCode()));
            if (CollectionUtil.isEmpty(roomInfoPage.getList())) {
                break;
            }
            pageNum++;
            List<RoomStateCheckInInfoPO> roomInfoList = roomInfoPage.getList();
            //房间信息
            List<RoomStateCheckInRoomInfoPO> rooms = careClient.listByRoomIdList(roomInfoList.stream().map(RoomStateCheckInInfoPO::getId).collect(Collectors.toList()));
            //已填写过的信息
            List<CustomerPostpartumInfoPO> customerPostpartumInfoList = listByOrderNo(roomInfoList.stream().map(RoomStateCheckInInfoPO::getOrderNo).collect(Collectors.toList()), ListUtil.toList(PostpartumTaskNodeEnum.SEVENTH_DAY_OF_ADMISSION.getCode()));
            roomInfoList.removeIf(i -> {
                boolean b = customerPostpartumInfoList.stream().anyMatch(j -> Objects.equals(j.getOrderNo(), i.getOrderNo()));
                if (b){
                    return true;
                }
                List<RoomStateCheckInRoomInfoPO> roomsByInfoId = rooms.stream()
                        .filter(r -> Objects.equals(r.getRoomStateCheckInInfoId(), i.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(roomsByInfoId)){
                    return true;
                }
                Date dateAfter = DateUtils.getCheckInDateAfter(roomsByInfoId, 7);
                if (! Objects.equals(now,dateAfter)){
                    return true;
                }
                return false;
            });
            if (CollectionUtil.isEmpty(roomInfoList)){
                log.info("remindSeventhDayAdmission remindData is empty");
                continue;
            }
            List<Integer> storeIdList = roomInfoList.stream().map(i -> i.getStoreId().intValue()).distinct().collect(Collectors.toList());
            Result<List<TherapistVo>> therapistResult = ssoClient.queryTherapistByStore(storeIdList);
            if (CollectionUtil.isEmpty(therapistResult.getData())){
                log.info("remindSeventhDayAdmission store therapist is empty");
                continue;
            }
            List<TherapistVo> therapistList = therapistResult.getData();
            for (RoomStateCheckInInfoPO checkInInfo : roomInfoList) {
                List<String> mobileList = filterTherapistByStore(therapistList, checkInInfo.getStoreId());
                if (CollectionUtil.isEmpty(mobileList)){
                    continue;
                }
                SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
                sceneTriggerReq.setRequestId(IdWorker.get32UUID());
                sceneTriggerReq.setSceneId(MsgConstant.POSTPARTUM_SEVENTH_DAY_ADMISSION_ID);
                sceneTriggerReq.setTargetList(mobileList);
                Map<String, String> contextDate = new HashMap<>();
                contextDate.put("customerName", checkInInfo.getCustomerName());
                sceneTriggerReq.setContextData(contextDate);
                try {
                    messageClient.triggerScene(sceneTriggerReq);
                } catch (Exception e) {
                    log.error("remindSeventhDayAdmission triggerScene error", e);
                }
            }
        }
    }

    @Override
    public void remindThreeDayBeforeDeparture() {
        int pageNum = 1;
        int pageSize = 500;
        Date now = DateUtils.toDate(LocalDate.now());

        while (true) {
            PageVO<RoomStateCheckInInfoPO> roomInfoPage = careClient.pageByCheckInStatus(pageNum, pageSize, ListUtil.toList(RoomStateCheckInEnum.ROOM_STATE_CHECK_IN.getCode()));
            if (CollectionUtil.isEmpty(roomInfoPage.getList())) {
                break;
            }
            pageNum++;
            List<RoomStateCheckInInfoPO> roomInfoList = roomInfoPage.getList();

            //房间信息
            List<RoomStateCheckInRoomInfoPO> rooms = careClient.listByRoomIdList(roomInfoList.stream().map(RoomStateCheckInInfoPO::getId).collect(Collectors.toList()));
            // 已经填写过的订单
            List<CustomerPostpartumInfoPO> customerPostpartumInfoList = listByOrderNo(roomInfoList.stream().map(RoomStateCheckInInfoPO::getOrderNo).collect(Collectors.toList()), ListUtil.toList(PostpartumTaskNodeEnum.THREE_DAYS_BEFORE_DEPARTURE.getCode()));
            roomInfoList.removeIf(i -> {
                boolean b = customerPostpartumInfoList.stream().anyMatch(j -> Objects.equals(j.getOrderNo(), i.getOrderNo()));
                if (b) {
                    return true;
                }
                List<RoomStateCheckInRoomInfoPO> roomsByInfoId = rooms.stream()
                        .filter(r -> Objects.equals(r.getRoomStateCheckInInfoId(), i.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(roomsByInfoId)) {
                    return true;
                }
                Date dateBefore = DateUtils.getCheckOutDateBefore(roomsByInfoId, 3);
                if (!Objects.equals(now, dateBefore)) {
                    return true;
                }
                return false;
            });
            if (CollectionUtil.isEmpty(roomInfoList)) {
                log.info("remindThreeDayBeforeDeparture remindData is empty");
                continue;
            }

            List<Integer> storeIdList = roomInfoList.stream().map(i -> i.getStoreId().intValue()).distinct().collect(Collectors.toList());
            Result<List<TherapistVo>> therapistResult = ssoClient.queryTherapistByStore(storeIdList);
            if (CollectionUtil.isEmpty(therapistResult.getData())) {
                log.info("remindThreeDayBeforeDeparture store therapist is empty");
                continue;
            }
            List<TherapistVo> therapistList = therapistResult.getData();
            for (RoomStateCheckInInfoPO checkInInfo : roomInfoList) {
                List<String> mobileList = filterTherapistByStore(therapistList, checkInInfo.getStoreId());
                if (CollectionUtil.isEmpty(mobileList)) {
                    continue;
                }
                SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
                sceneTriggerReq.setRequestId(IdWorker.get32UUID());
                sceneTriggerReq.setSceneId(MsgConstant.POSTPARTUM_THREE_DAY_BEFORE_DEPARTURE_ID);
                sceneTriggerReq.setTargetList(mobileList);
                Map<String, String> contextDate = new HashMap<>();
                contextDate.put("customerName", checkInInfo.getCustomerName());
                sceneTriggerReq.setContextData(contextDate);
                try {
                    messageClient.triggerScene(sceneTriggerReq);
                } catch (Exception e) {
                    log.error("remindThreeDayBeforeDeparture triggerScene error", e);
                }
            }
        }


    }


    @Override
    public void remindUterusInBasin() {
        List<BodyIndexVO> customerList = careClient.getCheckInCustomerBodyIndex(null);
        if (CollectionUtil.isEmpty(customerList)){
            log.info("remindUterusInBasin customerList is empty");
            return;
        }
        Date now = DateUtils.toDate(LocalDate.now());
        customerList.removeIf(i-> i.getRecordDate().compareTo(now) > 0);
        if (CollectionUtil.isEmpty(customerList)){
            log.info("remindUterusInBasin customerList nowData is empty");
            return;
        }

        List<CustomerPostpartumInfoPO> customerPostpartumInfoList = listByOrderNo(customerList.stream().map(BodyIndexVO::getOrderNo).collect(Collectors.toList()), ListUtil.toList(PostpartumTaskNodeEnum.UTERUS_IN_THE_BASIN.getCode()));
        customerList.removeIf(i-> customerPostpartumInfoList.stream().anyMatch(j-> Objects.equals(j.getOrderNo(),i.getOrderNo())));
        if (CollectionUtil.isEmpty(customerList)){
            log.info("remindUterusInBasin remindData is empty");
            return;
        }
        List<Integer> storeIdList = customerList.stream().map(i -> i.getStoreId().intValue()).distinct().collect(Collectors.toList());
        Result<List<TherapistVo>> therapistResult = ssoClient.queryTherapistByStore(storeIdList);
        if (CollectionUtil.isEmpty(therapistResult.getData())){
            log.info("remindUterusInBasin store therapist is empty");
            return;
        }
        List<TherapistVo> therapistList = therapistResult.getData();
        for (BodyIndexVO bodyIndex : customerList) {
            List<String> mobileList = filterTherapistByStore(therapistList, bodyIndex.getStoreId());
            if (CollectionUtil.isEmpty(mobileList)){
                continue;
            }
            SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
            sceneTriggerReq.setRequestId(bodyIndex.getOrderNo()+"-"+MsgConstant.POSTPARTUM_UTERUS_IN_BASIN_ID);
            sceneTriggerReq.setSceneId(MsgConstant.POSTPARTUM_UTERUS_IN_BASIN_ID);
            sceneTriggerReq.setTargetList(mobileList);
            Map<String, String> contextDate = new HashMap<>();
            contextDate.put("customerName", bodyIndex.getCustomerName());
            sceneTriggerReq.setContextData(contextDate);
            try {
                messageClient.triggerScene(sceneTriggerReq);
            } catch (Exception e) {
                log.error("remindUterusInBasin triggerScene error", e);
            }
        }
    }

    @Override
    public void savePostpartumBatch(List<CustomerPostpartumSaveRequest> requestList) {
        if (CollectionUtil.isEmpty(requestList)) {
            return;
        }

        requestList.forEach(request -> {
            if (!CustomerPostpartumSaveRequest.verifyNullData(request)) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "身体指标数据不能都为空");
            }
            request.verifyIntentionParam();
        });

        Date recordDate = requestList.get(0).getRecordDate();
        List<Long> customerIds = requestList.stream().map(CustomerPostpartumSaveRequest::getCustomerId).distinct().collect(Collectors.toList());
        Map<Long, CustomerPostpartumSaveRequest> customerRequestMap = requestList.stream().collect(Collectors.toMap(CustomerPostpartumSaveRequest::getCustomerId, Function.identity(), (a, b) -> a));

        List<CustomerPostpartumInfoPO> customerPostpartumList = this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
                .eq(CustomerPostpartumInfoPO::getRecordDate, recordDate)
                .in(CustomerPostpartumInfoPO::getCustomerId, customerIds));
        Map<Long, CustomerPostpartumInfoPO> customerPosMap = customerPostpartumList.stream().collect(Collectors.toMap(CustomerPostpartumInfoPO::getCustomerId, Function.identity(), (a, b) -> a));

        List<CustomerPostpartumInfoPO> postpartumInfoPOS = new ArrayList<>();
        for (Long customerId : customerIds) {
            CustomerPostpartumOperateRecordPO operateRecord = new CustomerPostpartumOperateRecordPO();
            CustomerPostpartumSaveRequest request = customerRequestMap.get(customerId);
            Long operateId = request.getOperateId();
            String operateName = request.getOperateName();

            CustomerPostpartumInfoPO customerPostpartumInfoPO = customerPosMap.get(customerId);
            if (null == customerPostpartumInfoPO) {
                customerPostpartumInfoPO = new CustomerPostpartumInfoPO();
                BeanUtils.copyProperties(request, customerPostpartumInfoPO);
                customerPostpartumInfoPO.setCreateId(operateId);
                customerPostpartumInfoPO.setCreateName(operateName);
            } else {
                operateRecord.setOriginalContent(JSON.toJSONString(customerPostpartumInfoPO));
                setNewPartum(request, operateId, operateName, customerPostpartumInfoPO);
            }
            operateRecord.setCustomerId(request.getCustomerId());
            operateRecord.setPostpartumId(customerPostpartumInfoPO.getId());
            operateRecord.setCreateId(operateId);
            operateRecord.setCreateName(operateName);
            operateRecord.setOperateContent(JSON.toJSONString(customerPostpartumInfoPO));
            postpartumInfoPOS.add(customerPostpartumInfoPO);
        }
        this.saveOrUpdateBatch(postpartumInfoPOS);
    }

    @Override
    public List<CustomerPostpartumInfoPO> listByCustomerIdList(List<Long> customerIdList) {
        if (CollectionUtil.isEmpty(customerIdList)){
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<CustomerPostpartumInfoPO>()
            .in(CustomerPostpartumInfoPO::getCustomerId,customerIdList));
    }


    private List<String> filterTherapistByStore(List<TherapistVo> therapistList,Long storeId) {
        return therapistList.stream()
                .filter(th -> th.getStores().contains(storeId.intValue()))
                .map(TherapistVo::getMobile)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
