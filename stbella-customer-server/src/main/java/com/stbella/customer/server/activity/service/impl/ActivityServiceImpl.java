package com.stbella.customer.server.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.activity.convert.ActivityInfoConvert;
import com.stbella.customer.server.activity.entity.ActivityInfoPO;
import com.stbella.customer.server.activity.entity.ActivityPartInfoPO;
import com.stbella.customer.server.activity.entity.CustomerGenericActivityAppointmentPO;
import com.stbella.customer.server.activity.enums.*;
import com.stbella.customer.server.activity.mapper.ActivityInfoMapper;
import com.stbella.customer.server.activity.mapper.ActivityPartInfoMapper;
import com.stbella.customer.server.activity.request.*;
import com.stbella.customer.server.activity.request.mini.ActivityListMiniRequest;
import com.stbella.customer.server.activity.service.ActivityPartInfoService;
import com.stbella.customer.server.activity.service.ActivityService;
import com.stbella.customer.server.activity.service.AppointmentBaseService;
import com.stbella.customer.server.activity.service.CustomerGenericActivityAppointmentService;
import com.stbella.customer.server.activity.vo.*;
import com.stbella.customer.server.activity.vo.mini.ActivityListMiniVO;
import com.stbella.customer.server.activity.vo.mini.ActivityPartSignupDetailVO;
import com.stbella.customer.server.brand.strategy.BrandStrategyService;
import com.stbella.customer.server.config.BizConfig;
import com.stbella.customer.server.customer.entity.CustomerAssetsPO;
import com.stbella.customer.server.customer.enums.BrandTypeEnum;
import com.stbella.customer.server.customer.service.CustomerAssetsService;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.enums.ActiveCityEnum;
import com.stbella.customer.server.ecp.enums.StoreTypeEnum;
import com.stbella.customer.server.ecp.mapper.helper.HeAddressMapper;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.scrm.enums.BrandTypeBusinessTypeEnum;
import com.stbella.customer.server.util.BPCheckUtil;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.message.scene.api.MessageCommandService;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.redis.service.RedisService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
/**
 * 用户运营2.0活动服务类
 * <p>
 * ActivityService
 * </p>
 *
 * <AUTHOR> @since 2023-04-18
 */
@Slf4j
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityInfoMapper, ActivityInfoPO> implements ActivityService {
    public static final String ACTIVITY_PREFIX = "用户运营2.0-";

    // redis存储二维码key
    private static final String QRCODE_CACHE_PREFIX = "qrcode_cache";

    @Resource
    private ActivityPartInfoService activityPartInfoService;

    @Resource
    private CustomerGenericActivityAppointmentService appointmentService;
    @Resource
    private CfgStoreService cfgStoreService;
    @Resource
    private TabActivityService tabActivityService;
    @Resource
    private ActivityPartInfoMapper activityPartInfoMapper;

    @Resource
    private TabWechatUserService tabWechatUserService;

    @Resource
    private HeUserBasicService heUserBasicService;

    @Resource
    private TabClientService tabClientService;

    @DubboReference
    private MessageCommandService messageCommandService;

    @Resource
    private ActivityInfoConvert activityInfoConvert;

    @Resource
    private HeAddressMapper heAddressMapper;

    @Resource
    @Lazy
    private CustomerGenericActivityAppointmentService customerGenericActivityAppointmentService;

    @Resource
    private CustomerAssetsService customerAssetsService;

    @Resource
    private AppointmentBaseService appointmentBaseService;

    @Resource
    private ActivityService activityService;

    @Resource
    private BizConfig bizConfig;

    @Resource
    private RedisService redisService;
    @Resource
    private BrandStrategyService brandStrategyService;

    @Resource
    private TabActivityReadRecordService tabActivityReadRecordService;

    @Override
    public PageVO<ActivityListVO> getActivityList(ActivityListRequest request) {
        if (request.getPageNum() <= 0) {
            request.setPageNum(1);
        }
        //如果选了活动状态 ，默认把发布状态置为1
        if (CollectionUtils.isNotEmpty(request.getActivityStatus()) && request.getPublishStatus() == null) {
            request.setPublishStatus(1);
        }
        if (CollectionUtils.isNotEmpty(request.getActivityStatus()) && (request.getPublishStatus() != null && request.getPublishStatus().equals(ActivityEnum.PublishStatusEnum.DOWN.getValue()))) {
            return new PageVO<>(Collections.emptyList(), 0, request.getPageSize(), request.getPageNum());
        }
        Page<ActivityListVO> page = this.baseMapper.getActivityListPage(new Page(request.getPageNum(), request.getPageSize()), request);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new PageVO<>(Collections.emptyList(), 0, request.getPageSize(), request.getPageNum());
        }
        buildAdminPageList(page.getRecords());
        PageVO<ActivityListVO> result = new PageVO(page, ActivityListVO.class);
        return result;
    }

    @Override
    public void exportActivityReport(HttpServletResponse response, ActivityReportRequest request) {
        try {
            // 查询活动基础信息
            ActivityInfoPO activityInfoPO = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getId, request.getActivityId()));
            if (activityInfoPO == null) {
                setResp(response, "活动不存在");
            } else {
                if (!activityInfoPO.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
                    setResp(response, "当前该页面暂未对接活动数据，请联系创新中心 下载数据");
                } else {
                    // 查询所有场次
                    List<ActivityPartInfoPO> activityPartInfoPOS = activityPartInfoService.list(new LambdaQueryWrapper<ActivityPartInfoPO>().in(ActivityPartInfoPO::getActivityId, activityInfoPO.getId()));
                    if (CollectionUtils.isEmpty(activityPartInfoPOS) && !activityInfoPO.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
                        setResp(response, "当前该页面暂未对接活动数据，请联系创新中心 下载数据");
                    } else {
                        XSSFWorkbook wb = new XSSFWorkbook();
                        for (ActivityPartInfoPO partInfoPO : activityPartInfoPOS) {
                            List<ActivityPartSignupDetailVO> partSignupDetailVOS = appointmentBaseService.getActivityPartSignupDetailList(partInfoPO.getId());
                            log.info("用户运营2.0报表导出获取数据:{}", partSignupDetailVOS == null ? "无数据" : JSON.toJSONString(partSignupDetailVOS));
                            if (CollectionUtils.isEmpty(partSignupDetailVOS)) {
                                processData(partInfoPO.getName(), wb, new ArrayList<>());
                            } else {
                                processData(partInfoPO.getName(), wb, partSignupDetailVOS);
                            }
                        }
                        String fileName = activityInfoPO.getActivityName();
                        response.setContentType("application/vnd.ms-excel;charset=utf-8");
                        fileName = URLEncoder.encode(fileName, "UTF-8");
                        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                        OutputStream out = response.getOutputStream();
                        wb.write(out);
                        out.close();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取报名详情失败", e);
        }
    }

    /**
     * 生成二维码
     *
     * @param request
     * @return
     */
    @Override
    public String generateCommonQrCode(QrCodeRequest request) {
        log.info("生成二维码图片, request:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"content"}, request, true);

        if (Objects.nonNull(request.getWidth()) || request.getWidth() < 0) {
            request.setWidth(300);
        }

        if (Objects.nonNull(request.getHeight()) || request.getHeight() < 0) {
            request.setHeight(300);
        }

        if (Objects.nonNull(request.getMarin()) || request.getMarin() < 0) {
            request.setMarin(1);
        }

        // 获取二维码对应内容的md5值作为该二维码的标识，用于缓存
        String qrcodeFlag = DigestUtil.md5Hex(request.getContent() + ":" + request.getWidth() + ":" + request.getHeight() + ":" + request.getMarin());

        QrConfig qrConfig = new QrConfig();
        qrConfig.setWidth(request.getWidth());
        qrConfig.setHeight(request.getHeight());
        qrConfig.setMargin(request.getMarin());

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        QrCodeUtil.generate(request.getContent(), qrConfig, ImgUtil.IMAGE_TYPE_PNG, outputStream);
//        if (outputStream.size() > 0) {
//            String path = "/qrcode/" + IdWorker.get32UUID() + ".png";
//            OssUploadResponse imageResult = OSSFactory.build().upload(outputStream.toByteArray(), path);
//            if (imageResult.isSuccess()) {
//
//                return imageResult.getUrl();
//            }
//        }


//        QrCodeUtil

        return null;
    }

    private static void processData(String sheetName, XSSFWorkbook wb, List<ActivityPartSignupDetailVO> partSignupDetailVOS) {
        sheetName = sheetName.replaceAll("[\\\\/:*?\\[\\]]", "_");
        // 确保名称长度不超过 31 个字符
        if (sheetName.length() > 31) {
            sheetName = sheetName.substring(0, 31);
        }

        // 如果名称以单引号开头或结尾，去掉（POI 会报错）
        sheetName = sheetName.replaceAll("^'+|'+$", "");
        XSSFSheet sheet = wb.createSheet(sheetName);
        if (CollectionUtils.isEmpty(partSignupDetailVOS)) {
            return;
        }
        List<String> headerList = partSignupDetailVOS.get(0).getHeaderList();
        List<List<String>> columnList = new ArrayList<>();
        for (ActivityPartSignupDetailVO detailVO : partSignupDetailVOS) {
            columnList.add(detailVO.getColumnList());
        }

        // 写入表头
        XSSFRow headerRow = sheet.createRow(0);
        for (int i = 0; i < headerList.size(); i++) {
            headerRow.createCell(i).setCellValue(headerList.get(i));
        }

        // 写入每行数据
        for (int i = 0; i < columnList.size(); i++) {
            XSSFRow row = sheet.createRow(i + 1);
            for (int j = 0; j < columnList.get(i).size(); j++) {
                String value = columnList.get(i).get(j);
                try {
                    if (StringUtils.isNotBlank(value) && value.equals("preparingPregnancy")) {
                        if (headerList.get(j).equals("预产期")) {
                            value = "备孕中";
                        }
                    }
                }catch (Exception e){
                    log.error("导出报名-处理备孕中字段失败", e);
                }
                row.createCell(j).setCellValue(value);
            }
        }
    }

    private void setResp(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=utf-8");
        String jsonResponse = "{\"code\":\"A0420\",\"msg\":\"" + message + "\",\"success\":false}";
        PrintWriter writer = response.getWriter();
        writer.write(jsonResponse);
        writer.flush();
        writer.close();
        throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, message);
    }

    private Map<Long, List<ActivityPartSimpleInfo>> getActivityPartsMap(List<ActivityInfoPO> records) {
        List<Long> activityIds = records.stream().map(ActivityInfoPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(activityIds)) {
            List<ActivityPartInfoPO> list = activityPartInfoService.list(new LambdaQueryWrapper<ActivityPartInfoPO>().in(ActivityPartInfoPO::getActivityId, activityIds));

            Map<Long, List<ActivityPartSimpleInfo>> simpleInfoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                List<ActivityPartSimpleInfo> activityPartInfo = Lists.newArrayList();
                for (Long activityId : activityIds) {
                    for (ActivityPartInfoPO infoPO : list) {
                        if (infoPO.getActivityId().equals(activityId)) {
                            activityPartInfo.add(new ActivityPartSimpleInfo(infoPO.getId(), infoPO.getName(), infoPO.getCityName(), infoPO.getStoreId()));
                        }
                    }
                    simpleInfoMap.put(activityId, activityPartInfo);
                }
                return simpleInfoMap;
            }
        }
        return new HashMap<>();
    }

    @Override
    public PageVO<ActivityListMiniVO> getActivityMiniList(ActivityListMiniRequest request) {
        log.info("大活动列表(最外层活动),request:{},basicUid:{}", JSON.toJSONString(request), request.getBasicUid());
        if (request.getPageNum() <= 0) {
            request.setPageNum(1);
        }
        Integer total = 0;
        List<ActivityListMiniVO> list = Lists.newArrayList();

        if (request.getCity() != null && request.getCity() >= 0) {
            //判断是否是附近，附近和城市走不同SQL
            if (request.getCity() == 0 && request.getLat() != null && request.getLng() != null) {
                log.info("city={},查附近", request.getCity());
                total = baseMapper.getActivityNearMiniListCount(request);
                if (total != null && total > 0) {
                    list = this.baseMapper.getActivityNearMiniList(new Page(request.getPageNum(), request.getPageSize()), request);
                }
            }
            if (request.getCity() > 0) {
                log.info("city={},查城市", request.getCity());
                total = baseMapper.getActivityMiniListCount(request);
                if (total != null && total > 0) {
                    list = this.baseMapper.getActivityMiniList(new Page(request.getPageNum(), request.getPageSize()), request);
                }
            }
        } else {
            //查全部
            log.info("city={},查全部", request.getCity());
            total = baseMapper.getActivityMiniListCount(request);
            if (total != null && total > 0) {
                list = this.baseMapper.getActivityMiniList(new Page(request.getPageNum(), request.getPageSize()), request);
            }
        }
        buildMiniListVO(list);
        //写入已读记录
        //用户调用活动接口，记录最新的活动时间，只在第一次分页请求list接口的时候才需要更新
        if (CollectionUtils.isNotEmpty(list) && list.size() > 0) {
            updateActiveReadRecordIfPageFirst(request.getBrandType(), request.getBasicUid(), request.getCategory(), request.getCity(), request.getPageNum(), request.getActivityStatus(), list.get(0).getGmtCreate());
        }
        return new PageVO<>(list, total, request.getPageSize(), request.getPageNum());
    }

    private void buildMiniListVO(List<ActivityListMiniVO> list) {
        Map<Long, Set<String>> storeMap = list.stream().filter(activity -> activity.getStoreId() != null && !activity.getStoreId().isEmpty()).collect(Collectors.groupingBy(ActivityListMiniVO::getActivityId, Collectors.mapping(ActivityListMiniVO::getStoreId, Collectors.toSet())));
        final Map<Integer, Set<String>> storeMaps = getStoreMaps(storeMap);
        list.stream().forEach(vo -> {
            if(vo.getBizId()!=null) {
                buildStoreInfo(vo);
            }
            //针对圣小贝拉的单独文案显示逻辑,如果是h5
            if (vo.getBrandType().equals(BrandTypeEnum.SAINT_BELLA.code()) || vo.getBrandType().equals(BrandTypeEnum.BABY_BELLA.code())) {
                if (vo.getLinkType().equals(ActivityLinkTypeEnum.H5.getValue())) {
                    if (StringUtils.isBlank(vo.getBrandLogo()) && StringUtils.isNotBlank(bizConfig.getBrandDefaultLogo())) {
                        for (String string : bizConfig.getBrandDefaultLogo().split("\\|")) {
                            if (vo.getBrandType().equals(Integer.parseInt(string.split(",")[0]))) {
                                vo.setBrandLogo(string.split(",")[1]);
                                return;
                            }
                        }
                    } else {
                        vo.setBrandLogo(vo.getBrandLogo());
                    }
                } else {
                    //处理多场次活动门店信息聚合
                    //门店展示优先，如果都没有，才展示城市
                    ArrayList<String> storeNames = processStoreNames(vo.getStoreId(), storeMaps);
                    if (!storeNames.isEmpty()) {
                        vo.setStoreName(StringUtils.join(storeNames, "·").endsWith("·") ? StringUtils.join(storeNames, "·").substring(0, StringUtils.join(storeNames, "·").length() - 1) : StringUtils.join(storeNames, "·"));
                    } else {
                        if(StringUtils.isBlank(vo.getCityNames())) {
                            vo.setStoreName("");
                        }else {
                            String[] join = vo.getCityNames().split(",");
                            if (bizConfig.getBrandStoreNameShowSuffix().equals(0)) {
                                Pattern pattern = Pattern.compile("[省市区县]$");
                                for (int i = 0; i < join.length; i++) {
                                    join[i] = pattern.matcher(join[i]).replaceAll("");
                                }
                            }
                            String result = String.join("·", join);
                            vo.setStoreName(result);
                        }
                    }
                }
            }
        });
    }

    /**
     * 兼容圣贝拉
     *
     * @param e
     */
    private void buildStoreInfo(ActivityListMiniVO e) {
        ActivityListMiniVO.StoreInfo storeInfo = new ActivityListMiniVO.StoreInfo();
        e.setStoreInfo(storeInfo);
        try {
            if (e.getBizId() == null) {
                return;
            }
            int ecpAid = e.getBizId().intValue();
            TabActivityPO one = tabActivityService.getOne(new LambdaQueryWrapper<TabActivityPO>().eq(
                    TabActivityPO::getId, ecpAid
            ));
            if(one == null||one.getStoreId()==null){
                return;
            }
            EcpStorePO store = cfgStoreService.queryStoreByStoreId(one.getStoreId());
            log.info("最外层活动store:{}", JSON.toJSONString(store));
            if (Objects.nonNull(store)) {
                BeanUtil.copyProperties(store, storeInfo);
            }
            storeInfo.setId(one.getStoreId());
            storeInfo.setName(store.getStoreName());
            e.setStoreInfo(storeInfo);
        } catch (Exception ex) {
            log.error("最外层活动", ex);
        }
    }

    public int getCanAppointmentStatus(ActivityListMiniVO item) {
        Date currentTime = new Date();
        if (currentTime.after(DateUtils.parse(item.getEndTime(), DateUtils.YYYY_MM_DD))) {
            return 0;
        } else if (currentTime.before(DateUtils.parse(item.getStartTime(), DateUtils.YYYY_MM_DD))) {
            if (currentTime.before(item.getSignStartTime())) {
                return -1; // 活动未开始，预约时间未到
            } else if (currentTime.after(item.getSignStartTime()) && currentTime.before(item.getSignEndTime())) {
                return 0; // 可预约
            } else {
                return -1; // 活动未开始，预约时间已过
            }
        } else {
            if (currentTime.before(item.getSignStartTime())) {
                return -1; // 活动已开始，预约时间未到
            } else if (currentTime.after(item.getSignStartTime()) && currentTime.before(item.getSignEndTime())) {
                return 0; // 可预约
            } else {
                return -1; // 活动未结束，预约时间已过
            }
        }
    }

    private static @NotNull LambdaQueryWrapper<ActivityInfoPO> getQueryWrapper(ActivityListRequest request) {
        LambdaQueryWrapper<ActivityInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        //根据request 构建查询筛选项
        if (Objects.nonNull(request.getActivityName())) {
            queryWrapper.like(ActivityInfoPO::getActivityName, request.getActivityName());
        }
        if (Objects.nonNull(request.getPublishStatus())) {
            queryWrapper.eq(ActivityInfoPO::getPublishStatus, request.getPublishStatus());
        }
        if (Objects.nonNull(request.getActivityStatus())) {
            queryWrapper.in(CollectionUtils.isNotEmpty(request.getActivityStatus()), ActivityInfoPO::getActivityStatus, request.getActivityStatus());
        }
        if (Objects.nonNull(request.getBrandType())) {
            queryWrapper.eq(ActivityInfoPO::getBrandType, request.getBrandType());
        }
        return queryWrapper;
    }

    private List<ActivityListVO> buildAdminPageList(List<ActivityListVO> records) {
        Map<Long, Set<String>> storeMap = records.stream().filter(activity -> activity.getStoreId() != null && !activity.getStoreId().isEmpty()).collect(Collectors.groupingBy(ActivityListVO::getId, Collectors.mapping(ActivityListVO::getStoreId, Collectors.toSet())));
        final Map<Integer, Set<String>> storeMaps = getStoreMaps(storeMap);
        Map<Long, List<ActivityPartSimpleInfo>> activityPartsMap = getActivityPartsMapByIds(records);
        if (CollectionUtils.isNotEmpty(records)) {
            return records.stream().map(vo -> {
                vo.setBrandTypeStr(Optional.ofNullable(BrandTypeEnum.getEnumByCode(vo.getBrandType())).map(BrandTypeEnum::desc).orElse(""));
                vo.setPublishStatusStr(Optional.ofNullable(ActivityPublishEnum.getValueByStatus(vo.getPublishStatus())).orElse(""));
                vo.setCategoryStr(Optional.ofNullable(BrandClassEnum.getClassNameByValue(vo.getBrandType(), vo.getCategory())).map(BrandClassEnum::getClassName).orElse(""));
                vo.setActivityStatusStr(processActivityStatusStr(vo));
//                vo.setCreatorName(vo.getCreateId());
//                vo.setModifierName(vo.getModifyId());
                //聚合处理
                vo.setCityStr(vo.getCityNames() == null ? new ArrayList<>() : Arrays.asList(vo.getCityNames().split(",")));
                vo.setStoreName(processStoreNames(vo.getStoreId(), storeMaps));
                vo.setActivityTimeSection((vo.getStartTime() == null ? "" : DateUtils.format(vo.getStartTime(), DateUtils.YYYY_MM_DD_HH_MM_SS)) + " - " + (vo.getEndTime() == null ? "" : DateUtils.format(vo.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS)));

                vo.setActivityPartInfo(activityPartsMap.get(vo.getId()) != null ? activityPartsMap.get(vo.getId()) : Lists.newArrayList());
                return vo;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 处理多场次活动名称
     *
     * @param storeId
     * @param storeMaps
     * @return
     */
    private ArrayList<String> processStoreNames(String storeId, Map<Integer, Set<String>> storeMaps) {
        Set<String> names = new HashSet<>();
        if (storeId != null) {
            if (storeId.split(",").length > 0) {
                Arrays.stream(storeId.split(",")).forEach(e -> {
                    if (storeMaps.get(Integer.parseInt(e)) != null) {
                        names.addAll(storeMaps.get(Integer.parseInt(e)));
                    }
                });
                return new ArrayList<>(names);
            } else {
                return (storeMaps.get(Integer.parseInt(storeId)) == null ? new ArrayList<>() : new ArrayList<>(storeMaps.get(Integer.parseInt(storeId))));
            }
        }
        return new ArrayList<>(names);
    }

    /**
     * 处理显示字段
     *
     * @param vo
     * @return
     */
    private static @NotNull String processActivityStatusStr(ActivityListVO vo) {
        return vo.getPublishStatus() == 0 ? "/" : Optional.ofNullable(vo.getActivityStatus()).flatMap(status -> {
            if (ActivityStatusEnum.getValueByStatus(status) != null) {
                return Optional.of(ActivityStatusEnum.getValueByStatus(status).getName());
            } else {
                return Optional.empty();
            }
        }).orElse("");
    }

    private Map<Integer, Set<String>> getStoreMaps(Map<Long, Set<String>> storeMap) {
        Map<Integer, Set<String>> storeNameMap = new HashMap<>();
        if (storeMap != null) {
            Set<String> storeIdset = new HashSet<>();
            for (Set<String> value : storeMap.values()) {
                for (String string : value) {
                    Set<String> collect = Arrays.stream(string.split(",")).collect(Collectors.toSet());
                    storeIdset.addAll(collect);
                }
            }
            if (CollectionUtils.isNotEmpty(storeIdset)) {
                ArrayList<Integer> storeIds = new ArrayList<>(storeIdset.stream().map(e -> Integer.parseInt(e)).collect(Collectors.toList()));
                List<EcpStorePO> storeList = cfgStoreService.queryByStoreIdList(storeIds);
                if (CollectionUtils.isNotEmpty(storeList)) {
                    //组装storeNameMap
                    storeList.forEach(store -> {
                        if (storeNameMap.get(store.getStoreId()) != null) {
                            storeNameMap.get(store.getStoreId()).add(store.getStoreName());
                        } else {
                            Set<String> storeNameList = new HashSet<>();
                            storeNameList.add(store.getStoreName());
                            storeNameMap.put(store.getStoreId(), storeNameList);
                        }
                    });
                    return storeNameMap;
                }
            }
        }
        return new HashMap<>();
    }

    private Map<Long, List<ActivityPartSimpleInfo>> getActivityPartsMapByIds(List<ActivityListVO> records) {
        Set<Long> partsId = records.stream().map(ActivityListVO::getPartsId).filter(Objects::nonNull).map(s -> s.split(",")).flatMap(Arrays::stream).map(Long::valueOf).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(partsId)) {
            return new HashMap<>();
        }
        List<ActivityPartInfoPO> infoPOS = activityPartInfoMapper.selectList(new LambdaQueryWrapper<ActivityPartInfoPO>().in(ActivityPartInfoPO::getId, partsId));
        if (CollectionUtils.isEmpty(infoPOS)) {
            return new HashMap<>();
        }
        //把所有infoPOS 中的storeName 进行聚合
        return infoPOS.stream().collect(Collectors.groupingBy(ActivityPartInfoPO::getActivityId, Collectors.mapping(e -> new ActivityPartSimpleInfo(e.getId(), e.getName(), e.getCityName(), e.getStoreId()), Collectors.toList())));
    }

    private List<String> getStoreNameStr(Map<Long, String> activityPartsStoreId, Long activityId) {
        //对应所有的门店id
        String ids = activityPartsStoreId.get(activityId);
        if (Strings.isBlank(ids)) {
            return Lists.newArrayList();
        }
        //门店id对应门店名称
        Map<Integer, String> storeNameMap = getStoreNameMap(activityPartsStoreId);
        return ids.split(",").length > 0 ? Arrays.stream(ids.split(",")).map(e -> storeNameMap.get(Integer.valueOf(e))).collect(Collectors.toList()) : Lists.newArrayList();
    }

    private Map<Integer, String> getStoreNameMap(Map<Long, String> activityPartsStoreId) {
        List<? extends Serializable> list = activityPartsStoreId.values().stream().map(e -> StringUtils.isBlank(e) ? "" : Long.valueOf(e.split(",") == null ? "" : e.split(",")[0])).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        List<EcpStorePO> storePOS = cfgStoreService.listByIds(list);
        if (CollectionUtils.isEmpty(storePOS)) {
            return new HashMap<>();
        }
        return storePOS.stream().collect(Collectors.toMap(EcpStorePO::getStoreId, EcpStorePO::getStoreName));
    }

    private Map<Long, String> getActivityPartsStoreId(Map<Long, List<ActivityPartSimpleInfo>> activityPartsMap) {
        Map<Long, String> storeIdMap = new HashMap<>();
        if (MapUtils.isNotEmpty(activityPartsMap)) {
            activityPartsMap.forEach((k, v) -> {
                StringBuilder storeIds = new StringBuilder();
                v.forEach(e -> {
                    if (StringUtils.isNotBlank(e.getStoreId())) {
                        storeIds.append(e.getStoreId()).append(",");
                    }
                });
                storeIdMap.put(k, storeIds.toString());
            });
        }
        return storeIdMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createActivity(ActivityCreateRequest request) {
        log.info("用户运营2.0活动-创建request:{}", JSON.toJSONString(request));
        //活动校验
        activityFormCheck(request, false);
        //场次校验
        activityPartFormCheck(request);
        //保存
        ActivityInfoPO activityInfoPO = save(request);
        return activityInfoPO.getId().toString();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public String updateActivity(ActivityCreateRequest request) {
        log.info("用户运营2.0活动-编辑request:{}", JSON.toJSONString(request));
        if (request == null || request.getId() == null) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "参数错误");
        }
        //更新
        update(request);
        return request.getId().toString();
    }

    /**
     * 更新活动
     *
     * @param request
     */
    public void update(ActivityCreateRequest request) {
        ActivityInfoPO activityInfoPO = getActivityInfoPO(request);
        checkWhenUpdate(request);
        //组装新的po
        ActivityInfoPO updatePo = activityInfoConvert.toPO(request);
        //兼容ecp活动同步
        if(request.getActivitySource()!=null && request.getActivitySource()== 1){
            updatePo.setDeleted(request.getDeleted());
        }else {
            updatePo.setDeleted(0);
        }
        updatePo.setId(activityInfoPO.getId());
        updatePo.setGmtModified(new Date());
        updatePo.setModifierName(request.getOperator() == null ? "" : request.getOperator().getOperatorName());
        updatePo.setModifyId(request.getOperator() == null ? "" : request.getOperator().getOperatorGuid());
        //更新场次
        if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
            setActivityPartTimeIfNull(request);
            updatePart(request);
        }

        //时间判断:有场次根据场次判断，没场次判断大活动时间
        updateActivityTime(request, updatePo);
        //判断大活动状态
        updatePo.setActivityStatus(getStatusByRequest(request, updatePo));
        log.info("更新活动:{}", JSON.toJSONString(updatePo));
        this.updateById(updatePo);
    }

    /**
     * 根据活动场次状态集合，计算主活动状态
     *
     * @param request
     * @param updatePo
     * @return
     */
    public Integer getStatusByRequest(ActivityCreateRequest request, ActivityInfoPO updatePo) {
        Date latestEndTime = null;
        Date minStartTime = null;
        if (CollectionUtils.isEmpty(request.getActivityPart())) {
            if (request.getStartTime() == null) {
                request.setStartTime(new Date());
            }
            if (request.getEndTime() == null) {
                request.setEndTime(DateUtils.parse("2099-12-31 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
            latestEndTime = request.getEndTime();
            minStartTime = request.getStartTime();

        } else {
            latestEndTime = request.getActivityPart().stream().max(Comparator.comparing(ActivityPartRequest::getEndTime)).get().getEndTime();
            minStartTime = request.getActivityPart().stream().min(Comparator.comparing(ActivityPartRequest::getStartTime)).get().getStartTime();
        }
        if (new Date().before(minStartTime)) {
            return ActivityStatusEnum.NO_READY.getValue(); // 未开始
        } else if (new Date().after(latestEndTime)) {
            return ActivityStatusEnum.FINISH.getValue(); // 已结束
        } else {
            //如果当前时间处于整场之间,要么会处于某个场次时间之中 ，要么会处于某个未开始的场次时间之前
            //所有都返回进行中
            //[1,2][4,5][10-20] ==>[1,20] 如果当前时间是9 则[已结束][已结束][未开始]，也返回进行中
            return ActivityStatusEnum.DOING.getValue(); // 进行中
        }
    }

    private static void updateActivityTime(ActivityCreateRequest request, ActivityInfoPO activityInfoPO) {
        try {
            if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
                Date earliestStartTime = request.getActivityPart().get(0).getStartTime();
                Date latestEndTime = request.getActivityPart().get(0).getEndTime();
                // 遍历partsPoList找到最早的开始时间和最晚的结束时间
                for (ActivityPartRequest part : request.getActivityPart()) {
                    if (part.getStartTime().before(earliestStartTime)) {
                        earliestStartTime = part.getStartTime();
                    }
                    if (part.getEndTime().after(latestEndTime)) {
                        latestEndTime = part.getEndTime();
                    }
                }
                log.info("更新活动名称:{},updatePo最早时间:{}，最晚场次时间{}", request.getActivityName(), earliestStartTime, latestEndTime);
                activityInfoPO.setStartTime(earliestStartTime);
                activityInfoPO.setEndTime(latestEndTime);
            } else {
                setActivityTimeIfNull(request);
            }
        } catch (Exception e) {
            log.error("更新活动时间段失败", e);
        }
    }

    /**
     * 获取存在的活动
     *
     * @param request
     * @return
     */
    private @NotNull ActivityInfoPO getActivityInfoPO(ActivityCreateRequest request) {
        if (Objects.isNull(request.getId())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "活动不存在");
        }
        //根据id获取活动
        ActivityInfoPO activityInfoPO = this.getById(request.getId());
        if (Objects.isNull(activityInfoPO)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "活动不存在");
        }
        return activityInfoPO;
    }

    /**
     * 更新时校验
     *
     * @param request
     */
    private void checkWhenUpdate(ActivityCreateRequest request) {
        checkDown(request);
        //活动校验
        activityFormCheck(request, true);
        //场次校验
        activityPartFormCheck(request);
//        //更新时间，不要在这里判断
//        setActivityTimeIfNull(request);
    }

    /**
     * 活动下架判断
     *
     * @param request
     */
    private void checkDown(ActivityCreateRequest request) {
        Integer numByActivityId = appointmentService.countSignupUserNumByActivityId(request.getId());
        if (numByActivityId != null && numByActivityId > 0) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "已有用户报名，该活动不可编辑");
        }
    }

    private void updatePart(ActivityCreateRequest request) {
        List<ActivityPartInfoPO> activityPartInfoPOS = activityPartInfoService.list(new LambdaQueryWrapper<ActivityPartInfoPO>().eq(ActivityPartInfoPO::getActivityId, request.getId()).eq(ActivityPartInfoPO::getDeleted, 0));
        log.info(ACTIVITY_PREFIX + "更新{}活动,原入参场次{},新入参场次{}", request.getActivityName(), CollectionUtils.isEmpty(activityPartInfoPOS) ? "{}" : activityPartInfoPOS.stream().map(e -> new HashMap().put(e.getName(), e.getDeleted())).collect(Collectors.toList()), CollectionUtils.isEmpty(request.getActivityPart()) ? "{}" : request.getActivityPart().stream().map(e -> e.getName()).collect(Collectors.toList()));
        updatePart(request, activityPartInfoPOS);
    }

    private void updatePart(ActivityCreateRequest request, List<ActivityPartInfoPO> activityPartInfoPOS) {
        if (CollectionUtils.isEmpty(activityPartInfoPOS)) {
            if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
                //如果原数据查不到 ，前端页面也可以新增场次
                activityPartInfoPOS = new ArrayList<>();
            } else {
                return;
            }
        }
        Set<Long> existingIds = new HashSet<>();
        for (ActivityPartInfoPO info : activityPartInfoPOS) {
            existingIds.add(info.getId());
        }
        //先做一次删除
        List<Long> oldIds = activityPartInfoPOS.stream().map(ActivityPartInfoPO::getId).collect(Collectors.toList());
        List<Long> newIds = request.getActivityPart().stream().map(ActivityPartRequest::getId).collect(Collectors.toList());
        deleteUpdate(request, oldIds, newIds);
        for (ActivityPartRequest partRequest : request.getActivityPart()) {
            if (bizConfig.isBlockWhenRegionNotFound()) {
                if (partRequest.getRegion() == null || partRequest.getRegion().compareTo(0) <= 0) {
                    throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "操作失败,系统不存在region:[" + partRequest.getRegion() + "]的信息,请联系创新中心");
                }
            }
            if (partRequest.getId() == null || partRequest.getId() == 0) {
                // 新增操作：id为空或为0且不在原有数据中
                if (!existingIds.contains(partRequest.getId())) {
                    ActivityPartInfoPO partPO = activityInfoConvert.toPartPO(partRequest);
                    //计算场次活动状态
                    updatePartStatus(partPO.getStartTime(), partPO.getEndTime(), partPO);
                    processCityName(partPO);
                    partPO.setDeleted(0);
                    partPO.setGmtModified(new Date());
                    partPO.setCreateId(Long.valueOf(request.getOperator().getOperatorGuid()));
                    partPO.setModifyId(Long.valueOf(request.getOperator().getOperatorGuid()));
                    partPO.setActivityId(request.getId());
                    activityPartInfoService.save(partPO);
                }
            } else {
                if (existingIds.contains(partRequest.getId())) {
                    // 执行更新操作
                    ActivityPartInfoPO partPO = activityInfoConvert.toPartPO(partRequest);
                    //计算场次活动状态
                    updatePartStatus(partPO.getStartTime(), partPO.getEndTime(), partPO);
                    processCityName(partPO);
                    partPO.setGmtModified(new Date());
                    partPO.setActivityId(request.getId());
                    partPO.setDeleted(0);
                    activityPartInfoService.saveOrUpdate(partPO);
                }
            }
        }
    }

    private void logForDebug(String label, Object... obj) {
        if (bizConfig.isLog4debug()) {
            log.info(label, obj);
        }
    }

    private void deleteUpdate(ActivityCreateRequest request, List<Long> oldIds, List<Long> newIds) {
        List<Long> deleteIds = oldIds.stream().filter(id -> !newIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            activityPartInfoService.remove(new LambdaQueryWrapper<ActivityPartInfoPO>().in(ActivityPartInfoPO::getId, deleteIds));
            //剔除已经删除的id
            request.getActivityPart().removeIf(activityPartRequest -> deleteIds.contains(activityPartRequest.getId()));
        }
    }

    @Override
    public String updatePublishStatus(ActivityUpdateStatusRequest activityEditRequest) {
        if (activityEditRequest == null || activityEditRequest.getId() == null) {
            return "下架失败失败";
        }
        if (activityEditRequest.getOperator() == null) {
            return "操作人获取失败";
        }

        Integer numByActivityId = appointmentService.countSignupUserNumByActivityId(activityEditRequest.getId());
        if (numByActivityId != null && numByActivityId > 0) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "已有用户报名，该活动不可下架");
        }
        // 通过id更新状态
        try {
            UpdateWrapper<ActivityInfoPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", activityEditRequest.getId()).set("publish_status", 0).set("gmt_modified", new Date()).set("modify_id", activityEditRequest.getOperator().getOperatorGuid()).set("modifier_name", activityEditRequest.getOperator().getOperatorName());
            this.update(updateWrapper);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "操作失败");
        }
        return "";
    }

    @Override
    public ActivityDetailVO detail(Long id, Integer publishStatus) {
        logForDebug("用户运营2.0-findbug-detail,id:{}", id);
        if (Objects.nonNull(id)) {
            //查询活动基础信息
            ActivityInfoPO activityInfoPO = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getId, id).eq(ActivityInfoPO::getDeleted, 0).eq(publishStatus != null, ActivityInfoPO::getPublishStatus, publishStatus));
            //查询场次
            if (Objects.nonNull(activityInfoPO)) {
                //处理时间
                List<ActivityPartInfoPO> pos = activityPartInfoService.list(new LambdaQueryWrapper<ActivityPartInfoPO>().eq(ActivityPartInfoPO::getActivityId, id));
                //根据activityInfoPO和activityPartInfoPOS组装 ActivityDetailVO对象
                return buildDetailVO(activityInfoPO, pos);
            }
        }
        return null;
    }

    /**
     * 组件构建活动详情
     *
     * @param activityInfoPO
     * @param activityPartInfoPOS
     * @return
     */
    private ActivityDetailVO buildDetailVO(ActivityInfoPO activityInfoPO, List<ActivityPartInfoPO> activityPartInfoPOS) {
        ActivityDetailVO detailVO = new ActivityDetailVO();
        BeanUtil.copyProperties(activityInfoPO, detailVO);
        List<ActivityPartVO> activityPartVOS = activityPartInfoPOS.stream().map(activityPartInfoPO -> {
            return activityInfoConvert.toVO(activityPartInfoPO);
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(activityPartVOS)) {
            detailVO.setActivityPart(activityPartVOS);
        }
        return detailVO;
    }

    private @NotNull ActivityInfoPO save(ActivityCreateRequest request) {
        String operatorGuid = request.getOperator().getOperatorGuid();
        setActivityTimeIfNull(request);
        ActivityInfoPO activityInfoPO = activityInfoConvert.toPO(request);
        //兼容ecp活动同步
        if(request.getActivitySource()!=null && request.getActivitySource()== 1){
            activityInfoPO.setDeleted(request.getDeleted());
        }else {
            activityInfoPO.setDeleted(0);
        }
        activityInfoPO.setGmtCreate(new Date());
        activityInfoPO.setGmtModified(new Date());
        activityInfoPO.setCreateId(operatorGuid);
        activityInfoPO.setModifyId(operatorGuid);
        activityInfoPO.setCreatorName(request.getOperator().getOperatorName());
        activityInfoPO.setModifierName(request.getOperator().getOperatorName());
        activityInfoPO.setBizId(request.getBizId());

        if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
            setActivityPartTimeIfNull(request);
            List<ActivityPartInfoPO> partsPoList = activityInfoConvert.toPoList(request.getActivityPart());
            if (activityInfoPO.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
                for (ActivityPartInfoPO activityPartInfoPO : partsPoList) {
                    //处理场次活动状态
                    updatePartStatus(activityPartInfoPO.getStartTime(), activityPartInfoPO.getEndTime(), activityPartInfoPO);
                    processCityName(activityPartInfoPO);
                }
                //处理大活动时间
                setActivityInfoTime(partsPoList, activityInfoPO);
            }
            //处理大活动状态
            activityInfoPO.setActivityStatus(getStatus(partsPoList));
            this.save(activityInfoPO);
            if(request.getBizId()==null){
                setActivityPartInfoCommonFileds(request, activityInfoPO, partsPoList);
                activityPartInfoService.saveBatch(partsPoList);
            }
        } else {
            //没有场次，判断活动状态
            updateStatus(activityInfoPO.getStartTime(), activityInfoPO.getEndTime(), activityInfoPO);
            this.save(activityInfoPO);
        }
        return activityInfoPO;
    }

    private static void setActivityPartTimeIfNull(ActivityCreateRequest request) {
        if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
            request.getActivityPart().stream().forEach(e -> {
                if (e.getStartTime() == null) {
                    e.setStartTime(new Date());
                }
                if (e.getEndTime() == null) {
                    e.setEndTime(DateUtils.parse("2099-12-31 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS));
                }
            });
        }
    }

    private static void setActivityTimeIfNull(ActivityCreateRequest request) {
        if (request.getStartTime() == null) {
            request.setStartTime(new Date());
        }
        if (request.getEndTime() == null) {
            request.setEndTime(DateUtils.parse("2099-12-31 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
    }

    /**
     * 处理省市区
     *
     * @param activityPartInfoPO
     */
    private void processCityName(ActivityPartInfoPO activityPartInfoPO) {
        ActivityAddressInfoVO activityAddressInfo = heAddressMapper.selectProvincesCitiesDistrictsById(activityPartInfoPO.getRegion());
        if (activityAddressInfo == null) {
            log.error("操作失败,region:{}的信息有误,请联系创新中心!", activityPartInfoPO.getRegion());
            if (bizConfig.isBlockWhenRegionNotFound()) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), String.format("操作失败,系统不存在region:%s的信息，请联系创新中心", activityPartInfoPO.getRegion()));
            }
        } else {
            activityPartInfoPO.setProvince(activityAddressInfo.getProvinceId());
            activityPartInfoPO.setCity(activityAddressInfo.getCityId());
        }
    }

    /**
     * 处理时间
     *
     * @param partsPoList
     * @param activityInfoPO
     */
    private static void setActivityInfoTime(List<ActivityPartInfoPO> partsPoList, ActivityInfoPO activityInfoPO) {
        Date earliestStartTime = partsPoList.get(0).getStartTime();
        Date latestEndTime = partsPoList.get(0).getEndTime();
        // 遍历partsPoList找到最早的开始时间和最晚的结束时间
        for (ActivityPartInfoPO part : partsPoList) {
            if (part.getStartTime().before(earliestStartTime)) {
                earliestStartTime = part.getStartTime();
            }
            if (part.getEndTime().after(latestEndTime)) {
                latestEndTime = part.getEndTime();
            }
        }
        log.info("用户运营2.0-处理所有场次时间范围:start:{},end:{}", earliestStartTime, latestEndTime);
        activityInfoPO.setStartTime(earliestStartTime);
        activityInfoPO.setEndTime(latestEndTime);
    }

    /**
     * 处理活动场次
     *
     * @param request
     * @param activityInfoPO
     * @param partsPoList
     */
    private void setActivityPartInfoCommonFileds(ActivityCreateRequest request, ActivityInfoPO activityInfoPO, List<ActivityPartInfoPO> partsPoList) {
        for (ActivityPartInfoPO activityPartInfoPO : partsPoList) {
            activityPartInfoPO.setActivityId(activityInfoPO.getId());
            activityPartInfoPO.setDeleted(0);
            activityPartInfoPO.setCreateId(Long.valueOf(request.getOperator().getOperatorGuid()));
            activityPartInfoPO.setGmtCreate(new Date());
            activityPartInfoPO.setModifyId(Long.valueOf(request.getOperator().getOperatorGuid()));
            activityPartInfoPO.setGmtModified(new Date());
        }
    }

    /**
     * 设置场次活动时间
     *
     * @param startTime
     * @param endTime
     * @param partInfoPO
     */
    private void updatePartStatus(Date startTime, Date endTime, ActivityPartInfoPO partInfoPO) {
        if (Objects.nonNull(endTime)) {
            //已过期
            if (endTime.before(new Date())) {
                partInfoPO.setActivityStatus(ActivityStatusEnum.FINISH.getValue());
                return;
            }
            //进行中
            if (startTime.compareTo(new Date()) <= 0) {
                partInfoPO.setActivityStatus(ActivityStatusEnum.DOING.getValue());
                return;
            }
            //未开始
            if (startTime.compareTo(new Date()) > 0) {
                partInfoPO.setActivityStatus(ActivityStatusEnum.NO_READY.getValue());
                return;
            }
        }
    }

    private void updateStatus(Date startTime, Date endTime, ActivityInfoPO activityInfoPO) {
        if (Objects.nonNull(endTime)) {
            //已过期
            if (endTime.before(new Date())) {
                activityInfoPO.setActivityStatus(ActivityStatusEnum.FINISH.getValue());
                return;
            }
            //进行中
            if (startTime.compareTo(new Date()) <= 0) {
                activityInfoPO.setActivityStatus(ActivityStatusEnum.DOING.getValue());
                return;
            }
            //未开始
            if (startTime.compareTo(new Date()) > 0) {
                activityInfoPO.setActivityStatus(ActivityStatusEnum.NO_READY.getValue());
                return;
            }
        }
    }

    /**
     * 根据活动场次状态集合，计算主活动状态
     *
     * @param activityPartInfoPOS
     * @return
     */
    public Integer getStatus(List<ActivityPartInfoPO> activityPartInfoPOS) {
        Date currentTime = new Date();
        Date latestEndTime = activityPartInfoPOS.stream().max(Comparator.comparing(ActivityPartInfoPO::getEndTime)).get().getEndTime();
        Date minStartTime = activityPartInfoPOS.stream().min(Comparator.comparing(ActivityPartInfoPO::getStartTime)).get().getStartTime();
        if (currentTime.before(minStartTime)) {
            return ActivityStatusEnum.NO_READY.getValue(); // 未开始
        } else if (currentTime.after(latestEndTime)) {
            return ActivityStatusEnum.FINISH.getValue(); // 已结束
        } else {
            //如果当前时间处于整场之间,要么会处于某个场次时间之中 ，要么会处于某个未开始的场次时间之前
            //所有都返回进行中
            //[1,2][4,5][10-20] ==>[1,20] 如果当前时间是9 则[已结束][已结束][未开始]，也返回进行中
            return ActivityStatusEnum.DOING.getValue(); // 进行中
        }
    }

    @Override
    public Map<String, Object> init() {
        Map<String, Object> category = new HashMap<>();
        //品牌列表
        category.put("brandType", getBrandTypeEnum());
        // 品牌对应会员等级
        category.put("brandLevel", getBrandLevelEnum());
        // 品牌对应
        category.put("activityCategory", getActivityEnum());
        // 活动状态
        category.put("activityStatus", getStatusEnum());
        //发布状态
        category.put("publishStatus", getPublishStatusEnum());
        //活动内容配置
        category.put("linkType", getLinkTypeEnum());
        return category;
    }

    @Override
    public List<ActiveCityListVOV2> getActivityCityList(ActivityCityListRequestV2 request) {
        log.info("调用城市列表接口,request:{}", JSON.toJSONString(request));
        List<ActiveCityListVOV2> activeCityList = new ArrayList<ActiveCityListVOV2>();
        if (request.getType().equals(0)) {
            activeCityList.addAll(setCityCommonMenu(request));
        }
        try {
            if (request.getType().equals(0)) {
                activeCityList.addAll(activityPartInfoService.getActivityCityList(request));
                return activeCityList;
            }
            return getSortedCityList(request);
        } catch (Exception e) {
            log.error("获取活动城市数据失败,参数{}", JSON.toJSONString(request), e);
        }
        return activeCityList;
    }

    private List<ActiveCityListVOV2> getSortedCityList(ActivityCityListRequestV2 request) {
        ActivityInfoPO activityInfoPO = activityService.getById(request.getActivityId());
        if (activityInfoPO == null) {
            return new ArrayList<>();
        }
        request.setActivityStartTime(activityInfoPO.getStartTime());
        request.setActivityEndTime(activityInfoPO.getEndTime());
        List<SortPartListVO> sortPartListVOS = getSortPartListVOS(request);
        List<ActiveCityListVOV2> sortCityList = new ArrayList<ActiveCityListVOV2>();
        if (sortPartListVOS != null) {
            for (SortPartListVO vo : sortPartListVOS) {
                Optional<ActiveCityListVOV2> first = sortCityList.stream().filter(e -> e.getCity().equals(vo.getCity())).findFirst();
                if (first.isPresent()) {
                    first.get().setCount(first.get().getCount() + 1);
                } else {
                    ActiveCityListVOV2 city = new ActiveCityListVOV2();
                    city.setCity(vo.getCity());
                    city.setCityName(vo.getCityName());
                    city.setCount(1);
                    sortCityList.add(city);
                }
            }
        }
        log.info("调用城市列表接口-排序后的所有城市:{}", JSON.toJSONString(sortCityList));
        return sortCityList;
    }

    @Override
    public List<SortPartListVO> getSortPartListVOS(ActivityCityListRequestV2 request) {
        List<SortPartListVO> sortPartListVOS = activityPartInfoService.getAllSortPartList(request);
        if (CollectionUtils.isNotEmpty(sortPartListVOS)) {
            log.info("调用城市列表接口-未排序的所有场次:{}", JSON.toJSONString(sortPartListVOS));
            // 自定义Comparator来实现排序逻辑
            Collections.sort(sortPartListVOS, new Comparator<SortPartListVO>() {
                @Override
                public int compare(SortPartListVO o1, SortPartListVO o2) {
                    // 按照canAppointmentStatus从大到小排序
                    int compareByCanAppointmentStatus = Integer.compare(o2.getCanAppointmentStatus(), o1.getCanAppointmentStatus());
                    if (compareByCanAppointmentStatus != 0) {
                        return compareByCanAppointmentStatus;
                    }

                    // 如果canAppointmentStatus相同，再按照activityStatus从小到大排序
                    int compareByActivityStatus = Integer.compare(o1.getActivityPartStatus(), o2.getActivityPartStatus());
                    if (compareByActivityStatus != 0) {
                        return compareByActivityStatus;
                    }
                    if(request.getLat() == null || request.getLng() == null || request.getLat().equals(BigDecimal.ZERO) || request.getLng().equals(BigDecimal.ZERO)){
                        return o1.getGmtCreate().compareTo(o2.getGmtCreate());
                    }else {
                        // 如果activityPartStatus相同，按照distance字段从小到大排序
                        return o1.getDistance().compareTo(o2.getDistance());
                    }
                }
            });
        }
        log.info("调用城市列表接口-排序后的所有场次:{}", JSON.toJSONString(sortPartListVOS));
        return sortPartListVOS;
    }

    private List<ActiveCityListVOV2> setCityCommonMenu(ActivityCityListRequestV2 request) {
        List<ActiveCityListVOV2> activeCityList = new ArrayList<ActiveCityListVOV2>();
        activeCityList.add(ActiveCityListVOV2.builder().city(ActiveCityEnum.ALL.code()).cityName(ActiveCityEnum.ALL.desc()).build());
        if (request != null && request.getLat() != null && request.getLng() != null) {
            activeCityList.add(ActiveCityListVOV2.builder().city(ActiveCityEnum.NEAR.code()).cityName(ActiveCityEnum.NEAR.desc()).build());
        }
        return activeCityList;
    }


    public List<EnumValueVO> getLinkTypeEnum() {
        List<EnumValueVO> subEnums = new ArrayList<>();
        for (ActivityLinkTypeEnum linkTypeEnum : ActivityLinkTypeEnum.values()) {
            subEnums.add(new EnumValueVO(linkTypeEnum.getName(), linkTypeEnum.getValue()));
        }
        return subEnums;
    }

    public List<EnumValueVO> getBrandTypeEnum() {
        List<EnumValueVO> publish = new ArrayList<>();
        if (bizConfig != null) {
            for (String brandType : bizConfig.getBrandTypes().split(",")) {
                for (ActivityEnum value : ActivityEnum.values()) {
                    if (value.getValue().equals(Integer.parseInt(brandType))) {
                        publish.add(createEnumMap(value.getName(), Integer.parseInt(brandType)));
                    }
                }
            }
        } else {
            publish.add(createEnumMap("ISLA", 100));
        }
        return publish;
    }

    public List<EnumValueVO> getPublishStatusEnum() {
        List<EnumValueVO> publish = new ArrayList<>();
        publish.add(createEnumMap("草稿", 0));
        publish.add(createEnumMap("发布", 1));
        return publish;
    }

    public List<EnumValueVO> getStatusEnum() {
        List<EnumValueVO> subEnums = new ArrayList<>();
        for (ActivityStatusEnum statusEnum : ActivityStatusEnum.values()) {
            subEnums.add(new EnumValueVO(statusEnum.getName(), statusEnum.getValue()));
        }
        return subEnums;
    }

    /**
     * 解析enum
     *
     * @return
     */
    public Map<Integer, List<EnumValueVO>> getActivityEnum() {
        Map<Integer, List<EnumValueVO>> root = new HashMap<>();
        for (ActivityEnum activity : ActivityEnum.values()) {
            List<EnumValueVO> subEnums = new ArrayList<>();
            for (BrandClassEnum brandClassEnum : activity.getBrandClasses()) {
                if (brandClassEnum.getBrandType().equals(activity.getValue())) {
                    subEnums.add(new EnumValueVO(brandClassEnum.getClassName(), brandClassEnum.getValue()));
                }
            }
            root.put(activity.getValue(), subEnums);
        }
        return root;
    }

    public Map<Integer, List<EnumValueVO>> getBrandLevelEnum() {
        Map<Integer, List<EnumValueVO>> root = new HashMap<>();
        for (ActivityEnum activity : ActivityEnum.values()) {
            List<EnumValueVO> subEnums = new ArrayList<>();
            for (BrandLevelEnum levelEnum : BrandLevelEnum.values()) {
                if (levelEnum.getBrandType().equals(-1)) {
                    subEnums.add(new EnumValueVO(levelEnum.getLevelName(), levelEnum.getLevelId()));
                }
            }
            root.put(activity.getValue(), subEnums);
        }
        return root;
    }

    private EnumValueVO createEnumMap(String name, Integer value) {
        return new EnumValueVO(name, value);
    }

    /**
     * 场次校验
     *
     * @param request
     */
    private void activityPartFormCheck(ActivityCreateRequest request) {
        //校验场次
        if (request.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
            if (CollectionUtils.isNotEmpty(request.getActivityPart())) {
                //校验场次不能大于11场
                if (request.getActivityPart().size() > 11) {
                    throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动场次不能大于11场");
                }
                //场次名不能空或者重复
                checkActivityPartName(request);
                for (ActivityPartRequest partRequest : request.getActivityPart()) {
                    checkRegion(partRequest);
                    //校验必填字段
                    checkPartRequireField(request, partRequest);
                }
            }
        }
    }

    private static void checkRegion(ActivityPartRequest partRequest) {
        if (partRequest.getRegion() == null || partRequest.getRegion().compareTo(0) <= 0) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "操作失败,region:[" + partRequest.getRegion() + "]有误,请联系创新中心");
        }
    }

    /**
     * 参数校验
     *
     * @param request
     * @param func
     * @param checkNew true:只校验新活动,老活动放行; false:校验所有活动
     * @param errorMsg
     * @return
     */
    public void validateField(ActivityCreateRequest request, String errorMsg, Supplier<Boolean> func, boolean checkNew, boolean checkDraft) {
        boolean condition = func.get();
        if (checkNew) {
            condition = condition && (request.getActivitySource() == null);
        }
        if (checkDraft) {
            condition = condition && bizConfig.skipCheckWhenDraft(request.getPublishStatus());
        }
        if (condition) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, errorMsg);
        }
    }


    private void checkPartRequireField(ActivityCreateRequest request, ActivityPartRequest partRequest) {
        validateField(request, "场次名称必填", () -> Strings.isBlank(partRequest.getName()), false, false);
        validateField(request, "报名人数限制填写错误", () -> (partRequest.getMaxSignupNum() == null || partRequest.getMaxSignupNum() <= 0), true, true);
        validateField(request, "可报名会员等级填写错误", () -> CollectionUtils.isEmpty(partRequest.getSignupLevelId()), true, true);
        validateField(request, "活动二级海报不能为空", () -> StringUtils.isBlank(partRequest.getSecondaryImageUrl()), true, true);

        validateField(request, "活动场次介绍[description]不能为空", () -> Strings.isBlank(partRequest.getDescription()), true, true);
        validateField(request, "活动位置名称[locationName]不能为空", () -> Strings.isBlank(partRequest.getLocationName()), true, false);

        validateField(request, "活动场次预约须知[notes]不能为空", () -> Strings.isBlank(partRequest.getNotes()), true, true);

        validateField(request, "场次详细地址[addressDetail]不能为空", () -> Strings.isBlank(partRequest.getAddressDetail()), true, false);
        //校验可报名时间段必填
        if (partRequest.getSignStartTime() == null || partRequest.getSignEndTime() == null) {
        } else {
            if (partRequest.getSignStartTime().compareTo(partRequest.getSignEndTime()) > 0) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "报名开始时间不能大于报名结束时间");
            }
        }
        //场次时间
        if (partRequest.getStartTime() != null && partRequest.getEndTime() != null) {
            if (partRequest.getStartTime().compareTo(partRequest.getEndTime()) > 0) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "场次开始时间不能大于结束时间");
            }
        }
    }

    private void checkActivityPartName(ActivityCreateRequest request) {
        if (request.getActivityPart().stream().anyMatch(e -> StringUtils.isBlank(e.getName()))) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动场次名称不能为空");
        }
        List<String> partNameList = request.getActivityPart().stream().map(ActivityPartRequest::getName).collect(Collectors.toList());
        long distinctCount = partNameList.stream().distinct().count();
        if (partNameList.size() != distinctCount) {
            List<String> duplicateNames = partNameList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting())).entrySet().stream().filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
            String duplicateNamesStr = String.join(", ", duplicateNames);
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "存在重复的场次名称 [" + duplicateNamesStr + "]，请修改");
        }
    }


    private void activityFormCheck(ActivityCreateRequest request, boolean update) {
        checkNotNull(request, update);
        checkActivityRequire(request);
    }

    /**
     * 只有提交时才去校验活动名是否存在 ，否则全部保存
     *
     * @param request
     * @param update
     */
    private void checkActivityDuplicate(ActivityCreateRequest request, boolean update) {
        if (request.getPublishStatus().equals(ActivityPublishEnum.up.getStatus())) {
            ActivityInfoPO one = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getActivityName, request.getActivityName()).eq(ActivityInfoPO::getPublishStatus, ActivityPublishEnum.up.getStatus()));
            if (update) {
                if (Objects.nonNull(one) && !one.getId().equals(request.getId())) {
                    throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动名称[" + request.getActivityName() + "]已存在");
                }
            } else {
                if (Objects.nonNull(one)) {
                    throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动名称[" + request.getActivityName() + "]已存在");
                }
            }
        }
    }

    /**
     * 校验大活动必传字段
     *
     * @param request
     */
    private void checkActivityRequire(ActivityCreateRequest request) {
        //校验request.getCancelBeforeMinutes()
//        if (bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Objects.nonNull(request.getCancelBeforeMinutes()) && request.getCancelBeforeMinutes() < 0) {
//            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "提前多久可取消配置错误");
//        }
        validateField(request, "提前多久可取消配置错误", () -> Objects.nonNull(request.getCancelBeforeMinutes()) && request.getCancelBeforeMinutes() < 0, false, true);
        //校验只在”页面配置”的选项后需要必传的字段
        if (request.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
            validateField(request, "[活动主题首屏海报图片]必填", () -> Strings.isBlank(request.getPosterImageUrl()), true, true);
            validateField(request, "活动配置页面内容,[活动场次]必填", () -> CollectionUtils.isEmpty(request.getActivityPart()), true, false);
            validateField(request, "活动配置页面内容,[门票图片]必填", () -> Strings.isBlank(request.getTicketImageUrl()), true, true);
            validateField(request, "活动配置页面内容,[活动分享文案]必填", () -> Strings.isBlank(request.getShareText()), true, true);
            validateField(request, "[转发朋友圈字段]必填", () -> Strings.isBlank(request.getWxPhotoUrl()), true, true);
//            if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getPosterImageUrl())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "[活动主题首屏海报图片]必填");
//            }
//            if (request.getActivitySource() == null && CollectionUtils.isEmpty(request.getActivityPart())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动配置页面内容,[活动场次]必填");
//            }
//            if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getTicketImageUrl())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动配置页面内容,[门票图片]必填");
//            }
//            if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getShareText())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动配置页面内容,[活动分享文案]必填");
//            }
//            if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getWxPhotoUrl())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "[转发朋友圈字段]必填");
//            }
        } else {
            validateField(request, "跳转链接未配置", () -> Strings.isBlank(request.getLinkUrl()), true, true);
//            if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getLinkUrl())) {
//                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "跳转链接未配置");
//            }
            if (request.getLinkType().equals(ActivityLinkTypeEnum.OTHER_MINI.getValue())) {
                validateField(request, "appId未填写", () -> Strings.isBlank(request.getLinkAppId()), true, true);
//                if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getLinkAppId())) {
//                    throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "appId未填写");
//                }
            }
        }
        validateField(request, "活动封面未配置", () -> Strings.isBlank(request.getCoverImageUrl()), true, true);
//        if (request.getActivitySource() == null && bizConfig.skipCheckWhenDraft(request.getPublishStatus()) && Strings.isBlank(request.getCoverImageUrl())) {
//            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动封面未配置");
//        }
    }

    private void checkNotNull(ActivityCreateRequest request, boolean update) {
        if (request == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "参数异常");
        }
        String operatorId = request == null ? null : (request.getOperator() == null ? null : request.getOperator().getOperatorGuid());
        if (Strings.isBlank(operatorId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "操作用户信息获取失败:" + operatorId);
        }
        //校验活动名称，必填
        if (StringUtils.isBlank(request.getActivityName())) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "[活动名称]必填");
        }
        ActivityInfoPO one = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getActivityName, request.getActivityName()).eq(ActivityInfoPO::getDeleted, 0));
        if (update) {
            if (Objects.nonNull(one) && !one.getId().equals(request.getId())) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "[活动名称]已存在");
            }
        } else {
            if (Objects.nonNull(one)) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "活动名称:[" + one.getActivityName() + "]已存在");
            }
        }
        if (request.getPublishStatus() == null) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "发布状态必传");
        }
        if (request.getBrandType() == null) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "品牌类型必传");
        }
    }

    /**
     * 校验品牌、分类是否正确
     *
     * @param brandType
     * @param classId
     * @return
     */
    public boolean isValidBrandClass(Integer brandType, Integer classId) {
        for (ActivityEnum activity : ActivityEnum.values()) {
            if (activity.getValue().equals(brandType)) {
                for (BrandClassEnum brandClass : activity.getBrandClasses()) {
                    if (brandClass.getValue().equals(classId)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 活动场次精彩时刻上传
     *
     * @param request
     * @return
     */
    @Override
    public Boolean activityMomentImageUpload(ActivityMomentImageUploadRequest request) {
        if (Objects.isNull(request.getImageUrls())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "图片列表不能为空");
        }

        ActivityPartInfoPO activityPartInfoPO = activityPartInfoService.getById(request.getId());
        if (Objects.isNull(activityPartInfoPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "活动场次不存在");
        }

        activityPartInfoPO.setMomentImageUrl(JSONUtil.toJsonStr(request.getImageUrls()));
        return activityPartInfoService.updateById(activityPartInfoPO);
    }

    /**
     * 通过活动场次id查询活动精彩时刻图片列表
     *
     * @param id
     * @return
     */
    @Override
    public List<MomentImageVO> queryMomentImageListById(Long id) {
        ActivityPartInfoPO activityPartInfoPO = activityPartInfoService.getById(id);
        if (Objects.isNull(activityPartInfoPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "活动场次不存在");
        }

        List<MomentImageVO> momentImageVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(activityPartInfoPO.getMomentImageUrl())) {
            momentImageVOList = JSONUtil.toList(activityPartInfoPO.getMomentImageUrl(), MomentImageVO.class);
        }
        return momentImageVOList;
    }

    /**
     * 校验客户是否能报名某场次的活动
     *
     * @param partId
     * @param basicUid
     * @return
     */
    @Override
    public SignupConditionCheckVO checkSignupCondition(Long partId, Long basicUid) {
        ActivityPartInfoPO activityPartInfoPO = activityPartInfoService.getById(partId);
        if (Objects.isNull(activityPartInfoPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "活动场次不存在");
        }

        ActivityInfoPO activityInfoPO = getById(activityPartInfoPO.getActivityId());
        if (Objects.isNull(activityInfoPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "活动不存在");
        }

        SignupConditionCheckVO signupConditionCheckVO = new SignupConditionCheckVO();

        CustomerGenericActivityAppointmentPO signupInfo = customerGenericActivityAppointmentService.getSignupInfo(partId, basicUid);
        if (Objects.nonNull(signupInfo)) {
            // 参加过该活动
            signupConditionCheckVO.setJoinResult(false);
            signupConditionCheckVO.setJoinMsg("您已报名过该活动");
        } else {
            signupConditionCheckVO.setJoinResult(true);
        }

        Date now = DateUtil.date();
        if (now.before(activityPartInfoPO.getSignStartTime())) {
            // 活动报名未开始
            signupConditionCheckVO.setValidSignStartDateResult(false);
            signupConditionCheckVO.setValidSignStartDateMsg("活动报名未开始");
        } else {
            signupConditionCheckVO.setValidSignStartDateResult(true);
        }

        if (now.after(activityPartInfoPO.getSignEndTime())) {
            // 活动报名已结束
            signupConditionCheckVO.setValidSignEndDateResult(false);
            signupConditionCheckVO.setValidSignEndDateMsg("活动报名已结束");
        } else {
            signupConditionCheckVO.setValidSignEndDateResult(true);
        }

        if (now.after(activityPartInfoPO.getEndTime())) {
            // 活动已结束
            signupConditionCheckVO.setValidDateResult(false);
            signupConditionCheckVO.setValidDateMsg("活动已结束");
        } else {
            signupConditionCheckVO.setValidDateResult(true);
        }

        signupConditionCheckVO.setSignupUserNumResult(true);
        if (activityPartInfoPO.getMaxSignupNum() > 0) {
            // 活动报名人数上限
            Integer signupUserNum = customerGenericActivityAppointmentService.countSignupUserNum(partId);
            if (signupUserNum >= activityPartInfoPO.getMaxSignupNum()) {
                signupConditionCheckVO.setSignupUserNumResult(false);
                signupConditionCheckVO.setSignupUserNumMsg("该活动报名人数已满");
            }
        }

        // 等级条件是否达到
        String signupLevelId = activityPartInfoPO.getSignupLevelId();
        signupConditionCheckVO.setLevelResult(true);
        if (!StringUtils.isBlank(signupLevelId)) {
            List<Long> signupLevelConditionList = Arrays.stream(signupLevelId.split(",")).map(Long::valueOf).collect(Collectors.toList());

            // 获取客户当前品牌的等级
            CustomerAssetsPO customerAssetsPO = customerAssetsService.getGrowthAssetsByBasicIdAndAssetsType(basicUid, activityInfoPO.getBrandType());
            if (!signupLevelConditionList.contains(customerAssetsPO.getGrowthLevelId())) {
                signupConditionCheckVO.setLevelResult(false);

                List<String> levelNameList = new ArrayList<>();
                signupLevelConditionList.forEach(levelId -> {
                    BrandLevelEnum levelEnum = BrandLevelEnum.getLevelEnum(levelId.intValue());
                    if (Objects.nonNull(levelEnum)) {
                        levelNameList.add(levelEnum.getLevelName().replace("会员", ""));
                    }
                });

                String levelNameStr = String.join("、", levelNameList);
                signupConditionCheckVO.setLevelMsg("该活动只对" + levelNameStr + "会员开放");
            }
        }

        return signupConditionCheckVO;
    }

    /**
     * 通过活动状态查询活动列表
     *
     * @param publishStatus
     * @return
     */
    @Override
    public List<ActivityInfoPO> getActivityListByStatus(Integer publishStatus) {
        return list(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getPublishStatus, publishStatus));
    }

    @Override
    public ActivityReportVO report(Long activityPartId, Long activityId, String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || activityId == null) {
            return new ActivityReportVO();
        }
        List<String> dateList = formatTime(startDate, endDate);
        //累计报名人数 数据list，长度和dateList一样 ，默认值都是0
        List<Integer> allSignUpLineDataList = new ArrayList<>(Collections.nCopies(dateList.size(), 0));
        //累计拉新人数 数据list，长度和dateList一样 ，默认值都是0
        List<Integer> newUserLineDataList = new ArrayList<>(Collections.nCopies(dateList.size(), 0));

        // 查询活动基础信息
        ActivityInfoPO activityInfoPO = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getId, activityId));
        if (activityInfoPO == null) {
            return getActivityReportVO(dateList, allSignUpLineDataList, newUserLineDataList);
        }

        // 查询所有场次
        List<ActivityPartInfoPO> activityPartInfoPOS = activityPartInfoService.list(new LambdaQueryWrapper<ActivityPartInfoPO>().in(ActivityPartInfoPO::getActivityId, activityInfoPO.getId()));
        if (CollectionUtils.isEmpty(activityPartInfoPOS)) {
            getActivityReportVO(dateList, allSignUpLineDataList, newUserLineDataList);
        }
        for (ActivityPartInfoPO po : activityPartInfoPOS) {
            List<ActivityPartSignupDetailVO> detailList = null;
            try {
                detailList = appointmentBaseService.getActivityPartSignupDetailList(po.getId());
                if (CollectionUtils.isEmpty(detailList)) {
                    continue;
                }
                //聚合计算
                for (ActivityPartSignupDetailVO detail : detailList) {
                    //如果detail的signupTime 报名时间在日期列表中，则累加报名人数
                    String formatDate = DateUtils.format(detail.getSignupTime(), DateUtils.YYYY_MM_DD);
                    if (dateList.contains(formatDate)) {
                        //累计报名
                        int index = dateList.indexOf(formatDate);
                        allSignUpLineDataList.set(index, allSignUpLineDataList.get(index) + 1);
                        //累计拉新
                        if (detail.getNewUser()) {
                            newUserLineDataList.set(index, newUserLineDataList.get(index) + 1);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取报名详情失败", e);
            }

        }
        ActivityReportVO reportVO = getActivityReportVO(dateList, allSignUpLineDataList, newUserLineDataList);
        return reportVO;
    }

    private static @NotNull ActivityReportVO getActivityReportVO(List<String> dateList, List<Integer> allSignUpLineDataList, List<Integer> newUserLineDataList) {
        List<ActivityReportDataLineVO> lineList = new ArrayList<>();
        // 构造累计参与人数的图表数据线
        ActivityReportDataLineVO allSignUpLine = ActivityReportDataLineVO.builder().labels("累计参与人数").dateList(dateList).data(allSignUpLineDataList.stream().map(Objects::toString).collect(Collectors.toList())).build();

        // 构造累计拉新人数的图表数据线
        ActivityReportDataLineVO newUserLine = ActivityReportDataLineVO.builder().labels("累计拉新人数").dateList(dateList).data(newUserLineDataList.stream().map(Objects::toString).collect(Collectors.toList())).build();
        lineList.add(allSignUpLine);
        lineList.add(newUserLine);

        ActivityReportVO reportVO = new ActivityReportVO();
        reportVO.setDateList(dateList);
        reportVO.setLabels(Arrays.asList("累计参与人数", "累计拉新人数")); // 设置图表标签
        reportVO.setLineList(lineList);
        return reportVO;
    }

    /**
     * 参数日期格式化
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private static @NotNull List<String> formatTime(String startDate, String endDate) {
        String pStartDate = startDate;
        String pEndDate = endDate;
        if (startDate.split(" ").length > 0) {
            pStartDate = startDate.split(" ")[0];
        }
        if (endDate.split(" ").length > 0) {
            pEndDate = endDate.split(" ")[0];
        }
        //根据startDate和endDate,算出每一天的日期，组成list
        List<String> dateList = getDateList(pStartDate, pEndDate);
        log.info("用户运营2.0-report:startDate{}, endDate:{},dateList:{}", pStartDate, pEndDate, dateList);
        return dateList;
    }

    public static List<String> getDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        while (!start.isAfter(end)) {
            dateList.add(start.format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
            start = start.plusDays(1);
        }
        return dateList;
    }

    @Override
    public void jobChangeActivityNewStatus() {
        log.info("更新用户运营2.0活动状态-开始");
//        //场次状态
//        // 未开始
//        ActivityPartInfoPO noReadyPO = new ActivityPartInfoPO();
//        noReadyPO.setActivityStatus(ActivityStatusEnum.NO_READY.getValue());
//        activityPartInfoMapper.update(noReadyPO, new UpdateWrapper<ActivityPartInfoPO>().gt("start_time", LocalDateTime.now())
//                //.ne("activity_status", ActivityStatusEnum.NO_READY.getValue())
//        );
//
//        // 进行中
//        ActivityPartInfoPO doingPO = new ActivityPartInfoPO();
//        doingPO.setActivityStatus(ActivityStatusEnum.DOING.getValue());
//        activityPartInfoMapper.update(doingPO, new UpdateWrapper<ActivityPartInfoPO>().le("start_time", LocalDateTime.now()).ge("end_time", LocalDateTime.now())
//                //.ne("activity_status", ActivityStatusEnum.DOING.getValue())
//        );
//
//        // 已结束
//        ActivityPartInfoPO finishPO = new ActivityPartInfoPO();
//        finishPO.setActivityStatus(ActivityStatusEnum.FINISH.getValue());
//        activityPartInfoMapper.update(finishPO, new UpdateWrapper<ActivityPartInfoPO>().lt("end_time", LocalDateTime.now())
//                //.ne("activity_status", ActivityStatusEnum.FINISH.getValue())
//        );

        activityPartInfoMapper.jobUpdatePartStatus();
        log.info("更新用户运营2.0活动状态-更新场次状态(1/2)-结束");
        //更新大活动
        this.getBaseMapper().jobUpdateActivityStatusAndTime();
        log.info("更新用户运营2.0活动状态-更新大活动状态(2/2)-结束");
        log.info("更新用户运营2.0活动状态-结束");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void jobSyncActivity(String params) {
        String[] split = new String[]{};
        if (StringUtils.isNotBlank(params)) {
            split = params.split(",");
        }
        //有参数，处理参数
        if (split.length > 0) {
            for (String id : split) {
                processById(id);
            }
        } else {
            processBatch();
        }
    }

    @Override
    @Deprecated
    public void jobLevelsExpiredTips(String params) {
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotBlank(params)) {
            jsonObject = JSONUtil.parseObj(params);
            log.info("jobLevelsExpiredTips json:{}",jsonObject);
        }
        //校验时间
        if (checkTs(jsonObject)) return;
        //短信模板id,天数
        JobLevelExpiredReq req = new JobLevelExpiredReq();
        req.setDaysIn(jsonObject.getInt("daysIn"));
        //查出一批即将过期的数据
        List<JobLevelExpiredVO> levelExpiredVO = activityPartInfoMapper.jobLevelsExpiredTips(req);
        log.info("jobLevelsExpiredTips 即将过期的数据:{}",JSON.toJSONString(levelExpiredVO));
        sendMessage(jsonObject, levelExpiredVO);
    }

    private static boolean checkTs(JSONObject jsonObject) {
        String ts = jsonObject.getStr("ts");
        String nowStr=DateUtil.format(LocalDateTime.now(),"yyyyMMddHHmmss");
        if (StringUtils.isBlank(ts)) {
            return true;
        }
        if (!ts.equals(nowStr)) {
            log.info("jobLevelsExpiredTips 时间参数有误，当前时间:{},参数时间:{}", LocalDateTime.now(), ts);
            return true;
        }
        return false;
    }

    private void sendMessage(JSONObject jsonObject, List<JobLevelExpiredVO> levelExpiredVO) {
        JSONObject finalJsonObject = jsonObject;
        levelExpiredVO.forEach(item -> {
            //方便测试，如果传了basicId，则只发这一个用户
            if (finalJsonObject.containsKey("basicId") && !finalJsonObject.getLong("basicId").equals(item.getBasicId())) {
                return;
            }
            TabWechatUserPO wechatUserPO = tabWechatUserService.queryWechatInfoByBasicUid(item.getBasicId().intValue());
            HeUserBasicPO heUserBasicPO =null;
            try {
                heUserBasicPO = heUserBasicService.queryUserBasicInfoByPhone(wechatUserPO.getPurePhoneNumber());
                if (Objects.isNull(heUserBasicPO)) {
                    log.info("JobLevelExpired发送时未找到手机号对应的basicInfo, item:{},phone:{}", item.getId(),wechatUserPO.getPurePhoneNumber());
                }
            }catch (Exception e){
                log.error("JobLevelExpired发送时未找到手机号对应的basicInfo, item:{}",item.getId(),e);
                return;
            }
            HashSet<Integer> basicIdSet = new HashSet<>();
            basicIdSet.add(heUserBasicPO.getId().intValue());
            log.info("jobLevelsExpiredTips发送basicIdSet:{}", basicIdSet);
            Map<Integer, TabClientPO> clientInfoMap = tabClientService.getFirstClientMapByBasicId(basicIdSet);
            if (Objects.isNull(clientInfoMap) || clientInfoMap.isEmpty()) {
                log.info("JobLevelExpired发送时未找到手机号对应的客户信息, phone:{}", wechatUserPO.getPurePhoneNumber());
            }

            TabClientPO clientPO = clientInfoMap.get(heUserBasicPO.getId().intValue());
            SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
            //根据biz_type找对应的sceneid
            sceneTriggerReq.setSceneId(jsonObject.getLong("sms-sceneId-"+item.getBizType()));
            sceneTriggerReq.setTargetList(Collections.singletonList(clientPO.getId().toString()));
            //发短信
            Map<String, String> contextData = new HashMap<>();
            contextData.put("currentLevelName", item.getCurrent());
            contextData.put("day", item.getLeftDate() + "");
            contextData.put("afterLevelName", item.getNext());
            log.info("jobLevelsExpiredTips发送短信参数, item:{},request:{}",item.getId(), JSONUtil.toJsonStr(sceneTriggerReq));

            Integer test = jsonObject.getInt("test");
            log.info("jobLevelsExpiredTips发送, test:{}",test);
            if(test == null || test == 0){
                //test=1 不发短信
                try {
                    sceneTriggerReq.setContextData(contextData);
                    Result<Long> smsResult = messageCommandService.triggerScene(sceneTriggerReq);
                    log.info("jobLevelsExpiredTips发送短信, item:{},result:{}",item.getId(),  JSONUtil.toJsonStr(smsResult));
                }catch (Exception e){
                    log.error("jobLevelsExpiredTips发送短信失败, item:{},request:{}, error:{}", item.getId(),JSONUtil.toJsonStr(sceneTriggerReq), e);
                }

                //发公众号
                try {
                    Map<Integer, TabClientPO> tabClientPOMap = tabClientService.getFirstClientMapByBasicId(basicIdSet);
                    TabClientPO tabClientPO = tabClientPOMap.getOrDefault(item.getBasicId().intValue(), null);
                    if (ObjectUtil.isNotNull(tabClientPO)) {
                        log.info("jobLevelsExpiredTips发送公众号basicIdSet:{},tabClientPO.phone:{}", basicIdSet, tabClientPO.getPhone(),item.getBizType());
                        SceneTriggerReq req = new SceneTriggerReq();
                        req.setRequestId(IdWorker.get32UUID());
                        req.setTargetList(Collections.singletonList(tabClientPO.getId().toString()));
                        Integer storeType = 0;
                        if (ObjectUtil.isNotNull(tabClientPO)) {
                            EcpStorePO storePO = cfgStoreService.queryStoreByStoreId(tabClientPO.getStoreId());
                            if (ObjectUtil.isNotNull(storePO)) {
                                storeType = storePO.getType();
                            }
                        }
                        req.setSceneId(getWechatMessageScene(storeType));
                        Map<String, String> contextDate = new HashMap<>();
                        contextDate.put("userPhone", tabClientPO.getPhone());
                        req.setContextData(contextDate);
                        Result<Long> wxSendResult = messageCommandService.triggerScene(req);
                        log.info("jobLevelsExpiredTips开始发送微信公众号消息, request:{}, result:{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(wxSendResult));
                    }
                }catch (Exception e){
                    log.error("jobLevelsExpiredTips发送公众号失败, error:{}",  e);
                }
            }
        });
    }

    /**
     * 不同品牌模板id
     *
     * @param storeType
     * @return
     */
    private Long getWechatMessageScene(Integer storeType) {
        if (ObjectUtil.equals(storeType, StoreTypeEnum.BABY_BELLA.code())) {
            return 0L;
        } else if (ObjectUtil.equals(storeType, StoreTypeEnum.ISLA_BELLA.code())) {
            return 1L;
        }

        return 2L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void jobSyncActivityRecentyly(String params) {
        if (StringUtils.isNotBlank(params)) {
            int second = Integer.parseInt(params);
            List<TabActivityPO> list = tabActivityService.list(new LambdaQueryWrapper<TabActivityPO>().ne(TabActivityPO::getShowType, 100).ge(TabActivityPO::getUpdatedAt, LocalDateTime.now().minusSeconds(second + 10)));
            if (CollectionUtils.isNotEmpty(list)) {
                log.info("job同步ecp新活动-查找前{}秒的所有修改过的记录数-一共{}条", list.size(), params);
                processSyncList(list);
            }
        }
    }

    private void processBatch() {
        log.info("jobSyncActivity-批量分页-同步活动-开始");
        int count = tabActivityService.count(new LambdaQueryWrapper<>());
        log.info("jobSyncActivity-批量分页-同步活动-ecp活动总数:{}", count);
//        int count = 10;
        for (int i = 0; i < count; i += 10) {
            List<TabActivityPO> tabActivityPOS = tabActivityService.list(new LambdaQueryWrapper<TabActivityPO>().ne(TabActivityPO::getShowType, 100).last("limit 10 offset " + i));
            processSyncList(tabActivityPOS);
            log.info("jobSyncActivity-批量分页-同步活动-第[{}-{}]同步完成", tabActivityPOS.get(0).getId(), tabActivityPOS.get(tabActivityPOS.size() - 1).getId());
        }
        log.info("jobSyncActivity-批量分页-同步活动-结束");
    }

    private void processSyncList(List<TabActivityPO> tabActivityPOS) {
        if(tabActivityPOS.isEmpty()){
            return;
        }
        log.info("jobSyncActivity-批量分页-同步活动-正在同步id[{}-{}]", tabActivityPOS.get(0).getId(), tabActivityPOS.get(tabActivityPOS.size() - 1).getId());
        for (TabActivityPO po : tabActivityPOS) {
            //没有活动名称跳过
            if (Strings.isBlank(po.getName())) {
                log.info("jobSyncActivity-批量分页-同步活动-无活动名，跳过同步，id为{}", po.getId());
                continue;
            }
            ActivityInfoPO one = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getActivityName, po.getName()).eq(ActivityInfoPO::getDeleted, 0));
            ActivityCreateRequest activityCreateRequest = buildSyncActivityReq(po);
            buildPart(po, activityCreateRequest);
            try {
                if (Objects.nonNull(one)) {
                    //更新
                    activityCreateRequest.setId(one.getId());
                    update(activityCreateRequest);
                    log.info("jobSyncActivity-更新活动:活动名称:{},ecp id:{}", po.getName(), po.getId());
                } else {
                    activityCreateRequest.setBizId(po.getId());
                    save(activityCreateRequest);
                    log.info("jobSyncActivity-保存活动:活动名称:{},ecp id:{}", po.getName(), po.getId());
                }
            } catch (Exception e) {
                log.error("同步活动失败", e);
            }
        }
    }

    @Transactional
    public void processById(String id) {
        TabActivityPO tabActivityPO = tabActivityService.getById(Long.parseLong(id));
        if (tabActivityPO == null) {
            return;
        }
        ActivityInfoPO one = this.getOne(new LambdaQueryWrapper<ActivityInfoPO>().eq(ActivityInfoPO::getActivityName, tabActivityPO.getName()).eq(ActivityInfoPO::getDeleted, 0));
        ActivityCreateRequest request = buildSyncActivityReq(tabActivityPO);
        buildPart(tabActivityPO, request);
        try {
            if (Objects.nonNull(one)) {
                //更新
                request.setId(one.getId());
                updateActivity(request);
            } else {
                request.setBizId(tabActivityPO.getId());
                save(request);
            }
        } catch (Exception e) {
            log.error("同步活动失败", e);
        }
    }

    private void buildPart(TabActivityPO tabActivityPO, ActivityCreateRequest request) {
        if (request.getLinkType().equals(ActivityLinkTypeEnum.PAGE.getValue())) {
            List<ActivityPartRequest> activityPart = new ArrayList<>();
            ActivityPartRequest activityPartRequest = new ActivityPartRequest();
            activityPartRequest.setName(request.getActivityName());
            //详细地址是活动门店地址
            EcpStorePO storePO = cfgStoreService.queryStoreByStoreId(tabActivityPO.getStoreId());
            if (ObjectUtil.isNotNull(storePO)) {
                //todo 城市名+场
                ActivityAddressInfoVO activityAddressInfo = heAddressMapper.selectProvincesCitiesDistrictsById(Long.valueOf(storePO.getRegionCode()));
                if (activityAddressInfo != null) {
                    activityPartRequest.setCityName(activityAddressInfo.getCityName());
                }
                activityPartRequest.setAddressDetail(storePO.getAddress());
                activityPartRequest.setStoreId(storePO.getStoreId() + "");
                activityPartRequest.setLat(storePO.getLat());
                activityPartRequest.setLng(storePO.getLng());
                activityPartRequest.setCity(Long.valueOf(storePO.getCityCode()));
                activityPartRequest.setProvince(Long.valueOf(storePO.getProvinceCode()));
                activityPartRequest.setLocationName(storePO.getAddress());
                activityPartRequest.setRegion(storePO.getRegionCode() == null ? null : Integer.parseInt(storePO.getRegionCode()));
                activityPartRequest.setOrganizerId(null);
                activityPartRequest.setOrganizerName("");
                activityPartRequest.setStartTime(new Date(tabActivityPO.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                activityPartRequest.setEndTime(new Date(tabActivityPO.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                activityPartRequest.setSignupLevelId(null);
                activityPartRequest.setMaxSignupNum(999999);
                activityPartRequest.setDescription(tabActivityPO.getDesc());
                activityPartRequest.setNotes("");
                activityPartRequest.setPosterImageUrl("");
                activityPartRequest.setSecondaryImageUrl("");
                activityPartRequest.setSignupFields(null);
                //todo deleted
                activityPart.add(activityPartRequest);
                request.setActivityPart(activityPart);
            }
        }
    }

    /**
     * 参数构建
     *
     * @param tabActivityPO
     * @return
     */
    private @NotNull ActivityCreateRequest buildSyncActivityReq(TabActivityPO tabActivityPO) {
        ActivityCreateRequest request = new ActivityCreateRequest();
        Operator operator = new Operator();
        operator.setOperatorGuid(Integer.toString(999));
        operator.setOperatorName("张心路");
        operator.setOperatorPhone("***********");
        request.setOperator(operator);
        //活动名称
        request.setActivityName(tabActivityPO.getName());
        Integer brandType = brandStrategyService.processBrandType(tabActivityPO.getShowType(), BrandTypeBusinessTypeEnum.SYNC_ACTIVITY_FROM_ECP);
        request.setBrandType(brandType);
        setCategory(tabActivityPO, request);
        request.setBrandLogo("");
        request.setCoverImageUrl(StringUtils.isBlank(tabActivityPO.getCoverUrl()) ? "" : "https://cos.primecare.top/" + tabActivityPO.getCoverUrl());
        //同步linkType
        request.setLinkType(mapLinkType(tabActivityPO.getRedirectType()));
        request.setLinkUrl(tabActivityPO.getRedirectUrl());
        request.setLinkAppId(null);
        request.setTicketImageUrl("");
        request.setWxPhotoUrl("");
        request.setCancelBeforeMinutes(0);
        request.setShareText("");
        request.setPosterImageUrl("");
        request.setPublishStatus(ActivityPublishEnum.up.getStatus());
        request.setStartTime(new Date(tabActivityPO.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        request.setEndTime(new Date(tabActivityPO.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        request.setActivitySource(1);
        request.setDeleted(tabActivityPO.getDeletedAt()!=null?1:0);
        return request;
    }

    private static void setCategory(TabActivityPO tabActivityPO, ActivityCreateRequest request) {
        try {
            Integer value = BrandClassEnum.getClassNameByValue(tabActivityPO.getShowType(), tabActivityPO.getClassId()).getValue();
            if (value != null) {
                request.setCategory(value);
            }
        } catch (Exception e) {
            request.setCategory(null);
        }
    }

    private Integer mapLinkType(Integer redirectType) {
        switch (redirectType) {
            case 0:
                return ActivityLinkTypeEnum.PAGE.getValue();
            case 1:
                return ActivityLinkTypeEnum.H5.getValue();
            case 2:
                return ActivityLinkTypeEnum.H5.getValue();
        }
        return null;
    }

    @Override
    public void updateActiveReadRecordIfPageFirst(Integer brandType, Long basicUid, Integer classId, Long cityId, Integer pageNum, Integer status, Date createTime) {
        log.info("new-判断是否满足写入已读标识的权限:basicUid:{},classId:{},city:{},pageNum:{},status:{},createTime:{}", basicUid, classId, cityId, pageNum, status, createTime);
        if (basicUid == null) {
            log.info("未获取到用户登录:brandType:{}:basicUid:{}", brandType, basicUid);
            return;
        }
        if (brandType == null) {
            log.info("未获取到brandType:{}:basicUid:{}", brandType, basicUid);
            return;
        }
        if (cityId != null && cityId.equals(-1L) && classId != null && classId.equals(0) && pageNum.equals(1) && ((Objects.isNull(status) || status.equals(-1)))) {
            //为了获取创建时间最新的那一条数据
            if (createTime != null) {
                updateActiveReadRecord(brandType, basicUid + "", createTime);
            }
        }
    }

    /**
     * 更新用户访问list接口请求的时间点
     * 用于已读逻辑
     *
     * @param brandType
     * @param startTime
     * @description: 更新用户访问list接口请求的时间点
     * @return: void
     * @date: 2024/1/24 13:29
     * auther: zhangxinlu
     */
    public void updateActiveReadRecord(Integer brandType, String basicUid, Date startTime) {
        if (brandType == null) {
            return;
        }
        try {
            if (StringUtil.isEmpty(basicUid)) {
                return;
            }
            TabActivityReadRecordPO one = tabActivityReadRecordService.getOne(new LambdaQueryWrapper<TabActivityReadRecordPO>().eq(TabActivityReadRecordPO::getUserId, basicUid).eq(TabActivityReadRecordPO::getBrandType, brandType));
            if (one != null) {
                one.setUserId(basicUid);
                one.setRequestAt(LocalDateTime.now());
                one.setLastestAt(startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                tabActivityReadRecordService.update(new LambdaUpdateWrapper<TabActivityReadRecordPO>().eq(TabActivityReadRecordPO::getUserId, basicUid).eq(TabActivityReadRecordPO::getBrandType, brandType).eq(TabActivityReadRecordPO::getId, one.getId()).set(TabActivityReadRecordPO::getRequestAt, LocalDateTime.now()).set(TabActivityReadRecordPO::getLastestAt, startTime));
            } else {
                //新增
                TabActivityReadRecordPO updateRecord = new TabActivityReadRecordPO();
                updateRecord.setUserId(basicUid);
                updateRecord.setCreatedAt(LocalDateTime.now());
                updateRecord.setRequestAt(LocalDateTime.now());
                updateRecord.setLastestAt(startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                updateRecord.setBrandType(brandType);
                tabActivityReadRecordService.save(updateRecord);
            }
        } catch (Exception e) {
            log.error("设置用户list页面访问时间出错", e);
        }
    }

    @Override
    public Integer unread(String userId, Integer brandType) {
        if (StringUtil.isEmpty(userId) || brandType == null) {
            return 0;
        }
        //如果tab_activity_read_record表没记录 ，则直接返回 0 ：没有新活动
        TabActivityReadRecordPO readRecordOne = tabActivityReadRecordService.getOne(new LambdaQueryWrapper<TabActivityReadRecordPO>().eq(TabActivityReadRecordPO::getUserId, userId).eq(TabActivityReadRecordPO::getBrandType, brandType));
        if (readRecordOne == null || readRecordOne.getUserId() == null || readRecordOne.getRequestAt() == null) {
            return 1;
        }
        //判断最新的活动创建时间
        ActivityListMiniVO lastestActivity = baseMapper.getLastestActivity(brandType);
        if (lastestActivity != null) {
            Date readRecordOneDate = new Date(readRecordOne.getLastestAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            log.info("小红点-用户:{},品牌:{},最后访问时间:{},最新活动创建时间:{},结果:{}", userId, brandType, readRecordOne.getLastestAt(), lastestActivity.getGmtCreate(), lastestActivity.getGmtCreate().compareTo(readRecordOneDate));
            //如果 活动在访问之后有新增,返回1
            int compare = lastestActivity.getGmtCreate().compareTo(readRecordOneDate);
            if (compare > 0) {
                return 1;
            }
        }
        return 0;
    }
}
