package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.service.RoomStateCheckInInfoService;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.client.CareClient;
import com.stbella.customer.server.client.MessageClient;
import com.stbella.customer.server.client.OrderClient;
import com.stbella.customer.server.cts.enums.KnowledgePushTypeEnum;
import com.stbella.customer.server.customer.dto.KnowledgeBasePushRecordStatisticsListDTO;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBaseLikeRecordPO;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBasePO;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBasePushRecordPO;
import com.stbella.customer.server.customer.entity.CustomerKnowledgeBaseViewRecordPO;
import com.stbella.customer.server.customer.enums.OrderTypeEnum;
import com.stbella.customer.server.customer.mapper.CustomerKnowledgeBasePushRecordMapper;
import com.stbella.customer.server.customer.request.CustomerWechatFansListRequest;
import com.stbella.customer.server.customer.request.DeletePushKnowledgeRequest;
import com.stbella.customer.server.customer.request.knowledgeBase.*;
import com.stbella.customer.server.customer.dto.knowledgeBaseArticlesSendDTO;
import com.stbella.customer.server.customer.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.customer.server.customer.vo.*;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.customer.server.util.JsoupUtil;
import com.stbella.redis.service.RedisService;
import com.stbella.redisson.DistributedLocker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库推送记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-12-12
 */
@Slf4j
@Service
@DubboService
public class CustomerKnowledgeBasePushRecordServiceImpl extends ServiceImpl<CustomerKnowledgeBasePushRecordMapper, CustomerKnowledgeBasePushRecordPO> implements CustomerKnowledgeBasePushRecordService {
    
    @Resource
    private CfgStoreService cfgStoreService;

    @Resource
    private HeOrderService heOrderService;

    @Resource
    private TabWechatUserService tabWechatUserService;

    @Resource
    private TabClientService tabClientService;

    @Resource
    private CustomerKnowledgeBaseService customerKnowledgeBaseService;//知识库文章信息

    @Resource
    private CustomerKnowledgeBaseLikeRecordService customerKnowledgeBaseLikeRecordService;//知识库用户点赞信息

    @Resource
    private CustomerKnowledgeBaseViewRecordService customerKnowledgeBaseViewRecordService;

    @Resource
    private DistributedLocker redisson;

    @DubboReference
    private RoomStateCheckInInfoService roomStateCheckInInfoService;

    @Resource
    private MessageClient messageClient;

    @Resource
    private CustomerWechatFansService customerWechatFansService;

    @Resource
    private RedisService redisService;

    @Resource
    private CareClient careClient;

    @Resource
    private OrderClient orderClient;

    @Resource
    private CustomerKnowledgeBasePushRecordService customerKnowledgeBasePushRecordService;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;


    /**
     * 查询可推送的文章列表
     *
     * @param request request
     * @return list<all knowledge base essay vo>
     * <AUTHOR>
     * @date 2023/12/19 10:56:45
     * @since 1.0.0
     */
    @Override
    public List<AllKnowledgeBaseEssayVO> queryAllKnowledgeBaseArticles(CanPushArticlesListRequest request) {

        HeOrderPO order = heOrderService.getByOrderSn(request.getOrderNo());

        if (Objects.isNull(order)) {
            log.info("查询可推送的文章列表，未找到订单信息，orderSn：" + request.getOrderNo());
            return new ArrayList<>();
        }

        EcpStorePO storePO = cfgStoreService.queryStoreByStoreId(order.getStoreId());

        if (Objects.isNull(storePO)) {
            log.info("查询可推送的文章列表，未找到门店信息，storeId：" + order.getStoreId());
            return new ArrayList<>();
        }

        // 小月子订单 判断是否超过14 周
        Map<String, Integer> gestationWeekTypeMap = careClient.getGestationWeekType(Lists.newArrayList(request.getOrderNo()));
        Integer gestationWeekType = gestationWeekTypeMap.get(request.getOrderNo());

        LambdaQueryWrapper<CustomerKnowledgeBasePO> queryWrapper = new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                .eq(CustomerKnowledgeBasePO::getKnowledgeType, request.getType())
                .eq(Objects.nonNull(request.getSubtypes()), CustomerKnowledgeBasePO::getSubtypes, request.getSubtypes());
        if (null != gestationWeekType) {
            queryWrapper.eq(CustomerKnowledgeBasePO::getSmallMonthGestationWeekType, gestationWeekType);
        } else {
            queryWrapper.isNull(CustomerKnowledgeBasePO::getSmallMonthGestationWeekType);
        }
        List<CustomerKnowledgeBasePO> knowledgeBaseList = customerKnowledgeBaseService.list(queryWrapper);
        if (CollectionUtil.isEmpty(knowledgeBaseList)) {
            return new ArrayList<>();
        }

        List<CustomerKnowledgeBasePushRecordPO> pushRecordPOList = list(new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, request.getOrderNo())
                .eq(CustomerKnowledgeBasePushRecordPO::getDeleted, 0));

        List<AllKnowledgeBaseEssayVO> voList = new ArrayList<>();
        //查主类型的所有信息，前端自己根据返回的子类型作区分
        if (CollectionUtil.isNotEmpty(pushRecordPOList)) {
            knowledgeBaseList.forEach(item -> {
                Optional<CustomerKnowledgeBasePushRecordPO> record = pushRecordPOList.stream()
                        .filter(r -> Objects.equals(item.getId(), r.getKnowledgeBaseId())).findFirst();

                AllKnowledgeBaseEssayVO essayVO = new AllKnowledgeBaseEssayVO();
                essayVO.setId(item.getId());
                essayVO.setTitle(item.getTitle());
                essayVO.setSubtypes(item.getSubtypes());
                essayVO.setSort(item.getSort());
                //有记录 不管成功或失败 状态都为1 ，这个地方只是区分是否有推送记录
                if (record.isPresent()) {
                    essayVO.setPushStatus(1);
                } else {
                    essayVO.setPushStatus(0);
                }
                voList.add(essayVO);
            });
        } else {
            knowledgeBaseList.forEach(item -> {
                AllKnowledgeBaseEssayVO essayVO = new AllKnowledgeBaseEssayVO();
                essayVO.setId(item.getId());
                essayVO.setTitle(item.getTitle());
                essayVO.setSubtypes(item.getSubtypes());
                essayVO.setPushStatus(0);
                essayVO.setSort(item.getSort());
                voList.add(essayVO);
            });
        }
        //排序
        if (CollectionUtil.isNotEmpty(voList)) {
            return voList.stream()
                    .sorted(Comparator.comparing(AllKnowledgeBaseEssayVO::getSort).reversed())
                    .collect(Collectors.toList());
        }
        return voList;
    }

    /**
     * 搜索条件信息  门店，推荐人，文章
     *
     * @return query criteria vo
     * <AUTHOR>
     * @date 2023/12/14 03:03:35
     * @since 1.0.0
     */
    @Override
    public QueryCriteriaVO queryCriteriaInfo() {
        List<CustomerKnowledgeBasePushRecordPO> pushRecordPOList = list(new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .eq(CustomerKnowledgeBasePushRecordPO::getDeleted, 0));

        QueryCriteriaVO criteriaVO = new QueryCriteriaVO();
        List<QueryCriteriaVO.CriteriaInfo> storeInfoList = new ArrayList<>();
        List<QueryCriteriaVO.CriteriaInfo> pushUserInfoList = new ArrayList<>();
        List<QueryCriteriaVO.CriteriaInfo> articleInfoList = new ArrayList<>();

        pushRecordPOList.forEach(item -> {
            QueryCriteriaVO.CriteriaInfo storeInfo = new QueryCriteriaVO.CriteriaInfo();
            storeInfo.setId(item.getStoreId().longValue());
            storeInfo.setName(item.getStoreName());
            storeInfoList.add(storeInfo);

            QueryCriteriaVO.CriteriaInfo pushUserInfo = new QueryCriteriaVO.CriteriaInfo();
            pushUserInfo.setId(item.getPushUserId());
            pushUserInfo.setName(item.getPushUserName());
            pushUserInfoList.add(pushUserInfo);

            QueryCriteriaVO.CriteriaInfo articleInfo = new QueryCriteriaVO.CriteriaInfo();
            articleInfo.setId(item.getKnowledgeBaseId());
            articleInfo.setName(item.getArticleTitle());
            articleInfoList.add(articleInfo);
        });
        //去重
        List<QueryCriteriaVO.CriteriaInfo> store = storeInfoList.stream().distinct().collect(Collectors.toList());
        List<QueryCriteriaVO.CriteriaInfo> pushUser = pushUserInfoList.stream().distinct().collect(Collectors.toList());
        List<QueryCriteriaVO.CriteriaInfo> article = articleInfoList.stream().distinct().collect(Collectors.toList());

        criteriaVO.setStoreInfo(store);
        criteriaVO.setPushUserInfo(pushUser);
        criteriaVO.setArticleInfo(article);
        return criteriaVO;
    }

    /**
     * 根据查询条件，查询知识库文章推送记录
     *
     * @param request request
     * @return page vo< knowledge base push record list vo>
     * <AUTHOR>
     * @date 2023/12/12 05:59:27
     * @since 1.0.0
     */
    @Override
    public PageVO<KnowledgeBasePushRecordListVO> queryKnowledgeBasePushRecordByQueryCriteriaInfo(KnowledgeBasePushRecordListRequest request) {
        //判断手机号是否纯数字
        if (ObjectUtil.isNotEmpty(request.getPhone()) && !StringUtils.isNumeric(request.getPhone())) {
            throw new BusinessException("手机号不符合格式，phone:" + request.getPhone());
        }


        List<KnowledgeBasePushRecordStatisticsListDTO> dtoList = baseMapper.queryKnowledgeBasePushRecordStatisticsList(request);
        if (CollectionUtil.isEmpty(dtoList)) {
            return new PageVO<>(new ArrayList<>(), 0, request.getPageNum(), request.getPageSize());
        }

        Map<String, KnowledgeBasePushRecordStatisticsListDTO> numMap = new HashMap<>();
        dtoList.forEach(item -> numMap.put(item.getOrderNo() + "_" + item.getPushState(), item));
        List<String> orderNos = dtoList.stream().map(KnowledgeBasePushRecordStatisticsListDTO::getOrderNo)
                .distinct().collect(Collectors.toList());
        List<RoomStateCheckInInfoPO> roomInfoList = roomStateCheckInInfoService.getCheckedInRoomInfoByOrderNos(orderNos);

        List<KnowledgeBasePushRecordListVO> voList = dtoList.stream().map(item -> {
            KnowledgeBasePushRecordListVO vo = new KnowledgeBasePushRecordListVO();
            vo.setClientName(item.getClientName());
            vo.setMobile(item.getMobile());
            vo.setOrderNo(item.getOrderNo());
            vo.setStoreId(item.getStoreId());
            vo.setStoreName(item.getStoreName());
            getPushRecordInfo(item, vo, numMap);


            if (CollectionUtil.isNotEmpty(roomInfoList)) {
                Optional<RoomStateCheckInInfoPO> roomInfo = roomInfoList.stream()
                        .filter(room -> Objects.equals(item.getOrderNo(), room.getOrderNo()))
                        .findFirst();
                if (roomInfo.isPresent()) {
                    RoomStateCheckInInfoPO room = roomInfo.get();
                    vo.setCheckInTime(ObjectUtil.isNotEmpty(room.getCheckInDate()) ? DateUtils.format(DateUtils.toDate(room.getCheckInDate()), DateUtils.YYYY_MM_DD_HH_MM_SS) : "");
                    vo.setCheckOutTime(ObjectUtil.isNotEmpty(room.getCheckOutDate()) ? DateUtils.format(DateUtils.toDate(room.getCheckOutDate()), DateUtils.YYYY_MM_DD_HH_MM_SS) : "");
                } else {
                    vo.setCheckInTime("");
                    vo.setCheckOutTime("");
                }
            } else {
                vo.setCheckInTime("");
                vo.setCheckOutTime("");
            }
            return vo;
        }).distinct().collect(Collectors.toList());

        if (CollectionUtil.isEmpty(voList)) {
            return new PageVO<>(voList, 0, request.getPageSize(), request.getPageNum());
        }
        List<KnowledgeBasePushRecordListVO> tempList = voList.stream()
                //按照门店id大小 从小到大排序，同一门店按照订单第一次推送的时间，新推送的排前面
                .sorted(Comparator.comparing(KnowledgeBasePushRecordListVO::getStoreId)
                        .thenComparing(Comparator.comparing(KnowledgeBasePushRecordListVO::getPushTime).reversed()))
                .skip((long) (request.getPageNum() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .collect(Collectors.toList());

        return new PageVO<>(tempList, voList.size(), request.getPageSize(), request.getPageNum());
    }


    private KnowledgeBasePushRecordListVO getPushRecordInfo(KnowledgeBasePushRecordStatisticsListDTO item, KnowledgeBasePushRecordListVO vo, Map<String, KnowledgeBasePushRecordStatisticsListDTO> numMap) {

        KnowledgeBasePushRecordStatisticsListDTO dto = new KnowledgeBasePushRecordStatisticsListDTO();
        Date pushTime = null;
        if (numMap.containsKey(item.getOrderNo() + "_1")) {
            dto = numMap.get(item.getOrderNo() + "_1");
            vo.setPushSuccessNum(dto.getNum());

            if (ObjectUtil.isNotEmpty(dto.getIdList())) {
                List<Long> idList = Arrays.stream(dto.getIdList().split(",")).map(Long::parseLong).collect(Collectors.toList());
                vo.setPushSuccessIdList(idList);
            }
            pushTime = dto.getPushTime();
        } else {
            vo.setPushSuccessNum(0);
        }

        if (numMap.containsKey(item.getOrderNo() + "_0")) {
            dto = numMap.get(item.getOrderNo() + "_0");
            if (ObjectUtil.isNotEmpty(dto.getIdList())) {
                List<Long> idList = Arrays.stream(dto.getIdList().split(",")).map(Long::parseLong).collect(Collectors.toList());
                vo.setPushFailIdList(idList);
            }
            vo.setPushFailNum(dto.getNum());
            if (Objects.nonNull(pushTime)) {
                //事件在
                pushTime = pushTime.before(dto.getPushTime()) ? pushTime : dto.getPushTime();
            } else {
                pushTime = dto.getPushTime();
            }
        } else {
            vo.setPushFailNum(0);
        }
        vo.setPushTime(Objects.nonNull(pushTime) ? DateUtils.format(pushTime, DateUtils.YYYY_MM_DD_HH_MM_SS) : "");
        return vo;
    }

    /**
     * 根据订单号和文章类型 查询推送记录
     *
     * @param request request
     * @return page vo< knowledge base push record list vo>
     * <AUTHOR>
     * @date 2023/12/12 08:10:09
     * @since 1.0.0
     */
    @Override
    public PageVO<KnowledgeBasePushRecordDetailVO> queryKnowledgeBasePushRecordByOrderSn(KnowledgeBasePushRecordDetailRequest request) {

        if (CollectionUtil.isEmpty(request.getIdList())) {
            return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
        }

        IPage<CustomerKnowledgeBasePushRecordPO> pushRecordList = page(
                new Page<>(request.getPageNum(), request.getPageSize())
                , new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                        .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, request.getOrderNo())
                        .eq(Objects.nonNull(request.getPushStatus()), CustomerKnowledgeBasePushRecordPO::getPushState, request.getPushStatus())
                        .in(CustomerKnowledgeBasePushRecordPO::getKnowledgeBaseId, request.getIdList())
                        .orderByDesc(CustomerKnowledgeBasePushRecordPO::getPushTime));

        if (CollectionUtil.isEmpty(pushRecordList.getRecords())) {
            log.info("未找到相关订单的知识库文章推送记录，orderNo:" + request.getOrderNo() + "，type" + request.getType());
            return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
        }

        List<CustomerKnowledgeBasePO> knowledgeBaseList = customerKnowledgeBaseService.list(new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                .eq(CustomerKnowledgeBasePO::getKnowledgeType, request.getType())
                .in(CustomerKnowledgeBasePO::getId, request.getIdList()));

        if (CollectionUtil.isEmpty(knowledgeBaseList)) {
            log.info("未找到相关类型的知识库文章，type:" + request.getType());
            return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
        }

        List<KnowledgeBasePushRecordDetailVO> voList = new ArrayList<>();
        pushRecordList.getRecords().forEach(item -> {
            Optional<CustomerKnowledgeBasePO> baseOptional = knowledgeBaseList.stream()
                    .filter(r -> Objects.equals(r.getId(), item.getKnowledgeBaseId())).findFirst();
            if (baseOptional.isPresent()) {
                CustomerKnowledgeBasePO base = baseOptional.get();
                KnowledgeBasePushRecordDetailVO vo = new KnowledgeBasePushRecordDetailVO();
                vo.setSort(base.getSort());
                vo.setTitle(item.getArticleTitle());
                vo.setId(item.getId());
                vo.setPushUserName(item.getPushUserName());
                vo.setPushTime(DateUtils.format(item.getPushTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                vo.setPushStatus(item.getPushState());
                vo.setErrorMsg(Objects.equals(item.getPushState(), 0) ? item.getErrorMsg() : "");
                voList.add(vo);
            }
        });

        if (CollectionUtil.isNotEmpty(voList)) {
            List<KnowledgeBasePushRecordDetailVO> tempList = voList.stream()
                    .sorted(Comparator.comparing(KnowledgeBasePushRecordDetailVO::getPushTime).reversed()
                            .thenComparing(Comparator.comparing(KnowledgeBasePushRecordDetailVO::getSort).reversed()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(tempList)) {
                return new PageVO<>(tempList, (int) pushRecordList.getTotal(), request.getPageSize(), request.getPageNum());
            }
        }
        return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * query record
     *
     * @param request request
     * @return page vo< knowledge base push record detail vo>
     * <AUTHOR>
     * @date 2023/12/22 06:57:29
     * @since 1.0.0
     */
    @Override
    public PageVO<KnowledgeBasePushRecordDetailVO> queryRecord(QueryRecordRequest request) {

        IPage<CustomerKnowledgeBasePushRecordPO> pushRecordList = page(
                new Page<>(request.getPageNum(), request.getPageSize())
                , new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                        .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, request.getOrderNo())
                        .orderByDesc(CustomerKnowledgeBasePushRecordPO::getPushTime));

        if (CollectionUtil.isEmpty(pushRecordList.getRecords())) {
            log.info("未找到相关订单的知识库文章推送记录，orderNo:" + request.getOrderNo() + "，type" + request.getType());
            return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
        }

        List<CustomerKnowledgeBasePO> knowledgeBaseList = customerKnowledgeBaseService.list(new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                .eq(CustomerKnowledgeBasePO::getKnowledgeType, request.getType()));

        if (CollectionUtil.isEmpty(knowledgeBaseList)) {
            log.info("未找到相关类型的知识库文章，type:" + request.getType());
            return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
        }

        List<KnowledgeBasePushRecordDetailVO> voList = new ArrayList<>();
        pushRecordList.getRecords().forEach(item -> {
            Optional<CustomerKnowledgeBasePO> baseOptional = knowledgeBaseList.stream()
                    .filter(r -> Objects.equals(r.getId(), item.getKnowledgeBaseId())).findFirst();
            if (baseOptional.isPresent()) {
                CustomerKnowledgeBasePO base = baseOptional.get();
                KnowledgeBasePushRecordDetailVO vo = new KnowledgeBasePushRecordDetailVO();
                vo.setSort(base.getSort());
                vo.setTitle(item.getArticleTitle());
                vo.setId(item.getId());
                vo.setPushUserName(item.getPushUserName());
                vo.setPushTime(DateUtils.format(item.getPushTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                vo.setPushStatus(item.getPushState());
                vo.setErrorMsg(Objects.equals(item.getPushState(), 0) ? item.getErrorMsg() : "");
                voList.add(vo);
            }
        });

        if (CollectionUtil.isNotEmpty(voList)) {
            List<KnowledgeBasePushRecordDetailVO> tempList = voList.stream()
                    .sorted(Comparator.comparing(KnowledgeBasePushRecordDetailVO::getPushTime).reversed()
                            .thenComparing(Comparator.comparing(KnowledgeBasePushRecordDetailVO::getSort).reversed()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(tempList)) {
                return new PageVO<>(tempList, (int) pushRecordList.getTotal(), request.getPageSize(), request.getPageNum());
            }
        }
        return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * 推送文章
     *
     * @param headerValue header value
     * @param request     request
     * @return boolean
     * <AUTHOR>
     * @date 2023/12/28 03:08:49
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pushWxMsgAndCreatePushRecord(String headerValue, KnowledgeBasePushRecordCreateRequest request) {
        try {
            headerValue = URLDecoder.decode(headerValue, String.valueOf(StandardCharsets.UTF_8));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        JSONObject userMap = JSONUtil.parseObj(headerValue);
        Long pushUserId = userMap.getLong("userId");
        String pushUserName = userMap.getStr("userName");

        String orderNo = request.getOrderNo();

        List<CustomerKnowledgeBasePO> customerKnowledgeBasePOList = new ArrayList<>();

        List<KnowledgeBasePushRecordCreateRequest.KnowledgeBaseInfo> knowledgeBaseInfoList = request.getKnowledgeBaseInfoList();
        knowledgeBaseInfoList.forEach(item -> {
            CustomerKnowledgeBasePO customerKnowledgeBasePO = new CustomerKnowledgeBasePO();
            customerKnowledgeBasePO.setId(item.getKnowledgeBaseId());
            customerKnowledgeBasePO.setTitle(item.getTitle());

            customerKnowledgeBasePOList.add(customerKnowledgeBasePO);
        });

        return pushKnowledge(request.getOrderNo(), customerKnowledgeBasePOList, KnowledgePushTypeEnum.MANUAL, pushUserId, pushUserName);

//        HeOrderPO order = heOrderService.getByOrderSn(orderNo);
//        if (Objects.isNull(order)) {
//            throw new BusinessException("推送文章消息时，未找到订单信息，orderNo:" + orderNo);
//        }
//
//        EcpStorePO ecpStorePO = cfgStoreService.queryStoreByStoreId(order.getStoreId());
//        if (Objects.isNull(ecpStorePO)) {
//            throw new BusinessException("推送文章消息时，未找到门店信息，StoreId:" + order.getStoreId());
//        }
//
//        TabClientPO clientPO = tabClientService.getById(order.getClientUid());
//        if (Objects.isNull(clientPO)) {
//            throw new BusinessException("推送文章消息时，未找到用户信息，client:" + order.getClientUid());
//        }
//
//        TabWechatUserPO wechatUserPO = tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
//            .eq(TabWechatUserPO::getBasicUid, clientPO.getBasicUid())
//            .eq(TabWechatUserPO::getFromType, ecpStorePO.getType() + 1)
//            .orderByDesc(TabWechatUserPO::getCreatedAt)
//            .last("limit 1"));
//
//        List<CustomerWechatFansListVO> fans;
//        if (Objects.nonNull(wechatUserPO)) {
//            CustomerWechatFansListRequest fansListRequest = new CustomerWechatFansListRequest();
//            fansListRequest.setUnionId(wechatUserPO.getUnionid());
//            //来源1小贝拉 2圣贝拉，  门店类型 0圣贝拉 1小贝拉
//            fansListRequest.setSource(ecpStorePO.getType() == 0 ? 2 : 1);
//            fans = customerWechatFansService.queryWechatFans(fansListRequest);
//        } else {
//            fans = new ArrayList<>();
//        }
//
//
//        List<CustomerKnowledgeBasePushRecordPO> recordList = new ArrayList<>();
//        knowledgeBaseInfoList.forEach(item -> {
//            CustomerKnowledgeBasePushRecordPO record = new CustomerKnowledgeBasePushRecordPO();
//            record.setPushType(2);//默认人工
//            record.setOrderNo(orderNo);
//            record.setKnowledgeBaseId(item.getKnowledgeBaseId());
//            record.setArticleTitle(item.getTitle());
//            record.setStoreId(ecpStorePO.getStoreId());
//            record.setStoreName(ecpStorePO.getStoreName());
//            record.setStoreType(ecpStorePO.getType());
//            record.setBasicUid(clientPO.getBasicUid());
//            record.setClientId(clientPO.getId());
//            record.setClientName(clientPO.getName());
//            record.setMobile(clientPO.getPhone());
//            record.setPushChannel(0);
//            record.setPushUserName(pushUserName);
//            record.setPushUserId(Long.valueOf(pushUserId));
//            record.setRead(0);
//            record.setPushState(1);//默认文章记录都成功
//            record.setErrorMsg("");
//
//            //* 需求变更
//            //     * 原：根据通知消息判断是否推送成功失败
//            //     * 现：根据文章记录是否创建成功，判断是否推送失败
//            //     *
//            //     * 因时间问题，暂时默认文章记录都成功
//            //     *
//            //     * 后续等迭代修改
//            /*if (Objects.isNull(wechatUserPO) || ObjectUtil.isEmpty(wechatUserPO.getOpenid())) {
//                record.setErrorMsg("客户未登录过小程序");
//            } else if (CollectionUtil.isEmpty(fans)) {
//                record.setErrorMsg("客户未关注公众号");
//            } else {
//                record.setErrorMsg("");
//            }*/
//            record.setOpenid(Objects.nonNull(wechatUserPO) && ObjectUtil.isNotEmpty(wechatUserPO.getOpenid()) ? wechatUserPO.getOpenid() : "");
//            record.setPushTime(new Date());
//            recordList.add(record);
//        });
//        //批量新增
//        saveBatch(recordList);
//        if (Objects.isNull(wechatUserPO) || CollectionUtil.isEmpty(fans)) {
//            return true;
//        }
//        List<CustomerKnowledgeBasePushRecordPO> tempList = new ArrayList<>();
//        recordList.forEach(item -> {
//            knowledgeBaseArticlesSendDTO dto = new knowledgeBaseArticlesSendDTO();
//            dto.setClientId(String.valueOf(clientPO.getId()));
//            dto.setBrandType(ecpStorePO.getType());
//            dto.setTitle(item.getArticleTitle());
//            dto.setDetail("文章内容");
//            dto.setImgUrl("文章图片路径");
//            dto.setPushTime(DateUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
//            dto.setRemark("文章备注");
//
//            CompletableFuture<Result<Boolean>> completableFuture = messageClient.knowledgeBaseArticlesTriggerScene(dto);
//            CustomerKnowledgeBasePushRecordPO pushRecordPO = new CustomerKnowledgeBasePushRecordPO();
//            if (Objects.nonNull(completableFuture)) {
//                try {
//                    Result<Boolean> result = completableFuture.get();
//                    if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
//                        pushRecordPO.setPushState(result.getData() ? 1 : 0);
//                        pushRecordPO.setErrorMsg(result.getData() ? "" : result.getMsg());
//                    }
//                } catch (ExecutionException | InterruptedException e) {
//                    pushRecordPO.setPushState(0);
//                    pushRecordPO.setErrorMsg(e.getMessage());
//                }
//            }
//            pushRecordPO.setId(item.getId());
//            pushRecordPO.setPushTime(new Date());
//            tempList.add(pushRecordPO);
//        });
//        if (CollectionUtil.isNotEmpty(tempList)) {
//            return updateBatchById(tempList);
//        }
//
//        return true;
    }

    /**
     * 重新推送
     *
     * @param headerValue header value
     * @param request     request
     * @return boolean
     * <AUTHOR>
     * @date 2023/12/16 03:37:30
     * @since 1.0.0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> RePush(String headerValue, KnowledgeBasePushRecordRePushRequest request) {
        try {
            headerValue = URLDecoder.decode(headerValue, String.valueOf(StandardCharsets.UTF_8));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        Map<String, Object> userMap = JSONUtil.parseObj(headerValue);
        String pushUserId = (String) userMap.get("userId");
        String pushUserName = (String) userMap.get("userName");
        Map<String, Object> map = new HashMap<>();
        //根据手机号和品牌类型，时间， 查询文章推送记录
        CustomerKnowledgeBasePushRecordPO pushRecord = getOne(new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .eq(CustomerKnowledgeBasePushRecordPO::getId, request.getPushId())
                .eq(CustomerKnowledgeBasePushRecordPO::getPushState, 0)
                .eq(CustomerKnowledgeBasePushRecordPO::getDeleted, 0));
        if (Objects.isNull(pushRecord)) {
            throw new BusinessException("未找到符合要求的记录，id:" + request.getPushId());
        }
        pushRecord.setPushUserId(Long.valueOf(pushUserId));
        pushRecord.setPushUserName(pushUserName);

        if (ObjectUtil.isEmpty(pushRecord.getOpenid())) {
            pushRecord.setErrorMsg("客户未登录过小程序");
            pushRecord.setPushTime(new Date());
            updateById(pushRecord);
            map.put("pushState", 0);
            return map;
        } else {
            TabWechatUserPO wechatUserPO = tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
                    .eq(TabWechatUserPO::getOpenid, pushRecord.getOpenid()));
            if (Objects.isNull(wechatUserPO)) {
                pushRecord.setErrorMsg("客户未登录过小程序");
                pushRecord.setPushTime(new Date());
                updateById(pushRecord);
                map.put("pushState", 0);
                return map;
            } else {
                CustomerWechatFansListRequest fansListRequest = new CustomerWechatFansListRequest();
                fansListRequest.setUnionId(wechatUserPO.getUnionid());
                //来源1小贝拉 2圣贝拉，  门店类型 0圣贝拉 1小贝拉
                fansListRequest.setSource(pushRecord.getStoreType() == 0 ? 2 : 1);
                List<CustomerWechatFansListVO> fansListVOS = customerWechatFansService.queryWechatFans(fansListRequest);
                if (CollectionUtil.isEmpty(fansListVOS)) {
                    pushRecord.setErrorMsg("客户未关注公众号");
                    pushRecord.setPushTime(new Date());
                    updateById(pushRecord);
                    map.put("pushState", 0);
                    return map;
                }
            }
        }


        RLock lock = redisson.lock("customer:knowledgeBasePushRecord:" + request.getPushId(), TimeUnit.SECONDS, 3);
        try {
            knowledgeBaseArticlesSendDTO dto = new knowledgeBaseArticlesSendDTO();
            dto.setClientId(String.valueOf(pushRecord.getClientId()));
            dto.setBrandType(pushRecord.getStoreType());
            dto.setTitle(pushRecord.getArticleTitle());
            dto.setDetail("文章内容");
            dto.setImgUrl("文章图片路径");
            dto.setPushTime(DateUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            dto.setRemark("文章备注");

            CompletableFuture<Result<Boolean>> completableFuture = messageClient.knowledgeBaseArticlesTriggerScene(dto);

            if (Objects.nonNull(completableFuture)) {
                try {
                    Result<Boolean> result = completableFuture.get();
                    if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                        pushRecord.setPushState(result.getData() ? 1 : 0);
                        pushRecord.setErrorMsg(result.getData() ? "" : result.getMsg());
                    }
                } catch (ExecutionException | InterruptedException e) {
                    pushRecord.setPushState(0);
                    pushRecord.setErrorMsg(e.getMessage());
                }
            }
            pushRecord.setPushTime(new Date());
            updateById(pushRecord);
            map.put("pushState", pushRecord.getPushState());
            return map;
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }


    /**
     * 查询所有已推送的文章
     *
     * @param request request
     * @return page vo< knowledge base push record detail vo>
     * <AUTHOR>
     * @date 2023/12/13 07:29:01
     * @since 1.0.0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> queryUserAllPushArticles(UserAllPushArticlesRequest request) {

        if (Objects.equals(request.getDamonStatus(), 1)) {
            Map<String, Object> map = new HashMap<>();
            PageVO<KnowledgeBaseArticlesDetailVO> pageList = getDefaultArticlesInfo(request.getBrandType(), request);
            map.put("pageList", pageList);
            map.put("red", true);
            return map;
        }

        HeOrderPO heOrderPO = heOrderService.getByOrderSn(request.getOrderNo());
        if (Objects.isNull(heOrderPO)) {
            throw new BusinessException("未找到订单信息");
        }

        EcpStorePO store = cfgStoreService.queryStoreByStoreId(heOrderPO.getStoreId());
        if (Objects.isNull(store)) {
            throw new BusinessException("未找到门店信息");
        }

        //根据订单号，查询文章推送记录
        List<CustomerKnowledgeBasePushRecordPO> pushRecordList = list(new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, request.getOrderNo())
                .eq(CustomerKnowledgeBasePushRecordPO::getDeleted, 0));

        List<Long> customerKnowledgeBaseIdList = new ArrayList<>();

        AtomicReference<Boolean> red = new AtomicReference<>(false);
        PageVO<KnowledgeBaseArticlesDetailVO> pageList = new PageVO<>();
        //有记录
        if (CollectionUtil.isNotEmpty(pushRecordList)) {
            customerKnowledgeBaseIdList = pushRecordList.stream()
                    .map(CustomerKnowledgeBasePushRecordPO::getKnowledgeBaseId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(customerKnowledgeBaseIdList)) {
                List<CustomerKnowledgeBasePO> baseList = customerKnowledgeBaseService.list(new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                        .in(CustomerKnowledgeBasePO::getId, customerKnowledgeBaseIdList));

                //批量查询文章的read记录,并转成map<Long, int>
                Map<String, Integer> readMap = getReadMap(request, heOrderPO);
                log.info("批量查询文章的view记录,customerKnowledgeBaseIdList:{},readMap:{}",customerKnowledgeBaseIdList,readMap);

                if (CollectionUtil.isNotEmpty(baseList)) {
                    List<KnowledgeBaseArticlesDetailVO> detailVOList = new ArrayList<>();
                    List<CustomerKnowledgeBasePushRecordPO> tempList = new ArrayList<>();
                    pushRecordList.forEach(item -> {
                        Optional<CustomerKnowledgeBasePO> customerKnowledgeBase = baseList.stream()
                                .filter(i -> Objects.equals(i.getId(), item.getKnowledgeBaseId())).findFirst();
                        if (!customerKnowledgeBase.isPresent()) {
                            return;
                        }
                        CustomerKnowledgeBasePO base = customerKnowledgeBase.get();
                        KnowledgeBaseArticlesDetailVO vo = setKnowledgeBaseArticlesDetailVOInfo(store.getType(), base);
                        //设置readStatus
                        vo.setReadStatus(getReadStatus(request, item, readMap));
                        vo.setPushTime(DateUtils.format(item.getPushTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                        detailVOList.add(vo);
                        //查看列表时如果有未读，则变为已读
                        if (item.getRead() == 0) {
                            item.setRead(1);
                            red.set(true);
                            tempList.add(item);
                        }
                    });
                    //查看列表时如果有未读，则变为已读
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        updateBatchById(tempList);
                    }

                    List<KnowledgeBaseArticlesDetailVO> temp = detailVOList.stream()
                            .sorted(Comparator.comparing(KnowledgeBaseArticlesDetailVO::getPushTime).reversed()
                                    .thenComparing(Comparator.comparing(KnowledgeBaseArticlesDetailVO::getSort).reversed()))
                            .skip((long) (request.getPageNum() - 1) * request.getPageSize())
                            .limit(request.getPageSize())
                            .collect(Collectors.toList());
                    pageList = new PageVO<>(temp, pushRecordList.size(), request.getPageSize(), request.getPageNum());
                }
            }
        } else {
            //如果是小月子客户，然后没有推送记录，则返回空，前端隐藏今日的模块
            if (Objects.equals(heOrderPO.getOrderType(), OrderTypeEnum.ORDER_TYPE_YZ_SMALL.getCode())) {
                pageList = new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
            }
            //如果是标准月子客户，然后没有推送记录，则返回通用文章。
            if (Objects.equals(heOrderPO.getOrderType(), OrderTypeEnum.ORDER_TYPE_YZ_NORMAL.getCode())) {
                pageList = getDefaultArticlesInfo(store.getType(), request);
            }
            Map<String, Integer> readMap = getReadMap(request, heOrderPO);
            log.info("通用-批量查询文章的view记录,customerKnowledgeBaseIdList:{},readMap:{}",customerKnowledgeBaseIdList,readMap);
            pageList.getList().stream().forEach(item->{
                String key =request.getOrderNo()+"_"+heOrderPO.getBasicUid()+"_"+request.getBrandType()+"_"+item.getKnowledgeBaseId();
                Integer readValue = readMap.get(key)==null?0:readMap.get(key);
                log.info("通用-查询文章的view记录,key:{},value:{}",key, readValue);
                item.setReadStatus(readValue);
            });
        }
        Map<String, Object> map = new HashMap<>();
        map.put("pageList", pageList);
        map.put("red", red.get());
        return map;
    }

    private static @NotNull Integer getReadStatus(UserAllPushArticlesRequest request, CustomerKnowledgeBasePushRecordPO item, Map<String, Integer> readMap) {
        String key = request.getOrderNo()+"_"+ item.getBasicUid()+"_"+ request.getBrandType()+"_"+ item.getKnowledgeBaseId();
        Integer readValue = readMap.get(key)==null?0: readMap.get(key);
        log.info("查询文章的view记录,key:{},value:{}",key, readValue);
        return readValue;
    }

    private @NotNull Map<String, Integer> getReadMap(UserAllPushArticlesRequest request, HeOrderPO heOrderPO) {
        Map<String, Integer> readMap = new HashMap<>();
        try {
            List<CustomerKnowledgeBaseViewRecordPO> list = customerKnowledgeBaseViewRecordService.list(new LambdaQueryWrapper<CustomerKnowledgeBaseViewRecordPO>()
                    .eq(CustomerKnowledgeBaseViewRecordPO::getBasicUid, heOrderPO.getBasicUid())
                    .eq(CustomerKnowledgeBaseViewRecordPO::getOrderNo, request.getOrderNo())
                    .eq(CustomerKnowledgeBaseViewRecordPO::getDeleted, 0)
            );
            log.info("批量查询文章的view记录:{}", JSON.toJSONString(list));
            //按照basicuid+"_"+knowledgeBaseId的方式 ，组装map
            list.forEach(item -> {
                String key = request.getOrderNo() + "_" + item.getBasicUid() + "_" + item.getBrandType() + "_" + item.getKnowledgeBaseId();
                log.info("批量查询文章的view记录,key:{}", key);
                readMap.put(key, 1);
            });
        }catch (Exception e){
            log.error("批量查询文章的view记录出错,orderNo:{}", request.getOrderNo(),e);
        }
        return readMap;
    }

    /**
     * 获取默认文章信息
     *
     * @param type    品牌类型
     * @param request request
     * @return page vo< knowledge base articles detail vo>
     * <AUTHOR>
     * @date 2023/12/25 01:19:25
     * @since 1.0.0
     */
    private PageVO<KnowledgeBaseArticlesDetailVO> getDefaultArticlesInfo(Integer type, UserAllPushArticlesRequest request) {
        //获取通用文章id
        List<Long> defaultArticles = redisService.getCacheList("customer:knowledgeBase:articles:default");
        if (CollectionUtil.isEmpty(defaultArticles)) {
            defaultArticles = Arrays.asList(3L, 7L, 19L, 22L);
            redisService.setCacheList("customer:knowledgeBase:articles:default", defaultArticles);
        }

        //通用文章 Subtypes = 2
        List<CustomerKnowledgeBasePO> basePOList = customerKnowledgeBaseService.list(new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                .in(CustomerKnowledgeBasePO::getId, defaultArticles));

        if (CollectionUtil.isNotEmpty(basePOList)) {
            List<KnowledgeBaseArticlesDetailVO> detailVOList = basePOList.stream()
                    .map(item -> setKnowledgeBaseArticlesDetailVOInfo(type, item))
                    .sorted(Comparator.comparing(KnowledgeBaseArticlesDetailVO::getSort).reversed())
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(detailVOList)) {
                //返回通用文章。
                return new PageVO<>(detailVOList, detailVOList.size(), request.getPageSize(), request.getPageNum());
            }
        }
        return new PageVO<>(new ArrayList<>(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * set knowledge base articles detail voinfo
     *
     * @param item item
     * @return queryknowledgebasearticlesdetailvo
     * <AUTHOR>
     * @date 2023/12/19 08:06:59
     * @since 1.0.0
     */
    private KnowledgeBaseArticlesDetailVO setKnowledgeBaseArticlesDetailVOInfo(Integer brandType, CustomerKnowledgeBasePO item) {

        KnowledgeBaseArticlesDetailVO detailVO = new KnowledgeBaseArticlesDetailVO();
        detailVO.setKnowledgeBaseId(item.getId());
        detailVO.setTitle(item.getTitle());

        if (ObjectUtil.isNotEmpty(item.getDetail())) {
            String htmlTxtList = JsoupUtil.getHtmlTxt(item.getDetail());
            detailVO.setDetail(ObjectUtil.isNotEmpty(htmlTxtList) ? htmlTxtList : "");
        } else {
            detailVO.setDetail("");
        }
        
        Map<String, Object> thumbMap = JSONUtil.parseObj(item.getThumb());
        //圣贝拉和小贝拉图不一样
        if (Objects.equals(brandType, 0) || Objects.equals(brandType, 100)) {
            detailVO.setImg(thumbMap.containsKey("sbl") ? thumbMap.get("sbl").toString() : "");
        } else {
            detailVO.setImg(thumbMap.containsKey("xbl") ? thumbMap.get("xbl").toString() : "");
        }

        detailVO.setSort(item.getSort());
        //总浏览量=基础浏览量+真实浏览量
        detailVO.setPageView(item.getBasePageView() + item.getPageView());
        //点赞量= 总浏览量/基础点赞量或比例+真实点赞量
        BigDecimal pageView = new BigDecimal(detailVO.getPageView());
        BigDecimal baseNum = BigDecimal.valueOf(item.getBaseUpvoteNum());
        BigDecimal UpvoteNum = baseNum.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : pageView.divide(baseNum, 0, RoundingMode.CEILING);
        detailVO.setUpvoteNum(UpvoteNum.add(BigDecimal.valueOf(item.getUpvoteNum())).intValue());
        return detailVO;
    }

    /**
     * 根据文章id,查询文章详情,并记录文章数据
     *
     * @param id        id
     * @param brandType
     * @param orderNo
     * @return queryknowledgebasearticlesdetailvo
     * <AUTHOR>
     * @date 2023/12/13 07:52:49
     * @since 1.0.0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public QueryKnowledgeBaseArticlesDetailVO queryKnowledgeBaseArticlesDetailById(Long id, Integer basicUid, Integer brandType, String orderNo) {
        CustomerKnowledgeBasePO basePO = customerKnowledgeBaseService.getById(id);
        if (Objects.isNull(basePO)) {
            throw new BusinessException("未找到对应文章信息，id:" + id);
        }

        CustomerKnowledgeBaseLikeRecordPO likeRecord = customerKnowledgeBaseLikeRecordService.getOne(new LambdaQueryWrapper<CustomerKnowledgeBaseLikeRecordPO>()
                .eq(CustomerKnowledgeBaseLikeRecordPO::getBasicUid, basicUid)
                .eq(CustomerKnowledgeBaseLikeRecordPO::getKnowledgeBaseId, basePO.getId()));

        QueryKnowledgeBaseArticlesDetailVO detailVO = new QueryKnowledgeBaseArticlesDetailVO();
        detailVO.setKnowledgeBaseId(basePO.getId());
        detailVO.setTitle(basePO.getTitle());
        //Todo 这个要改
        detailVO.setDetail(basePO.getDetail());
        //detailVO.setDetail(JSONUtil.toList(basePO.getDetail(), QueryKnowledgeBaseArticlesDetailVO.detailInfoVO.class));
        detailVO.setImg(JSONUtil.toList(basePO.getImg(), String.class));
        detailVO.setProofreader(basePO.getProofreader());
        detailVO.setWriter(basePO.getWriter());
        //有点赞记录，取点赞类型，否则默认为0
        detailVO.setUpvoteType(Objects.nonNull(likeRecord) ? likeRecord.getUpvoteType() : 0);
        //该字段页面不展示 前端需要用
        detailVO.setPageView(basePO.getBasePageView() + basePO.getPageView() + 1);
        //总浏览量=基础浏览量+真实浏览量
        BigDecimal pageView = new BigDecimal(detailVO.getPageView());
        BigDecimal baseNum = BigDecimal.valueOf(basePO.getBaseUpvoteNum());
        BigDecimal UpvoteNum = baseNum.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : pageView.divide(baseNum, 0, RoundingMode.CEILING);
        detailVO.setUpvoteNum(UpvoteNum.add(BigDecimal.valueOf(basePO.getUpvoteNum())).intValue());

        //保存浏览记录
        saveViewRecord(basicUid, brandType, orderNo, basePO);
        RLock lock = redisson.lock("customer:knowledgeBasePageView:" + basePO.getId(), TimeUnit.SECONDS, 3);
        try {
            //记录文章浏览数
            basePO.setPageView(basePO.getPageView() + 1);
            customerKnowledgeBaseService.updateById(basePO);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
        return detailVO;
    }

    private void saveViewRecord(Integer basicUid, Integer brandType, String orderNo, CustomerKnowledgeBasePO basePO) {
        if(brandType !=null && StringUtils.isNotBlank(orderNo)){
            CustomerKnowledgeBaseViewRecordPO readOne = customerKnowledgeBaseViewRecordService.getOne(new LambdaQueryWrapper<CustomerKnowledgeBaseViewRecordPO>()
                    .eq(CustomerKnowledgeBaseViewRecordPO::getKnowledgeBaseId, basePO.getId())
                    .eq(CustomerKnowledgeBaseViewRecordPO::getOrderNo, orderNo)
                    .eq(CustomerKnowledgeBaseViewRecordPO::getBasicUid, basicUid)
                    .eq(CustomerKnowledgeBaseViewRecordPO::getBrandType, brandType)
                    .eq(CustomerKnowledgeBaseViewRecordPO::getDeleted, 0)
            );
            log.info("查询文章阅读记录,orderNo:{}，basicUid:{},knowledgeBaseId:{}", orderNo, basicUid, basePO.getId());
            if (Objects.isNull(readOne)) {
                boolean save = customerKnowledgeBaseViewRecordService.save(new CustomerKnowledgeBaseViewRecordPO()
                        .setKnowledgeBaseId(basePO.getId())
                        .setBasicUid(basicUid)
                        .setBrandType(brandType)
                        .setOrderNo(orderNo)
                        .setOperationTime(LocalDateTime.now()));
                log.info("保存文章阅读记录,orderNo:{},basicUid:{},knowledgeBaseId:{},brandType:{},save:{}", orderNo, basicUid, basePO.getId(), brandType,save);
            }
        }
    }

    /**
     * 用户点赞操作
     *
     * @param request request
     * @return boolean
     * <AUTHOR>
     * @date 2023/12/13 08:13:20
     * @since 1.0.0
     */
    @Override
    public Boolean userUpvote(UserUpvoteRequest request) {

        CustomerKnowledgeBasePO basePO = customerKnowledgeBaseService.getById(request.getKnowledgeBaseId());
        if (Objects.isNull(basePO)) {
            throw new BusinessException("未找到对应文章信息，id:" + request.getKnowledgeBaseId());
        }

        CustomerKnowledgeBaseLikeRecordPO likeRecord = customerKnowledgeBaseLikeRecordService.getOne(new LambdaQueryWrapper<CustomerKnowledgeBaseLikeRecordPO>()
                .eq(CustomerKnowledgeBaseLikeRecordPO::getBasicUid, request.getBasicUid())
                .eq(CustomerKnowledgeBaseLikeRecordPO::getKnowledgeBaseId, basePO.getId()));


        RLock lock = redisson.lock("customer:knowledgeBaseUserUpvote:" + basePO.getId(), TimeUnit.SECONDS, 3);
        try {
            //默认为0
            int oldUpvoteType = 0;
            int newUpvoteType = request.getUpvoteType();

            //有点赞记录，则获取点赞记录的
            if (Objects.nonNull(likeRecord)) {
                oldUpvoteType = likeRecord.getUpvoteType();
                //修改点赞记录
                UpdateWrapper<CustomerKnowledgeBaseLikeRecordPO> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("upvote_type", request.getUpvoteType());
                updateWrapper.eq("id", likeRecord.getId());
                customerKnowledgeBaseLikeRecordService.update(updateWrapper);
            } else {
                CustomerKnowledgeBaseLikeRecordPO recordPO = new CustomerKnowledgeBaseLikeRecordPO();
                recordPO.setKnowledgeBaseId(request.getKnowledgeBaseId());
                recordPO.setBasicUid(request.getBasicUid());
                recordPO.setUpvoteType(request.getUpvoteType());
                customerKnowledgeBaseLikeRecordService.save(recordPO);
            }

            int upvoteNum = basePO.getUpvoteNum();//点赞数
            int notUpvoteNum = basePO.getNotUpvoteNum();//不推荐数

            if (oldUpvoteType != newUpvoteType) {
                //之前为无动作
                if (oldUpvoteType == 0) {
                    //现在为点赞
                    if (newUpvoteType == 1) {
                        upvoteNum++;
                    }
                    //现在为不推荐
                    if (newUpvoteType == 2) {
                        notUpvoteNum++;
                    }
                }
                //之前为点赞
                if (oldUpvoteType == 1) {
                    //现在为无动作
                    upvoteNum--;
                    //现在为不推荐
                    if (newUpvoteType == 2) {
                        notUpvoteNum++;
                    }
                }
                //之前为不推荐
                if (oldUpvoteType == 2) {
                    //现在为无动作
                    notUpvoteNum--;
                    //现在为点赞
                    if (newUpvoteType == 1) {
                        upvoteNum++;
                    }
                }
            }
            UpdateWrapper<CustomerKnowledgeBasePO> update = new UpdateWrapper<>();
            update.set("upvote_num", upvoteNum);
            update.set("not_upvote_num", notUpvoteNum);
            update.eq("id", basePO.getId());
            return customerKnowledgeBaseService.update(update);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    /**
     * 搜索框
     *
     * @param request request
     * @return list<all knowledge base essay vo>
     * <AUTHOR>
     * @date 2023/12/14 02:15:13
     * @since 1.0.0
     */
    @Override
    public List<AllKnowledgeBaseEssayVO> searchBox(SearchBoxRequest request) {
        //根据手机号和品牌类型，时间， 查询文章推送记录
        List<CustomerKnowledgeBasePushRecordPO> pushRecordPOList = list(new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, request.getOrderNo())
                .eq(CustomerKnowledgeBasePushRecordPO::getDeleted, 0));

        if (CollectionUtil.isEmpty(pushRecordPOList)) {
            return new ArrayList<>();
        }
        List<Long> idList = pushRecordPOList.stream()
                .map(item -> item.getKnowledgeBaseId().longValue())
                .distinct()
                .collect(Collectors.toList());

        List<CustomerKnowledgeBasePO> baseList = customerKnowledgeBaseService.listByIds(idList);
        if (CollectionUtil.isEmpty(baseList)) {
            return new ArrayList<>();
        }

        String regexContent = request.getContent(); // 定义要匹配的正则表达式
        Pattern pattern = Pattern.compile(regexContent); // 编译正则表达式
        AtomicInteger num = new AtomicInteger(0);
        List<AllKnowledgeBaseEssayVO> essayVOList = new ArrayList<>();
        pushRecordPOList.forEach(item -> {
            Optional<CustomerKnowledgeBasePO> baseOptional = baseList.stream()
                    .filter(b -> Objects.equals(b.getId(), item.getKnowledgeBaseId())).findFirst();
            if (baseOptional.isPresent()) {
                CustomerKnowledgeBasePO base = baseOptional.get();

                Matcher matcher = pattern.matcher(base.getTitle() + "_" + base.getDetail()); // 创建匹配器
                while (matcher.find()) {
                    num.getAndIncrement(); // 统计匹配内容的出现次数
                }
                AllKnowledgeBaseEssayVO vo = new AllKnowledgeBaseEssayVO();
                vo.setId(base.getId());
                vo.setTitle(base.getTitle());
                //不想创建新字段了，借用老字段来排序
                vo.setPushStatus(num.get());
                essayVOList.add(vo);
                num.set(0);
            }
        });

        if (CollectionUtil.isNotEmpty(essayVOList)) {
            return essayVOList.stream()
                    .sorted(Comparator.comparing(AllKnowledgeBaseEssayVO::getPushStatus).reversed())
                    .skip((long) (request.getPageNum() - 1) * request.getPageSize())
                    .limit(request.getPageSize())
                    .collect(Collectors.toList());
        }
        return essayVOList;
    }

    /**
     * 产妇宣教签字后自动推送文章
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> autoPushKnowledgeByEducation(AutoPushKnowledgeRequest request) {
        log.info("产妇宣教签字后自动推送文章参数:{}", JSONUtil.toJsonStr(request));
        List<CustomerKnowledgeBasePO> customerKnowledgeBasePOList = customerKnowledgeBaseService.list(
            new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                .eq(CustomerKnowledgeBasePO::getKnowledgeType, request.getType())
                .eq(CustomerKnowledgeBasePO::getSubtypes, request.getSubtypes())
                    .in(CustomerKnowledgeBasePO::getEducationProjectId, request.getEducationProjectIds())
        );

        if (CollectionUtil.isEmpty(customerKnowledgeBasePOList)) {
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "未找到宣教对应的文章");
        }


        CompletableFuture.runAsync(() -> pushKnowledge(request.getOrderNo(), customerKnowledgeBasePOList, KnowledgePushTypeEnum.AUTO, request.getOperatorId(), request.getOperatorName()), taskExecutor);

        log.info("执行推送文章成功");
        return Result.success(true);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deletePushKnowledgeRecord(DeletePushKnowledgeRequest request) {
        List<CustomerKnowledgeBasePO> basePOS = customerKnowledgeBaseService.list(
                new LambdaQueryWrapper<CustomerKnowledgeBasePO>()
                        .eq(CustomerKnowledgeBasePO::getKnowledgeType, request.getType())
                        .in(CustomerKnowledgeBasePO::getSubtypes, request.getSubtypes())
                        // 是否过滤小月子
                        .isNotNull((Objects.nonNull(request.getIsSmallMonth()) && request.getIsSmallMonth()), CustomerKnowledgeBasePO::getSmallMonthGestationWeekType)
        );
        if (CollectionUtil.isEmpty(basePOS)) {
            return true;
        }

        List<String> orderNos = request.getOrderNos();
        Set<Long> baseIds = basePOS.stream().map(CustomerKnowledgeBasePO::getId).collect(Collectors.toSet());

        LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .select(CustomerKnowledgeBasePushRecordPO::getId, CustomerKnowledgeBasePushRecordPO::getBasicUid)
                .in(CustomerKnowledgeBasePushRecordPO::getOrderNo, orderNos)
                .in(CustomerKnowledgeBasePushRecordPO::getKnowledgeBaseId, baseIds);
        List<CustomerKnowledgeBasePushRecordPO> pushRecordPOS = this.baseMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(pushRecordPOS)) {
            return true;
        }

        Set<Long> pushRecordIds = pushRecordPOS.stream().map(CustomerKnowledgeBasePushRecordPO::getId).collect(Collectors.toSet());
        LambdaUpdateWrapper<CustomerKnowledgeBasePushRecordPO> updateWrapper = new LambdaUpdateWrapper<CustomerKnowledgeBasePushRecordPO>()
                .in(CustomerKnowledgeBasePushRecordPO::getId, pushRecordIds)
                .set(CustomerKnowledgeBasePushRecordPO::getDeleted, 1);
        this.update(updateWrapper);

        Set<Integer> basicUids = pushRecordPOS.stream().map(CustomerKnowledgeBasePushRecordPO::getBasicUid).collect(Collectors.toSet());
        customerKnowledgeBaseLikeRecordService.updateDeleted(basicUids, baseIds);
        return true;
    }
    /**
     * 通用推送文章方法
     *
     * @param orderNo 推动的订单编号
     * @param pushKnowledgeList 要推送的知识库列表
     * @param pushType 推送类型
     * @param pushStaffId 推送员工的id
     * @param pushStaffName 推送员工的姓名
     * @return
     */
    private Boolean pushKnowledge(String orderNo, List<CustomerKnowledgeBasePO> pushKnowledgeList, KnowledgePushTypeEnum pushType, Long pushStaffId, String pushStaffName) {
        log.info("开通推送知识库, orderNo:{}, knowledgeList:{}", orderNo, JSONUtil.toJsonStr(pushKnowledgeList));
        HeOrderPO order = heOrderService.getByOrderSn(orderNo);
        if (Objects.isNull(order)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "推送文章消息时，未找到订单信息，orderNo:" + orderNo);
        }

        EcpStorePO ecpStorePO = cfgStoreService.queryStoreByStoreId(order.getStoreId());
        if (Objects.isNull(ecpStorePO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "推送文章消息时，未找到门店信息，StoreId:" + order.getStoreId());
        }

        TabClientPO clientPO = tabClientService.getById(order.getClientUid());
        if (Objects.isNull(clientPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "推送文章消息时，未找到用户信息，client:" + order.getClientUid());
        }


        TabWechatUserPO wechatUserPO = tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
            .eq(TabWechatUserPO::getBasicUid, clientPO.getBasicUid())
                .eq(TabWechatUserPO::getFromType, Objects.equals(ecpStorePO.getType(), 0) ? 1 : Objects.equals(ecpStorePO.getType(), 1) ? 2 : 100)
            .orderByDesc(TabWechatUserPO::getCreatedAt)
            .last("limit 1"));

        List<CustomerWechatFansListVO> fans;
        if (Objects.nonNull(wechatUserPO)) {
            CustomerWechatFansListRequest fansListRequest = new CustomerWechatFansListRequest();
            fansListRequest.setUnionId(wechatUserPO.getUnionid());
            //来源1小贝拉 2圣贝拉，  门店类型 0圣贝拉 1小贝拉
            fansListRequest.setSource(ecpStorePO.getType() == 0 ? 2 : ecpStorePO.getType() == 1 ? 1 : 100);
            fans = customerWechatFansService.queryWechatFans(fansListRequest);
        } else {
            fans = new ArrayList<>();
        }

        // 获取已经推送过的文章
        List<Long> knowledgeIdList = pushKnowledgeList.stream()
            .map(CustomerKnowledgeBasePO::getId)
            .collect(Collectors.toList());

        List<CustomerKnowledgeBasePushRecordPO> pushedRecordList = list(
            new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                .in(CustomerKnowledgeBasePushRecordPO::getKnowledgeBaseId, knowledgeIdList)
                .eq(CustomerKnowledgeBasePushRecordPO::getOrderNo, orderNo));

        Map<Long, CustomerKnowledgeBasePushRecordPO> pushRecordPOMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(pushedRecordList)) {
            pushRecordPOMap = pushedRecordList.stream()
                .collect(Collectors.toMap(CustomerKnowledgeBasePushRecordPO::getKnowledgeBaseId, i -> i, (first, second) -> second));
        }

        List<CustomerKnowledgeBasePushRecordPO> recordList = new ArrayList<>();
        Map<Long, CustomerKnowledgeBasePushRecordPO> finalPushRecordPOMap = pushRecordPOMap;
        pushKnowledgeList.forEach(item -> {
            // 判断文章是否推送过，推送过的无需推送
            if (!finalPushRecordPOMap.containsKey(item.getId())) {
                CustomerKnowledgeBasePushRecordPO record = new CustomerKnowledgeBasePushRecordPO();
                record.setPushType(pushType.getCode());// 默认人工
                record.setOrderNo(orderNo);
                record.setKnowledgeBaseId(item.getId());
                record.setArticleTitle(item.getTitle());
                record.setStoreId(ecpStorePO.getStoreId());
                record.setStoreName(ecpStorePO.getStoreName());
                record.setStoreType(ecpStorePO.getType());
                record.setBasicUid(clientPO.getBasicUid());
                record.setClientId(clientPO.getId());
                record.setClientName(clientPO.getName());
                record.setMobile(clientPO.getPhone());
                record.setPushChannel(0);
                record.setPushUserName(pushStaffName);
                record.setPushUserId(pushStaffId);
                record.setRead(0);
                record.setPushState(1);//默认文章记录都成功
                record.setErrorMsg("");
                record.setOpenid(Objects.nonNull(wechatUserPO) && ObjectUtil.isNotEmpty(wechatUserPO.getOpenid()) ? wechatUserPO.getOpenid() : "");
                record.setPushTime(new Date());
                recordList.add(record);
            }
        });

        //批量新增
        if (recordList.size() > 0) {
            saveBatch(recordList);
        }
        Boolean smallAgeOrder = orderClient.isSmallAgeOrder(orderNo);
        if (!smallAgeOrder) {
            // 管家和销售发送提醒
            messageClient.educationPushRemindEmp(pushStaffName, clientPO.getName(), order.getStoreId().longValue());
        }
        if (Objects.isNull(wechatUserPO) || CollectionUtil.isEmpty(fans)) {
            log.info("开通推送知识库, 登录信息或粉丝信息为空 orderNo:{}",orderNo);
            return true;
        }

        List<CustomerKnowledgeBasePushRecordPO> tempList = new ArrayList<>();
        recordList.forEach(item -> {
            knowledgeBaseArticlesSendDTO dto = new knowledgeBaseArticlesSendDTO();
            dto.setClientId(String.valueOf(clientPO.getId()));
            dto.setBrandType(ecpStorePO.getType());
            dto.setTitle(item.getArticleTitle());
            dto.setDetail("文章内容");
            dto.setImgUrl("文章图片路径");
            dto.setPushTime(DateUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            dto.setRemark("文章备注");

            CompletableFuture<Result<Boolean>> completableFuture = messageClient.knowledgeBaseArticlesTriggerScene(dto);
            CustomerKnowledgeBasePushRecordPO pushRecordPO = new CustomerKnowledgeBasePushRecordPO();
            if (Objects.nonNull(completableFuture)) {
                try {
                    Result<Boolean> result = completableFuture.get();
                    if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                        pushRecordPO.setPushState(result.getData() ? 1 : 0);
                        pushRecordPO.setErrorMsg(result.getData() ? "" : result.getMsg());
                    }
                } catch (ExecutionException | InterruptedException e) {
                    pushRecordPO.setPushState(0);
                    pushRecordPO.setErrorMsg(e.getMessage());
                }
            }
            pushRecordPO.setId(item.getId());
            pushRecordPO.setPushTime(new Date());
            tempList.add(pushRecordPO);
        });
        if (CollectionUtil.isNotEmpty(tempList)) {
            updateBatchById(tempList);
            if (!smallAgeOrder) {
                // 客户发送提醒
                messageClient.educationPushRemindCustomer(clientPO, orderNo, ecpStorePO.getStoreName(), ecpStorePO.getType());
            }
        }

        return true;
    }



    @Override
    public List<KnowledgePushVO> getPushRecordByOrderNo(List<String> orderNo) {
        List<CustomerKnowledgeBasePushRecordPO> pushedRecordList = list(
                new LambdaQueryWrapper<CustomerKnowledgeBasePushRecordPO>()
                        .select(CustomerKnowledgeBasePushRecordPO::getOrderNo)
                        .in(CustomerKnowledgeBasePushRecordPO::getOrderNo, orderNo));
        if (CollectionUtil.isEmpty(pushedRecordList)){
            return new ArrayList<>();
        }
        return pushedRecordList.stream().map(i->{
            KnowledgePushVO knowledgePush = new KnowledgePushVO();
            knowledgePush.setOrderNo(i.getOrderNo());
            return knowledgePush;
        }).collect(Collectors.toList());
    }
}
