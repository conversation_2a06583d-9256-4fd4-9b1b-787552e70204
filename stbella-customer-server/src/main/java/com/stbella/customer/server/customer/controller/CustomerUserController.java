package com.stbella.customer.server.customer.controller;

import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.customer.request.*;
import com.stbella.customer.server.customer.service.CustomerCheckinStoreFailListService;
import com.stbella.customer.server.customer.service.CustomerUserIncreaseService;
import com.stbella.customer.server.customer.service.CustomerUserService;
import com.stbella.customer.server.customer.vo.*;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.customer.server.ecp.request.UserBasicKeywordSearchRequest;
import com.stbella.customer.server.ecp.service.HeUserBasicService;
import com.stbella.customer.server.tracking.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@Api(tags = "会员相关接口")
@RestController
@RequestMapping("/customer/user")
@Slf4j
public class CustomerUserController {

    @Resource
    private CustomerUserService customerUserService;

    @Resource
    private CustomerUserIncreaseService customerUserIncreaseService;

    @Resource
    private CustomerCheckinStoreFailListService customerCheckinStoreFailListService;

    @Resource
    private HeUserBasicService heUserBasicService;

    @GetMapping("/center")
    @ApiOperation(value = "查询会员中心信息")
    public Result<CustomerUserCenterVO> center(UserCenterRequest request) {
        CustomerUserCenterVO customerUserCenterVO = customerUserService.center(request);
        return Result.success(customerUserCenterVO);
    }

    /**
     * 逻辑来自于用户中心/center接口
     *
     * @param request
     * @return
     */
    @GetMapping("/center/data")
    @ApiOperation(value = "查询会员中心统计信息")
    public Result<CountData> countData(UserCenterRequest request) {
        return Result.success(customerUserService.getCountDataByBrandType(request));
    }



    @GetMapping("/queryBannerList")
    @ApiOperation(value = "查询会员中心Banner列表")
    public Result<CustomerBannerInfoVO> queryBannerList(@RequestParam(value = "brandType", required = false) Integer brandType, @RequestParam(value = "page", required = false) Integer page) {
        CustomerBannerInfoVO customerBannerInfoVO = customerUserService.queryBannerList(brandType, page);
        return Result.success(customerBannerInfoVO);
    }


    @PostMapping("/queryUserTestRoleCheck")
    @ApiOperation(value = "查询会员是否拥有演示权限")
    public Result<Integer> queryUserTestRoleCheck(@RequestBody UserTestRoleCheckRequest req) {
        Integer isSuccess = customerUserService.queryUserTestRoleCheck(req);
        return Result.success(isSuccess);
    }

    @ApiOperation(value = "获取我的二维码")
    @PostMapping("/getQrCode")
    public Result<UserQrCodeVO> getUserQrCode(@RequestBody UserQrCodeRequest request) {
        UserQrCodeVO userQrCodeVO = customerUserService.getUserQrCode(request);
        return Result.success(userQrCodeVO);
    }

    @ApiOperation(value = "通过二维码获取用户头像和昵称")
    @PostMapping("/getNickName")
    public Result<Map<String, Object>> getUserNickNameByQrCode(@Validated @RequestBody QrCodeRequest request) {
        Map<String, Object> map = customerUserService.getUserNickNameByQrCode(request);
        return Result.success(map);
    }

    @ApiOperation(value = "获取新的邀请积分规则(brandType: 0圣贝拉 1小贝拉)")
    @GetMapping("/getInviteRule")
    public Result<List<InviteRuleVO>> getInviteRule(Integer brandType) {
        List<InviteRuleVO> inviteRule = customerUserService.getInviteRule(brandType);
        return Result.success(inviteRule);
    }

    @ApiOperation(value = "推荐好友列表")
    @PostMapping("/shareList")
    public Result<InvitedUserListVO> getMyShareList(@Valid @RequestBody UserShareRequest request) {
        InvitedUserListVO invitedUserListVO = customerUserIncreaseService.getMyShareList(request);
        return Result.success(invitedUserListVO);
    }

    @GetMapping("/userInfo")
    @ApiOperation(value = "查询会员微信信息")
    public Result<CustomerWechatUserInfoVO> userInfo(CustomerWechatInfoRequest request) {
        CustomerWechatUserInfoVO customerWechatUserInfoVO = customerUserService.getUserInfo(request.getPhone(), request.getBrandType());
        return Result.success(customerWechatUserInfoVO);
    }

    @PostMapping("/updateUserInfo")
    @ApiOperation(value = "修改用户信息")
    public Result<Boolean> updateUserInfo(@RequestBody CustomerUserEditInfoRequest req) {
        Boolean isSuccess = customerUserService.updateUserInfo(req);
        return Result.success(isSuccess);
    }


    @PostMapping("/setUserPregnancy")
    @ApiOperation(value = "修改用户预产期")
    public Result<Boolean> setUserPregnancy(@RequestBody CustomerUserEditPregnancyRequest req) {
        req.setModifySource(1);
        Boolean isSuccess = customerUserService.setUserPregnancy(req);
        return Result.success(isSuccess);
    }

    @PostMapping("/pi/setUserPregnancy")
    @ApiOperation(value = "修改用户预产期")
    public Result<Boolean> piSetUserPregnancy(@RequestBody CustomerUserEditPregnancyRequest req) {
        req.setModifySource(2);
        Boolean isSuccess = customerUserService.setUserPregnancy(req);
        return Result.success(isSuccess);
    }

    @GetMapping("/getTopicsDuringPregnancy")
    @ApiOperation(value = "查询孕期专题")
    public Result<Map<String, Object>> getTopicsDuringPregnancy(@RequestParam("type") Integer type,
                                                                @RequestParam(value = "clientType",required = false) Integer clientType,
                                                                @RequestParam(value = "brandType",required = false) Integer brandType) {
        if(clientType==null && brandType == null){
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "品牌参数必传");
        }
        if(brandType!=null){
            if(brandType==100){
                clientType = 100;
            }else if(brandType ==0){
                clientType = 1;
            }else if(brandType ==1){
                clientType = 2;
            }
        }
        Map<String, Object> map = customerUserService.getTopicsDuringPregnancy(type, clientType);
        return Result.success(map);
    }

    @ApiOperation(value = "上报客户探店打卡失败信息")
    @PostMapping("/reportCheckinStoreFail")
    public Result<Boolean> reportCheckinStoreFail(@Valid @RequestBody CustomerCheckinStoreFailRequest request) {
        return Result.success(customerCheckinStoreFailListService.report(request));
    }

    @ApiOperation(value = "朋友圈广告用户手机号上报")
    @PostMapping("/wechatMomentPhoneReport")
    public Result<Boolean> wechatMomentPhoneReport(@Valid @RequestBody MomentClientPhoneReportRequest request) {
        return Result.success(customerUserService.wechatMomentPhoneReport(request));
    }

    @ApiOperation(value = "查询朋友圈广告点击报价人数")
    @GetMapping("/queryWechatMomentClickCount")
    public Result<Long> queryWechatMomentClickCount(@RequestParam("brandType") Integer brandType) {
        return Result.success(customerUserService.queryWechatMomentClickCount(brandType));
    }

    @ApiOperation(value = "保存品牌客户第一次登录小程序时的调研问题")
    @PostMapping("/saveBrandResearchQuestion")
    public Result<Boolean> saveBrandResearchQuestion(@Validated @RequestBody BrandResearchQuestionRequest request) {
        return Result.success(customerUserService.saveBrandResearchQuestion(request));
    }



    @ApiOperation(value = "用户列表")
    @GetMapping(value = "/userInfoList")
    public Result<PageVO<UserInfoVO>> list(@Valid UserBasicKeywordSearchRequest userBasicKeywordSearchRequest) {

        PageVO<UserInfoVO> heUserBasicPOPageVO = heUserBasicService.queryByKeyword(userBasicKeywordSearchRequest);

        return Result.success(heUserBasicPOPageVO);
    }

    @ApiOperation(value = "用户蓝宝石会员信息查询")
    @GetMapping(value = "/queryUserSapphireInfo")
    public Result<Integer> queryUserSapphireInfo(@RequestParam("basicId") Integer basicId) {
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(basicId.longValue());
        if (Objects.nonNull(userBasicPO) && userBasicPO.getSapphire().equals(1)) {
            return Result.success(1);
        }
        return Result.success(0);
    }
}
