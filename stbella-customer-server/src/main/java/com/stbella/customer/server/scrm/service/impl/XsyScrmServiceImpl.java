package com.stbella.customer.server.scrm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.cts.enums.CustomerAssetsBrandEnum;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.scrm.ScrmRequestReplace;
import com.stbella.customer.server.scrm.constant.ConvertScrmPicpConstant;
import com.stbella.customer.server.scrm.dto.OrderGoodsDTO;
import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.customer.server.scrm.dto.ScrmCreateClockInStoreDTO;
import com.stbella.customer.server.scrm.entity.ScrmConfigPO;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.enums.PicpOrderTpyeEnum;
import com.stbella.customer.server.scrm.enums.ScrmConfigBizTypeEnum;
import com.stbella.customer.server.scrm.manager.SmsManager;
import com.stbella.customer.server.scrm.request.*;
import com.stbella.customer.server.scrm.service.ScrmConfigService;
import com.stbella.customer.server.scrm.service.XsyScrmService;
import com.stbella.customer.server.scrm.vo.*;
import com.stbella.customer.server.scrm.vo.ScrmDescriptionResultVO.FieldData;
import com.stbella.customer.server.scrm.vo.ScrmDescriptionResultVO.FieldData.SelectItemData;
import com.stbella.customer.server.scrm.vo.ScrmUserResponsibilityVO.Records;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
@DubboService
public class XsyScrmServiceImpl implements XsyScrmService {

    // 成功code码
    public static final String SUCCESS_CODE = "200";

    // 有时候成功code码为0
    public static final String SUCCESS_CODE_MORE = "0";

    // 认证类型；此参数的值必须为 password
    private String grantType = "password";

    // 客户端 ID ；您的集成连接器详情中的 client_id
    @Value("${scrm.xsy.clientId}")
    private String clientId;

    // 客户端秘钥；您的集成连接器详情中的 client_secret
    @Value("${scrm.xsy.clientSecret}")
    private String clientSecret;

    // 用户在销售易系统中的用户名
    @Value("${scrm.xsy.username}")
    private String username;

    // 用户在销售易系统中的账号密码加上 8 位的安全令牌。
    @Value("${scrm.xsy.password}")
    private String password;

    // 销售易登录api(https://api-scrm.xiaoshouyi.com/oauth2/token.action)
    @Value("${scrm.xsy.loginHost}")
    private String loginHost;

    // 销售易api(https://api-scrm.xiaoshouyi.com)
    @Value("${scrm.xsy.apiHost}")
    private String apiHost;

    // 销售易自定义api前缀
    private String customApiPrefix = "/rest/data/v2.0/scripts/api";

    @Resource
    private RedisService redisService;

    @Resource
    private ScrmConfigService scrmConfigService;

    @Resource
    private SmsManager smsManager;

    /**
     * 统计scrm每天每个请求的次数
     */
    private void scrmRequestStat() {
        String methodName = "unknown";
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length > 3) {
            methodName = stackTrace[3].getMethodName();
        }

        String redisKey = "xsy:scrm:request_stat:" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + ":" + methodName;
        redisService.increment(redisKey);
    }

    /**
     * 获取可用的scrm access_token
     *
     * @return
     */
    @Override
    public String getAccessToken(Boolean flush) {
        scrmRequestStat();
        String scrmAccessTokenKey = "xsy:scrm:accessToken";

        if (!flush) {
            String accessToken = redisService.getCacheObject(scrmAccessTokenKey);
            if (ObjectUtil.isNotEmpty(accessToken)) {
                return accessToken;
            }
        }

        // 需要从scrm api获取token
        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", grantType);
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);
        params.put("username", username);
        params.put("password", password);

        String response = HttpUtil.get(loginHost, params);
        Oauth2TokenVO oauth2TokenVO = JSON.parseObject(response, Oauth2TokenVO.class);

        if (StringUtils.isBlank(oauth2TokenVO.getAccessToken())) {
            log.error("获取销售易access token失败，{}", response);
            return null;
        }

        String accessToken = oauth2TokenVO.getTokenType() + " " + oauth2TokenVO.getAccessToken();
        redisService.setCacheObject(scrmAccessTokenKey, accessToken, 86000L, TimeUnit.SECONDS);

        return accessToken;
    }

    /**
     * 获取scrm中的员工信息
     *
     * @param id
     * @return
     */
    public ScrmUserInfoVO getUserInfoBak(Long id) {
        log.info("查询scrm员工信息, id:{}", id);

        String uri = "/rest/data/v2.0/xobjects/user";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中查询员工信息时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询员工信息时获取token失败");
        }

        String response = HttpRequest.get(apiHost + uri + "/" + id)
                .header("Authorization", accessToken)
                .execute()
                .body();

        if (StringUtils.isBlank(response)) {
            log.error("在scrm中查询员工信息时失败,无返回值,入参:{}", id);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询员工信息时失败,无返回值");
        }

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("在scrm中查询员工信息时失败，入参:{}, 返回值:{}", id, response);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询员工信息时失败,原因:" + baseResultVO.getMsg());
        }

        ScrmUserInfoVO scrmUserInfoVO = JSON.parseObject(baseResultVO.getData(),
                ScrmUserInfoVO.class);

        // 获取直属上级手机号
        if (ObjectUtil.isNotEmpty(scrmUserInfoVO) && ObjectUtil.isNotEmpty(scrmUserInfoVO.getManagerId()) && scrmUserInfoVO.getManagerId() > 0) {
            String managerResponse = HttpRequest.get(apiHost + uri + "/" + scrmUserInfoVO.getManagerId())
                    .header("Authorization", accessToken)
                    .execute()
                    .body();

            if (StringUtils.isNotBlank(managerResponse)) {
                BaseResultVO managerResultVO = JSON.parseObject(managerResponse, BaseResultVO.class);

                if (managerResultVO.getCode().equals(SUCCESS_CODE)) {
                    ScrmUserInfoVO managerUserInfoVO = JSON.parseObject(managerResultVO.getData(),
                            ScrmUserInfoVO.class);
                    scrmUserInfoVO.setManagerPhone(managerUserInfoVO.getPhone());
                }
            }
        }

        List<Records> userResponsibilities = getUserResponsibilities(id);
        if (ObjectUtil.isNotEmpty(userResponsibilities)) {
            List<String> userResponsibilityNameList = new ArrayList<>();

            for (Records userResponsibility : userResponsibilities) {
                userResponsibilityNameList.add(userResponsibility.getName());
            }

            scrmUserInfoVO.setResponsibility(userResponsibilityNameList);
        }

        // 查询用户拥有的门店列表
        ScrmCustomUserStoreQuery scrmCustomUserStoreQuery = new ScrmCustomUserStoreQuery();
        scrmCustomUserStoreQuery.setSaleId(id);
        List<ScrmCustomUserStoreVO> scrmCustomUserStoreVOList = queryScrmCustomUserStoreList(
                scrmCustomUserStoreQuery);

        scrmUserInfoVO.setUserStoreList(scrmCustomUserStoreVOList);

        return scrmUserInfoVO;
    }

    /**
     * 获取scrm中的员工信息
     *
     * @param id
     * @return
     */
    @Override
    public ScrmUserInfoVO getUserInfo(Long id) {
        log.info("查询scrm员工信息, id:{}", id);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中查询员工信息时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询员工信息时获取token失败");
        }

        String uri = "/stbella/userinfo/query";

        Map<String, Object> params = new HashMap<>();
        params.put("id", id);

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

        if (StringUtils.isBlank(response)) {
            log.error("在scrm中查询员工信息时失败,无返回值,入参:{}", id);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询员工信息时失败,无返回值");
        }

        log.info("在scrm中查询员工信息, 返回值:{}", response);

        ScrmUserInfoVO scrmUserInfoVO = JSON.parseObject(response, ScrmUserInfoVO.class);
        return ObjectUtil.isNotEmpty(scrmUserInfoVO) ? scrmUserInfoVO : null;
    }

    /**
     * 添加客户到scrm中
     *
     * @param req
     * @return
     */
    @Override
    public Long accountEdit(AccountInfoRequest req) {
        log.info("正在新增或编辑客户, 参数:{}", JSON.toJSONString(req));
        String uri = "/rest/data/v2.0/xobjects/account";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中新增客户时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增客户时获取token失败");
        }

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 客户的scrmid不为空，表示要更新
            req.setCustomItem231__c(null);
            //req.setCustomItem232__c(null);
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新客户, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新客户失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增客户失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中编辑客户失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑客户失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            ScrmConfigBizTypeEnum bizType = null;
            if (ObjectUtil.isNotEmpty(req.getPhone())) {
                bizType = ScrmConfigBizTypeEnum.ACCOUNT_DEFAULT_TYPE;
            } else {
                bizType = ScrmConfigBizTypeEnum.ACCOUNT_BUSINESS_TYPE;
            }

            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(bizType.getCode());
            if (ObjectUtil.isEmpty(scrmConfigPO)) {
                log.error("未配置scrm客户的业务类型");
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm客户的业务类型");
            }

            req.setEntityType(Long.parseLong(scrmConfigPO.getBizContent()));
            req.setCustomerStage__c(1);
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增客户, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增客户失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增客户失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增客户失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增客户失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO baseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(baseDataVO)) {
                return baseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增失败，未获取到客户id");
        }
    }

    /**
     * 新增、编辑销售机会信息
     *
     * @param req
     * @return 返回销售机会在scrm中的id
     */
    @Override
    public Long opportunityEdit(OpportunityRequest req) {
        log.info("正在新增或编辑商机, 参数:{}", JSON.toJSONString(req));

        //强行删除预计回收时间，否则会报错
        req.setTerritoryExpireTime(null);
        //强制删除公海池状态，否则会报错
        req.setTerritoryHighSeaStatus(null);

        if (ObjectUtil.isEmpty(req.getOwnerId()) || ObjectUtil.isEmpty(req.getAccountId())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "商机所属人或客户id不能为空");
        }

        String uri = "/rest/data/v2.0/xobjects/opportunity";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中编辑商机时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑商机时获取token失败");
        }

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 商机的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新客户, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新商机失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增客户失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中编辑商机失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑商机失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // TODO 先默认创建标准月子的商机
            Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
            if (ObjectUtil.isEmpty(scrmConfig)) {
                log.error("未配置scrm销售机会的业务类型");
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm销售机会的业务类型");
            }

            if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())) {
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm销售机会的业务类型");
            }

            if (ObjectUtil.isEmpty(req.getEntityType())) {
                req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())));
            }

            req.setTerritoryHighSeaStatus(1);
            // TODO 公海池，先默认圣贝拉，需要根据门店来获取是圣贝拉或者小贝拉或者是艾屿
            if (ObjectUtil.isNull(req.getStoreType()) || req.getStoreType() == 0) {
                req.setTerritoryHighSeaId(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_SAINT_BELLA.getCode())));
            } else if (req.getStoreType() == 1) {
                req.setTerritoryHighSeaId(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_BABY_BELLA.getCode())));
            } else if (req.getStoreType() == 100) {
                req.setTerritoryHighSeaId(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_BELLA_ISLA.getCode())));
            }
            // 下面的为默认值
            req.setWinRate(10L);
            req.setCloseDate(DateUtil.current());
            // 默认价格表
            req.setPriceId(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_DEFAULT_PRICE.getCode())));
            if (ObjectUtil.isNull(req.getCustomItem173__c())) {
                req.setCustomItem173__c(DateUtil.current());
            }
            // 直接设置为已领取，不然会是被回收的商机
//            req.setTerritoryHighSeaStatus(2);

            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增商机, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增商机失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增商机失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增商机失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增商机失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增商机失败，未获取到商机id");
        }
    }

    /**
     * 通过销售机会id查询销售机会详情
     *
     * @param id
     * @return
     */
    @Override
    public ScrmOpportunityDetailVO queryOpportunityDetail(Long id) {
        log.info("查询scrm商机信息, id:{}", id);

        String uri = "/rest/data/v2.0/xobjects/opportunity";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询商机信息时获取token失败");
        }

        String response = HttpRequest.get(apiHost + uri + "/" + id)
                .header("Authorization", accessToken)
                .execute()
                .body();

        if (StringUtils.isBlank(response)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "查询scrm商机信息失败,无返回值");
        }

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("查询scrm商机信息失败，入参:{}, 返回值:{}", id, response);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "查询scrm商机信息失败,原因:" + baseResultVO.getMsg());
        }

        return JSON.parseObject(baseResultVO.getData(), ScrmOpportunityDetailVO.class);
    }

    /**
     * 新增、编辑订单到scrm
     *
     * @param req
     * @return 返回订单在scrm中的id
     */
    @Override
    public Long orderEdit(OrderInfoRequest req) {
        log.info("正在新增或编辑订单信息, 参数:{}", JSON.toJSONString(req));
        if (ObjectUtil.isEmpty(req.getOwnerId()) || ObjectUtil.isEmpty(req.getCustomItem2__c())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "订单所属人或客户id不能为空");
        }

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中编辑商机时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑商机时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            log.error("未配置scrm业务类型");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.ORDER_OBJECT.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm订单业务对象");
        }

        String uri = "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.ORDER_OBJECT.getCode());

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 订单的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新订单, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            log.info("更新scrm订单结果："+response);

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新订单失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新订单失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中更新订单失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新订单失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // 获取订单类型
            ScrmConfigBizTypeEnum saleStagType = null;
            if (req.getOrderType().equals(PicpOrderTpyeEnum.MONTH_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_MONTH;
            } else if (req.getOrderType().equals(PicpOrderTpyeEnum.SMALL_MONTH_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_SMALL_MONTH;
            } else if (req.getOrderType().equals(PicpOrderTpyeEnum.OTHER_MONTH_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_OTHER_MONTH;
            } else if (req.getOrderType().equals(PicpOrderTpyeEnum.NURSE_OUTSIDE_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_NURSE_OUTSIDE;
            } else if (req.getOrderType().equals(PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_PRODUCTION;
            } else if (req.getOrderType().equals(PicpOrderTpyeEnum.SBRA_ORDER.getCode())) {
                saleStagType = ScrmConfigBizTypeEnum.ORDER_SBRA;
            }

            req.setEntityType(Long.parseLong(scrmConfig.get(saleStagType.getCode())));

            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增订单, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增订单失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增订单失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增订单失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增订单失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增订单失败，未获取到订单id");
        }
    }

    /**
     * 新增、编辑到店打卡记录到scrm
     *
     * @param req
     * @return
     */
    @Override
    public Long checkinStoreEdit(CheckinStoreRequest req) {
        log.info("正在新增或编辑到店打卡信息, 参数:{}", JSON.toJSONString(req));
        if (ObjectUtil.isEmpty(req.getOwnerId()) || ObjectUtil.isEmpty(req.getCustomItem1__c())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "到店打卡所属人或客户手机号不能为空");
        }

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑打卡记录时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.ORDER_OBJECT.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm打卡业务对象");
        }

        String uri = "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.CHECKIN_OBJECT.getCode());

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 打卡的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新打卡, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新打卡失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新打卡失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中更新打卡失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新打卡失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // 获取打卡的业务类型
            req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.CHECKIN_DEFAULT_TYPE.getCode())));

            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增打卡, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增打卡失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增打卡失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增打卡失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增打卡失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增打卡失败，未获取到记录id");
        }
    }

    /**
     * 新增、编辑宝宝信息到scrm
     *
     * @param req
     * @return
     */
    @Override
    public Long babyInfoEdit(ContactInfoRequest req) {
        log.info("正在新增或编辑宝宝, 参数:{}", JSON.toJSONString(req));
        if (ObjectUtil.isEmpty(req.getOwnerId()) || ObjectUtil.isEmpty(req.getAccountId())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "宝宝所属人或客户id不能为空");
        }

        String uri = "/rest/data/v2.0/xobjects/contact";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑宝宝时获取token失败");
        }

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 宝宝的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新宝宝, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新宝宝失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新宝宝失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中更新宝宝失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新宝宝失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
            if (ObjectUtil.isEmpty(scrmConfig)) {
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm宝宝的业务类型");
            }

            if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.CONTACT_BABY.getCode())) {
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm宝宝的业务类型");
            }

            if (StringUtils.isEmpty(req.getContactName())) {
                req.setContactName(req.getNickname__c());
            }
            req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.CONTACT_BABY.getCode())));

            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增宝宝, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                    .header("Authorization", accessToken)
                    .body(JSON.toJSONString(params))
                    .execute()
                    .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增宝宝失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增宝宝失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增宝宝失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增宝宝失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(),
                    ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增宝宝失败，未获取到宝宝id");
        }
    }

    /**
     * 删除scrm中的宝宝信息
     *
     * @param babyScrmId
     * @return
     */
    @Override
    public Boolean babyDelete(Long babyScrmId) {
        log.info("开始删除scrm中的宝宝信息:{}", babyScrmId);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/rest/data/v2.0/xobjects/contact/";

        String response = HttpRequest.delete(apiHost + uri + babyScrmId)
                .header("Authorization", accessToken)
                .execute()
                .body();

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("删除scrm宝宝信息失败:{}", response);
            return false;
        }

        return true;
    }

    /**
     * 查询scrm某个对象中的字段所有的选项列表
     *
     * @param objectKey 对象名
     * @return 选项列表
     */
    @Override
    public Map<String, List<SelectItemData>> getScrmObjectOptionList(String objectKey) {
        String scrmOptionsKey = "xsy:scrm:objectOptions:" + objectKey;

        String scrmOptionsValue = redisService.getCacheObject(scrmOptionsKey);
        if (ObjectUtil.isNotEmpty(scrmOptionsValue)) {
            Map<String, List<SelectItemData>> fieldSelectItemMap = new HashMap<>();
            JSONObject jsonObject = JSON.parseObject(scrmOptionsValue);
            for (String s : jsonObject.keySet()) {
                fieldSelectItemMap.put(s, JSONUtil.toList(jsonObject.get(s).toString(), SelectItemData.class));
            }

            return fieldSelectItemMap;
        }

        String uri = "/rest/data/v2.0/xobjects/" + objectKey + "/description";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        String response = HttpRequest.get(apiHost + uri)
                .header("Authorization", accessToken)
                .execute()
                .body();

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("获取对象描述失败, 返回值:{}", response);
            return null;
        }

        ScrmDescriptionResultVO scrmDescriptionResultVO = JSON.parseObject(baseResultVO.getData(),
                ScrmDescriptionResultVO.class);

        if (ObjectUtil.isEmpty(scrmDescriptionResultVO)) {
            return null;
        }

        Map<String, List<SelectItemData>> fieldSelectItemMap = new HashMap<>();

        for (FieldData field : scrmDescriptionResultVO.getFields()) {
            if (field.getSelectitem().size() > 0) {
                fieldSelectItemMap.put(field.getApiKey(), field.getSelectitem());
            } else if (field.getCheckitem().size() > 0) {
                fieldSelectItemMap.put(field.getApiKey(), field.getCheckitem());
            }
        }

        redisService.setCacheObject(scrmOptionsKey, JSON.toJSONString(fieldSelectItemMap), 1L, TimeUnit.DAYS);

        return fieldSelectItemMap;
    }

    /**
     * scrm中选项id值转换为picp的枚举值
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName   在scrm中的字段枚举值
     * @return 返回picp中的枚举值
     */
    @Override
    public String convertScrmOptionIdToPicpId(String objectKey, String fieldName, Long keyName) {
        Map<String, List<SelectItemData>> scrmObjectOptionList = getScrmObjectOptionList(objectKey);
        return convertScrmOptionIdToPicpId(scrmObjectOptionList, fieldName, keyName);
    }

    /**
     * picp的枚举值转换为scrm中选项id值
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName   在picp中的字段枚举值
     * @return 返回scrm中的枚举值
     */
    @Override
    public Long convertPicpIdToScrmOptionId(String objectKey, String fieldName, String keyName) {
        Map<String, List<SelectItemData>> scrmObjectOptionList = getScrmObjectOptionList(objectKey);
        return convertPicpIdToScrmOptionId(scrmObjectOptionList, fieldName, keyName);
    }

    /**
     * scrm中选项id值转换为对应的label
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName   在scrm中的字段枚举值
     * @return 返回scrm该选项的label名
     */
    @Override
    public String convertScrmOptionIdToLabel(String objectKey, String fieldName, Long keyName) {
        Map<String, List<SelectItemData>> scrmObjectOptionList = getScrmObjectOptionList(objectKey);
        if (!scrmObjectOptionList.containsKey(fieldName)
            || ObjectUtil.isEmpty(scrmObjectOptionList.get(fieldName))) {
            return null;
        }

        Optional<SelectItemData> first = scrmObjectOptionList.get(fieldName).stream()
            .filter(i -> i.getValue().equals(keyName))
            .findFirst();

        return first.map(SelectItemData::getLabel).orElse(null);
    }

    /**
     * scrm中选项id值转换为picp的枚举值
     *
     * @param scrmObjectOptionMap
     * @param fieldName
     * @param keyName
     * @return
     */
    public String convertScrmOptionIdToPicpId(Map<String, List<SelectItemData>> scrmObjectOptionMap, String fieldName, Long keyName) {
        if (!scrmObjectOptionMap.containsKey(fieldName)
            || ObjectUtil.isEmpty(scrmObjectOptionMap.get(fieldName))) {
            return null;
        }

        Optional<SelectItemData> first = scrmObjectOptionMap.get(fieldName).stream()
            .filter(i -> i.getValue().equals(keyName))
            .findFirst();

        return first.map(SelectItemData::getApiKey).orElse(null);
    }

    /**
     * picp中选项id值转换为scrm的枚举值
     *
     * @param scrmObjectOptionMap
     * @param fieldName
     * @param keyName
     * @return
     */
    public Long convertPicpIdToScrmOptionId(Map<String, List<SelectItemData>> scrmObjectOptionMap, String fieldName, String keyName) {
        if (!scrmObjectOptionMap.containsKey(fieldName)
            || ObjectUtil.isEmpty(scrmObjectOptionMap.get(fieldName))) {
            return null;
        }

        Optional<SelectItemData> first = scrmObjectOptionMap.get(fieldName).stream()
            .filter(i -> i.getApiKey().equals(keyName))
            .findFirst();

        return first.map(SelectItemData::getValue).orElse(0L);
    }

    /**
     * 客户模块scrm请求中枚举值转为picp的枚举值
     *
     * @param request
     * @return
     */

    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.SCRM_TO_PICP)
    public AccountInfoRequest converAccountScrmRequestToPicpRequest(AccountInfoRequest request) {
        log.info("客户模块scrm请求中枚举值转为picp的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    /**
     * 客户模块picp请求中枚举值转为scrm的枚举值
     *
     * @param request
     * @return
     */
    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.PICP_TO_SCRM)
    public AccountInfoRequest converAccountPicpRequestToScrmRequest(AccountInfoRequest request) {
        log.info("客户模块picp请求中枚举值转为scrm的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    /**
     * 商机模块scrm请求中枚举值转为picp的枚举值
     *
     * @param request
     * @return
     */
    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.SCRM_TO_PICP)
    public SCRMOpportunityRequest convertOpportunityScrmRequestToPicpRequest(SCRMOpportunityRequest request) {
        log.info("商机模块scrm请求中枚举值转为picp的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    /**
     * 商机模块picp请求中枚举值转为scrm的枚举值
     *
     * @param request
     * @return
     */
    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.PICP_TO_SCRM)
    public SCRMOpportunityRequest convertOpportunityPicpRequestToScrmRequest(SCRMOpportunityRequest request) {
        log.info("商机模块picp请求中枚举值转为scrm的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    /**
     * 商机模块scrm请求中枚举值转为picp的枚举值(OpportunityRequest)
     *
     * @param request
     * @return
     */
    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.SCRM_TO_PICP)
    public OpportunityRequest convertOpportunityScrmRequestToPicpRequest(OpportunityRequest request) {
        log.info("商机模块scrm请求中枚举值转为picp的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    /**
     * 商机模块picp请求中枚举值转为scrm的枚举值(OpportunityRequest)
     *
     * @param request
     * @return
     */
    @Override
    @ScrmRequestReplace(value = ConvertScrmPicpConstant.PICP_TO_SCRM)
    public OpportunityRequest convertOpportunityPicpRequestToScrmRequest(OpportunityRequest request) {
        log.info("商机模块picp请求中枚举值转为scrm的枚举值 参数====={}", JSONUtil.toJsonStr(request));
        return request;
    }

    @Override
    @Async
    public void delTeamMember(Long id) {
        String uri = "/rest/data/v2.0/xobjects/teamMember";
        String accessToken = getAccessToken(false);
        String response = HttpRequest.delete(apiHost + uri + "/" + id)
                .header("Authorization", accessToken)
                .execute()
                .body();
        log.info("回收商机时删除团队成员相应：{}", response);
    }

    @Override
    @Async
    public void addTeamMember(ScrmAddTeamMemberRequest request) {
        log.info("将销售添加到团队成员中req:{}", JSONUtil.toJsonStr(request));
        if (ObjectUtil.isEmpty(request.getEntityType())) {
            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(
                    ScrmConfigBizTypeEnum.TEAM_MEMBER_DEFAULT_TYPE.getCode());
            request.setEntityType(Long.parseLong(scrmConfigPO.getBizContent()));
        }

        String uri = "/rest/data/v2.0/xobjects/teamMember";
        String accessToken = getAccessToken(false);

        Map<String, Object> params = new HashMap<>();
        params.put("data", request);

        String response = HttpRequest.post(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();
        log.info("重新分配商机时将销售添加到团队成员中的响应：{}", response);
    }

    @Override
    public List<ScrmTeamMemberVO> getAllTeamMember(Long customerId) {
        String uri = "/rest/data/v2.0/xobjects/teamMember/actions/list";
        String accessToken = getAccessToken(false);
        String response = HttpRequest.get(apiHost + uri + "?xObjectApiKey=account&recordId=" + customerId + "&status=all")
                .header("Authorization", accessToken)
                .execute()
                .body();

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("获取用户的团队成员失败:{}", response);
            return null;
        }

        List<ScrmTeamMemberVO> scrmTeamMemberVOList = JSON.parseArray(baseResultVO.getData(),
                ScrmTeamMemberVO.class);

        return scrmTeamMemberVOList;
        /*
        LinkedHashMap read = JsonUtil.read(response, LinkedHashMap.class);
        log.info("重新分配商机时将销售添加到团队成员中的响应：{}", read);
        return (ArrayList<LinkedHashMap>) read.get("data");
         */
    }

    /**
     * 发送企微消息
     *
     * @return
     */
    @Override
    public Long batchSendMsg(QwMsgRequest a) {
        String uri = "/rest/data/v2.0/social/qywxMsg/actions/batchSendMsg";

        String accessToken = getWxToken("wwf9bff816ca0697e7", 4);
        QwMsgRequest request = new QwMsgRequest();
        request.setTenantId("2540470778843428");
        QwMsgRequest.Msg msg = new QwMsgRequest.Msg();
        QwMsgRequest.Msg.Text text = new QwMsgRequest.Msg.Text();
        text.setContent("哈哈哈哈哈");
        msg.setText(text);
        request.setMsg(msg);
        request.setSource("1");
        request.setSenderList(Arrays.asList("woapBRBgAAXHSY7owCQt7jmrbIgnggIg"));
        Map<String, Object> params = BeanUtil.beanToMap(request);
        log.info("发送企微消息url={},参数={}", apiHost + uri, JSON.toJSONString(params));
        String response = HttpRequest.post(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();
        log.info("发送企微消息：{}", response);
        return null;
    }

    /**
     * 自建应用发送企微消息
     *
     * @return
     */
    @Override
    public Boolean localBatchSendMsg(LocalQwMsgRequest localQwMsgRequest) {
        return smsManager.localBatchSendMsg(localQwMsgRequest);
    }

    /**
     * 获取员工在scrm中的职能列表
     *
     * @param id
     * @return
     */
    @Override
    public List<Records> getUserResponsibilities(Long id) {
        log.info("获取用户的职能:客户id:{}", id);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/rest/data/v2.0/privileges/users/" + id + "/responsibilities";

        String response = HttpRequest.get(apiHost + uri)
                .header("Authorization", accessToken)
                .execute()
                .body();

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE) && !baseResultVO.getCode().equals(SUCCESS_CODE_MORE)) {
            log.error("获取用户的职能失败:{}", response);
        }

        ScrmUserResponsibilityVO scrmUserResponsibilityVO = JSON.parseObject(baseResultVO.getData(),
                ScrmUserResponsibilityVO.class);

        return ObjectUtil.isNotEmpty(scrmUserResponsibilityVO) ? scrmUserResponsibilityVO.getRecords() : null;
    }

    /**
     * 通过sql方式查询scrm中的客户信息
     *
     * @param query
     * @return
     */
    @Override
    public AccountInfoRequest queryScrmAccount(ScrmAccountQuery query) {
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        List<String> accountFields = queryScrmObjectFields("account");

        String queryUri = "/rest/data/v2/query";

        String sql = "select %s from account where %s order by createdAt desc limit 0,1";
        String where = "";

        if (ObjectUtil.isNotEmpty(query.getScrmCustomerId())) {
            where += "id=" + query.getScrmCustomerId();
        } else if (ObjectUtil.isNotEmpty(query.getPhone())) {
            where += "phone=" + query.getPhone();
        } else {
            throw new BusinessException("查询scrm客户信息时客户id和手机号不能同时为空");
        }

        sql = String.format(sql, StringUtils.join(accountFields, ","), where);

        log.info("获取客户信息sql："+sql);

        String accountResponse = HttpRequest.get(apiHost + queryUri + "?q=" + sql)
                .header("Authorization", accessToken)
                .execute()
                .body();

        log.info("获取客户信息响应："+accountResponse);

        ScrmBaseQueryDataVO scrmBaseQueryDataVO = JSON.parseObject(accountResponse,
                ScrmBaseQueryDataVO.class);

        List<AccountInfoRequest> accountList = JSON.parseArray(
                scrmBaseQueryDataVO.getResult().getRecords(), AccountInfoRequest.class);
        if (ObjectUtil.isNotEmpty(accountList)) {
            return accountList.get(0);
        }

        return null;
    }

    /**
     * 通过SQL方式查询scrm中的商机列表
     *
     * @param query
     * @return
     */
    @Override
    public List<OpportunityRequest> queryScrmOpportunityList(ScrmOpportunityQuery query) {
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        List<String> fields = queryScrmObjectFields("opportunity");

        String queryUri = "/rest/data/v2/query";

        String sql = "select %s from opportunity where accountId=%d and entityType=%d and saleStageId in (%s) and customItem174__c=%s order by createdAt desc";

        sql = String.format(sql, StringUtils.join(fields, ","), query.getAccountId(), query.getEntityType(), StringUtils.join(query.getSaleStageId(), ","), query.getCustomUserStoreId());
        String accountResponse = HttpRequest.get(apiHost + queryUri + "?q=" + sql)
                .header("Authorization", accessToken)
                .execute()
                .body();
        ScrmBaseQueryDataVO scrmBaseQueryDataVO = JSON.parseObject(accountResponse,
                ScrmBaseQueryDataVO.class);

        if (scrmBaseQueryDataVO.getCode().equals(200)) {
            return JSON.parseArray(scrmBaseQueryDataVO.getResult().getRecords(), OpportunityRequest.class);
        }

        return null;
    }

    @Override
    @Async
    public void signInInformationBindOrUnBindOpportunity(Long signInInformation, Integer bindOrUnBind) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        String uri = apiHost + "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.CHECKIN_OBJECT.getCode()) + "/" + signInInformation;
        log.info("扫码请求url:{}",uri);
        String accessToken = getAccessToken(false);
        String response = HttpRequest.get(uri)
                .header("Authorization", accessToken)
                .execute()
                .body();
        log.info("获取扫码详情：{}",response);
        getSignInInformation(signInInformation);

        LinkedHashMap read = JsonUtil.read(response, LinkedHashMap.class);
        if ((HttpStatus.HTTP_OK + "").equals(read.get("code"))) {
            LinkedHashMap data = (LinkedHashMap) read.get("data");
            if (ObjectUtil.isNotEmpty(read) && ObjectUtil.isNotEmpty(data)) {
                data.put("customItem5__c", bindOrUnBind);
                Map<String, Object> params = new HashMap<>();
                params.put("data", data);
                log.info("开始在scrm中更新打卡, 参数:{}", JSON.toJSONString(params));
                response = HttpRequest.patch(uri)
                        .header("Authorization", accessToken)
                        .body(JSON.toJSONString(params))
                        .execute()
                        .body();
                log.info("修改扫码：{}",response);
                if (StringUtils.isBlank(response)) {
                    log.error("在scrm中更新打卡失败,无返回值,入参:{}", JSON.toJSONString(params));
                    throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新打卡失败,无返回值");
                }
            }
        } else {
            log.error("扫码信息获取详情失败：resp:{},id:{},bindOrUnBind:{}", response, signInInformation, bindOrUnBind);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "扫码信息获取详情失败");
        }
    }



    @Override
    public ScrmCreateClockInStoreDTO getSignInInformation(Long signInInformation){
        ScrmCreateClockInStoreDTO result = new ScrmCreateClockInStoreDTO();
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        String uri = apiHost + "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.CHECKIN_OBJECT.getCode()) + "/" + signInInformation;
        log.info("扫码请求url:{}",uri);
        String accessToken = getAccessToken(false);
        String response = HttpRequest.get(uri)
                .header("Authorization", accessToken)
                .execute()
                .body();
        log.info("获取扫码详情：{}",response);

        LinkedHashMap read = JsonUtil.read(response, LinkedHashMap.class);
        if ((HttpStatus.HTTP_OK + "").equals(read.get("code"))) {
            LinkedHashMap data = (LinkedHashMap) read.get("data");
            if (ObjectUtil.isNotEmpty(read) && ObjectUtil.isNotEmpty(data)) {
                Object customItem6__c = data.get("checkinTime__c");
                Object customItem6__1 = data.get("customItem1__c");
                Object customItem3__1 = data.get("customItem3__c");
                if(ObjectUtil.isNotEmpty(customItem6__c)){
                    result.setSignTime(new Date(new Long(customItem6__c.toString())));
                }
                if(ObjectUtil.isNotEmpty(customItem6__1)){
                    result.setPhone(customItem6__1.toString());
                }
                if(ObjectUtil.isNotEmpty(customItem3__1)){
                    result.setStoreId(new Long(customItem3__1.toString()));
                }
                return result;
            }
        } else {
            log.error("扫码信息获取详情失败：resp:{},id:{}", response, signInInformation);
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "扫码信息获取详情失败");
        }
        return result;
    }

    /**
     * 通过SQL方式查询scrm中的销售与门店关系列表
     *
     * @param query
     * @return
     */
    @Override
    public List<ScrmCustomUserStoreVO> queryScrmCustomUserStoreList(
            ScrmCustomUserStoreQuery query) {
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(
                ScrmConfigBizTypeEnum.CUSTOM_USER_STORE_OBJECTT.getCode());

        if (ObjectUtil.isNull(scrmConfigPO)) {
            log.error("销售与门店关系表未设置");
            return null;
        }

        String queryUri = "/rest/data/v2/query";

        String sql = "select id,customItem3__c,customItem4__c,customItem5__c from %s where %s order by createdAt desc";

        List<String> whereList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(query.getId())) {
            whereList.add("id=" + query.getId());
        }

        if (ObjectUtil.isNotEmpty(query.getSaleId())) {
            whereList.add("customItem3__c=" + query.getSaleId());
        }

        if (ObjectUtil.isNotEmpty(query.getStoreId())) {
            // 要转成scrm中的门店id
            Long scrmStoreId = convertPicpIdToScrmOptionId(scrmConfigPO.getBizContent(),
                    "customItem4__c", query.getStoreId().toString());
            whereList.add("customItem4__c=" + scrmStoreId);
        }

        sql = String.format(sql, scrmConfigPO.getBizContent(), StringUtils.join(whereList, " and "));
        log.info("查询scrm销售与门店关联关系表sql: {}", sql);
        String accountResponse = HttpRequest.get(apiHost + queryUri + "?q=" + sql)
                .header("Authorization", accessToken)
                .execute()
                .body();
        log.info("查询scrm销售与门店关联关系表结果:{}", accountResponse);
        ScrmBaseQueryDataVO scrmBaseQueryDataVO = JSON.parseObject(accountResponse,
                ScrmBaseQueryDataVO.class);

        if (scrmBaseQueryDataVO.getCode().equals(200)) {
            List<ScrmCustomUserStoreVO> scrmCustomUserStoreVOList = JSON.parseArray(
                    scrmBaseQueryDataVO.getResult().getRecords(), ScrmCustomUserStoreVO.class);

            for (ScrmCustomUserStoreVO scrmCustomUserStoreVO : scrmCustomUserStoreVOList) {
                String picpStoreId = convertScrmOptionIdToPicpId(scrmConfigPO.getBizContent(),
                        "customItem4__c", scrmCustomUserStoreVO.getStoreId());

                scrmCustomUserStoreVO.setStoreId(Long.parseLong(picpStoreId));
            }

            return scrmCustomUserStoreVOList;
        }

        return null;
    }

    /**
     * 通过自定义接口同步宝宝信息
     *
     * @param req
     * @return
     */
    @Override
    public Long babySync(ContactInfoRequest req) {
        log.info("通过自定义接口同步宝宝信息, 参数:{}", JSON.toJSONString(req));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("通过自定义接口同步宝宝信息时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "通过自定义接口同步宝宝信息时获取token失败");
        }

        String uri = "/stbella/baby/sync";

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSON.toJSONString(req))
            .execute()
            .body();

        if (StringUtils.isBlank(response)) {
            log.error("通过自定义接口同步宝宝信息时失败,无返回值,入参:{}", JSON.toJSONString(req));
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "通过自定义接口同步宝宝信息时失败,无返回值");
        }

        return Long.parseLong(response);
    }

    /**
     * 商机中间表编辑更新
     *
     * @param req
     * @return
     */
    @Override
    public Long opportunityTempEdit(OpportunityTempRequest req) {
        log.info("正在新增或编辑商机中间表信息, 参数:{}", JSON.toJSONString(req));
        if (ObjectUtil.isEmpty(req.getOwnerId()) || ObjectUtil.isEmpty(req.getCustomItem1__c())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "记录所属人或销售机会id不能为空");
        }

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "正在新增或编辑商机中间表信息时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.OPPORTUNITY_TEMP_OBJECT.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm商机中间表对象");
        }

        if (ObjectUtil.isEmpty(req.getEntityType())) {
            req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_TEMP_DEFAULT_TYPE.getCode())));
        }

        String uri = "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_TEMP_OBJECT.getCode());

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 商机中间表的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中编辑商机中间表, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            log.info("更新商机中间表结果:{}",response);

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中编辑商机中间表失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑商机中间表失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中编辑商机中间表失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑商机中间表失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // 获取商机中间表的业务类型


            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增商机中间表, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            log.info("新增商机中间表结果:{}",response);

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增商机中间表,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "开始在scrm中新增商机中间表失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增商机中间表，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "开始在scrm中新增商机中间表失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "开始在scrm中新增商机中间表失败，未获取到记录id");
        }
    }

    /**
     * 删除商机中间表的数据
     *
     * @param scrmId
     * @return
     */
    @Override
    public Long opportunityTempDelete(Long scrmId) {
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "正在删除商机中间表信息时获取token失败");
        }

        String uri = "/stbella/opportunityTemp/deleteRecord?scrmId=" + scrmId;
        String deleteResult = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        log.info("删除商机中间表数据，scrmId:{}, 删除结果返回值:{}", scrmId, JSON.toJSONString(deleteResult));
        return scrmId;
    }

    @Override
    @Async
    public void batchUpdateActivityOwnerId(Long opportunityId, Long ownerId) {
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "批量更新活动记录获取token失败");
        }

        String uri = "/stbella/activity/batch/updateOwnerId";
        Map<String, Object> params = new HashMap<>();
        params.put("opportunityId", opportunityId);
        params.put("newOwnerId", ownerId);

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
                .header("Authorization", accessToken)
                .body(JSONUtil.toJsonStr(params))
                .execute()
                .body();
        log.info("批量更新活动记录返回："+response);

    }

    @Override
    @Async
    public void reclaimBusinessOpportunity(Long opportunityId) {

        String uri = "/rest/data/v2.0/xobjects/territory/actions/recycle";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "批量更新活动记录获取token失败");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("objectApiKey", "opportunity");
        params.put("ids", Arrays.asList(opportunityId));

        String response = HttpRequest.put(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSONUtil.toJsonStr(params))
                .execute()
                .body();
        log.info("回收商机入参：{}，出参：{}",params,response);
    }

    /**
     * 400分配客资中间表编辑更新
     *
     * @param req
     * @return
     */
    @Override
    public Long userAssignAccountTempEdit(ScrmAssignAccountTempRequest req) {
        log.info("正在新增或编辑400分配客资中间表信息, 参数:{}", JSON.toJSONString(req));
        if (ObjectUtil.isEmpty(req.getCustomItem1__c()) || ObjectUtil.isEmpty(req.getCustomItem2__c())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "记录所属人或销售机会id不能为空");
        }

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "正在新增或编辑400分配客资中间表信息时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.ASSIGN_ACCOUNT_TEMP_OBJECT.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm商机中间表对象");
        }

        if (ObjectUtil.isEmpty(req.getEntityType())) {
            req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.ASSIGN_ACCOUNT_TEMP_DEFAULT_TYPE.getCode())));
        }

        String uri = "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.ASSIGN_ACCOUNT_TEMP_OBJECT.getCode());

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 商机中间表的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中编辑400分配客资中间表, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            log.info("更新400分配客资中间表结果:{}",response);

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中编辑400分配客资中间表失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑400分配客资中间表失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中编辑400分配客资中间表失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑400分配客资中间表失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // 获取商机中间表的业务类型
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增400分配客资中间表, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            log.info("新增400分配客资中间表结果:{}",response);

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增400分配客资中间表,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增400分配客资中间表,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增400分配客资中间表，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增400分配客资中间表,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增400分配客资中间表，未获取到记录id");
        }
    }

    /**
     * 查询scrm中团队成员的对象信息
     *
     * @param type     对象id，比如客户、联系人等，在ScrmConfigBizTypeEnum中可以找到
     * @param userId   用户id
     * @param recordId 记录id，可以是客户id、联系人id、商机id等
     * @return
     */
    @Override
    public ScrmTeamMemberObjectVO queryTeamMemberInfo(Long type, Long userId, Long recordId) {
        log.info("正在查询scrm中团队成员的信息, 类型:{}, 用户id:{}, 被查询的记录id:{}", type, userId, recordId);
        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        List<String> fields = Arrays.asList("id", "createdAt", "createdBy", "recordFrom", "recordFrom_data", "userId");

        String queryUri = "/rest/data/v2/query";

        String sql = "select %s from teamMember where recordFrom=%d and userId=%d and recordFrom_data=%d order by createdAt desc limit 1";
        log.info("查询scrm中团队成员的信息，sql:{}", sql);

        sql = String.format(sql, StringUtils.join(fields, ","), type, userId, recordId);
        String accountResponse = HttpRequest.get(apiHost + queryUri + "?q=" + sql)
            .header("Authorization", accessToken)
            .execute()
            .body();
        ScrmBaseQueryDataVO scrmBaseQueryDataVO = JSON.parseObject(accountResponse,
            ScrmBaseQueryDataVO.class);
        log.info("查询到团队成员的信息结果为:{}", accountResponse);

        if (scrmBaseQueryDataVO.getCode().equals(200)) {
            if (StringUtils.isNotBlank(scrmBaseQueryDataVO.getResult().getRecords())) {
                JSONArray records = JSONArray.parseArray(
                    scrmBaseQueryDataVO.getResult().getRecords());

                return JSON.parseObject(records.get(0).toString(), ScrmTeamMemberObjectVO.class);
            }
        }

        log.info("查询到团队成员的信息失败, 结果为:{}", accountResponse);
        return null;
    }

    /**
     * 通过销售与门店关系中间表的id查询门店品牌记录id
     *
     * @param saleStoreRecordId
     * @return
     */
    @Override
    public Long queryStoreBrandRecordIdBySaleStoreId(Long saleStoreRecordId) {
        log.info("通过销售与门店关系中间表的id查询门店品牌记录id, id:{}", saleStoreRecordId);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("通过销售与门店关系中间表的id查询门店品牌记录id时获取token失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "通过销售与门店关系中间表的id查询门店品牌记录id时获取token失败");
        }

        String uri = "/stbella/storeBrand/query?saleStoreRecordId=" + saleStoreRecordId;

        String response = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        log.info("通过销售与门店关系中间表的id查询门店品牌记录id, 返回值:{}", response);

        if (StringUtils.isBlank(response)) {
            return 0L;
        }

//        log.info("通过销售与门店关系中间表的id查询门店品牌记录id, 返回值:{}", response);
        return Long.valueOf(response);
    }

    /**
     * 保存退单记录到scrm中
     *
     * @param req
     * @return
     */
    @Override
    public Long orderRefundEdit(ScrmOrderRefundRequest req) {
        log.info("正在新增或编辑退单信息, 参数:{}", JSON.toJSONString(req));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中编辑退单记录时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.ORDER_REFUND_OBJECT.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm退单业务对象");
        }

        String uri = "/rest/data/v2.0/xobjects/" + scrmConfig.get(ScrmConfigBizTypeEnum.ORDER_REFUND_OBJECT.getCode());

        if (ObjectUtil.isNotEmpty(req.getId())) {
            // 退单的scrmid不为空，表示要更新
            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中更新退单, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.patch(apiHost + uri + "/" + req.getId())
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中更新退单失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新退单失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中更新退单失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中更新退单失败,原因:" + baseResultVO.getMsg());
            }
            return req.getId();
        } else {
            // 要新建
            // 获取退单的业务类型
            req.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.ORDER_REFUND_DEFAULT_TYPE.getCode())));

            Map<String, Object> params = new HashMap<>();
            params.put("data", req);

            log.info("开始在scrm中新增退单, 参数:{}", JSON.toJSONString(params));

            String response = HttpRequest.post(apiHost + uri)
                .header("Authorization", accessToken)
                .body(JSON.toJSONString(params))
                .execute()
                .body();

            if (StringUtils.isBlank(response)) {
                log.error("在scrm中新增退单失败,无返回值,入参:{}", JSON.toJSONString(params));
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增退单失败,无返回值");
            }

            BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
            if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
                log.error("在scrm中新增退单失败，入参:{}, 返回值:{}", JSON.toJSONString(params), response);
                throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增退单失败,原因:" + baseResultVO.getMsg());
            }

            ScrmBaseDataVO scrmBaseDataVO = JSON.parseObject(baseResultVO.getData(), ScrmBaseDataVO.class);

            if (ObjectUtil.isNotEmpty(scrmBaseDataVO)) {
                return scrmBaseDataVO.getId();
            }

            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增退单失败，未获取到记录id");
        }
    }

    /**
     * 通过员工在scrm中的id列表，查询这一批次的用户角色和部门
     *
     * @param ids
     * @return
     */
    @Override
    public List<ScrmUserResponsibilyQueryVO> queryUserSaleRoleByIds(List<Long> ids) {
        log.info("批量获取员工的职能信息:{}", JSONUtil.toJsonStr(ids));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/stbella-picp-init/responsibily/query";

        Map<String, Object> params = new HashMap<>();
        params.put("userIds", ids);

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(params))
            .execute()
            .body();

        log.info("批量获取员工的职能信息响应:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.error("批量获取员工的职能信息失败");
            return null;
        }

        return JSONArray.parseArray(response, ScrmUserResponsibilyQueryVO.class);
    }

    /**
     * 初始化客户的商机及订单
     *
     * @param req
     * @return
     */
    @Override
    public PicpUserInitVO picpInitOpportunity(PicpUserInitRequest req) {
        log.info("初始化客户的商机及订单, 参数:{}", JSON.toJSONString(req));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/stbella-picp-init/opportunity";

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(req))
            .execute()
            .body();

        log.info("初始化客户的商机及订单返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.info("初始化客户的商机及订单返回值为空");
            return null;
        }

        return JSONObject.parseObject(response, PicpUserInitVO.class);
    }

    /**
     * 满意度调查编辑
     *
     * @param req
     * @return
     */
    @Override
    public Long satisfactionInvestigateEdit(ScrmSatisfactionInvestigateRequest req) {
        log.info("正在新增或编辑满意度调查, 参数:{}", JSON.toJSONString(req));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增或编辑满意度调查时获取token失败");
        }

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if (ObjectUtil.isEmpty(scrmConfig)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm业务类型");
        }

        if (!scrmConfig.containsKey(ScrmConfigBizTypeEnum.SATISFACTIONINVESTIGATE_DEFAULT_TYPE.getCode())) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "未配置scrm满意度调查对象");
        }

        Long createUserId = Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode()));

        String uri = "/stbella/satisfactionInvestigateEdit";
        req.setEntityType(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SATISFACTIONINVESTIGATE_DEFAULT_TYPE.getCode())));
        req.setOwnerId(createUserId);
        req.setCreatedBy(createUserId);
        req.setCreatedAt(new Date().getTime());
        req.setUpdatedBy(createUserId);
        req.setUpdatedAt(new Date().getTime());

        if (ObjectUtil.isNotNull(req.getMainNurseList())) {
            for (int i = 0; i < req.getMainNurseList().size(); i++) {
                req.getMainNurseList().get(i).setEntityType(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SATISFACTIONINVESTIGATE_MAIN_NURSE_DEFAULT_TYPE.getCode())));
                req.getMainNurseList().get(i).setOwnerId(createUserId);
                req.getMainNurseList().get(i).setCreatedBy(createUserId);
                req.getMainNurseList().get(i).setCreatedAt(new Date().getTime());
                req.getMainNurseList().get(i).setUpdatedBy(createUserId);
                req.getMainNurseList().get(i).setUpdatedAt(new Date().getTime());
            }
        }

        if (ObjectUtil.isNotNull(req.getOtherNurseList())) {
            for (int i = 0; i < req.getOtherNurseList().size(); i++) {
                req.getOtherNurseList().get(i).setEntityType(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SATISFACTIONINVESTIGATE_OTHER_NURSE_DEFAULT_TYPE.getCode())));
                req.getOtherNurseList().get(i).setOwnerId(createUserId);
                req.getOtherNurseList().get(i).setCreatedBy(createUserId);
                req.getOtherNurseList().get(i).setCreatedAt(new Date().getTime());
                req.getOtherNurseList().get(i).setUpdatedBy(createUserId);
                req.getOtherNurseList().get(i).setUpdatedAt(new Date().getTime());
            }
        }

        SatisfactionInvestigateRequest satisfactionInvestigateRequest = new SatisfactionInvestigateRequest();
        satisfactionInvestigateRequest.setSatisfactionInvestigate(req);
        satisfactionInvestigateRequest.setSatisfactionInvestigateMainNurseList(req.getMainNurseList());
        satisfactionInvestigateRequest.setSatisfactionInvestigateOtherNurseList(req.getOtherNurseList());

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(satisfactionInvestigateRequest))
            .execute()
            .body();

        log.info("新增或编辑满意度调查返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.info("新增或编辑满意度调查返回值为空");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals("00000")) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, responseObject.getString("msg"));
        }

        return responseObject.getLong("data");
    }

    /**
     * 通过id查询满意度调查信息
     *
     * @param id
     * @return
     */
    @Override
    public ScrmSatisfactionInvestigateRequest querySatisfactionInvestigateById(Long id) {
        log.info("正在查询满意度调查, 参数:{}", id);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询满意度调查时获取token失败");
        }

        String uri = "/stbella/querySatisfactionInvestigate?id=" + id;
        String response = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        log.info("正在查询满意度调查返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.info("正在查询满意度调查返回值为空");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals("00000")) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, responseObject.getString("msg"));
        }

        return responseObject.getObject("data", ScrmSatisfactionInvestigateRequest.class);
    }

    /**
     * 修改scrm客户的会员等级
     *
     * @param scrmId
     * @param growthLevelId
     * @param brandType
     */
    @Override
    @Async
    public void updateCustomerGrowthLevel(Long scrmId, Long growthLevelId, Integer brandType) {
        log.info("修改客户会员等级, scrmId:{}, levelId:{}", scrmId, growthLevelId);
        AccountInfoRequest request = new AccountInfoRequest();
        request.setId(scrmId);
        Long customerLevel = convertPicpIdToScrmOptionId("account", "customerLevel__c", growthLevelId.toString());
        request.setCustomerLevel__c(customerLevel.intValue());

        /*
        if (brandType.equals(CustomerAssetsBrandEnum.BRAND_SBL.getCode())) {
            Long saintBellaLevelId = convertPicpIdToScrmOptionId("account", "customItem242__c",
                growthLevelId.toString());

            request.setCustomItem242__c(saintBellaLevelId.intValue());
        } else if (brandType.equals(CustomerAssetsBrandEnum.BRAND_XBL.getCode())) {
            // 小贝拉逻辑
            Long saintBellaLevelId = convertPicpIdToScrmOptionId("account", "customItem261__c",
                growthLevelId.toString());

            request.setCustomItem261__c(saintBellaLevelId.intValue());
        } else if (brandType.equals(CustomerAssetsBrandEnum.BRAND_ISLA.getCode())) {
            // 艾屿逻辑
            Long islaLevelId = convertPicpIdToScrmOptionId("account", "islaMemberLevel__c",
                growthLevelId.toString());

            request.setIslaMemberLevel__c(islaLevelId.intValue());
        }*/
        accountEdit(request);
    }

    /**
     * 修改scrm客户名称和性别
     *
     * @param scrmId   scrm id
     * @param userName user name
     * @param gender   gender
     * <AUTHOR>
     * @date 2023/10/10 10:43:11
     * @since 1.0.0
     */
    @Override
    @Async
    public void updateCustomerUserNameAndGender(Long scrmId, String userName, Integer gender) {
        AccountInfoRequest request = new AccountInfoRequest();
        request.setId(scrmId);
        request.setAccountName(userName);
        request.setGender__c(gender);
        accountEdit(request);
    }

    /**
     * 通过职能查询该职能所有用户信息
     *
     * @param responsibilityKey
     * @return
     */
    @Override
    public List<ScrmUserInfoVO> queryUserInfoListByResponsibilityKey(String responsibilityKey) {
        log.info("正在查询职能对应的全部用户, 参数:{}", responsibilityKey);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中查询职能对应的全部用户时获取token失败");
        }

        String uri = "/stbella/queryUserListByResponsibilityKey?responsibilityKey=" + responsibilityKey;
        String response = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        log.info("正在查询职能对应的全部用户返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.info("正在查询职能对应的全部用户返回值为空");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals(ResultEnum.SUCCESS.getCode())) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, responseObject.getString("msg"));
        }

        return JSON.parseArray(responseObject.getJSONArray("data").toJSONString(), ScrmUserInfoVO.class);
    }

    /**
     * 在scrm中创建销售与门店关系记录
     *
     * @param request
     * @return
     */
    @Override
    public Long createScrmUserStoreConfig(ScrmUserStoreConfigOperateRequest request) {
        log.info("在scrm中创建销售与门店关系记录, 参数:{}", JSON.toJSONString(request));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/stbella/storeBrand/createUserStoreConfig";

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(request))
            .execute()
            .body();

        log.info("在scrm中创建销售与门店关系记录返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.error("在scrm中创建销售与门店关系记录返回值为null");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals(ResultEnum.SUCCESS.getCode())) {
            log.error("在scrm中创建销售与门店关系记录失败, message:{}", responseObject.getString("msg"));
            return null;
        }

        return responseObject.getLong("data");
    }

    /**
     * 转移商机给新的所有人
     *
     * @param opportunityId
     * @param newOwnerId
     * @return
     */
    @Override
    public OpportunityTransferVO opportunityTransfer(Long opportunityId, Long newOwnerId) {
        log.info("scrm转移商机:{}给新的用户:{}", opportunityId, newOwnerId);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("opportunityId", opportunityId);
        params.put("targetUserId", newOwnerId);

        String uri = "/stbella/opportunity/transfer";

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(params))
            .execute()
            .body();

        log.info("scrm转移商机返回值:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.error("scrm转移商机返回值返回值为null");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals(ResultEnum.SUCCESS.getCode())) {
            log.error("scrm转移商机返回值失败, message:{}", responseObject.getString("msg"));
            return null;
        }

        return JSONUtil.toBean(responseObject.getString("data"), OpportunityTransferVO.class);
    }

    /**
     * 查询手机号对应的scrm打卡列表
     *
     * @param phone
     * @return
     */
    @Override
    public List<ScrmCheckinStoreRequest> queryScrmCheckinStoreListByPhone(String phone) {
        log.info("查询scrm中某手机号对应的所有打卡记录:{}", phone);

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/stbella/checkinStore/list?phone=" + phone;

        String response = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        log.info("查询scrm中某手机号对应的所有打卡记录:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.error("查询scrm中某手机号对应的所有打卡记录返回值为null");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals(ResultEnum.SUCCESS.getCode())) {
            log.error("查询scrm中某手机号对应的所有打卡记录返回值失败, message:{}", responseObject.getString("msg"));
            return null;
        }

        return JSONUtil.toList(responseObject.getString("data"), ScrmCheckinStoreRequest.class);
    }

    /**
     * 在scrm中新增到店记录回访表
     *
     * @param request
     * @return
     */
    @Override
    public Long addCheckinStoreReturnVisit(CheckinStoreReturnVisitRequest request) {
        log.info("在scrm中新增到店记录回访表:{}", JSONUtil.toJsonStr(request));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("在scrm中新增到店记录回访表");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在scrm中新增到店记录回访表");
        }

        String uri = "/stbella/checkinStore/addVisitRecord";

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(request))
            .execute()
            .body();

        log.info("在scrm中新增到店记录回访表:{}", response);
        if (ObjectUtil.isEmpty(response)) {
            log.error("在scrm中新增到店记录回访表返回值为null");
            return null;
        }

        JSONObject responseObject = JSON.parseObject(response);
        if (!responseObject.getString("code").equals(ResultEnum.SUCCESS.getCode())) {
            log.error("在scrm中新增到店记录回访表失败, message:{}", responseObject.getString("msg"));
            return null;
        }

        return responseObject.getLong("data");
    }

    /**
     * 订单中的商品列表同步到scrm中
     *
     * @param orderGoodsDTOList
     */
    @Override
    public void orderGoodsSync(List<OrderGoodsDTO> orderGoodsDTOList) {
        log.info("订单中的商品列表同步到scrm, 参数:{}", JSONUtil.toJsonStr(orderGoodsDTOList));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在订单中的商品列表同步到scrm时获取token失败");
        }

        Map<String, List<OrderGoodsDTO>> params = new HashMap<>();
        params.put("data", orderGoodsDTOList);

        String uri = "/stbella/order/goods/sync";
        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(params))
            .execute()
            .body();

        log.info("订单中的商品列表同步到scrm结果:{}", response);
    }

    /**
     * 订单减免记录同步到scrm中
     *
     * @param recordDTO
     */
    @Override
    public void orderReductionRecordSync(OrderReductionRecordDTO recordDTO) {
        log.info("订单减免记录同步到scrm, 参数:{}", JSONUtil.toJsonStr(recordDTO));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在订单减免记录同步到scrm时获取token失败");
        }

        String uri = "/stbella/order/reduction/record/sync";
        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .body(JSONUtil.toJsonStr(recordDTO))
            .execute()
            .body();

        log.info("订单减免记录同步到scrm结果:{}", response);
    }

    /**
     * 移除团队成员
     *
     * @param request
     */
    @Override
    public void removeTeamMember(ScrmTeamMemberRecordRequest request) {
        log.info("移除团队成员, 参数:{}", JSONUtil.toJsonStr(request));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "在移除团队成员时获取token失败");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("recordId", request.getRecordId());
        params.put("userId", request.getUserId());
        params.put("type", request.getType());

        String uri = "/stbella/removeTeamMember";
        String response = HttpRequest.get(apiHost + customApiPrefix + uri)
            .header("Authorization", accessToken)
            .form(params)
            .execute()
            .body();

        log.info("移除团队成员结果:{}", response);
    }

    /**
     * 根据对象名称查询字段列表
     *
     * @param objectName
     * @return
     */
    private List<String> queryScrmObjectFields(String objectName) {
        String uri = "/rest/data/v2.0/xobjects/" + objectName + "/description";

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            return null;
        }

        String response = HttpRequest.get(apiHost + uri)
            .header("Authorization", accessToken)
            .execute()
            .body();

        BaseResultVO baseResultVO = JSON.parseObject(response, BaseResultVO.class);
        if (!baseResultVO.getCode().equals(SUCCESS_CODE)) {
            log.error("获取对象描述失败, 返回值:{}", response);
            return null;
        }

        ScrmDescriptionResultVO scrmDescriptionResultVO = JSON.parseObject(baseResultVO.getData(),
            ScrmDescriptionResultVO.class);

        if (ObjectUtil.isEmpty(scrmDescriptionResultVO)) {
            return null;
        }

        List<String> fieldList = scrmDescriptionResultVO.getFields()
            .stream()
            .filter(FieldData::getEnabled)
            .map(FieldData::getApiKey)
            .collect(Collectors.toList());

        if (!fieldList.contains("id")) {
            fieldList.add("id");
        }

        return fieldList;
    }

    private String getWxToken(String corpId, Integer appType) {
        String uri = "/rest/data/v2.0/qywx/inner/inside/actions/get-inner-token";
        String accessToken = getAccessToken(false);
        String response = HttpRequest.get(apiHost + uri + "?corpId=" + corpId + "&appType=" + appType)
                .header("Authorization", accessToken)
                .execute()
                .body();
        log.info("获取企微token：{}", response);
        final cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(response).getJSONObject("result");
        return jsonObject.get("accessToken").toString();
    }

    @Override
    @Async
    public void delTeamMember(Long customerId, Long ownerId) {
        //获取这个商机的所有的团队成员
        List<ScrmTeamMemberVO> allTeamMember = getAllTeamMember(customerId);
        //需要自动剔除团队成员中对应销售机会下的销售
        List<Long> ids = new ArrayList<>();
        for (ScrmTeamMemberVO scrmTeamMemberVO : allTeamMember) {
            if (scrmTeamMemberVO.getUserInfo().getId().toString().equals(ownerId.toString())) {
                ids.add(scrmTeamMemberVO.getUserInfo().getId());
            }
        }
        if (CollectionUtil.isNotEmpty(ids)) {
            for (Long id : ids) {
                delTeamMember(id);
            }
        }
    }


    /**
     * @param orderInfoRequestList
     * @return {@link List}<{@link OrderSyncResultVO}>
     */
    @Override
    public OrderSyncResultVO syncBatchOrder(List<OrderInfoRequest> orderInfoRequestList) {
        log.info("批量同步订单请求参数:{}", JSONUtil.toJsonStr(orderInfoRequestList));

        String accessToken = getAccessToken(false);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取scrmtoken失败");
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "获取scrmtoken失败");
        }

        String uri = "/stbella/order/sync";
        Map<String, Object> params = new HashMap<>();
        params.put("data", orderInfoRequestList);

        String response = HttpRequest.post(apiHost + customApiPrefix + uri)
                .header("Authorization", accessToken)
                .body(JSONUtil.toJsonStr(params))
                .execute()
                .body();
        log.info("scrm批量同步订单原始响应:{}", response);
        OrderSyncResultVO orderSyncResultVO = new OrderSyncResultVO();
        //todo 返回空字符串 scrm调用异常
        if (ObjectUtil.isEmpty(response)) {
            log.error("scrm批量同步订单失败");
            return orderSyncResultVO;
        }

        if (ObjectUtil.isNotEmpty(response)) {
            orderSyncResultVO = JSONUtil.toBean(response, OrderSyncResultVO.class);
            log.info("scrm批量同步订单响应结果:{}", JSONUtil.toJsonStr(orderSyncResultVO));
            return orderSyncResultVO;
        }

        return orderSyncResultVO;
    }
}
