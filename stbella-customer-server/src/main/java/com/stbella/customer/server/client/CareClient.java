package com.stbella.customer.server.client;

import com.stbella.base.server.dto.SysEmployeeDTO;
import com.stbella.care.server.care.entity.QuestionnairePO;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.entity.RoomStateCheckInRoomInfoPO;
import com.stbella.care.server.care.entity.comment.CommentResultPO;
import com.stbella.care.server.care.entity.food.FoodRepoPO;
import com.stbella.care.server.care.request.api.CareBoardRequest;
import com.stbella.care.server.care.request.api.CheckedInRoomInfoBasicReq;
import com.stbella.care.server.care.request.comment.CommentEntranceReq;
import com.stbella.care.server.care.vo.CareGuideInfoVO;
import com.stbella.care.server.care.vo.CustomerPostpartumVO;
import com.stbella.care.server.care.vo.api.CareBoardBaseVO;
import com.stbella.care.server.care.vo.mom.BodyIndexVO;
import com.stbella.care.server.care.vo.questionnaire.islahealthy.IsLaHealthyAnswerBaseResultVO;
import com.stbella.care.server.care.vo.survey.QuestionnaireInfoVO;
import com.stbella.core.base.PageVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CareClient {

    CustomerPostpartumVO getPostpartumData(Long customerId, Date date);

    /**
     * 获取最大离馆订单号
     * @param basicId
     * @return
     */
    String getMaxLeaveOrderNo(Integer basicId,Integer brandType);

    /**
     * 根据订单号查询离馆推送记录
     * @param orderNoList
     * @return
     */
    List<CareGuideInfoVO> careGuideByOrderNo(List<String> orderNoList);

    /**
     * 根据问卷id获取问卷信息
     * @param idList
     * @return
     */
    List<QuestionnaireInfoVO> getQuestionById(List<Long> idList);


    Map<String, Integer> getGestationWeekType(List<String> orderNos);

    CareBoardBaseVO queryCareBoard(CareBoardRequest request);

    List<SysEmployeeDTO> listEcpUserByStoreIdAndRoleCode(Long storeId, List<String> targetRoleCodes);

    /**
     * 宫体已入盆数据
     * @param orderNo
     * @param customerId
     * @return
     */
    List<BodyIndexVO> getMomBodyIndex(String orderNo, Long customerId);

    /**
     * 入住中的客户宫体已入盆数据
     * @return
     */
    List<BodyIndexVO> getCheckInCustomerBodyIndex(Date now);

    PageVO<RoomStateCheckInInfoPO> pageByCheckInStatus(Integer pageNum, Integer pageSize, List<Integer> checkInStatus);

    List<RoomStateCheckInInfoPO> getRoomInfoByOrderNoList(List<String> orderNoList);

    List<RoomStateCheckInInfoPO> getRoomInfoByCustomerId(List<Long> customerIdList, List<Integer> checkInStatusList);

    List<RoomStateCheckInRoomInfoPO> listByRoomIdList(List<Long> roomStateInfoList);

    Long genEntrance(CommentEntranceReq req);

    String genEntranceUrl(Long entranceId);

    /**
     * 批量获取评价结果
     */
    List<CommentResultPO> listResultByBizIds(List<String> bizIds);

    QuestionnairePO queryByBrandAndType(Integer brandType, Integer questionnaireType);

    List<IsLaHealthyAnswerBaseResultVO> listByQuestionAnswer(List<Integer> basicUids, Long questionnaireId);

    List<FoodRepoPO> listByFoodIds(List<Long> foodIds);

    RoomStateCheckInInfoPO getLastRoomStateCheckByBasicReq(CheckedInRoomInfoBasicReq roomInfoBasicReq);
}
