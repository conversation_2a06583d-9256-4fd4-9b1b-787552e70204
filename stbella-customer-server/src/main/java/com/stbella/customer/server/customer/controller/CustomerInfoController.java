package com.stbella.customer.server.customer.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.customer.request.*;
import com.stbella.customer.server.customer.service.*;
import com.stbella.customer.server.customer.vo.*;
import com.stbella.customer.server.customer.vo.CustomerCheckOutSummaryVO.Other;
import com.stbella.customer.server.ecp.request.QueryClientPageReq;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.ecp.vo.QueryClientInfoVO;
import com.stbella.mvc.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * @Description 客户管理功能
 * @Date 2021/11/25
 * <AUTHOR>
 */
@Validated
@Api(tags = "客户管理")
@RestController
@RequestMapping("/customer")
@Slf4j
public class CustomerInfoController extends BaseController{

    @Resource
    private CustomerInfoService customerInfoService;

    @Resource
    private CustomerVisitorRecordService customerVisitorRecordService;

    @Resource
    private CustomerArchiveService customerArchiveService;

    @Resource
    private CustomerFollowRecordService customerFollowRecordService;

    @Resource
    private CustomerDynamicRecordService customerDynamicRecordService;

    @Resource
    private CustomerSummaryService customerSummaryService;
    @Resource
    private TabClientService tabClientService;

    @Resource
    private CustomerUserService customerUserService;

    @ApiOperation(value = "员工新增客户")
    @PostMapping(value = "/add")
    public Result add(@RequestBody @Valid CustomerInfoAddRequest customerInfoAddRequest){

//        if (null == this.currentUser()){
//            return Result.failed("请重新登陆");
//        }
//        customerInfoAddRequest.setCreateBy(this.currentUser().getUserId());
//        customerInfoAddRequest.setCreateByName(this.currentUser().getUserName());
        customerInfoAddRequest.setCreateBy(1001L);
        customerInfoAddRequest.setCreateByName("销售小黄");
        JSONObject result =  customerInfoService.add(customerInfoAddRequest);
        return Result.success(result);
    }

    @ApiOperation(value = "客户列表")
    @GetMapping(value = "/list")
    public Result<PageVO<List<CustomerStoreListVO>>> list(@Valid CustomerStoreListRequest customerStoreListRequest){
//        if (null == this.currentUser()){
//            return Result.failed("请重新登陆");
//        }
//        if (customerStoreListRequest.getFalg()){
//            customerStoreListRequest.setEmpId(this.currentUser().getUserId());
//        }
        customerStoreListRequest.setEmpId(1001L);
        Page<CustomerStoreListVO> list = customerInfoService.customerList(customerStoreListRequest);
        return Result.success(new PageVO(list));
    }

    @ApiOperation(value = "新增访客记录")
    @PostMapping(value = "/visitor/add")
    public Result visitorAdd(@RequestBody @Valid CustomerVisitorAddRequest customerInfoAddRequest){

//        if (null == this.currentUser()){
//            return Result.failed("请重新登陆");
//        }
//        customerInfoAddRequest.setCreateBy(this.currentUser().getUserId());
//        customerInfoAddRequest.setCreateByName(this.currentUser().getUserName());

        customerInfoAddRequest.setCreateBy(1001L);
        customerInfoAddRequest.setCreateByName("销售小黄");
        JSONObject result = customerInfoService.visitorAdd(customerInfoAddRequest);
        return Result.success(result);
    }

    @ApiOperation(value = "访客记录列表")
    @GetMapping(value = "/visitor/list")
    public Result<PageVO<List<CustomerVisitorListVO>>> visitorList(@Valid CustomerVisitorListRequest customerVisitorListRequest){
        Page<CustomerVisitorListVO> list = customerInfoService.visitorList(customerVisitorListRequest);
        return Result.success(new PageVO(list));
    }

    @ApiOperation(value = "访客记录查看")
    @GetMapping(value = "/visitor/query/{visitorId}")
    public Result<CustomerVisitorQueryVO> visitorQuery(@PathVariable Long visitorId){
        return Result.success(customerVisitorRecordService.queryById(visitorId));
    }

    @ApiOperation(value = "查看客户档案")
    @GetMapping(value = "/basic/info/{customerId}")
    public Result<CustomerArchiveVO> basicInfo(@PathVariable @NotNull(message = "customerId不能为空") Long customerId){
        CustomerArchiveVO archiveVO = customerArchiveService.basicInfo(customerId);
        return Result.success(archiveVO);
    }


    @ApiOperation(value = "新增跟进记录")
    @PostMapping(value = "/follow/add")
    public Result followAdd(@RequestBody @Valid CustomerFollowAddRequest customerFollowAddRequest){

//        if (null == this.currentUser()){
//            return Result.failed("请重新登陆");
//        }
//        customerFollowAddRequest.setEmpId(this.currentUser().getUserId());
//        customerFollowAddRequest.setEmpName(this.currentUser().getUserName());
        customerFollowAddRequest.setEmpId(1001L);
        customerFollowAddRequest.setEmpName("销售小黄");

        JSONObject result = customerFollowRecordService.followAdd(customerFollowAddRequest);
        return Result.success(result);
    }

    @ApiOperation(value = "查看跟进记录单条")
    @GetMapping(value = "/follow/query/{followId}")
    public Result<CustomerFollowQueryVO> followQuery(@PathVariable @NotNull(message = "followId不能为空") Long followId){
        CustomerFollowQueryVO customerFollowQueryVO = customerFollowRecordService.followQuery(followId);
        return Result.success(customerFollowQueryVO);
    }

    @ApiOperation(value = "修改客户档案")
    @PostMapping(value = "/basic/edit")
    public Result basicEdit(@RequestBody @Valid CustomerArchiveEditRequest customerBasicRequest){

//        if (null == this.currentUser()){
//            return Result.failed("请重新登陆");
//        }
//        customerBasicRequest.setUpdateBy(this.currentUser().getUserId());
//        customerBasicRequest.setUpdateByName(this.currentUser().getUserName());
        customerBasicRequest.setUpdateBy(1001L);
        customerBasicRequest.setUpdateByName("销售小黄");
        Boolean flag = customerArchiveService.basicEdit(customerBasicRequest);
        return Result.judge(flag);
    }

    @ApiOperation(value = "查看客户动态")
    @GetMapping(value = "/dynamic/query/{customerId}")
    public Result<List<CustomerDynamicVO>> dynamicQuery(@PathVariable @NotNull(message = "customerId不能为空") Long customerId){
        List<CustomerDynamicVO> result = customerDynamicRecordService.dynamicQuery(customerId);
        return Result.success(result);
    }

    @ApiOperation(value = "查看客户信息List(护理页面)")
    @GetMapping(value = "/search/list")
    public Result<List<CustomerSearchVO>> searchList(CustomerSearchListRequest customerSearchListRequest){
        List<CustomerSearchVO> customerSearchVOS = customerInfoService.searchList(customerSearchListRequest);
        return Result.success(customerSearchVOS);
    }

    @ApiOperation(value = "检验手机号码是否存在,true-成功 false-失败")
    @GetMapping(value = "/check/phone")
    public Result checkPhoneRepetition(@RequestParam("nationCode") String nationCode,@RequestParam("phoneNumber") String phoneNumber){
        Boolean flag = customerInfoService.checkPhoneRepetition(nationCode,phoneNumber);
        JSONObject json = new JSONObject();
        json.put("flag",flag);
        return Result.success(json);
    }

    @ApiOperation(value = "测试离馆小结插入")
    @PostMapping(value = "/addCheckoutSummary")
    public Result addCheckoutSummary(@RequestParam(value = "id") Long id,
        @RequestParam(value = "orderSn") String orderSn,
        @RequestParam(value = "customerId") Long customerId){
        Boolean result = customerSummaryService.addCheckoutSummary(id, orderSn, customerId);
        return Result.success(result);
    }

    //离馆小结
    @ApiOperation(value = "离馆小结查看")
    @GetMapping(value = "/checkOutSummary")
    public Result<CustomerCheckOutSummaryVO> checkOutSummaryInfo(CustomerCheckOutSummaryRequest customerCheckOutSummaryRequest){
        CustomerCheckOutSummaryVO result = customerSummaryService.checkOutSummaryInfo(customerCheckOutSummaryRequest);
        return Result.success(result);
    }

    @ApiOperation(value = "离馆小结分享内容查看")
    @GetMapping(value = "/checkOutSummary/shareInfo/{shareId}")
    public Result<Other> queryCheckoutSummaryShareInfo(@PathVariable @NotNull(message = "未知的分享") Long shareId) {
        Other shareContent = customerSummaryService.queryCheckoutSummaryShareInfo(shareId);
        return Result.success(shareContent);
    }

    @ApiOperation(value = "客户离馆小结列表")
    @PostMapping(value = "/checkOutSummary/list")
    public Result<PageVO<CustomerCheckoutSummaryListVO>> queryCheckoutSummaryList(@Valid @RequestBody CustomerCheckoutSummaryListRequest request) {
        return Result.success(customerSummaryService.queryCheckoutSummaryList(request));
    }

    @ApiOperation(value = "离馆小结弹框关闭")
    @GetMapping(value = "/checkOutSummary/popClose/{summaryId}")
    public Result<Boolean> updateCheckoutSummaryPopStatus(@PathVariable @NotNull(message = "未知的离馆小结") Long summaryId) {
        Boolean result = customerSummaryService.updateCheckoutSummaryPopStatus(summaryId);
        return Result.success(result);
    }

    @ApiOperation(value = "根据订单号查询月子报告列表")
    @PostMapping(value = "/checkOutSummary/orderNo/queryList")
    public Result<List<CustomerCheckoutSummaryListVO>> queryCheckoutSummaryList (@RequestBody List<String> orderNoList) {
        return Result.success(customerSummaryService.queryCheckoutSummaryList(orderNoList));
    }

    @ApiOperation(value = "月子报告状态修改")
    @PostMapping(value = "/checkOutSummary/status/edit")
    public Result<Boolean> updateCheckoutSummaryStatus(@RequestBody CustomerCheckoutSummaryStatusEditRequest request) {
        return Result.success(customerSummaryService.updateCheckoutSummaryStatus(request));
    }

    @ApiOperation(value = "获取客户当前入馆状态")
    @GetMapping(value = "/currentCheckInStatus/{basicId}")
    public Result<Integer> queryCustomerCurrentCheckInStatus(@PathVariable @NotNull(message = "未知的用户") Integer basicId) {
        Integer currentStatus = customerSummaryService.queryCustomerCheckInStatus(basicId, null);
        return Result.success(currentStatus);
    }

    @ApiOperation(value = "php中的nurse info接口信息获取")
    @PostMapping(value = "/phpNurseInfo")
    public Result<PhpNurseInfoVO> phpNurseInfo(@RequestBody PhpNurseInfoRequest request) {
        return Result.success(customerSummaryService.phpNurseInfo(request));
    }


    @ApiOperation(value = "通过手机后几位&门店查询客户列表")
    @PostMapping(value = "/listByLastPhone")
    public Result<PageVO<QueryClientInfoVO>> listByLastPhone(@Valid @RequestBody QueryClientPageReq req) {
        return Result.success(tabClientService.listByLastPhone(req));
    }


    @ApiOperation(value = "通过basicId获取客户最新的一条月子报告")
    @GetMapping(value = "/getSummaryByBasicId")
    public Result<CustomerSummaryIdVO> getSummaryByBasicId(@RequestParam Integer basicId, @RequestParam Integer brandVal) {
        return Result.success(customerSummaryService.getSummaryByBasicId(basicId,brandVal));
    }

    @ApiOperation(value = "获取用户产康金基本信息")
    @GetMapping(value = "/getCustomerProductionAmountBaseInfoVO")
    public Result<CustomerProductionAmountBaseInfoVO> getCustomerProductionAmountBaseInfoVO(@RequestParam Integer basicId) {
        return Result.success(customerUserService.getCustomerProductionAmountBaseInfo(basicId));
    }

    @ApiOperation(value = "获取用户产康金流水")
    @PostMapping(value = "/queryCustomerProductionAmountStream")
    public Result<PageVO<CustomerProductionAmountStreamVO>> queryCustomerProductionAmountStream(@Valid @RequestBody CustomerProductionAmountStreamRequest request) {
        return Result.success(customerUserService.queryCustomerProductionAmountStream(request));
    }
}
