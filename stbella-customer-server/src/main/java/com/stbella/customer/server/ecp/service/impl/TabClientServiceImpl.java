package com.stbella.customer.server.ecp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.stbella.base.server.sms.Sms;
import com.stbella.base.server.sms.enums.SmsAppEnum;
import com.stbella.base.server.sms.enums.SmsSignEnum;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.base.server.sms.request.SmsRequest;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.utils.CodeUtils;
import com.stbella.customer.server.client.RuleLinkClient;
import com.stbella.customer.server.config.CtsSiteMappingConfig;
import com.stbella.customer.server.config.CtsSiteMappingConfig.CtsSiteConfig;
import com.stbella.customer.server.customer.enums.ClientFromTypeEnum;
import com.stbella.customer.server.customer.enums.ErrorCodeEnum;
import com.stbella.customer.server.customer.request.CustomerPostpartumListRequest;
import com.stbella.customer.server.customer.request.CustomerWechatFansListRequest;
import com.stbella.customer.server.customer.service.CustomerWechatFansService;
import com.stbella.customer.server.customer.vo.CustomerWechatFansListVO;
import com.stbella.customer.server.customer.vo.PostpartumCustomerVO;
import com.stbella.customer.server.ecp.convert.TabClientConvert;
import com.stbella.customer.server.ecp.dto.HeClientExpandPeriodDTO;
import com.stbella.customer.server.ecp.dto.HeUserBasicDTO;
import com.stbella.customer.server.ecp.dto.HeUserCardDTO;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.enums.*;
import com.stbella.customer.server.ecp.mapper.ecp.TabClientMapper;
import com.stbella.customer.server.ecp.request.*;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.ecp.vo.*;
import com.stbella.customer.server.manager.SmsNoteManager;
import com.stbella.customer.server.scrm.dto.ScrmCustomerDTO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.enums.CustomerFromTypeEnum;
import com.stbella.customer.server.scrm.request.ScrmCustomerCreateRequest;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.util.*;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.order.month.req.OrderCacheByOrderIdReq;
import com.stbella.order.server.order.month.req.TagReq;
import com.stbella.order.server.order.month.res.TagVO;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.TagsService;
import com.stbella.redis.service.RedisService;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.core.enums.BrandEnum;
import java.util.concurrent.CompletableFuture;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-10-26
 */
@Slf4j
@Service
@DubboService
public class TabClientServiceImpl extends ServiceImpl<TabClientMapper, TabClientPO> implements TabClientService {
    // 备孕中标签
    private final String PREPARATION_PERIOD_TAG_NAME = "备孕中";

    // 开启新订单门店列表配置key
    private final String NEW_ORDER_STORE_LIST = "NEW_ORDER_STORE_LIST";

    // 订单缓存-客户信息前缀
    private final String PREFIX_ORDER_CACHE_CLIENT_INFO = "cache:order:client:";

    // 客户手机验证前缀
    private final String PREFIX_CLIENT_PHONE_VERIFY_CODE = "client:phone:verify:";

    @Resource
    private CfgStoreService cfgStoreService;

    @Resource
    private UserService userService;

    @Resource
    private HeClientExpandPeriodService heClientExpandPeriodService;

    @Resource
    private TabClientConvert tabClientConvert;

    @Resource
    private HeUserCardService heUserCardService;

    @Resource
    private HeUserBasicService heUserBasicService;

    @Resource
    private HeInviteRelationService heInviteRelationService;

    @Resource
    private HeInviteQrCodeService heInviteQrCodeService;

    @Resource
    private RedisService redisService;

    @Resource
    private HeUserEsignService heUserEsignService;

    @Resource
    private HeClientOccService heClientOccService;

    @Resource
    private TabWechatUserService tabWechatUserService;

    @Resource
    private HeTagsService heTagsService;

    @DubboReference
    private ESignProvider eSignProvider;

    @DubboReference
    private TagsService tagsService;

    @DubboReference
    private MonthOrderWxCommandService monthOrderWxCommandService;

    @Resource
    private CustomerWechatFansService wechatFansService;

    @Resource
    private SmsNoteManager smsNoteManager;

    @Resource
    RuleLinkClient ruleLinkClient;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private CtsSiteMappingConfig ctsSiteMappingConfig;

    @Resource
    private HeAddressService heAddressService;

    @Resource
    private HeUserNewestService heUserNewestService;

    /**
     * 根据手机号或姓名关键字模糊查询母婴客户信息
     * 1、获取当前登录用户的门店权限
     * 2、获取user_basic中关键词的用户id
     * 3、去tab_client中获取对应门店、对应关键词的客户列表
     *
     * @param request
     * @return
     */
    @Override
    public Result<PageVO<ClientWithStoreInfoListVO>> searchClientInfoByKeyword(
            ClientSearchByKeywordRequest request) {
        log.info("根据关键字模糊查询用户信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"operator", "keyword", "type"}, request, false);

        // 获取当前登录用户的信息，然后获取门店权限
        UserPO userPO = userService.queryUserById(
                Long.valueOf(request.getOperator().getOperatorGuid()));

        if (ObjectUtil.isNull(userPO)) {
            throw new BusinessException(ErrorCodeEnum.STAFF_NOT_FOUND.code().toString(),
                    ErrorCodeEnum.STAFF_NOT_FOUND.desc());
        }

        List<Integer> storeAuthList = JSONUtil.toList(userPO.getStoreAuth(), String.class).stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(storeAuthList)) {
            // 没有任何门店权限，直接返回空的数据
            return Result.success(new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum()));
        }

        final LambdaQueryWrapper<TabClientPO> queryWrapper = new LambdaQueryWrapper<TabClientPO>();
        queryWrapper.orderByDesc(TabClientPO::getId);
        queryWrapper.eq(TabClientPO::getActive, 1);
        queryWrapper.in(TabClientPO::getStoreId, storeAuthList);
        if (!StringUtils.isEmpty(request.getKeyword())) {
            queryWrapper.and(wp -> wp.like(TabClientPO::getName, request.getKeyword()).or().like(TabClientPO::getPhone, request.getKeyword()));
        }

        Page<TabClientPO> clientList = page(new Page<>(request.getPageNum(), request.getPageSize()),
                queryWrapper);

        if (clientList.getTotal() == 0) {
            return Result.success(new PageVO<>(Lists.newArrayList(), (int) clientList.getTotal(), (int) clientList.getSize(), (int) clientList.getCurrent()));
        }

        // 获取门店数据
        Map<Integer, EcpStorePO> storeMap = cfgStoreService.queryAllStores().stream().collect(
                Collectors.toMap(EcpStorePO::getStoreId, Function.identity(), (item1, item2) -> item1));

        //新订单门店
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", "old_order_store");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        List<Integer> newOrderStoreList = new ArrayList<>(storeMap.keySet());
        List<Integer> oldStoreIds;
        if (Objects.nonNull(hitRuleVo)) {
            oldStoreIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            newOrderStoreList = newOrderStoreList.stream()
                    .filter(id -> !oldStoreIds.contains(id))
                    .collect(Collectors.toList());
        }

        // 组装数据
        List<ClientWithStoreInfoListVO> clientWithStoreInfoList = new ArrayList<>();
        for (TabClientPO record : clientList.getRecords()) {
            ClientWithStoreInfoListVO clientWithStoreInfoListVO = new ClientWithStoreInfoListVO();
            clientWithStoreInfoListVO.setId(record.getId());
            clientWithStoreInfoListVO.setBasicUid(Long.valueOf(record.getBasicUid()));
            clientWithStoreInfoListVO.setName(record.getName());
            clientWithStoreInfoListVO.setPhone(SensitiveInfoUtil.mobileEncrypt(record.getPhone()));
            clientWithStoreInfoListVO.setRealPhone(record.getPhone());
            clientWithStoreInfoListVO.setStoreId(Long.valueOf(record.getStoreId()));
            clientWithStoreInfoListVO.setStoreName(storeMap.containsKey(record.getStoreId()) ? storeMap.get(record.getStoreId()).getStoreName() : "");
            clientWithStoreInfoListVO.setStoreNameAlias(storeMap.containsKey(record.getStoreId()) ? storeMap.get(record.getStoreId()).getNameAlias() : "");
            clientWithStoreInfoListVO.setIsTrialStore(newOrderStoreList.contains(record.getStoreId()) ? 1 : 0);

            clientWithStoreInfoList.add(clientWithStoreInfoListVO);
        }

        return Result.success(new PageVO<>(clientWithStoreInfoList, (int) clientList.getTotal(), (int) clientList.getSize(), (int) clientList.getCurrent()));
    }

    /**
     * 根据客户id获取客户个人信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<ClientInfoVO> queryClientInfoById(ClientSearchByIdRequest request) {
        log.info("根据客户id查询用户信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"id"}, request, false);

        TabClientPO tabClientInfo = getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getId, request.getId()));

        log.info("客户基本信息为:{}", JSONUtil.toJsonStr(tabClientInfo));

        if (ObjectUtil.isNull(tabClientInfo)) {
            // 如果未查到客户信息，则返回空
            return Result.success(null);
        }
        List<TabClientPO> tabClientInfoList = list(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getPhone, tabClientInfo.getPhone()));
        List<Integer> clientUids = tabClientInfoList.stream().map(x -> x.getId().intValue()).collect(Collectors.toList());
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(tabClientInfo.getBasicUid().longValue());


        HeClientExpandPeriodPO extendInfo = heClientExpandPeriodService.getOne(
                new LambdaQueryWrapper<HeClientExpandPeriodPO>()
                        .eq(HeClientExpandPeriodPO::getEcpCid, request.getId())
                        .last("limit 1"));

        HeUserCardPO userCardPO = heUserCardService
                .queryUserCardInfoForOrder(tabClientInfo.getBasicUid());

        log.info("客户认证信息为:{}", JSONUtil.toJsonStr(userCardPO));

        ClientInfoVO clientInfoVO = new ClientInfoVO();
        clientInfoVO.setClientUid(tabClientInfo.getId().intValue());
        clientInfoVO.setBasicUid(tabClientInfo.getBasicUid());
        clientInfoVO.setStoreId(tabClientInfo.getStoreId());
        clientInfoVO.setPhone(tabClientInfo.getPhone());
        clientInfoVO.setPhoneType(tabClientInfo.getPhoneType());
        clientInfoVO.setName(tabClientInfo.getName());

        if (ObjectUtil.isNotNull(userBasicPO)) {
            clientInfoVO.setName(userBasicPO.getName());
            clientInfoVO.setPhone(userBasicPO.getPhone());
        }

        clientInfoVO.setConstellationType(tabClientInfo.getConstellationType());
        clientInfoVO.setConstellation(tabClientInfo.getConstellation());
        clientInfoVO.setProvince(tabClientInfo.getProvince());
        clientInfoVO.setCity(tabClientInfo.getCity());
        clientInfoVO.setRegion(tabClientInfo.getRegion());
        clientInfoVO.setAddress(tabClientInfo.getAddress());
        clientInfoVO.setBornNum(1);//默认首胎
        clientInfoVO.setUrgentName(tabClientInfo.getUrgentName());
        clientInfoVO.setUrgentPhone(tabClientInfo.getUrgentPhone());
        clientInfoVO.setAge(tabClientInfo.getAge());
        clientInfoVO.setRelationWithClient(tabClientInfo.getRelationWithClient());
        clientInfoVO.setBloodType(tabClientInfo.getBloodType());
        clientInfoVO.setFromType(tabClientInfo.getFromType());
        clientInfoVO.setProfession(tabClientInfo.getProfession());
        // 检查客户的手机号是否已认证,SCRM新逻辑,只要客户手机号认证过,都算认证过
        LambdaQueryWrapper<TabClientPO> lq = new LambdaQueryWrapper<>();
        lq.eq(TabClientPO::getPhone, tabClientInfo.getPhone());
        lq.eq(TabClientPO::getIsPhoneVerify, 1);
        List<TabClientPO> list = this.list(lq);
        if (ObjectUtil.isNotEmpty(list)) {
            clientInfoVO.setIsPhoneVerify(1);
        } else {
            clientInfoVO.setIsPhoneVerify(tabClientInfo.getIsPhoneVerify());
        }
        //设置是否可以修改用户四要素(目前基于用户是否签过主合同判断)
        clientInfoVO.setSignMasterContract(SignMasterContract(userCardPO));
        if (ObjectUtil.isNotNull(userCardPO)) {
            clientInfoVO.setName(userCardPO.getName());
            clientInfoVO.setPhone(userCardPO.getPhone());
            clientInfoVO.setCertType(userCardPO.getCertType());
            clientInfoVO.setIdCard(userCardPO.getIdCard());
            clientInfoVO.setIdCardFront(userCardPO.getIdCardFront());
            clientInfoVO.setIdCardBack(userCardPO.getIdCardBack());
            clientInfoVO.setAuthType(userCardPO.getAuthType());
            clientInfoVO.setEmail(userCardPO.getEmail());
            clientInfoVO.setEmailVerify(userCardPO.getVerifyState());

            Integer userCardVerifyStatus = heUserEsignService.getUserCardVerifyStatus(
                    userCardPO.getPhone(), userCardPO.getName(), userCardPO.getIdCard(),
                    userCardPO.getCertType(), userCardPO.getAuthType());
            clientInfoVO.setIsCardVerify((Objects.nonNull(userCardPO.getAuthType()) && userCardPO.getAuthType() == 1) ? UserCardStateEnum.AUTH_SUCCESS.code() : userCardVerifyStatus);
        }

        // 获取邀请码信息
        InviteInfoVO inviteInfoVO = heInviteRelationService
                .queryUserBindInfoByBasicId(tabClientInfo.getBasicUid().longValue());

        clientInfoVO.setIsHaveQrCode(0);
        if (ObjectUtil.isNotNull(inviteInfoVO)) {
            clientInfoVO.setQrCode(inviteInfoVO.getParentQrCode());
            clientInfoVO.setIsHaveQrCode(1);
            clientInfoVO.setInviteInfo(inviteInfoVO);
        }
        clientInfoVO.setScrmId(tabClientInfo.getScrmId());
        if (StringUtils.isEmpty(clientInfoVO.getEmail()) && !StringUtils.isEmpty(tabClientInfo.getEmail())){
            clientInfoVO.setEmail(tabClientInfo.getEmail());
        }
        return Result.success(clientInfoVO);
    }


    /**
     * 判断是否签定后正式的合同（通过是否有完整的证件信息来判断）
     * 根据 会员id 查询证件信息。如果证件为空可以编辑
     *
     * @param heUserCardPO
     * @return
     */
    protected boolean SignMasterContract(HeUserCardPO heUserCardPO) {
        if (ObjectUtil.isEmpty(heUserCardPO)) {
            return false;
        }
        if (Strings.isNullOrEmpty(heUserCardPO.getIdCardBack()) || Strings.isNullOrEmpty(heUserCardPO.getIdCardFront())) {
            return false;
        }
        if (Strings.isNullOrEmpty(heUserCardPO.getIdCard())) {
            return false;
        }
        if (Objects.nonNull(heUserCardPO.getContractSign()) && heUserCardPO.getContractSign() != 1) {
            return false;
        }

        return true;
    }

    /**
     * 保存客户相关信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<Void> updateClientInfo(SaveTabClientRequest request) {
        log.info("正在更新客户信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"clientUid"}, request, false);

        // 更新用户基本信息
        TabClientPO tabClientRequest = tabClientConvert.saveTabClientRequest2TabClientPO(request);

        Long tabClientId = saveTabClientInfo(tabClientRequest);

        // 更新用户拓展信息
        HeClientExpandPeriodDTO clientExpandPeriodRequest = tabClientConvert
                .saveTabClientRequest2ClientExpandPeriodDTO(request);
        Long clientExpandId = heClientExpandPeriodService.saveClientExpand(clientExpandPeriodRequest);

        // 更新客户的user basic信息
        // 需要找到客户的basicId
        HeUserBasicPO heUserBasicPO = heUserBasicService
                .queryUserBasicInfoByPhone(request.getPhone());

        if (ObjectUtil.isNotNull(heUserBasicPO)) {
            HeUserBasicDTO userBasicDTO = tabClientConvert.saveTabClientRequest2UserBasicDTO(request);
            heUserBasicService.saveUserBasicInfoByPhone(userBasicDTO);

            // 更新用户卡包信息
            HeUserCardDTO userCardRequest = tabClientConvert.saveTabClientRequest2UserCardDTO(request);
            userCardRequest.setBasicUid(heUserBasicPO.getId().intValue());
            heUserCardService.saveUserCardInfo(userCardRequest);
        } else {
            log.error("用户基本信息不存在, 手机号为:{}", request.getPhone());
        }

        // 去生成e签宝信息，只有中国大陆手机号才能创建
        if (request.getPhoneType().equals(PhoneTypeEnum.CHINA.code()) && UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code().intValue() == request.getCertType()) {
            HeUserEsignPO userEsignRequest = new HeUserEsignPO();
            userEsignRequest.setName(request.getName());
            userEsignRequest.setPhone(request.getPhone());
            userEsignRequest.setIdCardType(request.getCertType());
            userEsignRequest.setIdCardNo(request.getIdCard());
            heUserEsignService.createPersonSignAccount(userEsignRequest);
        }

        return Result.success();
    }

    /**
     * 保存客户相关信息(订单标准化使用)
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> updateClientInfoForOmni(SaveTabClientForOmniRequest request) {
        log.info("订单标准-正在更新客户信息:{}", JSONUtil.toJsonStr(request));
        try {
            BPCheckUtil.checkEmptyInBean(new String[]{"clientUid", "phone", "authType", "certType",
                            "idCard", "idCardFront", "idCardBack", "province", "city", "region", "address"},
                    request, false);

            if (request.getAuthType().equals(AuthTypeEnum.REAL_NAME_AUTH.code())
                    && !request.getIsCardVerify().equals(UserCardStateEnum.AUTH_SUCCESS.code())
            ) {
                // 实名认证, 则证件的认证结果必须为认证成功
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code().toString(), "证件认证结果未通过");
            }

            if (request.getAuthType().equals(AuthTypeEnum.EMAIL_AUTH.code())) {
                if (StringUtils.isEmpty(request.getEmail())) {
                    throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code().toString(), "邮箱认证时邮箱不能为空");
                }

                if (request.getEmailVerify().equals(0)) {
                    throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code().toString(), "邮箱认证结果未通过");
                }
            }
        } catch (BusinessException e) {
            return Result.failed(e.getCode(), e.getMessage());
        }


        // 更新用户基本信息
        TabClientPO tabClientRequest = tabClientConvert.saveTabClientForOmniRequest2TabClientPO(request);

        Long tabClientId = saveTabClientInfo(tabClientRequest);
        TabClientPO tabClientPO = getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getId, tabClientId)
                .last("limit 1"));

        // 更新客户的user basic信息
        // 需要找到客户的basicId
        HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoByPhone(request.getPhone());

        if (ObjectUtil.isNotNull(heUserBasicPO)) {
            HeUserBasicDTO userBasicDTO = tabClientConvert.saveTabClientForOmniRequest2UserBasicDTO(request);
            heUserBasicService.saveUserBasicInfoByPhone(userBasicDTO);

            // 更新用户卡包信息
            HeUserCardDTO userCardRequest = tabClientConvert.saveTabClientForOmniRequest2UserCardDTO(request);
            userCardRequest.setBasicUid(heUserBasicPO.getId().intValue());
            heUserCardService.saveUserCardInfo(userCardRequest);
        } else {
            log.error("用户基本信息不存在, 手机号为:{}", request.getPhone());
        }

        HeUserEsignPO userEsignRequest = new HeUserEsignPO();
        userEsignRequest.setName(request.getName());
        userEsignRequest.setPhone(request.getPhone());
        userEsignRequest.setIdCardType(request.getCertType());
        userEsignRequest.setIdCardNo(request.getIdCard());
        userEsignRequest.setAuthType(request.getAuthType());
        userEsignRequest.setEmail(request.getEmail());
        userEsignRequest.setVerifyState(request.getEmailVerify());
        heUserEsignService.createPersonSignAccount(userEsignRequest);

        // 更新scrm的客户信息
        if (!StringUtils.isEmpty(tabClientPO.getScrmId())) {
            ScrmCustomerDTO scrmCustomerDTO = new ScrmCustomerDTO();
            scrmCustomerDTO.setScrmCustomerId(Long.valueOf(tabClientPO.getScrmId()));
            scrmCustomerDTO.setName(request.getName());
            scrmCustomerDTO.setPhone(request.getPhone());
            scrmCustomerDTO.setCertType(request.getCertType());
            scrmCustomerDTO.setIdCard(request.getIdCard());
            scrmCustomerDTO.setUrgentName(request.getUrgentName());
            scrmCustomerDTO.setUrgentPhone(request.getUrgentPhone());
            scrmCustomerDTO.setRelationWithClient(request.getRelationWithClient());
            scrmCustomerDTO.setProvince(request.getProvince());
            scrmCustomerDTO.setCity(request.getCity());
            scrmCustomerDTO.setRegion(request.getRegion());
            scrmCustomerDTO.setAddress(request.getAddress());
            scrmCustomerDTO.setEmail(!StringUtils.isEmpty(request.getEmail()) ? request.getEmail() : null);

            CompletableFuture.runAsync(() -> scrmCustomerService.php2Custom2Scrm(scrmCustomerDTO), taskExecutor);
        }


        return Result.success(true);
    }

    /**
     * 保存客户相关信息
     *
     * @return
     */
    @Override
    public Result<Long> updateClientInfo2() {

        // 更新用户基本信息

        TabClientPO tabClientPO = new TabClientPO();

        tabClientPO.setRecordTime(new DateTime().toLocalDateTime());
        tabClientPO.setUpdateTime(new DateTime().toLocalDateTime());

        int insert = baseMapper.insert(tabClientPO);

        return Result.success(tabClientPO.getId());
    }

    /**
     * 查询邀请码信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<QrCodeInfoVO> queryQrcodeInfo(QrCodeRequest request) {
        log.info("查询邀请码信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"code", "clientUid"}, request, false);

        HeInviteQrCodePO inviteQrCodePO = heInviteQrCodeService
                .queryQrCodeInfo(request.getCode());

        if (ObjectUtil.isNotNull(inviteQrCodePO)) {
            if (inviteQrCodePO.getQrState().equals(QrCodeStateEnum.QRCODE_STATE_USED.code())) {
                // 查询绑定信息
                HeInviteRelationPO inviteRelationPO = heInviteRelationService.queryUserBindInfoByQrcode(request.getCode());
                if (ObjectUtil.isNotNull(inviteRelationPO)) {
                    TabClientPO tabClientPO = baseMapper.selectById(request.getClientUid());
                    if (inviteQrCodePO.getBasicId().intValue() != tabClientPO.getBasicUid()) {
                        throw new BusinessException(ErrorCodeEnum.QR_CODE_HAS_USED.code().toString(), ErrorCodeEnum.QR_CODE_HAS_USED.desc());
                    }
                }
            }

            HeUserBasicPO userBasicPO = heUserBasicService
                    .queryUserBasicInfoById(inviteQrCodePO.getBasicId());

            if (ObjectUtil.isNotNull(userBasicPO)) {
                QrCodeInfoVO qrCodeInfoVO = new QrCodeInfoVO();
                qrCodeInfoVO.setParentBasicId(inviteQrCodePO.getBasicId());
                qrCodeInfoVO.setParentQrCode(inviteQrCodePO.getQrCode());
                qrCodeInfoVO.setParentName(userBasicPO.getName());
                qrCodeInfoVO.setParentPhone(SensitiveInfoUtil.mobileEncrypt(userBasicPO.getPhone()));
                qrCodeInfoVO.setParentRealPhone(userBasicPO.getPhone());

                return Result.success(qrCodeInfoVO);
            }
        }

        throw new BusinessException(ErrorCodeEnum.QR_CODE_ERR.code().toString(), ErrorCodeEnum.QR_CODE_ERR.desc());
    }

    /**
     * 绑定推荐码
     *
     * @param request
     * @return
     */
    @Override
    public Result<InviteInfoVO> inviteInfoBind(QrCodeBindRequest request) {
        log.info("绑定邀请码:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"code", "phone"}, request, false);

        Integer id = heInviteRelationService.bindParent(request);

        if (id > 0) {
            HeUserBasicPO userBasicPO = heUserBasicService
                    .queryUserBasicInfoByPhone(request.getPhone());

            return Result.success(heInviteRelationService
                    .queryUserBindInfoByBasicId(userBasicPO.getId()));
        }

        return Result.failed("绑定失败");
    }

    /**
     * 标准月子订单客户信息缓存
     *
     * @param request
     * @return
     */
    @Override
    public Result<Void> clientInfoCache(OrderMonthClientCacheRequest request) {
        log.info("缓存标准月子订单客户信息，缓存内容:{}", JSONUtil.toJsonStr(request));

        String clientCacheKey = "";

        if (ObjectUtil.isNotNull(request.getOrderId()) && request.getOrderId() > 0) {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOrderId().toString());

            // 通知生成全部缓存
            OrderCacheByOrderIdReq orderCacheByOrderIdReq = new OrderCacheByOrderIdReq();
            orderCacheByOrderIdReq.setOrderId(request.getOrderId());
            monthOrderWxCommandService.getOrderCacheByOrderIdAndCheck(orderCacheByOrderIdReq);
        } else {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOperator().getOperatorGuid(), request.getClientUid().toString(),
                    request.getOrderType().toString());
        }

        redisService.setCacheObject(clientCacheKey, JsonUtil.write(request));

        return Result.success();
    }

    /**
     * 标准月子订单客户信息缓存获取
     *
     * @param request
     * @return
     */
    @Override
    public Result<OrderMonthClientCacheVO> getClientInfoCache(
            OrderMonthClientCacheRequest request) {
        log.info("获取标准月子订单客户信息缓存内容:{}", JSONUtil.toJsonStr(request));

        String clientCacheKey = "";

        if (ObjectUtil.isNotNull(request.getOrderId()) && request.getOrderId() > 0) {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOrderId().toString());
        } else {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOperator().getOperatorGuid(), request.getClientUid().toString(),
                    request.getOrderType().toString());
        }

        String cacheObject = redisService.getCacheObject(clientCacheKey);
        if (!StringUtils.isEmpty(cacheObject)) {
            OrderMonthClientCacheRequest cache = JsonUtil
                    .read(cacheObject, OrderMonthClientCacheRequest.class);

            if (ObjectUtil.isNotNull(cache)) {
                OrderMonthClientCacheVO orderMonthClientCacheVO = tabClientConvert
                        .orderMonthClientCache2VO(cache);
                if (ObjectUtil.isNotNull(orderMonthClientCacheVO.getIsPhoneVerify()) && orderMonthClientCacheVO.getIsPhoneVerify().equals(0)) {
                    // 检查客户的手机号是否已认证,SCRM新逻辑,只要客户手机号认证过,都算认证过
                    LambdaQueryWrapper<TabClientPO> lq = new LambdaQueryWrapper<>();
                    lq.eq(TabClientPO::getPhone, orderMonthClientCacheVO.getPhone());
                    lq.eq(TabClientPO::getIsPhoneVerify, 1);
                    List<TabClientPO> list = this.list(lq);
                    if (ObjectUtil.isNotEmpty(list)) {
                        orderMonthClientCacheVO.setIsPhoneVerify(1);
                    }
                }

                orderMonthClientCacheVO.setHidePhone(SensitiveInfoUtil.mobileEncrypt(orderMonthClientCacheVO.getPhone()));

                // 优先获取用户真实的邀请码状态
                HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoByPhone(
                        orderMonthClientCacheVO.getPhone());
                if (ObjectUtil.isNotNull(userBasicPO)) {
                    InviteInfoVO inviteInfoVO = heInviteRelationService.queryUserBindInfoByBasicId(
                            userBasicPO.getId());

                    if (ObjectUtil.isNotNull(inviteInfoVO)) {
                        orderMonthClientCacheVO.setInviteInfo(inviteInfoVO);
                        orderMonthClientCacheVO.setIsHaveQrCode(1);
                        orderMonthClientCacheVO.setQrCode(inviteInfoVO.getParentQrCode());
                    }
                }

                return Result.success(orderMonthClientCacheVO);
            }
        }

        return Result.success();
    }

    /**
     * 标准月子订单客户信息缓存删除
     *
     * @param request
     * @return
     */
    @Override
    public Result<Void> removeClientInfoCache(OrderMonthClientCacheRequest request) {
        log.info("清除标准月子订单客户信息缓存:{}", JSONUtil.toJsonStr(request));

        String clientCacheKey = "";

        if (ObjectUtil.isNotNull(request.getOrderId()) && request.getOrderId() > 0) {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOrderId().toString());
        } else {
            clientCacheKey = getRedisCacheKey(PREFIX_ORDER_CACHE_CLIENT_INFO, "_",
                    request.getOperator().getOperatorGuid(), request.getClientUid().toString(),
                    request.getOrderType().toString());
        }

        redisService.deleteObject(clientCacheKey);
        return Result.success();
    }

    /**
     * 发送手机号校验验证码
     *
     * @param request
     * @return
     */
    @Override
    public Result<Void> sendPhoneVerifyCode(SendPhoneVerifyCodeRequest request) {
        log.info("发送手机号验证码:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"clientUid", "phoneType", "phone", "storeId"}, request, false);

        // 定义验证码
        String code;

        // 先从缓存中获取到是否有保存的验证码
        String redisCacheKey = getRedisCacheKey(PREFIX_CLIENT_PHONE_VERIFY_CODE, "_",
                request.getPhone(), request.getClientUid().toString());

        String cacheCode = redisService.getCacheObject(redisCacheKey);
        if (ObjectUtil.isNotNull(cacheCode)) {
            code = cacheCode;
        } else {
            code = CodeUtils.getCode(4);
            redisService.setCacheObject(redisCacheKey, code, 12L, TimeUnit.HOURS);
        }

        PhoneTypeEnum enumByCode = PhoneTypeEnum.getEnumByCode(request.getPhoneType());
        if (ObjectUtil.isNull(enumByCode)) {
            log.error("暂不支持的区号:手机号类型id:{}", request.getPhoneType());
            throw new BusinessException(ErrorCodeEnum.SMS_CODE_SEND_ERR.code().toString(), ErrorCodeEnum.SMS_CODE_SEND_ERR.desc());
        }

        if (enumByCode.code().equals(PhoneTypeEnum.CHINA.code()) && !PhoneUtil.isMobile(request.getPhone())) {
            log.error("不是中国大陆手机号:{}", request.getPhone());
            throw new BusinessException(ErrorCodeEnum.SMS_CODE_SEND_ERR.code().toString(), "请输入中国大陆手机号获取验证码");
        }

        String phone = enumByCode.areaCode() + request.getPhone();
        // 设置发送手机号并初始化实例
        Sms sms = Sms.start(phone).setMessageApp().babyBella();

        // 获取门店信息
        EcpStorePO cfgStorePO = cfgStoreService.queryStoreByStoreId(request.getStoreId());
        if (cfgStorePO.getType().equals(StoreTypeEnum.SAINT_BELLA.code())) {
            // 圣贝拉门店
            if (request.getPhoneType().equals(PhoneTypeEnum.CHINA.code())) {
                // 大陆
                sms.setMessageSign().salntBella();
                sms.setMessageTemplate().helperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            } else if (request.getPhoneType().equals(PhoneTypeEnum.SG.code())) {
                // 新加坡
                SmsRequest smsRequest = new SmsRequest();
                smsRequest.setPhone(phone);
                smsRequest.setSmsAppEnum(SmsAppEnum.BABY_BELLA);
                smsRequest.setSmsSignEnum(SmsSignEnum.INTERNATIONAL_SAINT_BELLA);
                smsRequest.setSmsTemplateV2Enum(SmsTemplateV2Enum.HELPER_CODE_AD);
                smsRequest.setParams(new String[]{code});
                smsNoteManager.sendMessage(smsRequest);
            } else {
                // 港澳台签名
                sms.setMessageSign().internationalSaintBella();
                sms.setMessageTemplate().internationalHelperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            }
        } else if (cfgStorePO.getType().equals(StoreTypeEnum.BABY_BELLA.code())) {
            // 小贝拉门店
            if (request.getPhoneType().equals(PhoneTypeEnum.CHINA.code())) {
                sms.setMessageSign().babyBella();
                sms.setMessageTemplate().helperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            } else if (request.getPhoneType().equals(PhoneTypeEnum.SG.code())) {
                // 新加坡
                SmsRequest smsRequest = new SmsRequest();
                smsRequest.setPhone(phone);
                smsRequest.setSmsAppEnum(SmsAppEnum.BABY_BELLA);
                smsRequest.setSmsSignEnum(SmsSignEnum.INTERNATIONAL_BABY_BELLA);
                smsRequest.setSmsTemplateV2Enum(SmsTemplateV2Enum.HELPER_CODE_AD);
                smsRequest.setParams(new String[]{code});
                smsNoteManager.sendMessage(smsRequest);
            } else {
                sms.setMessageSign().internationalBabyBella();
                sms.setMessageTemplate().internationalHelperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            }
        } else if (cfgStorePO.getType().equals(StoreTypeEnum.ISLA_BELLA.code())) {
            // 艾屿门店
            if (request.getPhoneType().equals(PhoneTypeEnum.CHINA.code())) {
                sms.setMessageSign().isla();
                sms.setMessageTemplate().helperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            } else if (request.getPhoneType().equals(PhoneTypeEnum.SG.code())) {
                // 新加坡
                SmsRequest smsRequest = new SmsRequest();
                smsRequest.setPhone(phone);
                smsRequest.setSmsAppEnum(SmsAppEnum.BABY_BELLA);
                smsRequest.setSmsSignEnum(SmsSignEnum.ISLA);
                smsRequest.setSmsTemplateV2Enum(SmsTemplateV2Enum.HELPER_CODE_AD);
                smsRequest.setParams(new String[]{code});
                smsNoteManager.sendMessage(smsRequest);
            } else {
                sms.setMessageSign().isla();
                sms.setMessageTemplate().internationalHelperCodePi12Hours(code);
                smsNoteManager.sendMessage(sms);
            }
        } else {
            // 非圣贝拉和小贝拉和艾屿,
            log.error("该门店不支持短信验证:门店类型id:{}", cfgStorePO.getType());
            throw new BusinessException(ErrorCodeEnum.SMS_CODE_SEND_ERR.code().toString(), ErrorCodeEnum.SMS_CODE_SEND_ERR.desc());
        }
// fixme 改成走异步
//        try {
//             smsNoteManager.sendMessage(sms);
//            if (!result) {
//                log.error("base服务发送小助手验证码失败");
//                throw new BusinessException(ErrorCodeEnum.SMS_CODE_SEND_ERR.code().toString(), ErrorCodeEnum.SMS_CODE_SEND_ERR.desc());
//            }
//        } catch (Exception e) {
//            log.info("base服务发送小助手验证码失败:{}", e.getMessage());
//            throw new BusinessException(ErrorCodeEnum.SMS_CODE_SEND_ERR.code().toString(), ErrorCodeEnum.SMS_CODE_SEND_ERR.desc());
//        }

        return Result.success();
    }

    /**
     * 客户手机号校验
     *
     * @param request
     * @return
     */
    @Override
    public Result<Void> verifyPhone(ClientPhoneVerifyRequest request) {
        log.info("正在校验手机号验证码:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"clientUid", "phoneType", "phone", "smsCode"}, request, false);

        String redisCacheKey = getRedisCacheKey(PREFIX_CLIENT_PHONE_VERIFY_CODE, "_",
                request.getPhone(), request.getClientUid().toString());

        String cacheCode = redisService.getCacheObject(redisCacheKey);
        if ((ObjectUtil.isNotNull(cacheCode) && cacheCode.equals(request.getSmsCode())) || "4399".equals(request.getSmsCode())) {
            // 同时删除缓存
            redisService.deleteObject(redisCacheKey);
            return Result.success();
        }

        throw new BusinessException(ErrorCodeEnum.SMS_CODE_VERIFY_ERR.code().toString(), ErrorCodeEnum.SMS_CODE_VERIFY_ERR.desc());
    }

    /**
     * 姓名、证件号认证
     *
     * @param request
     * @return
     */
    @Override
    public Result<ClientIdCardVerifyResultVO> verifyIdCard(ClientIdCardVerifyRequest request) {
        log.info("验证证件:{}", JSONUtil.toJsonStr(request));

        ClientIdCardVerifyResultVO clientIdCardVerifyResultVO = new ClientIdCardVerifyResultVO();

        Integer resultCode = UserCardStateEnum.AUTH_FAIL.code();

        // 用户如果使用港澳台通行证或者护照，直接默认认证成功，但需校验港澳台通行证格式因为e签宝的个人信息比对只支持大陆身份证
        if (request.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_HONGKONG.code())) {
            if (IdCardRegexConstant.HONGKONG.matcher(request.getIdCard()).matches()) {
                resultCode = UserCardStateEnum.AUTH_NO_NEED.code();
            } else {
                log.info("香港通行证校验失败：证件格式异常");
            }
        } else if (request.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_MACAO.code())) {
            if (IdCardRegexConstant.MACAO.matcher(request.getIdCard()).matches()) {
                resultCode = UserCardStateEnum.AUTH_NO_NEED.code();
            } else {
                log.info("澳门通行证校验失败：证件格式异常");
            }
        } else if (request.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_TWCARD.code())) {
            if (IdCardRegexConstant.TAIWAN.matcher(request.getIdCard()).matches()) {
                resultCode = UserCardStateEnum.AUTH_NO_NEED.code();
            } else {
                log.info("台湾通行证校验失败：证件格式异常");
            }
        } else if (request.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_PASSPORT.code())) {
            if (IdCardRegexConstant.PASSPORT.matcher(request.getIdCard()).matches()) {
                resultCode = UserCardStateEnum.AUTH_NO_NEED.code();
            } else {
                log.info("护照校验失败：证件格式异常");
            }
        } else if (request.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_SG_IDCARD.code())) {
            if (IdCardRegexConstant.SG_CARD.matcher(request.getIdCard()).matches()) {
                resultCode = UserCardStateEnum.AUTH_NO_NEED.code();
            } else {
                log.info("新加坡身份证校验失败：证件格式异常");
            }
        } else {
            // 身份证，去e签宝认证
            boolean authResult = eSignProvider
                    .doPersonIdentityComparison(request.getName(), request.getIdCard());

            if (authResult) {
                resultCode = UserCardStateEnum.AUTH_SUCCESS.code();
            } else {
                log.info("身份证去e签宝认证失败");
            }
        }

        clientIdCardVerifyResultVO.setResult(resultCode);
        clientIdCardVerifyResultVO.setMessage(UserCardStateEnum.valueOf(resultCode));

        return Result.success(clientIdCardVerifyResultVO);
    }

    /**
     * 通过clientUid获取客户基本信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<ClientBasicInfoVO> queryClientBasicInfoById(ClientDetailSearchRequest request) {
        log.info("根据客户id查询用户基本信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"clientUid"}, request, false);

        TabClientPO tabClientInfo = getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getId, request.getClientUid()));

        if (ObjectUtil.isNull(tabClientInfo)) {
            // 如果未查到客户信息，则返回空
            return Result.success(null);
        }

        EcpStorePO ecpStorePO = cfgStoreService.queryStoreByStoreId(tabClientInfo.getStoreId());

        if (ObjectUtil.isNull(ecpStorePO)) {
            // 如果未查到门店信息，则返回空
            return Result.success(null);
        }

        Set<Integer> ctsStoreTypeSet = new HashSet<>();
        ctsStoreTypeSet.add(BrandEnum.YU_FAMILY.getCode());

        ClientBasicInfoVO clientBasicInfoVO = tabClientConvert.tabClientPO2UserBasicInfoVO(
                tabClientInfo);
        if (Objects.nonNull(clientBasicInfoVO.getServeCityId())) {
            clientBasicInfoVO.setServeCityName(heAddressService.getNameById(clientBasicInfoVO.getServeCityId()));
        }

        if (ctsStoreTypeSet.contains(ecpStorePO.getType())) {
            // 是予家的门店，需要特殊处理予家的来源渠道
            String typeName = CtsChannelTypeEnum.getValueByCode(tabClientInfo.getCtsFromType());
            if (CtsChannelTypeEnum.CONFINEMENT_CLUB.getCode().equals(tabClientInfo.getCtsFromType())
                && Objects.nonNull(tabClientInfo.getCtsFromTypeStore())) {
                // 是月子会所来的
                EcpStorePO ctsStoreInfo = cfgStoreService.queryStoreByStoreId(tabClientInfo.getCtsFromTypeStore());
                if (Objects.nonNull(ctsStoreInfo)) {
                    typeName += ("/" + ctsStoreInfo.getStoreName());
                }
            }
            clientBasicInfoVO.setFromTypeName(typeName);
        } else {
            clientBasicInfoVO.setFromTypeName(CustomerFromTypeEnum.getValueByCode(tabClientInfo.getFromType()));
        }

        // 获取客户微信头像信息
        TabWechatUserPO tabWechatUserPO = tabWechatUserService.queryWechatInfoByBasicUid(
                tabClientInfo.getBasicUid());

        if (ObjectUtil.isNotNull(tabWechatUserPO)) {
            clientBasicInfoVO.setAvatarUrl(tabWechatUserPO.getAvatarUrl());
        }

        // 获取客户手机号唯一基本信息
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(
                tabClientInfo.getBasicUid().longValue());

        if (ObjectUtil.isNotNull(userBasicPO)) {
            clientBasicInfoVO.setName(userBasicPO.getName());
            clientBasicInfoVO.setPhone(userBasicPO.getPhone());
            clientBasicInfoVO.setPredictBornTime(userBasicPO.getManualPredictBornTime());
            String predictBornTimeRemark = getPredictBornTimeRemark(userBasicPO.getManualPredictBornTime());
            clientBasicInfoVO.setBornTimeRemark(predictBornTimeRemark);
        }

        // 隐藏手机号中间四位
        clientBasicInfoVO.setHidePhone(SensitiveInfoUtil.mobileEncrypt(clientBasicInfoVO.getPhone()));

        // 获取客户其他信息
        HeClientExpandPeriodPO extendInfo = heClientExpandPeriodService.getOne(
                new LambdaQueryWrapper<HeClientExpandPeriodPO>()
                        .eq(HeClientExpandPeriodPO::getEcpCid, request.getClientUid())
                        .last("limit 1"));

        if (ObjectUtil.isNotNull(extendInfo)) {
            clientBasicInfoVO.setHospital(extendInfo.getHospital());
            clientBasicInfoVO.setGestationWeekNow(extendInfo.getGestationWeekNow());
        }

        // 获取客户标签
        if (!StringUtils.isEmpty(tabClientInfo.getTags())) {
            //过滤首位为逗号的情况
            String tags = tabClientInfo.getTags();
            if (tags.startsWith(",")) {
                tags = tags.substring(1, tabClientInfo.getTags().length());
            }
            String[] tagIds = tags.split(",");
            List<Long> tagIdList = Arrays.stream(tagIds).map(Long::parseLong)
                    .collect(Collectors.toList());

            List<HeTagsPO> heTagsPOList = heTagsService.queryTagListByTagIds(tagIdList);
            if (ObjectUtil.isNotNull(heTagsPOList)) {
                List<ClientTagVO> clientTagVOList = tabClientConvert.tagsPOList2ClientTagVOList(heTagsPOList);
                clientBasicInfoVO.setTagList(clientTagVOList);
            }
        }

        //查看客户是否使用圣贝拉/小贝拉小程序
        //查看客户是否关注公众号
        clientBasicInfoVO.setXcxStatus(WxStateEnum.WX_STATE_NO_USE.code());
        clientBasicInfoVO.setMpStatus(WxStateEnum.WX_STATE_NO_USE.code());
        List<TabWechatUserPO> tabWechatUserPOList = tabWechatUserService.queryWechatInfoByBasicUidList(tabClientInfo.getBasicUid());
        //getFromType 小程序类型:1=圣贝拉;2=小贝拉;100=艾屿
        Optional<TabWechatUserPO> first = tabWechatUserPOList.stream()
                .filter(t -> Objects.equals(t.getFromType() - 1, ecpStorePO.getType()) || Objects.equals(t.getFromType(), StoreTypeEnum.ISLA_BELLA.code()))
                .findFirst();
        if (first.isPresent()) {
            clientBasicInfoVO.setXcxStatus(WxStateEnum.WX_STATE_USED.code());
        }

        if (first.isPresent() && StringUtils.hasLength(first.get().getUnionid())) {
            CustomerWechatFansListRequest fansListRequest = new CustomerWechatFansListRequest();
            fansListRequest.setUnionId(first.get().getUnionid());
            if (Arrays.asList(StoreTypeEnum.SAINT_BELLA.code(), StoreTypeEnum.BABY_BELLA.code(), StoreTypeEnum.ISLA_BELLA.code()).contains(ecpStorePO.getType())) {
                if (Objects.equals(ecpStorePO.getType(), StoreTypeEnum.SAINT_BELLA.code())) {
                    fansListRequest.setSource(2);
                }
                if (Objects.equals(ecpStorePO.getType(), StoreTypeEnum.BABY_BELLA.code())) {
                    fansListRequest.setSource(1);
                }
                if (Objects.equals(ecpStorePO.getType(), StoreTypeEnum.ISLA_BELLA.code())) {
                    fansListRequest.setSource(100);
                }
                List<CustomerWechatFansListVO> customerWechatFansListVOS = wechatFansService.queryWechatFans(fansListRequest);
                if (!CollectionUtils.isEmpty(customerWechatFansListVOS)) {
                    clientBasicInfoVO.setMpStatus(WxStateEnum.WX_STATE_USED.code());
                }
            }
        }

        return Result.success(clientBasicInfoVO);
    }

    private static String getPredictBornTimeRemark(Long manualPredictBornTime) {
        if (manualPredictBornTime == -1) {
            return "备孕中";
        }
        if (manualPredictBornTime == 0) {
            return "去设置";
        }

        long day = 280L;
        // 怀孕周期根据预产期按照280天倒推计算孕周（即40周）计算，超过280天后在“我的孕期”回显“已过预产期X天”
        // 先获取怀孕期
        long pregnancyTime = manualPredictBornTime - (day * 86400);

        long newDate = System.currentTimeMillis() / 1000;

        //怀孕期大于当前时间
        if (pregnancyTime > newDate) {
            return "怀孕 0 周 0 天";
        }

        day = DateUtils.dateDifDay(new Date(pregnancyTime * 1000), new Date());
        int week = Math.toIntExact(day / 7);
        int remainder = Math.toIntExact(day % 7);
        String remark = "怀孕" + week + " 周" + remainder + " 天";
        if (week >= 40 && remainder > 0) {
            remark = "已过预产期" + ((week - 40) * 7 + remainder) + "天";
        }
        return remark;

    }

    /**
     * 保存客户基本信息
     *
     * @param tabClient
     * @return
     */
    private Long saveTabClientInfo(TabClientPO tabClient) {
        // 查询用户信息
        TabClientPO tabClientPO = getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getId, tabClient.getId())
                .last("limit 1"));

        BeanUtil.copyProperties(tabClient, tabClientPO);
        // 手机号不能修改
        tabClientPO.setPhone(null);
        tabClientPO.setUpdateTime(new DateTime().toLocalDateTime());

        // 如果客户的生日为空，且证件号是身份证，则获取一下客户的生日
        if (Objects.isNull(tabClientPO.getBirthdate())
            && !StringUtils.isEmpty(tabClient.getIdCard())
            && Objects.equals(UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code(), tabClient.getCertType())) {
            String birthByIdNo = UMSUtils.getBirthByIdNo(tabClient.getIdCard());
            if (Objects.nonNull(birthByIdNo)) {
                DateTime birthday = DateUtil.parse(birthByIdNo);
                tabClientPO.setBirthdate(birthday);
            }
        }

        // 查看客户是否在备孕期
        try {
            Date defaultPreparationPeriodDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2099-12-31 00:00:00");

            if (ObjectUtil.isNotNull(tabClient.getPredictBornDate()) && tabClient.getPredictBornDate().equals(defaultPreparationPeriodDate)) {
                // 客户为备孕期，要添加备孕中的标签
                // 查询备孕期的标签
                TagReq tagReq = new TagReq();
                tagReq.setTagName(PREPARATION_PERIOD_TAG_NAME);
                tagReq.setTagObj(0);

                TagVO tagVO = tagsService.queryTagByReq(tagReq);
                if (ObjectUtil.isNotNull(tagVO)) {
                    // 查看客户的标签
                    if (StringUtils.hasLength(tabClientPO.getTags())) {
                        String[] tags = tabClientPO.getTags().split(",");
                        List<Integer> tagIdList = Arrays.stream(tags).map(Integer::parseInt)
                                .collect(Collectors.toList());

                        if (ObjectUtil.isNotNull(tagIdList) && !tagIdList.contains(tagVO.getId())) {
                            tagIdList.add(tagVO.getId());

                            String newTags = org.apache.commons.lang3.StringUtils.join(tagIdList, ",");
                            tabClientPO.setTags(newTags);

                            // 更新occ的标签
                            HeClientOccPO heClientOccPO = heClientOccService.queryClientOccInfoByClientUid(
                                    tabClientPO.getId().intValue());
                            if (ObjectUtil.isNotNull(heClientOccPO)) {
                                heClientOccPO.setTags(newTags);
                                heClientOccService.updateClientOcc(heClientOccPO);
                            }
                        }
                    }
                }
            }
        } catch (ParseException e) {
            log.info("定义默认备孕期的时间错误:{}", e.getMessage());
        }

        log.info("保存客户基本数据:{}", tabClientPO);
        boolean result = updateById(tabClientPO);
        return result ? tabClientPO.getId() : null;
    }

    /**
     * 获取缓存key
     *
     * @param basePrefix 基础前缀
     * @param connect    连接符号
     * @param keyContent key内容
     * @return
     */
    private static String getRedisCacheKey(String basePrefix, String connect, String... keyContent) {
        StringBuilder sb = new StringBuilder();

        sb.append(basePrefix).append(connect);

        if (keyContent.length > 0) {
            for (String s : keyContent) {
                sb.append(s).append(connect);
            }
        }
        return sb.substring(0, sb.length() - 1);
    }

    @Override
    public Result<List<PostpartumCustomerVO>> postpartumCustomerList(CustomerPostpartumListRequest request) {
        if (Objects.isNull(request.getStoreId()) || org.apache.commons.lang3.StringUtils.isBlank(request.getKeyword())) {
            return Result.success(Collections.emptyList());
        }

        List<TabClientPO> list = this.list(new LambdaQueryWrapper<TabClientPO>()
                .select(TabClientPO::getId, TabClientPO::getName, TabClientPO::getPhone, TabClientPO::getStoreId, TabClientPO::getBasicUid)
                .eq(TabClientPO::getStoreId, request.getStoreId())
                .and(w -> w.like(TabClientPO::getName, request.getKeyword()).or().like(TabClientPO::getPhone, request.getKeyword())));
        if (CollectionUtil.isEmpty(list)) {
            return Result.success(Collections.emptyList());
        }

        EcpStorePO ecpStorePO = cfgStoreService.queryStoreByStoreId(request.getStoreId());
        List<PostpartumCustomerVO> dataList = new ArrayList<>();
        for (TabClientPO tabClient : list) {
            PostpartumCustomerVO customer = BeanMapper.map(tabClient, PostpartumCustomerVO.class);
            customer.setPhone(DesensitizedUtil.mobilePhone(tabClient.getPhone()));
            customer.setStoreName(Objects.nonNull(ecpStorePO) ? ecpStorePO.getStoreName() : null);
            customer.setBasicId(tabClient.getBasicUid());
            dataList.add(customer);
        }
        return Result.success(dataList);
    }


    @Override
    public List<TabClientPO> listByCustomerIdList(List<Long> customerIdList) {
        if (CollectionUtil.isEmpty(customerIdList)) {
            return new ArrayList<>();
        }
        List<TabClientPO> tabClientList = this.listByIds(customerIdList);
        return CollectionUtil.isEmpty(tabClientList) ? new ArrayList<>() : tabClientList;
    }

    @Override
    public List<Long> queryTabClientBasicIdByFromTypeOrSellerId(String sellerName, Integer fromType) {

        List<Long> userIdList = userService.list(new LambdaQueryWrapper<UserPO>()
                        .eq(StringUtil.isNotEmpty(sellerName), UserPO::getName, sellerName)
                        .eq(UserPO::getActive, 1)
                        .select(UserPO::getId))
                .stream()
                .map(UserPO::getId)
                .collect(Collectors.toList());

        List<Long> basicIdList = list(new LambdaQueryWrapper<TabClientPO>()
                .in(CollectionUtil.isNotEmpty(userIdList), TabClientPO::getSellerId, userIdList)
                .eq(fromType != null, TabClientPO::getFromType, fromType)
                .eq(TabClientPO::getActive, 1)
                .select(TabClientPO::getBasicUid))
                .stream()
                .map(i -> i.getBasicUid().longValue())
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(basicIdList)) {
            return new ArrayList<>();
        }
        return basicIdList;
    }

    @Override
    public Map<Integer, TabClientPO> getFirstClientMapByBasicIdAndBrandType(Set<Integer> basicId, Integer brandType) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(basicId)) {
            return new HashMap<>();
        }

        List<EcpStorePO> ecpStorePOList = cfgStoreService.queryByBrandType(brandType);
        if (CollectionUtil.isEmpty(ecpStorePOList)) {
            return new HashMap<>();
        }

        List<Integer> brandStoreIds = ecpStorePOList.stream()
                .map(EcpStorePO::getStoreId)
                .collect(Collectors.toList());

        List<TabClientPO> list = this.list(new LambdaQueryWrapper<TabClientPO>()
                .in(TabClientPO::getBasicUid, basicId)
                .in(TabClientPO::getStoreId, brandStoreIds)
                .orderByAsc(TabClientPO::getId));
        return org.apache.commons.collections4.CollectionUtils
                .emptyIfNull(list).stream().collect(Collectors.toMap(TabClientPO::getBasicUid, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<Integer, TabClientPO> getFirstClientMapByBasicId(Set<Integer> basicId) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(basicId)) {
            return new HashMap<>();
        }
        List<TabClientPO> list = this.list(new LambdaQueryWrapper<TabClientPO>()
                .in(TabClientPO::getBasicUid, basicId)
                .orderByAsc(TabClientPO::getId));
        return org.apache.commons.collections4.CollectionUtils
                .emptyIfNull(list).stream().collect(Collectors.toMap(TabClientPO::getBasicUid, Function.identity(), (a, b) -> a));
    }

    /**
     * 查询指定日期生日的客户
     *
     * @param date
     * @param brandType
     * @return
     */
    @Override
    public List<TabClientPO> getBirthdayUserListByDate(Date date, Integer brandType) {
        // 获取门店列表
        List<EcpStorePO> storePOList = cfgStoreService.queryByBrandType(brandType);
        List<Integer> storeIdList = storePOList.stream()
                .map(EcpStorePO::getStoreId)
                .collect(Collectors.toList());

        int month = DateUtil.month(date) + 1;
        int day = DateUtil.dayOfMonth(date);

        return baseMapper.queryBirthdayUserListByDate(month, day, storeIdList);
    }

    /**
     * 查询指定月份内生日的客户
     *
     * @param month
     * @param brandType
     * @return
     */
    @Override
    public List<TabClientPO> getBirthdayUserListByMonth(Integer month, Integer brandType) {
        // 获取门店列表
        List<EcpStorePO> storePOList = cfgStoreService.queryByBrandType(brandType);
        List<Integer> storeIdList = storePOList.stream()
                .map(EcpStorePO::getStoreId)
                .collect(Collectors.toList());

        return baseMapper.queryBirthdayUserListByMonth(month, storeIdList);
    }

    /**
     * 通过id 查询客户
     *
     * @param id
     * @return
     */
    @Override
    public TabClientPO queryClientById(Integer id) {
        return getOne(new LambdaQueryWrapper<TabClientPO>().eq(TabClientPO::getId, id.longValue()));
    }

    /**
     * 通过手机号和门店查询客户
     *
     * @param phone
     * @param storeId
     * @return
     */
    @Override
    public TabClientPO queryClientByPhoneStore(String phone, Integer storeId) {
        return getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getPhone, phone)
                .eq(TabClientPO::getStoreId, storeId)
                .eq(TabClientPO::getActive, 1)
                .last("limit 1"));
    }

    @Override
    public Result<List<TabClientPO>> queryClientListByBasicUid(Integer basicUid) {
        return Result.success(this.list(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getBasicUid, basicUid)));
    }


    /**
     * 根据客户id获取客户个人信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<ClientInfoVO> queryClientInfoByIdForAuth(ClientSearchByIdRequest request) {
        log.info("根据客户id查询用户信息:{}", JSONUtil.toJsonStr(request));
        BPCheckUtil.checkEmptyInBean(new String[]{"id"}, request, false);

        TabClientPO tabClientInfo = getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getId, request.getId()));

        log.info("客户基本信息为:{}", JSONUtil.toJsonStr(tabClientInfo));

        if (ObjectUtil.isNull(tabClientInfo)) {
            // 如果未查到客户信息，则返回空
            return Result.success(null);
        }
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(tabClientInfo.getBasicUid().longValue());

        List<HeUserCardPO> heUserCardPOS = heUserCardService
                .queryUserCardInfoByBasicUid(tabClientInfo.getBasicUid());

        log.info("客户认证信息为:{}", JSONUtil.toJsonStr(heUserCardPOS));
        if (!CollectionUtil.isEmpty(heUserCardPOS)){
            heUserCardPOS = heUserCardPOS.stream().filter(item -> Objects.nonNull(item.getCertType())).filter(item -> UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code().equals(item.getCertType()) || (AuthTypeEnum.EMAIL_AUTH.code().equals(item.getAuthType()) && !StringUtils.isEmpty(item.getEmail()))).collect(Collectors.toList());
        }

        ClientInfoVO clientInfoVO = new ClientInfoVO();
        clientInfoVO.setClientUid(tabClientInfo.getId().intValue());
        clientInfoVO.setBasicUid(tabClientInfo.getBasicUid());
        clientInfoVO.setStoreId(tabClientInfo.getStoreId());
        clientInfoVO.setPhone(tabClientInfo.getPhone());
        clientInfoVO.setPhoneType(tabClientInfo.getPhoneType());
        clientInfoVO.setName(tabClientInfo.getName());

        if (ObjectUtil.isNotNull(userBasicPO)) {
            clientInfoVO.setName(userBasicPO.getName());
            clientInfoVO.setPhone(userBasicPO.getPhone());
        }

        clientInfoVO.setConstellationType(tabClientInfo.getConstellationType());
        clientInfoVO.setConstellation(tabClientInfo.getConstellation());
        clientInfoVO.setProvince(tabClientInfo.getProvince());
        clientInfoVO.setCity(tabClientInfo.getCity());
        clientInfoVO.setRegion(tabClientInfo.getRegion());
        clientInfoVO.setAddress(tabClientInfo.getAddress());
        clientInfoVO.setBornNum(1);//默认首胎
        clientInfoVO.setUrgentName(tabClientInfo.getUrgentName());
        clientInfoVO.setUrgentPhone(tabClientInfo.getUrgentPhone());
        clientInfoVO.setAge(tabClientInfo.getAge());
        clientInfoVO.setRelationWithClient(tabClientInfo.getRelationWithClient());
        clientInfoVO.setBloodType(tabClientInfo.getBloodType());
        clientInfoVO.setFromType(tabClientInfo.getFromType());
        clientInfoVO.setProfession(tabClientInfo.getProfession());
        // 检查客户的手机号是否已认证,SCRM新逻辑,只要客户手机号认证过,都算认证过
        LambdaQueryWrapper<TabClientPO> lq = new LambdaQueryWrapper<>();
        lq.eq(TabClientPO::getPhone, tabClientInfo.getPhone());
        lq.eq(TabClientPO::getIsPhoneVerify, 1);
        List<TabClientPO> list = this.list(lq);
        if (ObjectUtil.isNotEmpty(list)) {
            clientInfoVO.setIsPhoneVerify(1);
        } else {
            clientInfoVO.setIsPhoneVerify(tabClientInfo.getIsPhoneVerify());
        }
        if (CollectionUtil.isNotEmpty(heUserCardPOS)) {
            HeUserCardPO userCardPO = null;
            for (HeUserCardPO listheUserCardPO : heUserCardPOS) {
                if (Objects.isNull(request.getAuthType()) && Objects.equals(listheUserCardPO.getCertType(), tabClientInfo.getCertType())) {
                    userCardPO = listheUserCardPO;
                    break;
                }
                if (Objects.nonNull(request.getAuthType()) && Objects.equals(listheUserCardPO.getAuthType(), request.getAuthType())){
                    userCardPO = listheUserCardPO;
                    break;
                }
            }

            //设置是否可以修改用户四要素(目前基于用户是否签过主合同判断)
            clientInfoVO.setSignMasterContract(SignMasterContract(userCardPO));
            if (ObjectUtil.isNotNull(userCardPO)) {
                clientInfoVO.setName(userCardPO.getName());
                clientInfoVO.setPhone(userCardPO.getPhone());
                clientInfoVO.setCertType(userCardPO.getCertType());
                clientInfoVO.setIdCard(userCardPO.getIdCard());
                clientInfoVO.setIdCardFront(userCardPO.getIdCardFront());
                clientInfoVO.setIdCardBack(userCardPO.getIdCardBack());
                clientInfoVO.setAuthType(userCardPO.getAuthType());
                clientInfoVO.setEmail(userCardPO.getEmail());
                clientInfoVO.setEmailVerify(userCardPO.getVerifyState());

                Integer userCardVerifyStatus = heUserEsignService.getUserCardVerifyStatus(
                        userCardPO.getPhone(), userCardPO.getName(), userCardPO.getIdCard(),
                        userCardPO.getCertType(), userCardPO.getAuthType());
                clientInfoVO.setIsCardVerify(userCardVerifyStatus);
            }
        }

        // 获取邀请码信息
        InviteInfoVO inviteInfoVO = heInviteRelationService
                .queryUserBindInfoByBasicId(tabClientInfo.getBasicUid().longValue());

        clientInfoVO.setIsHaveQrCode(0);
        if (ObjectUtil.isNotNull(inviteInfoVO)) {
            clientInfoVO.setQrCode(inviteInfoVO.getParentQrCode());
            clientInfoVO.setIsHaveQrCode(1);
            clientInfoVO.setInviteInfo(inviteInfoVO);
        }
        clientInfoVO.setScrmId(tabClientInfo.getScrmId());
        return Result.success(clientInfoVO);
    }

    /**
     * 通过手机后几位&门店查询客户列表
     */
    @Override
    public PageVO<QueryClientInfoVO> listByLastPhone(QueryClientPageReq req) {
        String mobile = req.getMobile();
        Integer storeId = req.getStoreId();

        PageVO<QueryClientInfoVO> re = new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum());

        if (StringUtils.isEmpty(mobile) || Objects.isNull(storeId)) return re;

        // 分页查询TabClient
        Page<TabClientPO> page = this.page(
                new Page<>(req.getPageNum(), req.getPageSize()),
                new LambdaQueryWrapper<TabClientPO>().likeLeft(TabClientPO::getPhone, mobile).eq(TabClientPO::getStoreId, storeId)
        );

        if (CollectionUtil.isEmpty(page.getRecords())) return re;

        List<TabClientPO> records = page.getRecords();
        List<Integer> basicIds = records.stream().map(TabClientPO::getBasicUid).collect(Collectors.toList());

        // 获取客户微信头像信息
        List<TabWechatUserPO> wechatUserList = tabWechatUserService.list(
                new LambdaQueryWrapper<TabWechatUserPO>()
                        .in(TabWechatUserPO::getBasicUid, basicIds)
                        .orderByDesc(TabWechatUserPO::getUpdatedAt)
        );

        EcpStorePO store = cfgStoreService.queryStoreByStoreId(req.getStoreId());

        // data assemble
        List<QueryClientInfoVO> data = records.stream().map(o -> {
            QueryClientInfoVO map = BeanMapper.map(o, QueryClientInfoVO.class);
            map.setPhone(SensitiveInfoUtil.mobileEncrypt(map.getPhone()));

            // 头像设置
            Optional<TabWechatUserPO> first = wechatUserList.stream().filter(usr -> Objects.equals(usr.getBasicUid(), o.getBasicUid())).findFirst();
            first.ifPresent(usr -> map.setAvatarUrl(usr.getAvatarUrl()));

            if (Objects.nonNull(store)) {
                map.setStoreName(store.getStoreName());
            }
            return map;
        }).collect(Collectors.toList());

        return new PageVO<>(data, (int) page.getTotal(), req.getPageSize(), req.getPageNum());
    }

    @Override
    public Result<ClientCardTypeVO> queryCardList(ClientCardTypeRequest request) {

        ClientCardTypeVO  clientCardTypeVO = new ClientCardTypeVO();
        clientCardTypeVO.setSupportOption(Boolean.FALSE);
        List<HeUserCardPO> heUserCardPOS = heUserCardService
                .queryUserCardInfoByBasicUid(request.getBasicUid());
        if (CollectionUtils.isEmpty(heUserCardPOS)){
           return Result.success(clientCardTypeVO);
        }
        Set<Integer> authTypeList = heUserCardPOS.stream().map(HeUserCardPO::getAuthType).filter(Objects::nonNull).collect(Collectors.toSet());
        clientCardTypeVO.setSupportOption(authTypeList.size() > 1 ? Boolean.TRUE : Boolean.FALSE);
        return Result.success(clientCardTypeVO);
    }

    @Override
    public Result<List<HeUserCardVO>> getCardList(ClientCardTypeRequest request) {

        log.info("根据客户id查询用户信息:{}", JSONUtil.toJsonStr(request));
        List<HeUserCardPO> heUserCardPOS = heUserCardService
                .queryUserCardInfoByBasicUid(request.getBasicUid());
        if (CollectionUtils.isEmpty(heUserCardPOS)){
            return Result.success(Lists.newArrayList());
        }
        return Result.success(BeanMapper.mapList(heUserCardPOS, HeUserCardVO.class));
    }

    /**
     * 查询指定日期生日的客户的basicId
     *
     * @param date
     * @return
     */
    @Override
    public List<Integer> queryBirthdayUserBasicIdByDate(Date date) {
        int month = DateUtil.month(date) + 1;
        int day = DateUtil.dayOfMonth(date);

        return baseMapper.queryBirthdayUserBasicIdByDate(month, day);
    }

    @Override
    public List<TabClientPO> queryUserLastActiveStoreByBasicIds(List<Integer> basicIds) {
        if (CollectionUtils.isEmpty(basicIds)) {
            return Lists.newArrayList();
        }

        List<Integer> storeIds = cfgStoreService.queryAllMotherStores().stream()
            .map(EcpStorePO::getStoreId)
            .collect(Collectors.toList());
        return baseMapper.queryUserLastActiveStoreByBasicIds(basicIds, storeIds);
    }

    /**
     * 予家雇主、育婴师同步
     *
     * @param request
     */
    @Override
    @Async
    public void ctsCustomerInfoSync(CtsCustomerInfoEditRequest request) {
        log.info("予家客户信息同步, request:{}", JSONUtil.toJsonStr(request));

        // 数据转换
        TabClientPO tabClientPO = tabClientConvert.ctsCustomerInfoEditRequest2TabClientPO(request);

        CtsSiteConfig newStoreConfig;
        if (request.getCustomerType().equals(1)) {
            newStoreConfig = ctsSiteMappingConfig.getConfigBySiteId(request.getCtsSiteId());
        } else {
            newStoreConfig = ctsSiteMappingConfig.getCstAuntStore();
        }

        if (Objects.isNull(newStoreConfig)) {
            log.error("予家站点转换失败，未找到配置, siteId:{}", request.getCtsSiteId());
            return;
        }
        tabClientPO.setStoreId(newStoreConfig.getStoreId());
        tabClientPO.setUpdateTime(new DateTime().toLocalDateTime());

        if (Objects.nonNull(request.getOperatorId())) {
            UserPO userPO = userService.queryUserByEmployeeId(request.getOperatorId());
            if (Objects.nonNull(userPO)) {
                tabClientPO.setSellerId(userPO.getId().intValue());
                tabClientPO.setUpId(userPO.getId().intValue());
            }
        }

        long nowTime = new DateTime().getTime() / 1000;

        // 查询客户是否存在
        TabClientPO oldTabClientPO = queryClientByPhoneStore(tabClientPO.getPhone(), tabClientPO.getStoreId());
        if (Objects.nonNull(oldTabClientPO)) {
            // 客户已存在，需要更新
            BeanUtil.copyProperties(tabClientPO, oldTabClientPO, CopyOptions.create().ignoreNullValue());
            updateById(oldTabClientPO);
            tabClientPO = oldTabClientPO;
        } else {
            // 客户不存在，需要新建
            Long basicId = heUserBasicService.getBasicIdOrCreateUserBasic(tabClientPO.getPhone());
            if (Objects.isNull(basicId)) {
                log.error("予家客户创建失败 ，basic未创建成功, phone:{}", tabClientPO.getPhone());
                return;
            }

            tabClientPO.setBasicUid(basicId.intValue());
            tabClientPO.setFromType(ClientFromTypeEnum.FROM_TYPE_29.getCode());



            tabClientPO.setRecordTime(new DateTime().toLocalDateTime());

            save(tabClientPO);
        }

        // 更新userBaisc信息
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(tabClientPO.getBasicUid().longValue());
        if (Objects.nonNull(userBasicPO)) {
            userBasicPO.setName(tabClientPO.getName());
            if (!StringUtils.isEmpty(tabClientPO.getIdCard())) {
                userBasicPO.setIdCardNo(tabClientPO.getIdCard());
            }
            userBasicPO.setUpdatedAt(nowTime);
            heUserBasicService.updateById(userBasicPO);

            // 更新用户卡包信息
            if (!StringUtils.isEmpty(tabClientPO.getIdCard())) {
                HeUserCardDTO userCardRequest = new HeUserCardDTO();
                userCardRequest.setBasicUid(tabClientPO.getBasicUid());
                userCardRequest.setName(tabClientPO.getName());
                userCardRequest.setPhone(tabClientPO.getPhone());
                userCardRequest.setCertType(tabClientPO.getCertType());
                userCardRequest.setIdCard(tabClientPO.getIdCard());

                heUserCardService.saveUserCardInfo(userCardRequest);
            }
        }

        // 插入occ表
        HeClientOccPO clientOccPO = new HeClientOccPO();
        clientOccPO.setClientType(1);
        clientOccPO.setStoreId(tabClientPO.getStoreId());
        clientOccPO.setFromType(tabClientPO.getFromType());
        clientOccPO.setCtsFromType(tabClientPO.getCtsFromType());
        clientOccPO.setRegisterFrom(3);
        clientOccPO.setConsultingTime((int) nowTime);
        clientOccPO.setDistributionTime((int) nowTime);
        clientOccPO.setBasicUid(tabClientPO.getBasicUid());
        clientOccPO.setClientUid(tabClientPO.getId().intValue());
        clientOccPO.setStoreType(BrandEnum.YU_FAMILY.getCode());
        clientOccPO.setLastAdminId(tabClientPO.getSellerId());

        heClientOccService.saveClientOcc(clientOccPO);

        heUserNewestService.addUserNewestInfo(tabClientPO);

        // 更新scrm
        ScrmCustomerPO scrmCustomerPO = syncCustomerToScrm(tabClientPO);
        if (Objects.nonNull(scrmCustomerPO)
            && (StringUtils.isEmpty(tabClientPO.getScrmId())
            || (!StringUtils.isEmpty(tabClientPO.getScrmId()) && tabClientPO.getScrmId().equals(scrmCustomerPO.getScrmCustomerId().toString())))) {
            tabClientPO.setScrmId(scrmCustomerPO.getScrmCustomerId().toString());
            updateById(tabClientPO);
        }
    }

    private ScrmCustomerPO syncCustomerToScrm(TabClientPO po) {
        ScrmCustomerCreateRequest request = new ScrmCustomerCreateRequest();
        request.setPhone(po.getPhone());
        request.setName(po.getName());
        request.setCertType(po.getCertType());
        request.setIdCard(po.getIdCard());
        request.setUrgentName(po.getUrgentName());
        request.setUrgentPhone(po.getUrgentPhone());
        request.setNation(po.getNation());
        request.setBirthdate(po.getBirthdate());
        request.setConstellationType(po.getConstellationType());
        request.setConstellation(po.getConstellation());
        request.setFetusNum(po.getFetusNum());

        if (Objects.nonNull(po.getProvince()) && !po.getProvince().equals(-1)) {
            request.setProvince(po.getProvince());
        }
        if (Objects.nonNull(po.getCity()) && !po.getCity().equals(-1)) {
            request.setCity(po.getCity());
        }
        if (Objects.nonNull(po.getRegion()) && !po.getRegion().equals(-1)) {
            request.setRegion(po.getRegion());
        }
        if (!StringUtils.isEmpty(po.getAddress())) {
            request.setAddress(po.getAddress());
        }

        request.setFromType(ClientFromTypeEnum.FROM_TYPE_29.getCode());
        request.setCtsFromType(po.getCtsFromType());
        request.setCtsFromTypeStore(po.getCtsFromTypeStore());
        request.setServeProvinceId(po.getServeProvinceId());
        request.setServeCityId(po.getServeCityId());

        return scrmCustomerService.createCustomer2Scrm(request);
    }

    /**
     * 创建客户
     *
     * @param tabClientPO
     * @return
     */
    @Override
    public Long createTabClient(TabClientPO tabClientPO) {
        if (Objects.isNull(tabClientPO)
            || StringUtils.isEmpty(tabClientPO.getPhone())
            || Objects.isNull(tabClientPO.getStoreId())) {
            return null;
        }

        if (!tabClientPO.getPhone().matches("^[0-9]+$")) {
            return null;
        }

        TabClientPO oldTabClient = queryClientByPhoneStore(tabClientPO.getPhone(), tabClientPO.getStoreId());
        if (Objects.nonNull(oldTabClient)) {
            return oldTabClient.getId();
        }

        Long basicId = heUserBasicService.getBasicIdOrCreateUserBasic(tabClientPO.getPhone());
        if (Objects.isNull(basicId)) {
            log.error("创建basic信息失败，phone:{}", tabClientPO.getPhone());
            return null;
        }

        tabClientPO.setBasicUid(basicId.intValue());
        save(tabClientPO);

        heClientOccService.createClientOcc(tabClientPO);
        return tabClientPO.getId();
    }

    /**
     * 通过手机号获取育婴师信息
     *
     * @param phone
     * @return
     */
    @Override
    public TabClientPO queryFamilyClientByPhone(String phone) {
        CtsSiteConfig newStoreConfig = ctsSiteMappingConfig.getCstAuntStore();
        return queryClientByPhoneStore(phone, newStoreConfig.getStoreId());
    }
}