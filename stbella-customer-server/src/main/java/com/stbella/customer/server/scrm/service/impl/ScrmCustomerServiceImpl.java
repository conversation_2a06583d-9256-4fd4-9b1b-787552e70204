package com.stbella.customer.server.scrm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.cts.enums.CustomerAssetsTypeEnum;
import com.stbella.customer.server.cts.request.SCRMClockStoreRequest;
import com.stbella.customer.server.customer.entity.CustomerAssetsPO;
import com.stbella.customer.server.customer.entity.CustomerStoreStaffConfigPO;
import com.stbella.customer.server.customer.enums.ScrmCustomerStatusEnum;
import com.stbella.customer.server.customer.service.CustomerAssetsService;
import com.stbella.customer.server.customer.service.CustomerStoreStaffConfigService;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.enums.UserCardCertTypeEnum;
import com.stbella.customer.server.ecp.service.HeUserBasicService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.scrm.constant.ScrmQwCustomerMessageConstant;
import com.stbella.customer.server.scrm.convert.SCRMConvert;
import com.stbella.customer.server.scrm.dto.*;
import com.stbella.customer.server.scrm.entity.*;
import com.stbella.customer.server.scrm.enums.*;
import com.stbella.customer.server.scrm.manager.ScrmManager;
import com.stbella.customer.server.scrm.manager.SmsManager;
import com.stbella.customer.server.scrm.mapper.ScrmCustomerMapper;
import com.stbella.customer.server.scrm.request.*;
import com.stbella.customer.server.scrm.request.PicpUserInitRequest.OrderRequest;
import com.stbella.customer.server.scrm.request.PicpUserInitRequest.SaleUserRequest;
import com.stbella.customer.server.scrm.service.*;
import com.stbella.customer.server.scrm.vo.PicpUserInitVO;
import com.stbella.customer.server.scrm.vo.ScrmDescriptionResultVO.FieldData.SelectItemData;
import com.stbella.customer.server.scrm.vo.ScrmUserInfoVO;
import com.stbella.customer.server.scrm.vo.ScrmUserResponsibilyQueryVO;
import com.stbella.customer.server.scrm.vo.StoreAppointmentVO;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.customer.server.util.UMSUtils;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.store.server.ecp.entity.CfgStore;
import com.stbella.store.server.ecp.service.CfgStoreService;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * scrm客户中心 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-03-13
 */
@Service
@RefreshScope
@Slf4j
@DubboService
public class ScrmCustomerServiceImpl extends ServiceImpl<ScrmCustomerMapper, ScrmCustomerPO> implements ScrmCustomerService {

    @Resource
    private ScrmManager scrmManager;
    @Resource
    private SmsManager smsManager;
    @Resource
    private SCRMConvert scrmConvert;
    @Resource
    private ScrmCustomerBabyService scrmCustomerBabyService;
    @Resource
    private ScrmUserStoreConfigService scrmUserStoreConfigService;
    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;
    @Resource
    private XsyScrmService xsyScrmService;
    @Resource
    private ScrmConfigService scrmConfigService;
    @Resource
    private ScrmCustomerService scrmCustomerService;
    @Resource
    private ScrmOpportunityStatusRecordService scrmOpportunityStatusRecordService;
    @Resource
    private ScrmCustomerOrderService scrmCustomerOrderService;
    @Resource
    private ScrmCustomerOrderRefundService scrmCustomerOrderRefundService;
    @Resource
    private ScrmStaffPhoneConfigService scrmStaffPhoneConfigService;
    @Resource
    private CustomerAssetsService customerAssetsService;
    @Resource
    private HeUserBasicService heUserBasicService;
    @Resource
    private ScrmUserService scrmUserService;
    @Resource
    private ScrmBrandStoreConfigService scrmBrandStoreConfigService;

    @DubboReference
    private CfgStoreService cfgStoreService;

    @Resource
    private ScrmTeamMemberRecordService scrmTeamMemberRecordService;

    @Resource
    private CustomerStoreStaffConfigService customerStoreStaffConfigService;

    @Resource
    private TabClientService tabClientService;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 客户编辑 scrm->客户中心->PHP
     *
     * @param accountInfoRequest
     */
    @Override
    public Result<Integer> scrm2CustomPhp(AccountInfoRequest accountInfoRequest) {
        log.info("客户编辑 scrm->客户中心->PHP,参数={}", JSONUtil.parse(accountInfoRequest));
        //同步客户中心
        ScrmCustomerPO scrmCustomerPO = scrmManager.scrm2Custom(accountInfoRequest);
        //同步PHP
        scrmManager.custom2Php(scrmConvert.scrmCustomerPO2DTO(scrmCustomerPO));

        return Result.success(scrmCustomerPO.getId());
    }

    /**
     * 同步客户信息 php->客户中心->SCRM
     *
     * @param scrmCustomerDTO
     */
    @Override
    public Result<Long> php2Custom2Scrm(ScrmCustomerDTO scrmCustomerDTO) {
        log.info("同步客户信息 php->客户中心->SCRM,参数={}", JSONUtil.parse(scrmCustomerDTO));
        //同步客户中心
        ScrmCustomerPO scrmCustomerPO = scrmManager.php2Custom(scrmCustomerDTO);

        log.info("客户信息:{}", JSONUtil.toJsonStr(scrmCustomerPO));
        // 解析客户的生日
        if (ObjectUtil.isNull(scrmCustomerPO.getBirthdate())
            && StringUtils.isNotBlank(scrmCustomerPO.getIdCard())
            && ObjectUtil.isNotNull(scrmCustomerPO.getCertType())
            && scrmCustomerPO.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code())) {
            // 如果客户生日为空, 则解析身份证上的生日
            String birthByIdNo = UMSUtils.getBirthByIdNo(scrmCustomerPO.getIdCard());
            if (ObjectUtil.isNotNull(birthByIdNo)) {
                DateTime birthDay = DateUtil.parse(birthByIdNo);
                if (ObjectUtil.isNotNull(birthDay)) {
                    scrmCustomerPO.setBirthdate(birthDay);
                }
                log.info("解析出来的生日为:{}", birthByIdNo);
            }
        }

        //同步SCRM
        Long aLong = scrmManager.custom2Scrm(scrmCustomerPO);
        //回填scrmId
        scrmCustomerPO.setScrmCustomerId(aLong);
        this.saveOrUpdate(scrmCustomerPO);
        return Result.success(aLong);
    }

    /**
     * 同步客户信息 客户中心->php
     */
    @Override
    public Result<Long> custom2Php(ScrmCustomerDTO scrmCustomerDTO) {
        log.info("同步客户信息 客户中心->php,参数={}", JSONUtil.parse(scrmCustomerDTO));
        //同步php
        Long aLong = scrmManager.custom2Php(scrmCustomerDTO);
        return Result.success(aLong);
    }

    /**
     * 分配销售 scrm->客户中心->php
     */
    @Override
    public Result<Long> distributionCustomer(List<ScrmDistributionCustomerDTO> list) {
        log.info("分配销售 scrm->客户中心->php,参数={}", JSONUtil.parse(list));

        list.forEach(scrmDistributionCustomerDTO -> {
            ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
                .recordId(scrmDistributionCustomerDTO.getUserId())
                .userId(scrmDistributionCustomerDTO.getOwnerId())
                .type(1)
                .build();

            if (!scrmDistributionCustomerDTO.getOperateType().equals(2)) {
                scrmTeamMemberRecordService.edit(recordRequest);
            } else {
                scrmTeamMemberRecordService.delete(recordRequest);
            }
        });

        // 优先查看用户是否存在
        Long userId = list.get(0).getUserId();
        LambdaQueryWrapper<ScrmCustomerPO> lq = new LambdaQueryWrapper<>();
        lq.eq(ScrmCustomerPO::getScrmCustomerId, userId);
        ScrmCustomerPO one = this.getOne(lq);
        if (ObjectUtil.isEmpty(one)) {
            //throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户信息不存在,ScrmCustomerId=" + userId);
            // 去scrm中拉取客户，然后更新到我们的客户表中
            ScrmAccountQuery scrmAccountQuery = new ScrmAccountQuery();
            scrmAccountQuery.setScrmCustomerId(userId);
            AccountInfoRequest accountInfoRequest = xsyScrmService.queryScrmAccount(scrmAccountQuery);
            if (ObjectUtil.isNotEmpty(accountInfoRequest)) {
                AccountInfoRequest newAccountInfoRequest = xsyScrmService.converAccountScrmRequestToPicpRequest(accountInfoRequest);
                one = scrmConvert.accountInfoRequest2ScrmCustomerPO(newAccountInfoRequest);
                scrmCustomerService.save(one);
            }
        }

        //同步php
        Long aLong = scrmManager.distributionCustomer(list);

        //发送企微通知消息
        //只有新建才通知销售及上级
        if (ObjectUtil.equals(list.get(0).getOperateType(), 0)) {

            ScrmAccountQuery query = new ScrmAccountQuery();
            query.setScrmCustomerId(userId);
            AccountInfoRequest accountInfoRequest = xsyScrmService.queryScrmAccount(query);
            log.info("客户分配销售的时候获取客户信息，入参：{},出参：{}",userId,JSONUtil.toJsonStr(accountInfoRequest));


            LocalQwMsgRequest localQwMsgRequest = new LocalQwMsgRequest();
            localQwMsgRequest.setCustomerPhone(one.getPhone());
            localQwMsgRequest.setCustomerName(one.getName());
            //localQwMsgRequest.setTouser(touser);

            if(ObjectUtil.isNotEmpty(accountInfoRequest)){
                String accountName = accountInfoRequest.getAccountName();
                accountName = ObjectUtil.isEmpty(accountName)?"":accountName;

                String customerStatusStr ="";
                if(ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem230__c())){
                    String customerStatus = xsyScrmService.convertScrmOptionIdToPicpId("account", "customItem230__c", accountInfoRequest.getCustomItem230__c().longValue());
                    if(ObjectUtil.isNotEmpty(customerStatus)){
                        customerStatusStr = CustomerStatusEnum.getValueByCode(new Integer(customerStatus));
                    }
                }

                String customerPhone = accountInfoRequest.getPhone();
                customerPhone = ObjectUtil.isEmpty(customerPhone)?"":customerPhone;

                Long predictBornDate__c = accountInfoRequest.getPredictBornDate__c();
                String predictBornDateStr = "";
                if(ObjectUtil.isNotEmpty(predictBornDate__c)){
                    predictBornDateStr = DateUtil.formatDate(new Date(predictBornDate__c));
                }

                String fromTypeStr ="";
                if(ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem231__c())){
                    String fromTypeInt = xsyScrmService.convertScrmOptionIdToPicpId("account", "customItem231__c", accountInfoRequest.getCustomItem231__c().longValue());
                    if(ObjectUtil.isNotEmpty(fromTypeInt)){
                        fromTypeStr = CustomerFromTypeEnum.getValueByCode(new Integer(fromTypeInt));
                    }
                }

                String hospital__c = accountInfoRequest.getHospital__c();
                hospital__c = ObjectUtil.isEmpty(hospital__c)?"":hospital__c;

                // 胎数
                String fetusNum__c = ObjectUtil.isEmpty(accountInfoRequest.getFetusNum__c()) ? "" : accountInfoRequest.getFetusNum__c().toString();

                String remark = accountInfoRequest.getCustomItem251__c__c();
                remark = ObjectUtil.isEmpty(remark)?"":remark;

                // 月子套餐预算
//                String budgetStr = "";
//                if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem270__c())) {
//                    String budgetInt = xsyScrmService.convertScrmOptionIdToPicpId("account",
//                        "customItem255__c", accountInfoRequest.getCustomItem255__c().longValue());
//                    if (ObjectUtil.isNotEmpty(budgetInt)) {
//                        budgetStr = BudgetTypeEnum.getValueByCode(new Integer(budgetInt));
//                    }
//                    budgetStr = xsyScrmService.convertScrmOptionIdToLabel("account",
//                        "customItem270__c", accountInfoRequest.getCustomItem270__c().longValue());
//                }

                // 一胎月子方式
                /*
                String firstBirthType = accountInfoRequest.getCustomItem265__c();
                firstBirthType = ObjectUtil.isEmpty(firstBirthType) ? "" : firstBirthType;
                 */

                // 客户关注点
                /*
                String customerConcern = "";
                if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem266__c())) {
                    Map<String, List<SelectItemData>> scrmObjectOptionList = xsyScrmService.getScrmObjectOptionList("account");
                    log.info("客户描述列表:{}", scrmObjectOptionList);
                    Map<Long, String> selectItemMap = new HashMap<>();
                    if (scrmObjectOptionList.containsKey("customItem266__c")) {
                        log.info("客户关注点选项列表:{}", scrmObjectOptionList.get("customItem266__c"));
                        for (SelectItemData selectItemData : scrmObjectOptionList.get("customItem266__c")) {
                            selectItemMap.put(selectItemData.getValue(), selectItemData.getLabel());
                        }
                    }

                    List<String> customerConcernList = Arrays.stream(accountInfoRequest.getCustomItem266__c())
                        .map(i -> selectItemMap.getOrDefault(i.longValue(), "")).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(customerConcernList)) {
                        customerConcern = StringUtils.join(customerConcernList, "，");
                    }
                }*/

                // 销售信息
                for (ScrmDistributionCustomerDTO dto : list) {
                    ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(dto.getOwnerId());
                    List<String> responsibility = userInfo.getResponsibility();
                    List<String> sale = responsibility.stream().filter(r -> r.contains("销售")).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(sale)) {
                        // 设置接收人
                        // 将scrm手机号转为picp手机号
                        List<String> touser = new ArrayList<>();
                        String selfPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getPhone());
                        if (ObjectUtil.isNotNull(selfPicpPhone)) {
                            touser.add(selfPicpPhone);
                        } else {
                            touser.add(userInfo.getPhone());
                        }

                        String managerPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getManagerPhone());
                        if (ObjectUtil.isNotNull(managerPicpPhone)) {
                            touser.add(managerPicpPhone);
                        } else {
                            touser.add(userInfo.getManagerPhone());
                        }
                        //List<String> touser = Arrays.asList(userInfo.getPhone(), userInfo.getManagerPhone());
                        localQwMsgRequest.setTouser(touser);

                        // 获取销售的姓名和门店信息
                        String saleName = userInfo.getName();
                        String storeName = "";
                        List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.infoListByUserId(dto.getOwnerId());
                        if (CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS)) {
                            if (scrmUserStoreConfigPOS.size() == 1) {
                                CfgStore cfgStore = cfgStoreService.queryStoreInfoByStoreId(
                                    scrmUserStoreConfigPOS.get(0).getStoreId().intValue());
                                if (ObjectUtil.isNotEmpty(cfgStore)) {
                                    storeName = cfgStore.getStoreName();
                                }
                            }
                        }
                        String content = String.format(ScrmQwCustomerMessageConstant.SEND_MESSAGE,
                            accountName,
                            customerStatusStr,
                            customerPhone,
                            ObjectUtil.isNotNull(accountInfoRequest.getCustomItem233__c()) ? accountInfoRequest.getCustomItem233__c() : "",
                            predictBornDateStr,
                            //budgetStr,
                            fromTypeStr,
                            //"",
                            //"",
                            hospital__c,
                            fetusNum__c,
                            //firstBirthType,
                            //customerConcern,
                            saleName,
                            storeName,
                            "",
                            remark,
                            "",
                            ""
                        );
                        localQwMsgRequest.setContent(content);
                        log.info("发送企微消息通知,参数={}", JSONUtil.parse(localQwMsgRequest));
                        smsManager.localBatchSendMsg(localQwMsgRequest);
                    }
                }
            } else {
//                String oldContent = String.format(ScrmQwCustomerMessageConstant.OLD_SEND_MESSAGE, one.getName(), one.getPhone());
//                localQwMsgRequest.setContent(oldContent);
                log.error("未在scrm中查找到客户, id:{}", userId);
            }
        }
        return Result.success(aLong);
    }

    /**
     * 同步分娩喜报->客户中心->SCRM
     *
     * @param scrmCustomerBirthDTO
     */
    @Override
    public Result<Long> synCustomerChildbirthRoom(ScrmCustomerBirthDTO scrmCustomerBirthDTO) {
        log.info("同步分娩喜报->客户中心->SCRM,参数={}", JSONUtil.parse(scrmCustomerBirthDTO));
        if (ObjectUtil.isEmpty(scrmCustomerBirthDTO)) {
            return null;
        }
        String phone = scrmCustomerBirthDTO.getPhone();
        ScrmCustomerPO one = getScrmCustomerPO(phone);
        //妊娠糖尿病;否/是")
        one.setGestationalDiabetes(scrmCustomerBirthDTO.getGestationalDiabetes());
        //高血压;否/是")
        one.setHighBloodPressure(scrmCustomerBirthDTO.getHighBloodPressure());
        //传染病检查;否/是")
        one.setInfectiousDiseaseExamination(scrmCustomerBirthDTO.getInfectiousDiseaseExamination());
        //药物过敏史;否/是")
        one.setHistoryOfDrugAllergy(scrmCustomerBirthDTO.getHistoryOfDrugAllergy());
        //"胎次 1-首胎 2-二胎 3三泰及以上")
        one.setBornNum(scrmCustomerBirthDTO.getBornNum());
        //"胎数")
        one.setFetusNum(scrmCustomerBirthDTO.getFetusNum());
        //"分娩医院")
        one.setHospital(scrmCustomerBirthDTO.getHospital());
        //"分娩方式0=自然生产,1=剖腹产")
        one.setProductionMode(scrmCustomerBirthDTO.getBornType());
        //"生产时间")
        one.setBornTime(scrmCustomerBirthDTO.getBornDate());
        //同步SCRM

        // 判断客户的当前状态是否是育儿中，不是的话改为育儿中
        if (ObjectUtil.isNull(one.getCustomerStatus()) || !one.getCustomerStatus().equals(CustomerStatusEnum.IN_PARENTING.getCode())) {
            one.setCustomerStatus(CustomerStatusEnum.IN_PARENTING.getCode());
        }

        Long aLong = scrmManager.custom2Scrm(one);
        one.setScrmCustomerId(aLong);
        this.updateById(one);
        //保存/修改宝宝信息
        scrmManager.saveOrUpdateBaby(scrmCustomerBirthDTO.getBabyDTOList(), one, scrmCustomerBirthDTO.getOrderNo());
        return Result.success(aLong);
    }


    /**
     * 同步客户分娩信息 房态->客户中心->SCRM
     *
     * @param scrmClientBirthDTO
     */
    @Override
    public Result<Long> synScrmClientBirth(ScrmClientBirthDTO scrmClientBirthDTO) {
        log.info("同步客户分娩信息 房态->客户中心->SCRM,参数={}", JSONUtil.parse(scrmClientBirthDTO));
        String phone = scrmClientBirthDTO.getPhone();
        ScrmCustomerPO one = getScrmCustomerPO(phone);
        one.setProductionMode(scrmClientBirthDTO.getBornType());
        one.setBornNum(scrmClientBirthDTO.getBornNum());
        one.setFetusNum(scrmClientBirthDTO.getFetusNum());
        one.setHospital(scrmClientBirthDTO.getHospital());
        one.setBornTime(scrmClientBirthDTO.getBornDate());
        //同步SCRM
        Long aLong = scrmManager.custom2Scrm(one);
        //修改客户信息
        one.setScrmCustomerId(aLong);
        this.updateById(one);
        //保存/修改宝宝信息
        scrmManager.saveOrUpdateBaby(scrmClientBirthDTO.getBabyDTOList(), one, scrmClientBirthDTO.getOrderNo());
        return Result.success(one.getScrmCustomerId());
    }

    /**
     * 同步客户房态信息 房态->客户中心->SCRM
     *
     * @param scrmCustomerRoomDTO
     */
    @Override
    public Result<Long> synScrmCustomerRoom(ScrmCustomerRoomDTO scrmCustomerRoomDTO) {
        log.info("同步客户房态信息 房态->客户中心->SCR,参数={}", JSONUtil.parse(scrmCustomerRoomDTO));
        String phone = scrmCustomerRoomDTO.getPhone();
        ScrmCustomerPO one = getScrmCustomerPO(phone);
        one.setCheckInDate(scrmCustomerRoomDTO.getCheckInDate());
        one.setCheckOutDate(scrmCustomerRoomDTO.getCheckOutDate());
        one.setInDays(scrmCustomerRoomDTO.getOvernightDays().toString());
        one.setCustomerStage(scrmCustomerRoomDTO.getCustomerStage());

        // 判断客户的当前状态是否是育儿中，且非小月子，否则改为育儿中
        if (ObjectUtil.isNull(one.getCustomerStatus())
            || !one.getCustomerStatus().equals(CustomerStatusEnum.IN_PARENTING.getCode())
            || !one.getCustomerStatus().equals(CustomerStatusEnum.MISCARRIAGE.getCode())) {
            one.setCustomerStatus(CustomerStatusEnum.IN_PARENTING.getCode());
        }

        //同步SCRM
        Long aLong = scrmManager.custom2Scrm(one);
        //修改客户信息
        one.setScrmCustomerId(aLong);
        this.updateById(one);
        return Result.success(one.getScrmCustomerId());
    }

    /**
     * 同步历史数据-添加销售到客户团队成员
     */
    @Override
    public Result addTeamMemberToCustomer(SynHistoryAddTeamMemberRequest synHistoryAddTeamMemberRequest) {
        List<Long> saleIdList = synHistoryAddTeamMemberRequest.getSaleIdList();
        for (Long asLong : saleIdList) {
            ScrmAddTeamMemberRequest request = new ScrmAddTeamMemberRequest();
            request.setUserId(asLong);
            //客户默认为1
            request.setRecordFrom(1L);
            request.setRecordFrom_data(synHistoryAddTeamMemberRequest.getScrmCustomerId());
            request.setOwnerFlag(1);
            log.info("同步历史数据-添加销售到客户团队成员={}", JSONUtil.parse(request));
            xsyScrmService.addTeamMember(request);

            // 添加团队成员到记录表中
            ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
                .recordId(request.getRecordFrom_data())
                .userId(request.getUserId())
                .type(1)
                .build();

            scrmTeamMemberRecordService.edit(recordRequest);

        }
        return Result.success();
    }

    /**
     * 同步历史数据-客户信息
     *
     * @param request
     */
    @Override
    public Result synHistoryCustomer(SynHistoryCustomerRequest request) {
        log.info("历史数据--同步客户信息 ,参数={}", JSONUtil.parse(request));
        //同步客户中心
        ScrmCustomerPO scrmCustomerPO = scrmManager.php2Custom(request);
        ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
        if (ObjectUtil.isNotEmpty(scrmConfigPO)) {
            scrmCustomerPO.setScrmOwnerId(Long.valueOf(scrmConfigPO.getBizContent()));
        }
        ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.infoByUserId(Long.valueOf(scrmConfigPO.getBizContent()));
        if (ObjectUtil.isNotEmpty(scrmUserStoreConfigPO)) {
            scrmCustomerPO.setScrmOwnerPhone(scrmUserStoreConfigPO.getPhone());
        }

        // 解析客户的生日
        if (ObjectUtil.isNull(scrmCustomerPO.getBirthdate())
            && StringUtils.isNotBlank(scrmCustomerPO.getIdCard())
            && ObjectUtil.isNotNull(scrmCustomerPO.getCertType())
            && scrmCustomerPO.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code())) {
            // 如果客户生日为空, 则解析身份证上的生日
            String birthByIdNo = UMSUtils.getBirthByIdNo(scrmCustomerPO.getIdCard());
            if (ObjectUtil.isNotNull(birthByIdNo)) {
                DateTime birthDay = DateUtil.parse(birthByIdNo);
                if (ObjectUtil.isNotNull(birthDay)) {
                    scrmCustomerPO.setBirthdate(birthDay);
                }
                log.info("解析出来的生日为:{}", birthByIdNo);
            }
        }

        // 获取客户在三个品牌的会员等级
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoByPhone(request.getPhone());
        if (ObjectUtil.isNotNull(userBasicPO)) {
            // 获取客户三个品牌的会员等级
            CustomerAssetsPO saintAssetPO = customerAssetsService.getGrowthAssetsByBasicIdAndAssetsType(
                userBasicPO.getId(), CustomerAssetsTypeEnum.GROW_SBL.getCode());
            if (ObjectUtil.isNotNull(saintAssetPO)) {
                //scrmCustomerPO.setSaintLevel(saintAssetPO.getGrowthLevelId());
                scrmCustomerPO.setCustomerLevel(saintAssetPO.getGrowthLevelId());
            }

            /*  新版会员不区分品牌了 2025-04-25
            CustomerAssetsPO babyAssetPO = customerAssetsService.getGrowthAssetsByBasicIdAndAssetsType(
                userBasicPO.getId(), CustomerAssetsTypeEnum.GROW_XBL.getCode());
            if (ObjectUtil.isNotNull(babyAssetPO)) {
                scrmCustomerPO.setBabyLevel(babyAssetPO.getGrowthLevelId());
            }

            CustomerAssetsPO islaAssetPO = customerAssetsService.getGrowthAssetsByBasicIdAndAssetsType(
                userBasicPO.getId(), CustomerAssetsTypeEnum.GROW_ISLA.getCode());
            if (ObjectUtil.isNotNull(islaAssetPO)) {
                scrmCustomerPO.setIslaLeve(islaAssetPO.getGrowthLevelId());
            }*/
        }

        //同步SCRM
        Long aLong = scrmManager.custom2Scrm(scrmCustomerPO);
        //回填scrmId
        scrmCustomerPO.setScrmCustomerId(aLong);
        this.saveOrUpdate(scrmCustomerPO);
        return Result.success(aLong);
    }

    /**
     * 根据手机号获取客户信息
     */
    private ScrmCustomerPO getScrmCustomerPO(String phone) {
        LambdaQueryWrapper<ScrmCustomerPO> lq = new LambdaQueryWrapper<>();
        lq.eq(ScrmCustomerPO::getPhone, phone);
        ScrmCustomerPO one = this.getOne(lq);
        if (ObjectUtil.isEmpty(one)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户中心不存在此客户信息,phone=" + phone);
        }
        return one;
    }


    @Override
    public Result<Long> clockStore(SCRMClockStoreRequest scrmClockStoreRequest) {
        //同步客户中心
        log.info("到店打卡参数={}", JSONUtil.parse(scrmClockStoreRequest));
        ScrmCustomerDTO scrmCustomerDTO = new ScrmCustomerDTO();
        scrmCustomerDTO.setPhone(scrmClockStoreRequest.getPhone());
        //同步客户信息 php->客户中心->scrm
        LambdaQueryWrapper<ScrmCustomerPO> lq = new LambdaQueryWrapper<>();
        lq.eq(ScrmCustomerPO::getPhone, scrmClockStoreRequest.getPhone())
                .orderByDesc(ScrmCustomerPO::getGmtCreate);
        List<ScrmCustomerPO> list = scrmCustomerService.list(lq);
        if (CollectionUtil.isNotEmpty(list)) {
            ScrmCustomerPO one = list.get(0);
            scrmCustomerDTO.setScrmCustomerId(one.getScrmCustomerId());
            scrmCustomerDTO.setName(one.getName());
        } else {
            // 如果scrm中不存在这个客户，则表明这个客户是打卡新增的，直接默认线下来源渠道为微信小程序
            scrmCustomerDTO.setOfflineFromType(CustomerFromTypeEnum.WECHAT_MINI_PROGRAM.getCode());
        }

        List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = null;
        ScrmCustomerPO scrmCustomerPO = null;
        if (Objects.nonNull(scrmCustomerDTO.getScrmCustomerId())) {
            scrmCustomerPO = scrmManager.php2Custom(scrmCustomerDTO);
            if (ObjectUtil.isEmpty(scrmCustomerPO.getCustomerStage()) || scrmCustomerPO.getCustomerStage() < ScrmCustomerStatusEnum.ARRIVE.getCode()) {
                scrmCustomerPO.setCustomerStage(ScrmCustomerStatusEnum.ARRIVE.getCode());
            }
            if (StringUtils.isEmpty(scrmCustomerPO.getName())) {
                scrmCustomerPO.setName(scrmClockStoreRequest.getPhone());
            }
            //同步客户信息 客户中心->scrm
            Long aLong = scrmManager.custom2Scrm(scrmCustomerPO);
            scrmCustomerPO.setScrmCustomerId(aLong);
            //数据库的update
            this.saveOrUpdate(scrmCustomerPO);

            scrmBusinessOpportunityPOS = scrmBusinessOpportunityService.listByCustomerIdAndStoreConfigIds(aLong, null);
        }


        //根据客户id和门店中的销售id匹配商机
        List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.listByStore(scrmClockStoreRequest.getStoreId());
        List<Long> collect = scrmUserStoreConfigPOS.stream().map(ScrmUserStoreConfigPO::getScrmRecordId).collect(Collectors.toList());

        Long ownerId = null;
        //通过商机匹配ownerId
        if (CollectionUtil.isNotEmpty(scrmBusinessOpportunityPOS)) {
            //取第一条
            // 2024.12.02, 产品跟业务方沟通，需要将所有进行中的商机全部变为到店
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = scrmBusinessOpportunityPOS.get(0);
            ownerId = scrmBusinessOpportunityPO.getOwnerId();
        } else {
            if (CollectionUtil.isNotEmpty(collect)) {
                //通过门店取最新的销售
                List<Long> scrmUserIdList = scrmUserStoreConfigPOS.stream().map(ScrmUserStoreConfigPO::getUserId).collect(Collectors.toList());
                List<ScrmUserResponsibilyQueryVO> userResponsibilyQueryVOList = xsyScrmService.queryUserSaleRoleByIds(scrmUserIdList);
                for (ScrmUserResponsibilyQueryVO scrmUserResponsibilyQueryVO : userResponsibilyQueryVOList) {
                    if (scrmUserResponsibilyQueryVO.getResponsibilyName().equals("销售")) {
                        ownerId = scrmUserResponsibilyQueryVO.getId();
                    }
                }

                if (Objects.isNull(ownerId)) {
                    // 如果该门店不存在销售，则直接去当前门店的第一个人
                    ownerId = scrmUserStoreConfigPOS.get(0).getUserId();
                }
            }
        }

        CheckinStoreRequest checkinStoreRequest = new CheckinStoreRequest();

        if (ObjectUtil.isNotEmpty(ownerId)) {
            checkinStoreRequest.setOwnerId(ownerId);
            checkinStoreRequest.setCreatedBy(ownerId);
            checkinStoreRequest.setUpdatedBy(ownerId);

            if (Objects.isNull(checkinStoreRequest.getCustomItem3__c())) {
                // 销售与门店关系不存在
                ScrmUserPO checkinStaffInfo = scrmUserService.queryByScrmId(checkinStoreRequest.getOwnerId());
                checkinStoreRequest.setDimDepart(checkinStaffInfo.getDimDepart());
                ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(checkinStaffInfo, scrmClockStoreRequest.getStoreId());
                if (Objects.nonNull(scrmUserStoreConfig)) {
                    checkinStoreRequest.setCustomItem3__c(scrmUserStoreConfig.getScrmRecordId());
                }
            }

            checkinStoreRequest.setCustomItem5__c(1);
            if (CollectionUtil.isNotEmpty(scrmBusinessOpportunityPOS)) {
                checkinStoreRequest.setCustomItem5__c(2);
            }

            //将签到信息存到SCRM
            checkinStoreRequest.setName(DateUtil.formatDateTime(new Date()));
            checkinStoreRequest.setUpdatedAt(System.currentTimeMillis());
            checkinStoreRequest.setLockStatus(1);
            checkinStoreRequest.setCustomItem1__c(scrmClockStoreRequest.getPhone());
            checkinStoreRequest.setCheckinTime__c(scrmClockStoreRequest.getCheckinTime().getTime());
            //将签到信息添加到SCRM
            Long checkinStoreId = xsyScrmService.checkinStoreEdit(checkinStoreRequest);

            if (CollectionUtil.isNotEmpty(scrmBusinessOpportunityPOS)) {
                scrmBusinessOpportunityPOS.forEach(scrmBusinessOpportunityPO -> {
                    Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
                    //将签到信息跟商机绑定
                    scrmBusinessOpportunityPO.setSignInInformation(checkinStoreId);
                    //是否自从更改销售阶段
                    if (scrmBusinessOpportunityPO.getEntityType().equals(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))) {
                        // 产康商机的话，变为产康商机的到店阶段
                        scrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode())));
                    } else {
                        scrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())));
                    }

                    //商机更新我们系统
                    scrmBusinessOpportunityService.updateById(scrmBusinessOpportunityPO);
                    //商机更新SCRMId
                    OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(scrmBusinessOpportunityPO);
                    opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);
                    xsyScrmService.opportunityEdit(opportunityRequest);
                    //记录到变更记录表
                    scrmBusinessOpportunityService.saveOpportunityStatus(
                        scrmBusinessOpportunityPO.getScrmId(),
                        scrmBusinessOpportunityPO.getOpportunityName(),
                        scrmBusinessOpportunityPO.getSaleStageId(),
                        scrmBusinessOpportunityPO.getContractedStore(),
                        scrmBusinessOpportunityPO.getOwnerId(),
                        scrmBusinessOpportunityPO.getDimDepart(),
                        scrmBusinessOpportunityPO.getEntityType(),
                        scrmBusinessOpportunityPO.getLockStatus(),
                        scrmClockStoreRequest.getCheckinTime()
                    );
                });
            }

            if (Objects.nonNull(scrmCustomerPO)) {
                // 生成回访记录
                ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(scrmClockStoreRequest.getStoreId());

                CheckinStoreReturnVisitRequest checkinStoreReturnVisitRequest = new CheckinStoreReturnVisitRequest();
                checkinStoreReturnVisitRequest.setCustomerId(scrmCustomerPO.getScrmCustomerId());
                checkinStoreReturnVisitRequest.setBrandStoreId(scrmBrandStoreConfigPO.getScrmId());
                checkinStoreReturnVisitRequest.setCheckinStoreRecordId(checkinStoreId);
                xsyScrmService.addCheckinStoreReturnVisit(checkinStoreReturnVisitRequest);
            }
        }
        return Result.success(0L);
    }

    /**
     * 大众点评/门店预约
     *
     * @param scrmClockStoreRequest
     */
    @Override
    public Result<Long> storeAppointment(SCRMClockStoreRequest scrmClockStoreRequest) {
        //根据手机号查询,有则更新,没有则新增
        ScrmAccountQuery query = new ScrmAccountQuery();
        query.setPhone(scrmClockStoreRequest.getPhone());
        AccountInfoRequest request = xsyScrmService.queryScrmAccount(query);
        ScrmCustomerPO one = scrmManager.accountInfoRequest2ScrmCustomerPO(request);
        if (ObjectUtil.isNotEmpty(one) && ObjectUtil.isNotEmpty(one.getScrmCustomerId())) {
            if (ObjectUtil.isNotEmpty(one.getStoreName())) {
                List<String> storeNameList = new ArrayList<>(Arrays.asList(one.getStoreName().split(",")));
                storeNameList.add(scrmClockStoreRequest.getStoreName());
                String storeName = storeNameList.stream().distinct().collect(Collectors.joining(","));
                one.setStoreName(storeName);
            } else {
                one.setStoreName(scrmClockStoreRequest.getStoreName());
            }
        } else {
            one = new ScrmCustomerPO();
            one.setPhone(scrmClockStoreRequest.getPhone());
            if (ObjectUtil.isNotEmpty(scrmClockStoreRequest.getName())) {
                one.setName(scrmClockStoreRequest.getName());
            } else {
                one.setName(scrmClockStoreRequest.getPhone());
            }
            one.setStoreName(scrmClockStoreRequest.getStoreName());
            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
            one.setScrmOwnerId(Long.valueOf(scrmConfigPO.getBizContent()));
        }
        LambdaQueryWrapper<ScrmCustomerPO> lq = new LambdaQueryWrapper<>();
        lq.eq(ScrmCustomerPO::getPhone, scrmClockStoreRequest.getPhone());
        ScrmCustomerPO one1 = this.getOne(lq);
        if (ObjectUtil.isNotEmpty(one1)) {
            one.setId(one1.getId());
        }
        one.setFromType(scrmClockStoreRequest.getFromType());
        one.setCustomItem229__c(2);
        Long aLong = scrmManager.custom2Scrm(one);
        one.setScrmCustomerId(aLong);
        this.saveOrUpdate(one);
        return Result.success(aLong);
    }

    @Override
    public ScrmCustomerPO getByScrmCustomerId(Long userId) {
        List<ScrmCustomerPO> scrmCustomerPOS = this.baseMapper.selectList(new LambdaQueryWrapper<ScrmCustomerPO>()
            .eq(ScrmCustomerPO::getScrmCustomerId, userId)
            .orderByDesc(ScrmCustomerPO::getGmtModified));
        if (CollectionUtil.isNotEmpty(scrmCustomerPOS)) {
            return scrmCustomerPOS.get(0);
        }
        return null;
    }

    @Override
    public Long ScrmCrateOpportunityRequest(ScrmCreateClockInStoreRequest scrmCrateOpportunityRequest) {
        List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.listByStore(scrmCrateOpportunityRequest.getStoreId());
        List<Long> collect = scrmUserStoreConfigPOS.stream().map(ScrmUserStoreConfigPO::getUserId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            Long ownerId = collect.get(0);
            CheckinStoreRequest checkinStoreRequest = new CheckinStoreRequest();
            checkinStoreRequest.setCustomItem5__c(1);
            ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(ownerId);
            checkinStoreRequest.setName(DateUtil.formatDateTime(new Date()));
            checkinStoreRequest.setOwnerId(ownerId);
            checkinStoreRequest.setCreatedBy(ownerId);
            checkinStoreRequest.setUpdatedAt(scrmCrateOpportunityRequest.getSignTime().getTime());
            checkinStoreRequest.setUpdatedBy(ownerId);
            checkinStoreRequest.setLockStatus(1);
            checkinStoreRequest.setCustomItem1__c(scrmCrateOpportunityRequest.getPhone());
            //checkinStoreRequest.setCustomItem6__c(scrmCrateOpportunityRequest.getSignTime().getTime());
            checkinStoreRequest.setCheckinTime__c(scrmCrateOpportunityRequest.getSignTime().getTime());
            checkinStoreRequest.setCreatedAt(scrmCrateOpportunityRequest.getSignTime().getTime());
            if (ObjectUtil.isNotEmpty(userInfo)) {
                checkinStoreRequest.setDimDepart(userInfo.getDimDepart());
            }
            //将签到信息添加到SCRM
            return xsyScrmService.checkinStoreEdit(checkinStoreRequest);
        }
        return null;
    }

    /**
     * 同步历史数据-宝宝信息
     *
     * @param scrmClientBirthDTO
     */
    @Override
    public Result<Long> synHistoryBaby(ScrmClientBirthDTO scrmClientBirthDTO) {
        log.info("同步历史数据-宝宝信息,参数={}", JSONUtil.parse(scrmClientBirthDTO));
        String phone = scrmClientBirthDTO.getPhone();
        ScrmCustomerPO one = getScrmCustomerPO(phone);
        List<ScrmCustomerBirthDTO.BabyDTO> babyDTOList = scrmClientBirthDTO.getBabyDTOList();
        List<ScrmCustomerBabyPO> scrmCustomerBabyPOS = scrmConvert.babyDTO2ScrmCustomerBabyPOList(babyDTOList);
        for (ScrmCustomerBabyPO scrmCustomerBabyPO : scrmCustomerBabyPOS) {
            ContactInfoRequest req = new ContactInfoRequest();
            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.CONTACT_BABY.getCode());
            ScrmConfigPO scrmConfigPO1 = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
            req.setEntityType(Long.valueOf(scrmConfigPO.getBizContent()));
            req.setAccountId(one.getScrmCustomerId());
            req.setCustomItem166__c(scrmClientBirthDTO.getOrderNo());
            req.setOwnerId(Long.valueOf(scrmConfigPO1.getBizContent()));
            Long scrmSex = xsyScrmService.convertPicpIdToScrmOptionId("contact", "sex__c",
                    scrmCustomerBabyPO.getSex().toString());
            req.setSex__c(scrmSex.intValue());
            req.setContactName(scrmCustomerBabyPO.getName());
            req.setNickname__c(scrmCustomerBabyPO.getNickname());
            req.setBirthdate__c(scrmCustomerBabyPO.getBirthdate().getTime());
            Long aLong = xsyScrmService.babySync(req);
            scrmCustomerBabyPO.setScrmId(aLong);
            scrmCustomerBabyPO.setScrmCustomerId(one.getScrmCustomerId());
            scrmCustomerBabyPO.setCreateBy(Long.valueOf(scrmConfigPO1.getBizContent()));
            scrmCustomerBabyPO.setOrderNo(scrmClientBirthDTO.getOrderNo());
        }
        log.info("同步历史数据-宝宝信息,入库信息={}", JSONUtil.parse(scrmCustomerBabyPOS));
        scrmCustomerBabyService.saveBatch(scrmCustomerBabyPOS);
        return Result.success(one.getScrmCustomerId());
    }

    /**
     * scrm联系人（亲友、宝宝）同步到客户中心
     *
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> contactUpdate(List<ContactInfoRequest> req) {
        log.info("开始同步scrm的联系人到客户中心, 参数:{}", JSONUtil.toJsonStr(req));

        Map<Integer, String> scrmConfigMap = scrmConfigService.queryAllConfig();
        // 联系人亲友类型id
        Long contactFriendType = Long.valueOf(scrmConfigMap.get(ScrmConfigBizTypeEnum.CONTACT_RELATIVES_AND_FRIENDS.getCode()));
        // 联系人宝宝类型id
        Long contactBabyType = Long.valueOf(scrmConfigMap.get(ScrmConfigBizTypeEnum.CONTACT_BABY.getCode()));

        for (ContactInfoRequest contactInfoRequest : req) {
            // 将request转我po
            ScrmCustomerBabyPO scrmCustomerBabyPO = new ScrmCustomerBabyPO();
            scrmCustomerBabyPO.setScrmCustomerId(contactInfoRequest.getAccountId());
            scrmCustomerBabyPO.setScrmId(contactInfoRequest.getId());
            scrmCustomerBabyPO.setOrderNo(contactInfoRequest.getCustomItem166__c());
            scrmCustomerBabyPO.setSex(contactInfoRequest.getSex__c());
            scrmCustomerBabyPO.setNickname(contactInfoRequest.getNickname__c());
            scrmCustomerBabyPO.setName(contactInfoRequest.getContactName());
            if (ObjectUtil.isNotEmpty(contactInfoRequest.getBirthdate__c())) {
                scrmCustomerBabyPO.setBirthdate(DateUtil.date(contactInfoRequest.getBirthdate__c()));
            }
            scrmCustomerBabyPO.setRemark(contactInfoRequest.getComment());
            scrmCustomerBabyPO.setCreateBy(contactInfoRequest.getCreatedBy());
            scrmCustomerBabyPO.setUpdateBy(contactInfoRequest.getUpdatedBy());
            scrmCustomerBabyPO.setEntityType(contactInfoRequest.getEntityType());
            if (contactInfoRequest.getEntityType().equals(contactBabyType)) {
                scrmCustomerBabyPO.setType(0);
            } else {
                scrmCustomerBabyPO.setType(1);
            }
            scrmCustomerBabyPO.setOwnerId(contactInfoRequest.getOwnerId());
            scrmCustomerBabyPO.setDepart(contactInfoRequest.getDepart());
            scrmCustomerBabyPO.setPost(contactInfoRequest.getPost());
            scrmCustomerBabyPO.setPhone(contactInfoRequest.getPhone());
            scrmCustomerBabyPO.setMobile(contactInfoRequest.getMobile());
            scrmCustomerBabyPO.setEmail(contactInfoRequest.getEmail());
            scrmCustomerBabyPO.setZipCode(contactInfoRequest.getZipCode());
            if (ObjectUtil.isNotEmpty(contactInfoRequest.getContactBirthday())) {
                scrmCustomerBabyPO.setContactBirthday(DateUtil.date(contactInfoRequest.getContactBirthday()));
            }
            scrmCustomerBabyPO.setWxUnionId(contactInfoRequest.getWxUnionID());
            scrmCustomerBabyPO.setWxUserType(contactInfoRequest.getWxUserType());
            if (ObjectUtil.isNotEmpty(contactInfoRequest.getRecentActivityRecordTime())) {
                scrmCustomerBabyPO.setRecentActivityRecordTime(DateUtil.date(contactInfoRequest.getRecentActivityRecordTime()));
            }
            scrmCustomerBabyPO.setLoss(contactInfoRequest.getLoss());
            scrmCustomerBabyPO.setShareTags(contactInfoRequest.getSharedTags());
            scrmCustomerBabyPO.setExternalUserId(contactInfoRequest.getSExternalUserId());
            scrmCustomerBabyPO.setCorpId(contactInfoRequest.getCorpId());
            scrmCustomerBabyPO.setWxPosition(contactInfoRequest.getWxPosition());
            scrmCustomerBabyPO.setWxAvatarUrl(contactInfoRequest.getWxAvatarUrl());
            scrmCustomerBabyPO.setWxUserName(contactInfoRequest.getWxUserName());
            scrmCustomerBabyPO.setContactChannel(contactInfoRequest.getContactChannel());
            scrmCustomerBabyPO.setDimDepart(contactInfoRequest.getDimDepart());
            scrmCustomerBabyPO.setContactRole(contactInfoRequest.getContactRole());
            scrmCustomerBabyPO.setQwContactId(contactInfoRequest.getQwContactId());
            if (ObjectUtil.isNotEmpty(contactInfoRequest.getRelationWithClient__c())) {
                String relationWithClient = xsyScrmService.convertScrmOptionIdToPicpId("contact",
                    "relationWithClient__c", contactInfoRequest.getRelationWithClient__c().longValue());
                scrmCustomerBabyPO.setRelationWithClient(Integer.valueOf(relationWithClient));
            }

            if (ObjectUtil.isNotEmpty(contactInfoRequest.getContactScore())) {
                scrmCustomerBabyPO.setContactScore(BigDecimal.valueOf(contactInfoRequest.getContactScore()));
            }

            if (contactInfoRequest.getOperateType().equals(ScrmOperateTypeEnum.INSERT_TYPE.getCode())) {
                // 新增
                scrmCustomerBabyService.save(scrmCustomerBabyPO);
            } else if (contactInfoRequest.getOperateType().equals(ScrmOperateTypeEnum.UPDATE_TYPE.getCode())) {
                // 编辑
                ScrmCustomerBabyPO oldBabyPo = scrmCustomerBabyService.getOne(
                    new LambdaQueryWrapper<ScrmCustomerBabyPO>()
                        .eq(ScrmCustomerBabyPO::getScrmId, scrmCustomerBabyPO.getScrmId())
                        .last("limit 1"));

                if (ObjectUtil.isNull(oldBabyPo)) {
                    scrmCustomerBabyService.save(scrmCustomerBabyPO);
                } else {
                    BeanUtil.copyProperties(scrmCustomerBabyPO, oldBabyPo, CopyOptions.create().setIgnoreNullValue(true));
                    scrmCustomerBabyService.updateById(oldBabyPo);
                }
            } else {
                // 删除
                ScrmCustomerBabyPO oldBabyPo = scrmCustomerBabyService.getOne(
                    new LambdaQueryWrapper<ScrmCustomerBabyPO>()
                        .eq(ScrmCustomerBabyPO::getScrmId, scrmCustomerBabyPO.getScrmId())
                        .last("limit 1"));

                if (ObjectUtil.isNotNull(oldBabyPo)) {
                    scrmCustomerBabyService.removeById(oldBabyPo.getId());
                }
            }
        }

        return Result.success(true);
    }

    /**
     * 同步母婴退单记录到scrm中
     *
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> synOrderRefund(OrderRefundRequest req) {
        log.info("PHP同步退单信息到scrm中, 入参:{}", JSONUtil.toJsonStr(req));

        ScrmCustomerOrderRefundPO orderRefundQuery = scrmConvert.orderRefundRequest2CustomerOrderRefundPO(req);

        ScrmCustomerPO scrmCustomerPO = queryCustomerByPhone(req.getPhone());
        if (ObjectUtil.isNull(scrmCustomerPO)) {
            log.info("PHP同步退单信息到scrm中失败，scrm中不存在该用户，无需同步");
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "scrm中不存在该用户");
        }

        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.queryScrmOrderByOrderId(
            req.getOrderId().longValue());
        if (ObjectUtil.isNull(scrmCustomerOrderPO)) {
            log.info("PHP同步退单信息到scrm中失败，scrm中不存在该订单，无需同步");
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "scrm中不存在该订单");
        }

        // 先查询客户中心存在该退款记录不
        ScrmCustomerOrderRefundPO scrmCustomerOrderRefundPO = scrmCustomerOrderRefundService.getOne(
            new LambdaQueryWrapper<ScrmCustomerOrderRefundPO>()
                .eq(ScrmCustomerOrderRefundPO::getRefundId, orderRefundQuery.getRefundId())
                .last("limit 1"));

        ScrmOrderRefundRequest scrmOrderRefundRequest = new ScrmOrderRefundRequest();

        if (ObjectUtil.isNotNull(scrmCustomerOrderRefundPO)) {
            scrmOrderRefundRequest.setId(scrmCustomerOrderRefundPO.getScrmId());
        }

        scrmOrderRefundRequest.setCustomItem1__c(scrmCustomerPO.getScrmCustomerId());
        scrmOrderRefundRequest.setCustomItem3__c(scrmCustomerOrderPO.getScrmId());
        scrmOrderRefundRequest.setCustomItem4__c(req.getOrderRefundSn());
        scrmOrderRefundRequest.setCustomItem5__c(req.getRefundAmount());
        scrmOrderRefundRequest.setCustomItem6__c(req.getRefundAchievement());
        scrmOrderRefundRequest.setCustomItem7__c(req.getRefundApplyDate().getTime());
        scrmOrderRefundRequest.setCustomItem8__c(req.getIncomeType());
        scrmOrderRefundRequest.setCustomItem9__c(req.getRefundFinishDate().getTime());
        scrmOrderRefundRequest.setCustomItem10__c(req.getRefundReason());
        scrmOrderRefundRequest.setCustomItem11__c(req.getRefundType());
        scrmOrderRefundRequest.setName(req.getOrderRefundSn());
        scrmOrderRefundRequest.setLockStatus(2);

        try {
            Long scrmRefundId = xsyScrmService.orderRefundEdit(scrmOrderRefundRequest);
            log.info("PHP同步退单信息到scrm中, scrm中的退款id为:{}", scrmRefundId);

            if (ObjectUtil.isNotEmpty(scrmRefundId)) {
                if (ObjectUtil.isNotNull(scrmCustomerOrderRefundPO)) {
                    // 编辑
                    BeanUtil.copyProperties(orderRefundQuery, scrmCustomerOrderRefundPO);
                    scrmCustomerOrderRefundService.updateById(scrmCustomerOrderRefundPO);
                } else {
                    // 新增
                    scrmCustomerOrderRefundPO = new ScrmCustomerOrderRefundPO();
                    BeanUtil.copyProperties(orderRefundQuery, scrmCustomerOrderRefundPO);
                    scrmCustomerOrderRefundPO.setScrmId(scrmRefundId);
                    scrmCustomerOrderRefundService.save(scrmCustomerOrderRefundPO);
                }
            }

            return Result.success(true);
        } catch (BusinessException e) {
            return Result.failed(e.getCode(), e.getMessage());
        }
    }

    /**
     * 通过手机号查询scrm客户信息
     *
     * @param phone
     * @return
     */
    @Override
    public ScrmCustomerPO queryCustomerByPhone(String phone) {
        return getOne(new LambdaQueryWrapper<ScrmCustomerPO>()
            .eq(ScrmCustomerPO::getPhone, phone)
            .orderByDesc(ScrmCustomerPO::getGmtModified)
            .last("limit 1"));
    }

    /**
     * 批量上传客户商机与订单
     *
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> synBatchCustomerOrderV2(SyncCustomerOpportunityRequest req) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        PicpUserInitRequest picpUserInitRequest = new PicpUserInitRequest();
        picpUserInitRequest.setAccountId(req.getAccountId());

        ScrmCustomerPO scrmCustomerPO = getByScrmCustomerId(req.getAccountId());

        picpUserInitRequest.setAccountName(scrmCustomerPO.getName());
        picpUserInitRequest.setPhone(scrmCustomerPO.getPhone());
        picpUserInitRequest.setStoreType(req.getStoreType());

        List<SaleUserRequest> saleUserRequestList = new ArrayList<>();

        Long saleUserId = null;

        List<ScrmUserStoreConfigPO> userStoreConfigPOList = scrmUserStoreConfigService.listByStore(req.getStoreId());
        List<Long> userIds = userStoreConfigPOList.stream().map(ScrmUserStoreConfigPO::getUserId).collect(Collectors.toList());
        List<ScrmUserResponsibilyQueryVO> userResponsibilyQueryVOList = xsyScrmService.queryUserSaleRoleByIds(userIds);
        for (ScrmUserResponsibilyQueryVO scrmUserResponsibilyQueryVO : userResponsibilyQueryVOList) {
            SaleUserRequest saleUserRequest = new SaleUserRequest();
            saleUserRequest.setUserId(scrmUserResponsibilyQueryVO.getId());
            saleUserRequest.setDimDepart(scrmUserResponsibilyQueryVO.getDimDepart());
            if (scrmUserResponsibilyQueryVO.getResponsibilyName().contains("销售")) {
                saleUserRequest.setIsSale(true);
                if (saleUserId == null) {
                    saleUserId = scrmUserResponsibilyQueryVO.getId();
                }
            } else {
                saleUserRequest.setIsSale(false);
            }

            saleUserRequestList.add(saleUserRequest);
        }

        // 闭店的取 默认管理员 作为订单的创建人
        if (CollectionUtil.isEmpty(saleUserRequestList)) {
            saleUserId = Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode()));
            ScrmUserPO scrmUserPO = scrmUserService.queryByScrmId(saleUserId);
            SaleUserRequest saleUserRequest = new SaleUserRequest();
            saleUserRequest.setUserId(saleUserId);
            saleUserRequest.setDimDepart(scrmUserPO.getDimDepart());
            saleUserRequest.setIsSale(false);
            saleUserRequestList.add(saleUserRequest);
        }
        picpUserInitRequest.setSaleIds(saleUserRequestList);

        if (saleUserId == null) {
            saleUserId = saleUserRequestList.get(0).getUserId();
        }

        // 获取销售与门店关系
        ScrmUserPO scrmUserPO = scrmUserService.queryByScrmId(saleUserId);
        ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(scrmUserPO, req.getStoreId());
        if (ObjectUtil.isNotNull(scrmUserStoreConfig)) {
            req.setContractedStore(scrmUserStoreConfig.getScrmRecordId());
        }

        ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(
            req.getStoreId());
        if (ObjectUtil.isNotEmpty(scrmBrandStoreConfigPO)) {
            picpUserInitRequest.setAffiliatedStore(scrmBrandStoreConfigPO.getScrmId());
        }

        if (req.getStoreType().equals(0)) {
            // 圣贝拉公海池
            picpUserInitRequest.setHighSeaId(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_SAINT_BELLA.getCode())));
        } else if (req.getStoreType().equals(1)) {
            // 小贝拉公海池
            picpUserInitRequest.setHighSeaId(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_BABY_BELLA.getCode())));
        } else {
            // 艾屿公海池
            picpUserInitRequest.setHighSeaId(Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_HIGH_SEA_BELLA_ISLA.getCode())));
        }

        picpUserInitRequest.setDistributionTime(req.getDistributionTime().getTime());
        picpUserInitRequest.setContractedStore(req.getContractedStore());
        if (ObjectUtil.isNotNull(req.getCheckInTime())) {
            picpUserInitRequest.setCheckInTime(req.getCheckInTime().getTime());
        }

        List<OrderRequest> orderRequestList = new ArrayList<>();
        // 封装订单
        for (ScrmCustomerOrderDTO scrmCustomerOrderDTO : req.getOrderList()) {
            OrderRequest orderRequest = new OrderRequest();
            BeanUtil.copyProperties(scrmCustomerOrderDTO, orderRequest);

            // 补充销售和部门
            Optional<ScrmUserStoreConfigPO> first = userStoreConfigPOList.stream()
                .filter(i -> i.getPhone().equals(scrmCustomerOrderDTO.getStaffPhone())).findFirst();
            if (first.isPresent()) {
                orderRequest.setSaleId(first.get().getUserId());
                SaleUserRequest saleUserRequest = saleUserRequestList.stream()
                    .filter(i -> i.getUserId().equals(orderRequest.getSaleId()))
                    .collect(Collectors.toList())
                    .get(0);
                orderRequest.setDimDepart(saleUserRequest.getDimDepart());
            } else {
                // 如果找不到，则去第一个销售信息
                List<SaleUserRequest> saleUserRequests = saleUserRequestList.stream()
                    .filter(i -> i.getIsSale()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(saleUserRequests)) {
                    orderRequest.setSaleId(saleUserRequests.get(0).getUserId());
                    orderRequest.setDimDepart(saleUserRequests.get(0).getDimDepart());
                } else {
                    // 如果找不到销售，则取第一个员工
                    orderRequest.setSaleId(saleUserRequestList.get(0).getUserId());
                    orderRequest.setDimDepart(saleUserRequestList.get(0).getDimDepart());
                }

            }
            orderRequestList.add(orderRequest);
        }

        picpUserInitRequest.setOrderList(orderRequestList);

        PicpUserInitVO picpUserInitVO = xsyScrmService.picpInitOpportunity(picpUserInitRequest);
        if (ObjectUtil.isNotEmpty(picpUserInitVO)) {
            if (CollectionUtil.isNotEmpty(picpUserInitVO.getOrderList())) {
                boolean b = scrmCustomerOrderService.saveBatch(picpUserInitVO.getOrderList());
                log.info("picp初始化订单保存结果:{}", b);
            }

            if (CollectionUtil.isNotEmpty(picpUserInitVO.getOpportunityList())) {
                List<ScrmBusinessOpportunityPO> collect = picpUserInitVO.getOpportunityList()
                    .stream()
                    .map(i -> scrmConvert.scrmOpportunityDetailVO2ScrmBusinessOpportunityPO(i))
                    .collect(Collectors.toList());
                boolean b = scrmBusinessOpportunityService.saveBatch(collect);
                log.info("picp初始化商机保存结果:{}", b);
            }

            if (CollectionUtil.isNotEmpty(picpUserInitVO.getOpportunityTempVOList())) {
                boolean b = scrmOpportunityStatusRecordService.saveBatch(
                    picpUserInitVO.getOpportunityTempVOList());
                log.info("picp初始化商机中间表保存结果:{}", b);
            }
        }

        return Result.success(true);
    }

    /**
     * 处理在scrm中新增打卡记录
     *
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> editScrmCheckinStoreRecord(ScrmCheckinStoreRequest req) {
        // 先判断客户存在不存在
        ScrmCustomerPO scrmCustomerPO = getOne(new LambdaQueryWrapper<ScrmCustomerPO>()
                .eq(ScrmCustomerPO::getScrmCustomerId, req.getCustomerId())
                .last("limit 1"));

        if (ObjectUtil.isNull(scrmCustomerPO)) {
            // 客户不存在，需要先保存一下客户的信息
            ScrmAccountQuery scrmAccountQuery = new ScrmAccountQuery();
            scrmAccountQuery.setScrmCustomerId(req.getCustomerId());
            AccountInfoRequest accountInfoRequest = xsyScrmService.queryScrmAccount(scrmAccountQuery);
            scrmCustomerPO = scrmManager.accountInfoRequest2ScrmCustomerPO(accountInfoRequest);

            save(scrmCustomerPO);
        }

        // 通过销售与门店关联关系id，找到真实的门店id
        ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(req.getStoreId());
        if (ObjectUtil.isNull(scrmUserStoreConfigPO)) {
            log.error("scrm补卡未找到打卡的真实门店id, scrm中的记录id:{}", req.getStoreId());
            return Result.failed("scrm补卡未找到打卡的真实门店id");
        }
        // 通知php保存补卡记录
        ScrmCheckinStoreDTO scrmCheckinStoreDTO = new ScrmCheckinStoreDTO();
        scrmCheckinStoreDTO.setStoreId(scrmUserStoreConfigPO.getStoreId());
        scrmCheckinStoreDTO.setCustomerId(req.getCustomerId());
        scrmCheckinStoreDTO.setPhone(req.getCustomerPhone());
        scrmCheckinStoreDTO.setCheckinTime(req.getCheckinTime().getTime() / 1000);

        Integer fromType = 0;
        if (ObjectUtil.isNull(scrmCustomerPO.getFromType()) || scrmCustomerPO.getFromType().equals(0)) {
            if (ObjectUtil.isNotNull(scrmCustomerPO.getOfflineFromType()) && !scrmCustomerPO.getOfflineFromType().equals(0)) {
                fromType = scrmCustomerPO.getOfflineFromType();
            }
        } else {
            fromType = scrmCustomerPO.getFromType();
        }
        scrmCheckinStoreDTO.setFromType(fromType);

        if (req.getOperationType().equals(2)) {
            scrmManager.deleteUserCheckinStoreInfoByScrm(scrmCheckinStoreDTO);
        } else {
            scrmManager.handleScrmCardReplacement(scrmCheckinStoreDTO);

            // 如果关联了商机，则需要去更新商机中间表的到店时间
            if (req.getOperationType().equals(1) && ObjectUtil.isNotEmpty(req.getOpportunityId())) {
                // 获取该商机的中间表记录
                ScrmOpportunityStatusRecordPO opportunityStatusRecordPO = scrmOpportunityStatusRecordService.getOne(
                    new LambdaQueryWrapper<ScrmOpportunityStatusRecordPO>()
                        .eq(ScrmOpportunityStatusRecordPO::getOpportunityScrmId, req.getOpportunityId())
                        .eq(ScrmOpportunityStatusRecordPO::getSaleStageId, OpportunityStatusSaleStageEnum.ARRIVAL.getCode())
                        .orderByDesc(ScrmOpportunityStatusRecordPO::getGmtCreate)
                        .last("limit 1"));

                if (ObjectUtil.isNotNull(opportunityStatusRecordPO)) {
                    // 判断两个时间是否一致，不一致则更新
                    if (!req.getCheckinTime().equals(opportunityStatusRecordPO.getRealFinishTime())) {
                        opportunityStatusRecordPO.setRealFinishTime(req.getCheckinTime());
                        scrmOpportunityStatusRecordService.updateById(opportunityStatusRecordPO);

                        OpportunityTempRequest opportunityTempRequest = new OpportunityTempRequest();
                        opportunityTempRequest.setId(opportunityStatusRecordPO.getScrmId());
                        opportunityTempRequest.setCustomItem8__c(opportunityStatusRecordPO.getRealFinishTime().getTime());
                        opportunityTempRequest.setCustomItem1__c(opportunityStatusRecordPO.getOpportunityScrmId());
                        opportunityTempRequest.setEntityType(opportunityStatusRecordPO.getEntityType());
                        opportunityTempRequest.setOwnerId(opportunityStatusRecordPO.getOwnerId());
                        xsyScrmService.opportunityTempEdit(opportunityTempRequest);
                    }
                }
            }

            // 生成回访记录
            ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(
                scrmUserStoreConfigPO.getStoreId().intValue());

            CheckinStoreReturnVisitRequest checkinStoreReturnVisitRequest = new CheckinStoreReturnVisitRequest();
            checkinStoreReturnVisitRequest.setCustomerId(scrmCustomerPO.getScrmCustomerId());
            checkinStoreReturnVisitRequest.setBrandStoreId(scrmBrandStoreConfigPO.getScrmId());
            checkinStoreReturnVisitRequest.setCheckinStoreRecordId(req.getScrmId());
            xsyScrmService.addCheckinStoreReturnVisit(checkinStoreReturnVisitRequest);
        }

        return Result.success(true);
    }

    /**
     * 通过手机号创建一个客户，并同步到scrm中
     *
     * @param request
     * @return
     */
    @Override
    public ScrmCustomerPO createCustomer2Scrm(ScrmCustomerCreateRequest request) {
        ScrmCustomerPO scrmCustomerPO = queryCustomerByPhone(request.getPhone());
        Long saintBellaLevelId = null;
        if (ObjectUtil.isNull(scrmCustomerPO)) {
            scrmCustomerPO = new ScrmCustomerPO();
            if (StringUtils.isBlank(request.getName())) {
                request.setName(request.getPhone());
            }
            // 设置所有人
            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
            scrmCustomerPO.setScrmOwnerId(Long.valueOf(scrmConfigPO.getBizContent()));

            HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoByPhone(request.getPhone());
            if (ObjectUtil.isNotNull(userBasicPO)) {
                // 获取客户三个品牌的会员等级
                CustomerAssetsPO saintAssetPO = customerAssetsService.getGrowthAssetsByBasicIdAndAssetsType(userBasicPO.getId(), CustomerAssetsTypeEnum.GROW_SBL.getCode());
                if (ObjectUtil.isNotNull(saintAssetPO)) {
                    saintBellaLevelId = xsyScrmService.convertPicpIdToScrmOptionId("account", "customerLevel__c", saintAssetPO.getGrowthLevelId().toString());
                }
            }
        } else {
            request.setFromType(null);
            request.setScrmCustomerId(scrmCustomerPO.getScrmCustomerId());
        }

        BeanUtil.copyProperties(request, scrmCustomerPO);

        // 解析客户的生日
        if (ObjectUtil.isNull(scrmCustomerPO.getBirthdate())
            && StringUtils.isNotBlank(scrmCustomerPO.getIdCard())
            && ObjectUtil.isNotNull(scrmCustomerPO.getCertType())
            && scrmCustomerPO.getCertType().equals(UserCardCertTypeEnum.CRED_PSN_CH_IDCARD.code())) {
            // 如果客户生日为空, 则解析身份证上的生日
            String birthByIdNo = UMSUtils.getBirthByIdNo(scrmCustomerPO.getIdCard());
            if (ObjectUtil.isNotNull(birthByIdNo)) {
                DateTime birthDay = DateUtil.parse(birthByIdNo);
                if (ObjectUtil.isNotNull(birthDay)) {
                    scrmCustomerPO.setBirthdate(birthDay);
                }
                log.info("解析出来的生日为:{}", birthByIdNo);
            }
        }

        AccountInfoRequest accountInfoRequest = scrmConvert.scrmCustomerPO2AccountInfoRequest(scrmCustomerPO);
        accountInfoRequest.setId(ObjectUtil.isNotEmpty(accountInfoRequest.getId()) && accountInfoRequest.getId() == 0L ? null : accountInfoRequest.getId());
        AccountInfoRequest scrmAccountInfoRequest = xsyScrmService.converAccountPicpRequestToScrmRequest(accountInfoRequest);

        if (ObjectUtil.isNotNull(saintBellaLevelId)) {
            scrmAccountInfoRequest.setCustomerLevel__c(saintBellaLevelId.intValue());
        }

        Long scrmId = xsyScrmService.accountEdit(scrmAccountInfoRequest);

        scrmCustomerPO.setScrmCustomerId(scrmId);
        this.saveOrUpdate(scrmCustomerPO);

        return scrmCustomerPO;
    }

    /**
     * 更新客户信息并同步scrm
     *
     * @param scrmCustomerPO
     * @return
     */
    @Override
    public Boolean updateScrmCustomer(ScrmCustomerPO scrmCustomerPO) {
        if (Objects.isNull(scrmCustomerPO)) {
            return false;
        }

        if (updateById(scrmCustomerPO)) {
            scrmManager.custom2Scrm(scrmCustomerPO);
            return true;
        }
        return false;
    }

    /**
     * 客户门店预约
     *
     * @param request
     * @return
     */
    @Override
    public Result<StoreAppointmentVO> customerStoreAppointment(StoreAppointmentRequest request) {
        log.info("客户门店预约, request:{}", JSONUtil.toJsonStr(request));
        // 检查客户是否存在在scrm中，不存在，则直接创建一个账户
        ScrmCustomerPO scrmCustomerPO = queryCustomerByPhone(request.getPhone());
        if (Objects.isNull(scrmCustomerPO)) {
            ScrmCustomerCreateRequest scrmCustomerCreateRequest = new ScrmCustomerCreateRequest();
            scrmCustomerCreateRequest.setPhone(request.getPhone());
            scrmCustomerCreateRequest.setName(request.getName());
            scrmCustomerCreateRequest.setFromType(request.getFromType());
            scrmCustomerCreateRequest.setWechatMomentSourceExtend(request.getWechatMomentSourceExtend());
            scrmCustomerCreateRequest.setPredictBornDate(request.getPredictBornDate());
            scrmCustomerCreateRequest.setCustomerStatus(request.getCustomerStatus());
            scrmCustomerPO = createCustomer2Scrm(scrmCustomerCreateRequest);
        }

        if (Objects.isNull(scrmCustomerPO)) {
            log.error("客户门店预约，创建客户到scrm中失败");
            return Result.failed(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "同步客户到scrm失败");
        }

        // 找到该门店的销售
        CustomerStoreStaffConfigPO customerStoreStaffConfigPO = customerStoreStaffConfigService.queryConfigByStoreId(request.getStoreId());
        if (Objects.isNull(customerStoreStaffConfigPO)) {
            // 没有配置销售，则去默认销售
            customerStoreStaffConfigPO = customerStoreStaffConfigService.queryDefaultConfigByBrandType(request.getBrandType());
            if (Objects.isNull(customerStoreStaffConfigPO)) {
                return Result.failed(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "没有找到门店销售");
            }
        }

        if (customerStoreStaffConfigPO.getType().equals(0)) {
            // 真实账号
            ScrmCustomerPO finalScrmCustomerPO = scrmCustomerPO;
            CustomerStoreStaffConfigPO finalCustomerStoreStaffConfigPO = customerStoreStaffConfigPO;
            CompletableFuture.runAsync(() -> storeAppointmentOpportunityHandler(finalScrmCustomerPO, finalCustomerStoreStaffConfigPO), taskExecutor);
        }

        StoreAppointmentVO storeAppointmentVO = new StoreAppointmentVO();
        storeAppointmentVO.setType(customerStoreStaffConfigPO.getType());
        storeAppointmentVO.setSalePhone(customerStoreStaffConfigPO.getStaffPhone());
        storeAppointmentVO.setSaleQrCode(customerStoreStaffConfigPO.getQwQrcode());
        storeAppointmentVO.setSaleAvatar(customerStoreStaffConfigPO.getAvatar());
        storeAppointmentVO.setSaleName(customerStoreStaffConfigPO.getStaffName());
        storeAppointmentVO.setSaleNameEn(customerStoreStaffConfigPO.getStaffNameEn());
        return Result.success(storeAppointmentVO);
    }

    /**
     * 门店客户预约，处理销售与商机的关系
     *
     * @param scrmCustomerPO
     * @param customerStoreStaffConfigPO
     */
    private void storeAppointmentOpportunityHandler(ScrmCustomerPO scrmCustomerPO, CustomerStoreStaffConfigPO customerStoreStaffConfigPO) {
        ScrmUserPO scrmUserPO = scrmUserService.queryByScrmPhone(customerStoreStaffConfigPO.getStaffPhone());
        if (Objects.nonNull(scrmUserPO)) {
            // 检查该客户有没有商机，没有进行中的商机，则需要创建一个
            boolean createNewOpportunity = false;
            List<ScrmBusinessOpportunityPO> opportunityList = scrmBusinessOpportunityService.queryCustomerOpportunityList(scrmCustomerPO.getScrmCustomerId(), OmniOrderTypeEnum.MONTH_ORDER.code());
            if (CollectionUtil.isEmpty(opportunityList)) {
                // 没有标准月子商机，需要创建
                createNewOpportunity = true;
            } else {
                // 查找是否有进行中的商机
                List<Long> inProcessStatusList = scrmBusinessOpportunityService.getOpportunityInProcessStatusByOrderType(OmniOrderTypeEnum.MONTH_ORDER.code());
                List<ScrmBusinessOpportunityPO> inProcessOpportunityList = opportunityList.stream()
                    .filter(opportunity -> CollectionUtil.isNotEmpty(inProcessStatusList) && inProcessStatusList.contains(opportunity.getSaleStageId()))
                    .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(inProcessOpportunityList)) {
                    createNewOpportunity = true;
                }
            }

            if (createNewOpportunity) {
                // 查找销售与门店关系的配置
                ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(scrmUserPO, customerStoreStaffConfigPO.getStoreId());
                Long scrmUserStoreConfigId = Objects.nonNull(scrmUserStoreConfig) ? scrmUserStoreConfig.getScrmRecordId() : null;

                OpportunityRequest opportunityRequest = new OpportunityRequest();
                opportunityRequest.setOwnerId(scrmUserPO.getScrmId());
                opportunityRequest.setDimDepart(scrmUserPO.getDimDepart());
                opportunityRequest.setAccountId(scrmCustomerPO.getScrmCustomerId());
                opportunityRequest.setStageUpdatedAt(System.currentTimeMillis());
                opportunityRequest.setCreatedAt(System.currentTimeMillis());
                opportunityRequest.setUpdatedAt(System.currentTimeMillis());
                opportunityRequest.setIntendedStore__c(customerStoreStaffConfigPO.getStoreId());
                opportunityRequest.setCustomItem174__c(scrmUserStoreConfigId);
                opportunityRequest.setOpportunityName(scrmCustomerPO.getName() + "标准月子商机");
                opportunityRequest.setStoreType(customerStoreStaffConfigPO.getBrandType());
                TabClientPO tabClientPO = tabClientService.queryClientByPhoneStore(scrmCustomerPO.getPhone(), customerStoreStaffConfigPO.getStoreId());
                if (Objects.nonNull(tabClientPO)) {
                    opportunityRequest.setStageUpdatedAt(DateUtils.toDateTime(tabClientPO.getRecordTime()).getTime());
                    opportunityRequest.setCustomItem173__c(DateUtils.toDateTime(tabClientPO.getRecordTime()).getTime());
                }

                ScrmBusinessOpportunityPO scrmOpportunity = scrmBusinessOpportunityService.createScrmOpportunity(opportunityRequest, null);
                if (Objects.nonNull(scrmOpportunity)) {
                    OpportunityDistributionMessage distributionMessage = OpportunityDistributionMessage.builder()
                        .customerId(opportunityRequest.getAccountId())
                        .saleId(scrmUserPO.getScrmId())
                        .contractedStoreId(customerStoreStaffConfigPO.getStoreId().longValue())
                        .orderType(OmniOrderTypeEnum.MONTH_ORDER.code())
                        .sendManager(true)
                        .build();

                    scrmBusinessOpportunityService.scrmDistributionCustomerSuccessMessageNotify(distributionMessage);
                }
            } else {
                // 添加销售至客户的团队成员
                scrmTeamMemberRecordService.addCustomerTeamMember(scrmCustomerPO.getScrmCustomerId(), scrmUserPO.getScrmId());
            }
        }
    }
}
