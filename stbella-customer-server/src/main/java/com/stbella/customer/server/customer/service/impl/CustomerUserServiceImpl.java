package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.stbella.asset.api.enums.AccountType;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.asset.api.enums.TradeAffect;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.facade.AccountQueryService;
import com.stbella.asset.api.req.StreamQueryReq;
import com.stbella.asset.api.res.AccountStreamDto;
import com.stbella.asset.api.res.AssetAccountDto;
import com.stbella.base.common.enums.core.IsNanEnum;
import com.stbella.base.common.utils.BeanMapper;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.base.server.dto.SysEmployeeDTO;
import com.stbella.base.server.security.ISysEmployeeRoleService;
import com.stbella.care.server.care.enmu.postpartum.CustomerPostpartumEvaluateStatusEnum;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.BasicEvaluateIndexVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.EvaluateBaseVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.EvaluateBreastVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.EvaluatePelvisVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.EvaluateSpineVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO.HistoryTableVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateVO;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.activity.service.CustomerActivityAppointmentSummaryService;
import com.stbella.customer.server.brand.strategy.BrandStrategyService;
import com.stbella.customer.server.client.DataReportClient;
import com.stbella.customer.server.coupon.service.CouponService;
import com.stbella.customer.server.cts.enums.CustomerAssetsBrandEnum;
import com.stbella.customer.server.cts.enums.CustomerIntegralSceneEnum;
import com.stbella.customer.server.customer.entity.CustomerAdvertisementConfigPO;
import com.stbella.customer.server.customer.entity.CustomerPostpartumInfoPO;
import com.stbella.customer.server.customer.enums.AssetTypeEnum;
import com.stbella.customer.server.customer.enums.BannerPageTypeEnum;
import com.stbella.customer.server.customer.enums.OrderTypeEnum;
import com.stbella.customer.server.customer.enums.QrCodeErrorEnum;
import com.stbella.customer.server.customer.request.*;
import com.stbella.customer.server.customer.request.store.ClientCreateRequest;
import com.stbella.customer.server.customer.request.store.ClientSearchAddUserRequest;
import com.stbella.customer.server.customer.service.CustomerAdvertisementConfigService;
import com.stbella.customer.server.customer.service.CustomerGrowthService;
import com.stbella.customer.server.customer.service.CustomerPostpartumInfoService;
import com.stbella.customer.server.customer.service.CustomerUserService;
import com.stbella.customer.server.customer.vo.*;
import com.stbella.customer.server.customer.vo.CustomerBannerInfoVO.BannerInfo;
import com.stbella.customer.server.customer.vo.CustomerProductionBodyVO.BodyIndexVO;
import com.stbella.customer.server.customer.vo.CustomerProductionBodyVO.MirrorDataExceptionVO;
import com.stbella.customer.server.customer.vo.CustomerProductionBodyVO.MirrorPostpartumDataVO;
import com.stbella.customer.server.customer.vo.CustomerProductionBodyVO.PostpartumDataVO;
import com.stbella.customer.server.customer.vo.growth.CustomerGrowthLevelDataVO;
import com.stbella.customer.server.customer.vo.growth.CustomerGrowthVO;
import com.stbella.customer.server.ecp.entity.*;
import com.stbella.customer.server.ecp.enums.PhoneTypeEnum;
import com.stbella.customer.server.ecp.enums.StoreTypeEnum;
import com.stbella.customer.server.ecp.request.GrantUserBasicQueryRequest;
import com.stbella.customer.server.ecp.request.SignInListRequest;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.ecp.vo.SignInListVO;
import com.stbella.customer.server.exception.BaseException;
import com.stbella.customer.server.invite.enums.InviteRewardTypeEnum;
import com.stbella.customer.server.invite.service.InviteService;
import com.stbella.customer.server.manager.AssetManager;
import com.stbella.customer.server.manager.CareManager;
import com.stbella.customer.server.manager.MarketingManager;
import com.stbella.customer.server.scrm.constant.ScrmQwCustomerMessageConstant;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.entity.ScrmUserPO;
import com.stbella.customer.server.scrm.enums.BrandTypeBusinessTypeEnum;
import com.stbella.customer.server.scrm.enums.CustomerFromTypeEnum;
import com.stbella.customer.server.scrm.producer.ScrmProducer;
import com.stbella.customer.server.scrm.request.ScrmCustomerCreateRequest;
import com.stbella.customer.server.scrm.service.*;
import com.stbella.customer.server.util.UMSUtils;
import com.stbella.marketing.api.req.UpdatePregnancyTimeReq;
import com.stbella.notice.enums.NoticeTypeEnum;
import com.stbella.notice.server.NoticeService;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.redis.service.RedisService;

import com.stbella.report.req.ReportDataQueryReq;
import com.stbella.report.res.ReportDataRes;
import com.stbella.sso.employee.req.ClientUserInfoRequest;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import com.stbella.sso.server.dingding.vo.ClientUserVO;
import java.util.*;
import com.stbella.customer.server.customer.vo.InviteRuleVO;

import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.stbella.store.goodz.res.PropertyValueVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 用户中心实现
 *
 * <AUTHOR>
 * @date 2023/09/20 02:20:25
 */
@Service
@Slf4j
public class CustomerUserServiceImpl implements CustomerUserService {

    private static final Integer PAGE_HOME = 0;//首页

    private static final Integer PAGE_PERSON_CENTER = 1;//个人中心

    private static final Integer POSITION_MIDDLE = 1;//图片位置

    private static final Integer POSITION_TOP = 0;//图片位置

    private static final String STBELLA_USER_DEMON_ROLE_ID = "STBELLA_USER_DEMON_ROLE_ID";

    private static final Long TENANT_ID = 10001L;

    private static final Integer ORDER_SELL_TYPE_APPOINTMENT = 2;//预约商品

    //来源
    private static final Integer SOURCE_SAINT = 2;//圣贝拉

    // 朋友圈广告点击数量缓存
    private static final String MOMENT_ADVERT_CLICK_REDIS_KEY = "moment_advert_click_count:";

    // 演示权限标识
    private static final String USER_TEST_ROLE_CHECK_MARK = "userTestRoleCheck";


    @DubboReference
    private ISysEmployeeRoleService iSysEmployeeRoleService; //角色接口-获取某个角色下用户列表

    @Resource
    private HeSystemConfigService heSystemConfigService;//系统配置

    @Resource
    private UserService userService;//ecp-user

    @Resource
    private CfgStoreService cfgStoreService;//ecp-store

    @Resource
    private TabClientService tabClientService;//ecp-tab_client

    @Resource
    private HeUserBasicService heUserBasicService;//saas-HeUserBasic
    @Resource
    private ScrmUserService scrmUserService;

    @Resource
    private ScrmTeamMemberRecordService scrmTeamMemberRecordService;

    @Resource
    private TabWechatUserService tabWechatUserService;//Ecp-WechatUser

    @Resource
    private CustomerAdvertisementConfigService customerAdvertisementConfigService;//C端小程序广告位配置

    @Resource
    private HeInviteQrCodeService heInviteQrCodeService;

    @Resource
    private CustomerGrowthService customerGrowthService;


    @Resource
    private HeOrderService heOrderService;//saas-he_order

    @Resource
    private HeOrderGoodsService heOrderGoodsService;

    @Resource
    private TabMiniProgramSignInService tabMiniProgramSignInService;

    @Resource
    private HeCustomerClientConfigService heCustomerClientConfigService;

    @DubboReference
    private AccountQueryService accountQueryService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private ScrmConfigService scrmConfigService;

    @DubboReference
    private NoticeService noticeService;

    @Resource
    private RedisService redisService;

    @Resource
    private ScrmProducer scrmProducer;

    @Resource
    private BrandStrategyService brandStrategyService;

    @DubboReference
    private DdEmployeeService ddEmployeeService;

    @Resource
    private CustomerActivityAppointmentSummaryService appointmentSummaryService;

    @Resource
    private ScrmBusinessOpportunityService  scrmBusinessOpportunityService;

    @Resource
    private InviteService inviteService;

    @Resource
    private CouponService couponService;

    @Resource
    private DataReportClient dataReportClient;

    @Resource
    private MarketingManager marketingManager;

    @Resource
    private AssetManager assetManager;

    @Resource
    private CustomerPostpartumInfoService customerPostpartumInfoService;

    @Resource
    private CareManager careManager;

    /**
     * query banner list
     *
     * @return customer banner info vo
     * <AUTHOR>
     * @date 2023/09/20 02:19:48
     * @since 1.0.0
     */
    @Override
    public CustomerBannerInfoVO queryBannerList(Integer brandType, Integer page) {
        CustomerBannerInfoVO customerBannerInfoVO = new CustomerBannerInfoVO();

        List<AdvertisementConfigVO> configList = queryCustomerAdvertisementConfig(brandType, page);
        if (CollUtil.isEmpty(configList)) {
            return customerBannerInfoVO;
        }
        //顶部
        List<BannerInfo> topBannerList = getBannerInfoList(POSITION_TOP, configList);
        if (CollUtil.isNotEmpty(topBannerList)) {
            customerBannerInfoVO.setTopBannerList(topBannerList);
        }
        //腰部
        List<BannerInfo> middleBannerList = getBannerInfoList(POSITION_MIDDLE, configList);
        if (CollUtil.isNotEmpty(middleBannerList)) {
            customerBannerInfoVO.setMiddleBannerList(middleBannerList);
        }
        return customerBannerInfoVO;
    }


    /**
     * query customer advertisement config 查询 图片 配置
     *
     * @return list<advertisement config vo>
     * <AUTHOR>
     * @date 2023/09/20 02:03:11
     * @since 1.0.0
     */
    private List<AdvertisementConfigVO> queryCustomerAdvertisementConfig(Integer brandType, Integer page) {
        if (Objects.isNull(page)) {
            page = PAGE_PERSON_CENTER;
        }

        List<CustomerAdvertisementConfigPO> dataList = customerAdvertisementConfigService.list(
                new LambdaQueryWrapper<CustomerAdvertisementConfigPO>()
                        .eq(CustomerAdvertisementConfigPO::getPageType, page)
                        .in(CustomerAdvertisementConfigPO::getModule, Arrays.asList(POSITION_TOP, POSITION_MIDDLE))
                        .eq(CustomerAdvertisementConfigPO::getShowFlag, IsNanEnum.YES.code())
                        .eq(CustomerAdvertisementConfigPO::getStoreBrand,
                                //圣贝拉
                                Objects.isNull(brandType) || brandType.equals(0) ? CustomerAssetsBrandEnum.BRAND_SBL.getCode() : brandType)
        );

        if (CollUtil.isNotEmpty(dataList)) {
            Date now = new Date();
            dataList.removeIf(item -> now.compareTo(item.getValidStartDate()) <= -1
                || now.compareTo(item.getValidEndDate()) >= 1);
        }
        if (CollUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        List<CustomerAdvertisementConfigPO> result = dataList
            .stream()
            .sorted(Comparator.comparing(CustomerAdvertisementConfigPO::getWeight).reversed())
            .limit(6)
            .collect(Collectors.toList());
        return BeanMapper.mapList(result, AdvertisementConfigVO.class);
    }


    /**
     * 将配置信息放入放回数据
     *
     * @param module     module
     * @param configList config list
     * @return list<banner info>
     * <AUTHOR>
     * @date 2023/09/20 02:19:59
     * @since 1.0.0
     */
    private List<BannerInfo> getBannerInfoList(Integer module,
        List<AdvertisementConfigVO> configList) {

        return configList.stream()
            .filter(config -> Objects.equals(config.getModule(), module))
            .map(config -> {
                BannerInfo bannerInfo = new BannerInfo();
                bannerInfo.setLabel("");
                bannerInfo.setImage(config.getPictureUrl());
                bannerInfo.setVideo("");
                bannerInfo.setDesImg("");
                bannerInfo.setAppId(config.getOtherAppId());
                bannerInfo.setRouteUrl(config.getLink());
                bannerInfo.setRouteType(
                    BannerPageTypeEnum.convertFrontRouteType(config.getLinkType()));
                bannerInfo.setRouteBtn("");
                return bannerInfo;
            })
            .collect(Collectors.toList());
    }


    /**
     * 检查用户是否拥有演示权限
     *
     * @return boolean
     * <AUTHOR>
     * @date 2023/09/20 02:21:03
     * @since 1.0.0
     */
    @Override
    public Integer queryUserTestRoleCheck(UserTestRoleCheckRequest request) {
        Integer isSuccess = 0;
        UserPO userPO = userService.getOne(new LambdaQueryWrapper<UserPO>()
            .eq(UserPO::getBasicUid, request.getBasicId())
            .eq(UserPO::getActive, 1)
            .last("limit 1"));

        if (Objects.isNull(userPO)) {
            return isSuccess;
        }

        ClientUserInfoRequest clientUserInfoRequest = new ClientUserInfoRequest();
        clientUserInfoRequest.setBasicId(userPO.getBasicUid().longValue());

        ClientUserVO currentClientUser = ddEmployeeService.getCurrentClientUser(
            clientUserInfoRequest);
        if (ObjectUtil.isNull(currentClientUser) || CollectionUtil.isEmpty(
            currentClientUser.getPerms())) {
            return isSuccess;
        }

        return currentClientUser.getPerms().contains(USER_TEST_ROLE_CHECK_MARK) ? 1 : 0;

        /*
        RoleIdsByEmployeeRequest req = new RoleIdsByEmployeeRequest();
        req.setTenantId(TENANT_ID);
        req.setBasicUid(request.getBasicId());
        //查询该用户拥有的权限id
        Result<List<Long>> result = iSysEmployeeRoleService.queryRoleIdsListByEmpId(req);
        if (Objects.isNull(result) || CollUtil.isEmpty(result.getData())) {
            return isSuccess;
        }
        //查询演示权限的配置
        //根据brandType类型获取不同的配置
        HeSystemConfigPO configPO = heSystemConfigService.queryOneByKey(getRoleKey(request));
        //配置不为空
        if (Objects.isNull(configPO) || StringUtils.isBlank(configPO.getValue())) {
            return isSuccess;
        }
        String[] configArr = configPO.getValue().split(",");
        List<Long> idList = Arrays.stream(configArr)
            .map(Long::parseLong)
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(idList)) {
            return isSuccess;
        }
        List<Long> ids = new ArrayList<>();
        idList.forEach(roleId -> {
            Optional<Long> roleIdOptional = result.getData().stream()
                    .filter(i -> Objects.equals(i, roleId)).findFirst();

            roleIdOptional.ifPresent(ids::add);
        });
        return CollUtil.isNotEmpty(ids) ? 1 : isSuccess;
         */
    }

    @NotNull
    private static String getRoleKey(UserTestRoleCheckRequest request) {
        if (request == null || request.getBrandType() == null) {
            return STBELLA_USER_DEMON_ROLE_ID;
        }
        return STBELLA_USER_DEMON_ROLE_ID + "_" + request.getBrandType();
    }

    /**
     * 用户中心
     *
     * <AUTHOR>
     * @date 2023/10/25 02:25:11
     * @since 1.0.0
     */
    @Override
    public CustomerUserCenterVO center(UserCenterRequest request) {
        Long basicId = request.getBasicId();
        String phone = request.getPhone();

        CustomerUserCenterVO userCenter = new CustomerUserCenterVO();
        //设置微信用户信息
        setWechatUserInfo(request, phone, userCenter);
        //basic信息
        HeUserBasicPO basic = heUserBasicService.getOne(new LambdaQueryWrapper<HeUserBasicPO>()
            .eq(HeUserBasicPO::getId, basicId)
            .eq(HeUserBasicPO::getIsDelete, 0));
        if (Objects.isNull(basic)) {
            log.info("未找到用户basic信息");
            return userCenter;
        }
        CustomerUserCenterVO.BasicInfo basicInfo = new CustomerUserCenterVO.BasicInfo();
        BeanUtil.copyProperties(basic, basicInfo);
        //打卡信息
        setStoreSignIn(request, basicId, basicInfo);
        userCenter.setBasicInfo(basicInfo);
        //等级信息，默认只支持圣贝拉 ，需要改造支持品牌
        setLevelInfo(request, basicId, userCenter);
        //统计信息模块
        userCenter.setCountData(getCountData(basicId));
        //client信息
        TabClientPO tabClientPO = tabClientService.getOne(new LambdaQueryWrapper<TabClientPO>()
            .eq(TabClientPO::getPhone, phone)
            .eq(TabClientPO::getActive, 1)
            .last("limit 1"));
        if (Objects.nonNull(tabClientPO)) {
            CustomerUserCenterVO.Client client = new CustomerUserCenterVO.Client();
            client.setId(tabClientPO.getId());
            client.setName(tabClientPO.getName());

            userCenter.setClient(client);
            userCenter.setClientId(tabClientPO.getId());
        }

        userCenter.setSapphireFlag(0);
        //Long totalPaidCashAmount = heOrderService.queryTotalPaidCashAmount(basicId.intValue());
        if (basic.getSapphire().equals(1)) {
            userCenter.setSapphireFlag(1);
        }

        return userCenter;
    }

    /**
     * 统计信息模块
     *
     * @param request
     * @return
     */
    @Override
    public CountData  getCountDataByBrandType(UserCenterRequest request) {
        if (request == null || request.getBrandType()==null) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "brandType未传");
        }
        //统计信息模块
        CountData countData = new CountData();
        //订单数
        countData.setMyOrderNumber(orderCount(request.getBasicId(), false));
        countData.setSoonExpiringPoints(0L);
        //探店打卡记录
        SignInListRequest signInListRequest = new SignInListRequest();
        signInListRequest.setBrandType(request.getBrandType());
        signInListRequest.setBasicUid(request.getBasicId().intValue());
        SignInListVO sign = tabMiniProgramSignInService.getSignInList(signInListRequest);
        log.info("getCountDataByBrandType统计:{}", sign==null?"":JSON.toJSONString(sign));
        countData.setCheckInStoreCount((sign==null||CollectionUtil.isEmpty(sign.getSignInList()))
                ?0:sign.getSignInList().size());
        //获取子账户
        ///asset-center/rpc/account/getAssetAccount?userId={$basicUid}&assetType={$assetType}
        Result<AssetAccountDto> accountDtoResult = accountQueryService.getAccountByUserAndAssetType(
                request.getBasicId().toString(), Long.valueOf(AssetTypeEnum.AVAILABLE_INTEGRAL.getCode()));
        if (Objects.nonNull(accountDtoResult.getData())) {
            countData.setMyPoints(accountDtoResult.getData().getBalance());

            Date startTime = new Date();
            Date endTime = DateUtil.offsetMonth(startTime, 3);
            Long soonExpiringIntegral = assetManager.getSoonExpiringIntegral(accountDtoResult.getData().getId(), startTime, endTime);
            countData.setSoonExpiringPoints(soonExpiringIntegral);
        }

        //获取活动预约数量
        int count = appointmentSummaryService.countByBasicId(request.getBasicId(),request.getBrandType());
        countData.setMyAppointment(count);
        //老带新 & 有赞优惠券//todo

        countData.setMyCoupons(getCouponCountV2(request.getBasicId()));
        return countData;
    }


    /**
     * 统计信息模块
     *
     * @param basicId
     * @return
     */
    @Override
    public CustomerUserCenterVO.CountData getCountData(Long basicId) {
        //统计信息模块
        CustomerUserCenterVO.CountData countData = new CustomerUserCenterVO.CountData();
        //订单数
        countData.setMyOrderNumber(orderCount(basicId, false));
        //预约数
        countData.setMyAppointment(getAppointmentInfo(basicId));
        //我的卡券数
        countData.setMyCoupons(getCouponCountV2(basicId));
        //获取子账户
        ///asset-center/rpc/account/getAssetAccount?userId={$basicUid}&assetType={$assetType}
        Result<AssetAccountDto> accountDtoResult = accountQueryService.getAccountByUserAndAssetType(
            basicId.toString(), Long.valueOf(AssetTypeEnum.AVAILABLE_INTEGRAL.getCode()));
        if (Objects.nonNull(accountDtoResult.getData())) {
            countData.setMyPoints(accountDtoResult.getData().getBalance().intValue());
        }
        return countData;
    }

    private Integer getCouponCount(Long basicId) {
        Integer total = inviteService.getTotalByRewardType(basicId, InviteRewardTypeEnum.TICKET.code(), Arrays.asList(0,1));
        return total==null?0:total;
    }

    /**
     * 获取我的优惠券 ，包含老带新、有赞的券
     *
     * @param basicId
     * @return
     */
    private Integer getCouponCountV2(Long basicId) {
        Integer total = couponService.getCouponCount(basicId,  Arrays.asList(0,1));
        return total==null?0:total;
    }


    private int getAppointmentInfo(Long basicId) {
        List<HeOrderPO> appointmentOrder = heOrderService.list(new LambdaQueryWrapper<HeOrderPO>()
            .eq(HeOrderPO::getBasicUid, basicId)
            .eq(HeOrderPO::getIsDelete, 0)
            .eq(HeOrderPO::getOrderType, OrderTypeEnum.ORDER_TYPE_MALL.getCode())
            .eq(HeOrderPO::getOrderSellType, ORDER_SELL_TYPE_APPOINTMENT)
            .eq(HeOrderPO::getSource, SOURCE_SAINT));

        if (CollUtil.isNotEmpty(appointmentOrder)) {
            return appointmentOrder.size();
        }
        return 0;
    }

    private void setWechatUserInfo(UserCenterRequest request, String phone, CustomerUserCenterVO userCenter) {
        TabWechatUserPO tabWechatUserPO = tabWechatUserService.getOne(
            new LambdaQueryWrapper<TabWechatUserPO>()
                .eq(TabWechatUserPO::getPurePhoneNumber, phone)
                .eq(TabWechatUserPO::getFromType,
                    brandStrategyService.processBrandType(request.getBrandType(),
                        BrandTypeBusinessTypeEnum.WECHAT_USER))
                .isNull(TabWechatUserPO::getDeletedAt));

        if (ObjectUtil.isNotNull(tabWechatUserPO)) {
            if (Objects.nonNull(tabWechatUserPO.getPurePhoneNumber())) {
                tabWechatUserPO.setPhoneNumber(tabWechatUserPO.getPurePhoneNumber());
            }
            String name = StringUtils.isBlank(tabWechatUserPO.getNickname()) ? "会员"
                + tabWechatUserPO.getPhoneNumber()
                .substring(tabWechatUserPO.getPhoneNumber().length() - 4)
                : tabWechatUserPO.getNickname();
            tabWechatUserPO.setNickname(name);

            CustomerUserCenterVO.WechatUser wechatUser = new CustomerUserCenterVO.WechatUser();
            BeanUtil.copyProperties(tabWechatUserPO, wechatUser);

            userCenter.setWechatUser(wechatUser);
        }
    }

    private void setStoreSignIn(UserCenterRequest request, Long basicId, CustomerUserCenterVO.BasicInfo basicInfo) {
        List<EcpStorePO> ecpStorePOList = cfgStoreService.list(new LambdaQueryWrapper<EcpStorePO>()
            .eq(EcpStorePO::getType, brandStrategyService.processBrandType(request.getBrandType(),
                BrandTypeBusinessTypeEnum.STORE))
            .eq(EcpStorePO::getActive, 1));

        if (CollUtil.isNotEmpty(ecpStorePOList)) {

            List<Integer> storeIdList = ecpStorePOList.stream().map(EcpStorePO::getStoreId)
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(storeIdList)) {

                List<TabMiniProgramSignInPO> list = tabMiniProgramSignInService.list(
                    new LambdaQueryWrapper<TabMiniProgramSignInPO>()
                        .eq(TabMiniProgramSignInPO::getBasicUid, basicId)
                        .eq(TabMiniProgramSignInPO::getDeleted, 0)
                        .in(TabMiniProgramSignInPO::getStoreId, storeIdList)
                        .orderByDesc(TabMiniProgramSignInPO::getSignInTime));

                if (CollUtil.isNotEmpty(list)) {
                    basicInfo.setSignInNum(list.size());

                    long time = (new Date().getTime() / 1000) - 86400;//当前时间减去一天
                    //直接查询探店打卡未超过24小时的数量
                    List<TabMiniProgramSignInPO> tempList = list.stream()
                        .filter(item -> item.getSignInTime() > time)
                        .collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(tempList)) {
                        basicInfo.setSignInIngNum(tempList.size());
                    } else {
                        basicInfo.setSignInIngNum(0);
                    }
                    //最后一次打卡详情
                    TabMiniProgramSignInPO signInPO = list.get(0);
                    if (Objects.nonNull(signInPO)) {
                        CustomerUserCenterVO.BasicInfo.SignInLast signInLast = new CustomerUserCenterVO.BasicInfo.SignInLast();
                        BeanUtil.copyProperties(signInPO, signInLast);

                        Optional<EcpStorePO> ecpStorePO = ecpStorePOList.stream()
                            .filter(
                                item -> Objects.equals(item.getStoreId(), signInPO.getStoreId()))
                            .findFirst();

                        if (ecpStorePO.isPresent()) {
                            //门店名称(别名)
                            String storeName = ecpStorePO.get().getNameAlias();
                            signInLast.setStoreName(storeName);
                        }
                        basicInfo.setSignInLast(signInLast);
                    }
                } else {
                    basicInfo.setSignInNum(0);
                }
            }
        }
    }

    private void setLevelInfo(UserCenterRequest request, Long basicId, CustomerUserCenterVO userCenter) {
        CustomerGrowthVO customerGrowthVO = customerGrowthService.detail(basicId, request.getBrandType());
        if (Objects.nonNull(customerGrowthVO)) {
            CustomerUserCenterVO.LevelData levelData = new CustomerUserCenterVO.LevelData();
            levelData.setLevel(Objects.nonNull(customerGrowthVO.getLevelId()) ? customerGrowthVO.getLevelId() : 0L);
            levelData.setLevelName(StringUtils.isNotBlank(customerGrowthVO.getLevelName()) ? customerGrowthVO.getLevelName() : "");
            levelData.setLevelNextName(StringUtils.isNotBlank(customerGrowthVO.getGapLevelName()) ? customerGrowthVO.getGapLevelName() : "");
            levelData.setGrowth(Objects.nonNull(customerGrowthVO.getBalance()) ? customerGrowthVO.getBalance() : 0L);
            levelData.setGrowthDifference(Objects.nonNull(customerGrowthVO.getGapBalance()) ? customerGrowthVO.getGapBalance() : 0L);
            levelData.setRightsList(CollUtil.isNotEmpty(customerGrowthVO.getRightsList()) ? customerGrowthVO.getRightsList() : new ArrayList<>());
            //添加是否最高等级标识(前端需要根据此字段判断)
            //添加最高等级(前端需要根据此字段判断)
            levelData.setIsTop(customerGrowthVO.getIsTop());
            levelData.setHighestLevelInfo(customerGrowthVO.getHighestLevelInfo());
            userCenter.setLevelData(levelData);
        }
    }

    /**
     * 此方法可以过滤门店
     *
     * @param basicId
     * @param brandType
     * @return
     */
    private int orderCountByBrandType(Long basicId, Integer brandType) {
        //获取小贝拉门店id
        List<Integer> storeIds = new ArrayList<>();
        List<EcpStorePO> ecpStorePOList = cfgStoreService.queryByBrandType(brandType);
        if (ObjectUtil.isNotEmpty(ecpStorePOList)) {
            storeIds = ecpStorePOList.stream().map(EcpStorePO::getStoreId).collect(Collectors.toList());
        }
        if(CollectionUtil.isEmpty(storeIds)){
            return 0;
        }
        List<HeOrderPO> order = heOrderService.list(new LambdaQueryWrapper<HeOrderPO>()
                .eq(HeOrderPO::getBasicUid, basicId)
                .eq(HeOrderPO::getIsDelete, 0)
                .in(HeOrderPO::getStoreId, storeIds)
                .in(HeOrderPO::getOrderType, Arrays.asList(OrderTypeEnum.ORDER_TYPE_YZ_NORMAL.getCode()
                        , OrderTypeEnum.ORDER_TYPE_YZ_SMALL.getCode(),
                        OrderTypeEnum.ORDER_TYPE_OTHER.getCode()
                        , OrderTypeEnum.ORDER_TYPE_YZ_NURSE_OUTSIDE.getCode(),
                        OrderTypeEnum.ORDER_TYPE_PRODUCTION.getCode()
                        , OrderTypeEnum.ORDER_TYPE_SBRA.getCode())));

        if (CollUtil.isNotEmpty(order)) {
            return order.size();
        }
        return 0;
    }



    /**
     * 此方法没有过滤门店
     *
     * @param basicId
     * @return
     */
    private int orderCount(Long basicId, Boolean isDeposit) {
        List<Integer> orderTypeList = Arrays.asList(OrderTypeEnum.ORDER_TYPE_YZ_NORMAL.getCode()
                , OrderTypeEnum.ORDER_TYPE_YZ_SMALL.getCode(),
                OrderTypeEnum.ORDER_TYPE_OTHER.getCode()
                , OrderTypeEnum.ORDER_TYPE_YZ_NURSE_OUTSIDE.getCode(),
                OrderTypeEnum.ORDER_TYPE_PRODUCTION.getCode()
                , OrderTypeEnum.ORDER_TYPE_SBRA.getCode());
        if (isDeposit) {
            orderTypeList.add(OrderTypeEnum.ORDER_TYPE_DE.getCode());
        }
        List<HeOrderPO> order = heOrderService.list(new LambdaQueryWrapper<HeOrderPO>()
                .eq(HeOrderPO::getBasicUid, basicId)
                .eq(HeOrderPO::getIsDelete, 0)
                .ne(HeOrderPO::getOrderStatus, OrderStatusV2Enum.CLOSE.getCode())
                .in(HeOrderPO::getOrderType, orderTypeList));

        if (CollUtil.isNotEmpty(order)) {
            return order.size();
        }
        return 0;
    }


    /**
     * 查询用户微信信息
     *
     * @param phone     phone
     * @param brandType 品牌 0-圣贝拉 1-小贝拉
     * <AUTHOR>
     * @date 2023/11/06 06:47:47
     * @since 1.0.0
     */
    @Override
    public CustomerWechatUserInfoVO getUserInfo(String phone, Integer brandType) {
        if (Objects.isNull(brandType)) {
            brandType = StoreTypeEnum.SAINT_BELLA.code();
        }
        int fromType= brandStrategyService.processBrandType(brandType, BrandTypeBusinessTypeEnum.WECHAT_USER);
        TabWechatUserPO wechatUser = tabWechatUserService.getOne(
            new LambdaQueryWrapper<TabWechatUserPO>()
                .eq(TabWechatUserPO::getPurePhoneNumber, phone)
                .eq(TabWechatUserPO::getFromType, fromType)
                .last("limit 1"));

        if (Objects.nonNull(wechatUser)) {
            //未授权昵称 获取固定格式会员+手机号后四位
            if (StringUtils.isEmpty(wechatUser.getNickname()) || StringUtils.isBlank(
                wechatUser.getNickname())) {
                wechatUser.setNickname("会员" + wechatUser.getPhoneNumber()
                    .substring(wechatUser.getPhoneNumber().length() - 4));
            }
        }

        CustomerWechatUserInfoVO customerWechatUserInfoVO = new CustomerWechatUserInfoVO();
        BeanUtil.copyProperties(wechatUser, customerWechatUserInfoVO);
        return customerWechatUserInfoVO;
    }

    @Override
    public Boolean updateUserInfo(CustomerUserEditInfoRequest request) {
        if (Objects.isNull(request.getBrandType())) {
            request.setBrandType(StoreTypeEnum.SAINT_BELLA.code());
        }

        int fromType= brandStrategyService.processBrandType(request.getBrandType(), BrandTypeBusinessTypeEnum.WECHAT_USER);
        TabWechatUserPO wechatUser = tabWechatUserService.getOne(
            new LambdaQueryWrapper<TabWechatUserPO>()
                .eq(TabWechatUserPO::getBasicUid, request.getBasicUid())
                .eq(TabWechatUserPO::getFromType, fromType)
                .last("limit 1"));

        if (Objects.isNull(wechatUser)) {
            return false;
        }
        UpdateWrapper<TabWechatUserPO> updateWrapper = new UpdateWrapper<>();

        if (StringUtils.isNotEmpty(request.getNickname()) && StringUtils.isNotBlank(
            request.getNickname())) {
            updateWrapper.set("nickname", request.getNickname());
        }

        if (Objects.nonNull(request.getGender())) {
            updateWrapper.set("gender", request.getGender());
        }

        if (StringUtils.isNotEmpty(request.getAvatarUrl()) && StringUtils.isNotBlank(
            request.getAvatarUrl())) {
            updateWrapper.set("avatar_url", request.getAvatarUrl());
        }

        if (StringUtils.isNotEmpty(request.getBirthday()) && StringUtils.isNotBlank(
            request.getBirthday())) {
            updateWrapper.set("birthday", request.getBirthday());
        }
        updateWrapper.eq("id", wechatUser.getId());
        return tabWechatUserService.update(updateWrapper);
    }

    /**
     * set user pregnancy
     *
     * @param request request
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/07 10:53:39
     * @since 1.0.0
     */
    public Boolean setUserPregnancy(CustomerUserEditPregnancyRequest request) {
        HeUserBasicPO basicPO = heUserBasicService.getById(request.getBasicUid());
        if (basicPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "用户不存在");
        }

        Long currentManualPredictBornTime = basicPO.getManualPredictBornTime();
// 根据当前和修改后的预产期计算修改类型
        Integer modifyType;
        if (currentManualPredictBornTime == -1 && request.getPredictBornTime() > 0) {
            // 从备孕状态修改为有孕期
            modifyType = 1;
        } else if (currentManualPredictBornTime > 0 && request.getPredictBornTime() == 0) {
            // 从有孕期修改为备孕状态
            modifyType = 2;
        } else if (currentManualPredictBornTime > 0 && request.getPredictBornTime() > 0) {
            // 从有孕期修改为另一个孕期
            modifyType = 3;
        } else {
            // 其他情况(如从备孕改为备孕)
            modifyType = 0;
        }
        UpdatePregnancyTimeReq updatePregnancyTimeReq = new UpdatePregnancyTimeReq();
        updatePregnancyTimeReq.setBasicUid(request.getBasicUid().intValue());
        updatePregnancyTimeReq.setPredictBornTime(request.getPredictBornTime());
        updatePregnancyTimeReq.setModifyType(modifyType);

        // PI端修改孕期可以随意修改

        if (request.getModifySource() == 2) {
            Boolean b = updatePregnancyTime(request);
            marketingManager.updatePregnancyTime(updatePregnancyTimeReq);
            return b;
        }

        // C端修改孕期
        // 如果当前是备孕状态(-1)，可以正常修改
        if (currentManualPredictBornTime == -1) {
            Boolean b = updatePregnancyTime(request);
            marketingManager.updatePregnancyTime(updatePregnancyTimeReq);
            return b;
        }

        // 当前不是备孕状态，要改为备孕状态
        if (request.getPredictBornTime() == 0) {
            // 检查是否有已发放的资产
            Boolean hasAssets = marketingManager.getActivityAssetInfo(request.getBasicUid());
            log.info("hasAssets:{}", hasAssets);
            if (hasAssets) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "已领取孕期福利，不能修改为备孕状态");
            }
            // 没有资产，可以修改为备孕
            request.setPredictBornTime(-1);
            Boolean b = updatePregnancyTime(request);
            marketingManager.updatePregnancyTime(updatePregnancyTimeReq);
            return b;
        }

        Boolean b = updatePregnancyTime(request);

        if (Objects.equals(modifyType, 3)) {
            marketingManager.updatePregnancyTime(updatePregnancyTimeReq);
        }

        // 其他正常修改孕期的情况
        return b;
    }

    private Boolean updatePregnancyTime(CustomerUserEditPregnancyRequest request) {
        UpdateWrapper<HeUserBasicPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", request.getBasicUid());
        updateWrapper.set("manual_predict_born_time",
                request.getPredictBornTime() == 0 ? -1 : request.getPredictBornTime());
        return heUserBasicService.update(updateWrapper);
    }

    /**
     * 孕期专题
     *
     * @param type       type
     * @param clientType client type
     * <AUTHOR>
     * @date 2023/11/14 03:35:06
     * @since 1.0.0
     */
    @Override
    public Map<String, Object> getTopicsDuringPregnancy(Integer type, Integer clientType) {

        HeCustomerClientConfigPO config = heCustomerClientConfigService.getOne(
            new LambdaQueryWrapper<HeCustomerClientConfigPO>()
                .eq(HeCustomerClientConfigPO::getType, type)
                .eq(HeCustomerClientConfigPO::getClientType, clientType)
                .eq(HeCustomerClientConfigPO::getDeleted, 0)
                .last("limit 1"));

        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(config)) {
            if (StringUtils.isNotEmpty(config.getContent()) && JSONUtil.isTypeJSON(
                config.getContent())) {

                JSONObject jsonObject = JSONUtil.parseObj(config.getContent());
                map = jsonObject.getRaw();
            }
        }
        return map;
    }

    /**
     * 朋友圈广告用户上报
     *
     * @param request
     * @return
     */
    @Override
    public Boolean wechatMomentPhoneReport(MomentClientPhoneReportRequest request) {
        log.info("朋友圈广告用户上报参数:{}", JSONUtil.toJsonStr(request));
        String redisKey = MOMENT_ADVERT_CLICK_REDIS_KEY + request.getBrandType();

        boolean keyExist = redisService.hasKey(redisKey);
        if (keyExist) {
            redisService.increment(redisKey);
        } else {
            redisService.setCacheObject(redisKey, 908L);
        }

        // 判断客户手机号是否存在于scrm
        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.queryCustomerByPhone(
            request.getPhone());

        String scrmCustomerExist = "是";
        String scrmCustomerCreateStatus = "无需创建";
        if (ObjectUtil.isNull(scrmCustomerPO)) {
            scrmCustomerExist = "否";
            // 需要在scrm中新增
            ScrmCustomerCreateRequest scrmCustomerCreateRequest = new ScrmCustomerCreateRequest();
            scrmCustomerCreateRequest.setPhone(request.getPhone());
            scrmCustomerCreateRequest.setName(request.getPhone());

            if (StringUtils.isBlank(request.getPhoneType())) {
                request.setPhoneType(PhoneTypeEnum.CHINA.areaCode());
            }

            PhoneTypeEnum phoneTypeEnum = PhoneTypeEnum.getEnumByAreaCode(request.getPhoneType());
            if (ObjectUtil.isNull(phoneTypeEnum)) {
                phoneTypeEnum = PhoneTypeEnum.CHINA;
            }
            scrmCustomerCreateRequest.setPhoneType(phoneTypeEnum.code());
            scrmCustomerCreateRequest.setFromType(
                CustomerFromTypeEnum.WECHAT_MINI_PROGRAM.getCode());
            scrmCustomerCreateRequest.setWechatMomentSourceExtend(request.getSourceExtend());

            scrmProducer.createCustomer2Scrm(scrmCustomerCreateRequest);
            scrmCustomerCreateStatus = "已创建";
//            ScrmCustomerPO newScrmCustomerPO = scrmCustomerService.createCustomer2Scrm(scrmCustomerCreateRequest);
//            if (ObjectUtil.isNotNull(newScrmCustomerPO)) {
//                scrmCustomerCreateStatus = "已创建";
//            } else {
//                scrmCustomerCreateStatus = "创建失败";
//            }
        }

        // 去企微群里通知
        String message = String.format(
            ScrmQwCustomerMessageConstant.MINI_PROGRAM_CUSTOMER_NOTICE,
            request.getPhone(),
            "",
            "",
            scrmCustomerExist,
            scrmCustomerCreateStatus,
            "微信小程序",
            request.getSourceExtend(),
            StoreTypeEnum.getValueByCode(request.getBrandType()));

        WechatNailNailRobotDeclarationRequest noticeRequest = new WechatNailNailRobotDeclarationRequest();
        noticeRequest.setType(1);
        noticeRequest.setBizType(NoticeTypeEnum.DIANPING_RESERVATION_STORE_DIMENSION.getBizType());
        noticeRequest.setStoreId(0);
        noticeRequest.setContent(message);
        log.info("朋友圈广告推送内容:{}", JSONUtil.toJsonStr(noticeRequest));
        Result<Boolean> noticeResult = noticeService.notice(noticeRequest);
        log.info("朋友圈广告推送结果:{}", JSONUtil.toJsonStr(noticeResult));

        return true;
    }

    /**
     * 获取朋友圈广告点击人数
     *
     * @param brandType
     * @return
     */
    @Override
    public Long queryWechatMomentClickCount(Integer brandType) {
        String redisKey = MOMENT_ADVERT_CLICK_REDIS_KEY + brandType;

        Long clickCount = 0L;
        boolean keyExist = redisService.hasKey(redisKey);
        if (keyExist) {
            Integer cacheObject = redisService.getCacheObject(redisKey);
            clickCount = Long.valueOf(cacheObject);
        } else {
            clickCount = 907L;
            redisService.setCacheObject(redisKey, clickCount);
        }

        return clickCount;
    }

    /**
     * 保存品牌客户第一次登录小程序时的调研问题
     *
     * @param request
     * @return
     */
    @Override
    public Boolean saveBrandResearchQuestion(BrandResearchQuestionRequest request) {
        Integer wechatFromType = brandStrategyService.processBrandType(request.getBrandType(),
            BrandTypeBusinessTypeEnum.WECHAT_USER);

        TabWechatUserPO wechatUserPO = tabWechatUserService.getOne(new LambdaQueryWrapper<TabWechatUserPO>()
            .eq(TabWechatUserPO::getFromType, wechatFromType)
            .eq(TabWechatUserPO::getOpenid, request.getOpenid())
            .isNull(TabWechatUserPO::getDeletedAt)
            .last("limit 1"));

        if (Objects.isNull(wechatUserPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "用户不存在");
        }

        wechatUserPO.setResearchQuestion(request.getQuestion());
        tabWechatUserService.updateById(wechatUserPO);
        return true;
    }


    /**
     * get user qr code
     *
     * @param request request
     * @return user qr code vo
     * <AUTHOR>
     * @date 2023/10/31 05:13:32
     * @since 1.0.0
     */
    @Override
    public UserQrCodeVO getUserQrCode(UserQrCodeRequest request) {
        String qrCodeDTO = getUserQrCode(request.getBasicId(), request.getSource());
        if (StringUtils.isBlank(qrCodeDTO)) {
            return new UserQrCodeVO();
        }

        Integer brandType = brandStrategyService.processBrandType(request.getSource(), BrandTypeBusinessTypeEnum.DEFAULT);
        //查询微信信息
        TabWechatUserPO wechatUser = tabWechatUserService.queryWechatInfoByBasicUidAndBrandType(
            request.getBasicId().intValue(),brandType);
        if (wechatUser == null) {
            return new UserQrCodeVO();
        }
        //获取微信的token
        String accessToken = UMSUtils.getWeChatToken(request.getSource());
        //获取微信的小程序码url
        String url = UMSUtils.getWeChatLiteCode(qrCodeDTO, accessToken, request.getSource());

        UserQrCodeVO userQrCodeVO = new UserQrCodeVO();
        userQrCodeVO.setQrCode(qrCodeDTO);
        userQrCodeVO.setBasicUid(request.getBasicId());
        userQrCodeVO.setAvatar(wechatUser.getAvatarUrl());
        userQrCodeVO.setNickname(
            StringUtils.isNotBlank(wechatUser.getNickname()) ? wechatUser.getNickname()
                : "会员" + wechatUser.getPurePhoneNumber()
                    .substring(wechatUser.getPurePhoneNumber().length() - 4));
        userQrCodeVO.setAccessToken(accessToken);
        userQrCodeVO.setImageUrl(url);
        return userQrCodeVO;
    }


    public String getUserQrCode(long basicUid, int source) {
        //生成6位二维码的key
        String qrCodeDTO = UMSUtils.generateQrCode(6);
        //跟用户basicUid关联上
        HeInviteQrCodePO inviteQrCodePO = new HeInviteQrCodePO();
        inviteQrCodePO.setBasicId(basicUid);
        inviteQrCodePO.setQrCode(qrCodeDTO);
        int mySqlSource = UMSUtils.getDataBaseType(source);
        inviteQrCodePO.setQrSource(mySqlSource);
        boolean ret = heInviteQrCodeService.save(inviteQrCodePO);
        if (!ret) {
            return "";
        }
        return qrCodeDTO;
    }

    /**
     * 通过二维码获取用户头像和昵称
     *
     * @param request request
     * @return map<string, object>
     * <AUTHOR>
     * @date 2023/11/01 11:47:32
     * @since 1.0.0
     */
    @Override
    public Map<String, Object> getUserNickNameByQrCode(QrCodeRequest request) {
        // 查询二维码信息
        HeInviteQrCodePO inviteQrCodePO = heInviteQrCodeService.getOne(
            new LambdaQueryWrapper<HeInviteQrCodePO>()
                .eq(HeInviteQrCodePO::getQrCode, request.getQrCode()));

        if (inviteQrCodePO == null) {
            throw new BaseException(QrCodeErrorEnum.QR_CODE_ERR.getMsg());
        }
        HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoById(
            inviteQrCodePO.getBasicId());

        if (Objects.isNull(heUserBasicPO) || StringUtils.isBlank(heUserBasicPO.getPhone())) {
            throw new BaseException(QrCodeErrorEnum.QR_CODE_USER_NOT_EXIST_ERR.getMsg());
        }
        int fromType = UMSUtils.getDataBaseType(request.getSource());

        TabWechatUserPO weChatInfo = tabWechatUserService.getOne(
            new LambdaQueryWrapper<TabWechatUserPO>()
                .eq(TabWechatUserPO::getPurePhoneNumber, heUserBasicPO.getPhone())
                .eq(TabWechatUserPO::getFromType, fromType)
                .last("limit 1"));

        Map<String, Object> map = new HashMap<>();

        if (Objects.nonNull(weChatInfo)) {
            if (StringUtils.isNotBlank(weChatInfo.getNickname())) {
                map.put("nickName", weChatInfo.getNickname());
            } else if (StringUtils.isNotBlank(heUserBasicPO.getName())) {
                map.put("nickName", heUserBasicPO.getName());
            } else {
                map.put("nickName", "会员" + weChatInfo.getPurePhoneNumber()
                    .substring(weChatInfo.getPurePhoneNumber().length() - 4));
            }
            map.put("avatarUrl", weChatInfo.getAvatarUrl());
        }
        return map;
    }

    /**
     * 获取新的邀请积分规则
     *
     * @param brandType brand type
     * @return invite rule vo
     * <AUTHOR>
     * @date 2023/11/01 11:46:50
     * @since 1.0.0
     */
    @Override
    public List<InviteRuleVO> getInviteRule(Integer brandType) {
        List<CustomerGrowthLevelDataVO> dataList = customerGrowthService.listLevelByBrand(
            brandType);
        List<InviteRuleVO> inviteRuleList = new ArrayList<>();

        if (CollUtil.isNotEmpty(dataList)) {
            dataList.forEach(data -> {

                InviteRuleVO rule = new InviteRuleVO();
                rule.setId(data.getId());
                rule.setLevelName(data.getLevelName());

                if (CollUtil.isNotEmpty(data.getScenes())) {
                    List<InviteRuleVO.Scene> sceneList = new ArrayList<>();
                    for (CustomerGrowthLevelDataVO.Scene scene : data.getScenes()) {
                        if (Objects.isNull(scene)) {
                            continue;
                        }
                        InviteRuleVO.Scene sceneTemp = new InviteRuleVO.Scene();

                        if (Objects.equals(scene.getScene(),
                            CustomerIntegralSceneEnum.SCENE_TYPE_SBL_WORD_MOUTH.getCode())) {
                            sceneTemp.setName("门店打卡");
                            sceneTemp.setIntegralVal(String.valueOf(scene.getIntegralVal()));
                            sceneList.add(sceneTemp);
                            continue;
                        }

                        if (Objects.equals(scene.getScene(),
                            CustomerIntegralSceneEnum.SCENE_TYPE_SBL_WORD_MOUTH_SIGN.getCode())) {
                            sceneTemp.setName("签单月子套餐");
                            sceneTemp.setIntegralVal("签单金额/" + scene.getAdditionalVal());
                            sceneList.add(sceneTemp);
                        }
                    }
                    rule.setScenes(sceneList);
                }
                inviteRuleList.add(rule);
            });
        }

        return inviteRuleList;
    }

    @Override
    public ClientSearchVO createScrmUser(ClientSearchAddUserRequest request) {
        ClientCreateRequest addReq = new ClientCreateRequest();
        addReq.setUserPhone(request.getUserPhone());
        addReq.setStoreId(request.getStoreId());
        addReq.setOperator(request.getOperator());
        return scrmBusinessOpportunityService.createScrmUser(addReq);
    }

    /**
     * 获取对应VVIP客户的数据报表
     *
     * @param reportId
     * @param dateStart
     * @param dateEnd
     * @return
     */
    @Override
    public ReportDataVO getVvipCustomerData(Long reportId, Date dateStart, Date dateEnd) {
        ReportDataQueryReq req = new ReportDataQueryReq();
        req.setReportId(reportId);
        req.setPageNum(1);
        req.setPageSize(99999999);

        Map<String, String> params = new HashMap<>();
        params.put("dateStart", DateUtil.format(dateStart, DatePattern.NORM_DATE_PATTERN));
        params.put("dateEnd", DateUtil.format(dateEnd, DatePattern.NORM_DATE_PATTERN));

        req.setParams(params);
        ReportDataRes result = dataReportClient.getReportData(req);

        ReportDataVO reportDataVO = new ReportDataVO();
        reportDataVO.setTitles(result.getTitles());
        reportDataVO.setData(result.getData());

        return reportDataVO;
    }

    /**
     * 获取客户产康金基础信息
     *
     * @param basicId
     * @return
     */
    @Override
    public CustomerProductionAmountBaseInfoVO getCustomerProductionAmountBaseInfo(Integer basicId) {
        if (Objects.isNull(basicId)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "用户ID不能为空");
        }

        AssetAccountDto availableDto = assetManager.queryAccountAssetInfo(basicId, AssetType.AVAILABLE_CKJ.getCode());
        AssetAccountDto frozenDto = assetManager.queryAccountAssetInfo(basicId, AssetType.FROZEN_CKJ.getCode());

        CustomerProductionAmountBaseInfoVO vo = new CustomerProductionAmountBaseInfoVO();
        vo.setAvailableNum(Optional.ofNullable(availableDto).map(AssetAccountDto::getBalance).map(i -> i.intValue() / 100).orElse(0));
        vo.setFreezeNum(Optional.ofNullable(frozenDto).map(AssetAccountDto::getBalance).map(i -> i.intValue() / 100).orElse(0));
        vo.setTotalNum(vo.getAvailableNum() + vo.getFreezeNum());
        vo.setBasicId(basicId);

        return vo;
    }

    /**
     * 查询客户产康金流水列表
     *
     * @param request
     * @return
     */
    @Override
    public PageVO<CustomerProductionAmountStreamVO> queryCustomerProductionAmountStream(CustomerProductionAmountStreamRequest request) {
        StreamQueryReq streamQueryReq = new StreamQueryReq();
        streamQueryReq.setUserId(request.getBasicId().toString());
        streamQueryReq.setPageNum(request.getPageNum());
        streamQueryReq.setPageSize(request.getPageSize());
        streamQueryReq.setAccountType(AccountType.CKJ.getCode().longValue());
        if (Objects.nonNull(request.getType())) {
            if (request.getType().equals(0)) {
                streamQueryReq.setTradeAffect(TradeAffect.ADD.getCode());
            } else {
                streamQueryReq.setTradeAffect(TradeAffect.SUB.getCode());
            }
        }

        PageVO<AccountStreamDto> assetPageVO = assetManager.getAssets(streamQueryReq);
        if (Objects.isNull(assetPageVO)) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }

        List<CustomerProductionAmountStreamVO> dataList = assetPageVO.getList().stream()
            .map(this::convertStreamDto)
            .collect(Collectors.toList());

        return new PageVO<>(dataList, assetPageVO.getTotalCount(), request.getPageSize(), request.getPageNum());
    }

    /**
     * 产康预约查询客户身体数据
     *
     * @param basicId
     * @return
     */
    @Override
    public CustomerProductionBodyVO getCustomerProductionBodyInfo(Integer basicId) {
        CustomerProductionBodyVO customerProductionBodyVO = new CustomerProductionBodyVO();

        List<CustomerPostpartumEvaluateVO> evaluateVOList = careManager.queryPostpartumEvaluateListByBasicId(basicId.longValue());
        if (CollectionUtil.isNotEmpty(evaluateVOList)) {
            Optional<CustomerPostpartumEvaluateVO> firstRecord = evaluateVOList.stream()
                .filter(i -> i.getStatus().equals(CustomerPostpartumEvaluateStatusEnum.COMPLETED.getCode()))
                .findFirst();

            CustomerPostpartumEvaluateReportVO mirrorBodyReportVO = firstRecord.map(customerPostpartumEvaluateVO -> careManager.queryPostpartumEvaluateReport(customerPostpartumEvaluateVO.getId())).orElse(null);
            if (Objects.nonNull(mirrorBodyReportVO)) {
                customerProductionBodyVO.setRecordTime(DateUtil.date(firstRecord.get().getAppointDate()));

                // 解析体测镜数据
                MirrorPostpartumDataVO mirrorPostpartumDataVO = new MirrorPostpartumDataVO();
                mirrorPostpartumDataVO.setReportId(firstRecord.get().getId());
                buildCustomerMirrorBodyVO(mirrorPostpartumDataVO, mirrorBodyReportVO);

                customerProductionBodyVO.setType(1);
                customerProductionBodyVO.setMirrorPostpartumDataVO(mirrorPostpartumDataVO);
            }
        }

        if (Objects.isNull(customerProductionBodyVO.getMirrorPostpartumDataVO())) {
            // 获取客户所有的clientId
            List<Long> clientIdList = tabClientService.queryClientListByBasicUid(basicId).getData().stream()
                .map(TabClientPO::getId).collect(Collectors.toList());

            List<CustomerPostpartumInfoPO> postpartumInfoPOList = customerPostpartumInfoService.listByCustomerIdList(clientIdList);
            if (CollectionUtil.isEmpty(postpartumInfoPOList)) {
                return customerProductionBodyVO;
            }

            postpartumInfoPOList.sort(Comparator.comparing(CustomerPostpartumInfoPO::getRecordDate).reversed());

            List<CustomerPostpartumInfoPO> recentTwoRecords = postpartumInfoPOList.stream().limit(2).collect(Collectors.toList());

            PostpartumDataVO postpartumDataVO = new PostpartumDataVO();
            buildCustomerProductionBodyVO(postpartumDataVO, recentTwoRecords);

            customerProductionBodyVO.setType(0);
            customerProductionBodyVO.setPostpartumDataVO(postpartumDataVO);
            customerProductionBodyVO.setRecordTime(postpartumInfoPOList.get(0).getRecordDate());
        }

        return customerProductionBodyVO;
    }

    /**
     * 根据客户的体侧镜数据构建客户体测镜身体数据VO
     *
     * @param mirrorPostpartumDataVO
     * @param evaluateReportVO
     */
    private void buildCustomerMirrorBodyVO(MirrorPostpartumDataVO mirrorPostpartumDataVO, CustomerPostpartumEvaluateReportVO evaluateReportVO) {
        EvaluateBaseVO evaluateBaseVO = evaluateReportVO.getEvaluateBaseVO();
        if (Objects.nonNull(evaluateBaseVO) && Objects.nonNull(evaluateBaseVO.getBaseIndexVOS())) {
            List<BasicEvaluateIndexVO> currentBaseIndexVOS = evaluateBaseVO.getBaseIndexVOS();
            if (Objects.isNull(currentBaseIndexVOS)) {
                currentBaseIndexVOS = Lists.newArrayList();
            }

            List<HistoryTableVO> historyTableVOS = evaluateBaseVO.getHistoryTableVOS();
            List<BasicEvaluateIndexVO> lastBaseIndexVOS = new ArrayList<>();
            // 检查历史记录是否存在，至少需要两条记录（倒数第二条是上次测量值）
            if (CollectionUtil.isNotEmpty(historyTableVOS) && historyTableVOS.size() >= 2) {
                HistoryTableVO historyTableVO = historyTableVOS.get(historyTableVOS.size() - 2);
                if (Objects.nonNull(historyTableVO) && CollectionUtil.isNotEmpty(historyTableVO.getIndexVOS())) {
                    lastBaseIndexVOS = historyTableVO.getIndexVOS();
                }
            }

            // 提取体重数据
            mirrorPostpartumDataVO.setWeight(buildMirrorBodyIndexVO(currentBaseIndexVOS, lastBaseIndexVOS, "weight", "体重"));
            // 提取胸围数据
            mirrorPostpartumDataVO.setBust(buildMirrorBodyIndexVO(currentBaseIndexVOS, lastBaseIndexVOS, "bust_girth", "胸围"));
            // 提取腰围数据
            mirrorPostpartumDataVO.setWaist(buildMirrorBodyIndexVO(currentBaseIndexVOS, lastBaseIndexVOS, "waist_girth", "腰围"));
            // 提取臀围数据
            mirrorPostpartumDataVO.setHip(buildMirrorBodyIndexVO(currentBaseIndexVOS, lastBaseIndexVOS, "hip_girth", "臀围"));

            // 胸部
            EvaluateBreastVO evaluateBreastVO = evaluateReportVO.getEvaluateBreastVO();
            if (Objects.nonNull(evaluateBreastVO) && evaluateBreastVO.getErrorCount() > 0) {
                mirrorPostpartumDataVO.setBreast(buildMirrorBodyExceptionInfo(evaluateBreastVO.getBaseIndexVOS(), "胸部"));
            }

            // 脊柱
            EvaluateSpineVO evaluateSpineVO = evaluateReportVO.getEvaluateSpineVO();
            if (Objects.nonNull(evaluateSpineVO) && evaluateSpineVO.getErrorCount() > 0) {
                mirrorPostpartumDataVO.setSpine(buildMirrorBodyExceptionInfo(evaluateSpineVO.getBaseIndexVOS(), "脊柱"));
            }

            // 骨盆
            EvaluatePelvisVO evaluatePelvisVO = evaluateReportVO.getEvaluatePelvisVO();
            if (Objects.nonNull(evaluatePelvisVO) && evaluatePelvisVO.getErrorCount() > 0) {
                mirrorPostpartumDataVO.setPelvis(buildMirrorBodyExceptionInfo(evaluatePelvisVO.getBaseIndexVOS(), "骨盆"));
            }
        }
    }

    /**
     * 构建体测镜身体基础数据
     *
     * @param currentBaseIndexVOS
     * @param lastBaseIndexVOS
     * @param detKey
     * @param cname
     * @return
     */
    private BodyIndexVO buildMirrorBodyIndexVO(List<BasicEvaluateIndexVO> currentBaseIndexVOS, List<BasicEvaluateIndexVO> lastBaseIndexVOS, String detKey, String cname) {
        BodyIndexVO bodyIndexVO = new BodyIndexVO();
        bodyIndexVO.setIndexName(cname);

        String currentValue = "0";
        String unit = "";

        for (BasicEvaluateIndexVO indexVO : currentBaseIndexVOS) {
            if (detKey.equals(indexVO.getDetKey())) {
                currentValue = indexVO.getDetValue();
                unit = indexVO.getUnit();
                break;
            }
        }

        bodyIndexVO.setCurrentIndexValue(currentValue);
        bodyIndexVO.setIndexUnit(unit);

        String lastValue = "0";
        for (BasicEvaluateIndexVO indexVO : lastBaseIndexVOS) {
            if (detKey.equals(indexVO.getDetKey())) {
                lastValue = indexVO.getDetValue();
                break;
            }
        }

        if (!lastValue.equals("0")) {
            bodyIndexVO.setLastIndexValue(lastValue);
        }

        BigDecimal currentVal = new BigDecimal(currentValue);
        BigDecimal lastVal = new BigDecimal(lastValue);
        BigDecimal contrastValue = currentVal.subtract(lastVal);
        if (lastVal.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal rate = contrastValue.divide(lastVal, 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            bodyIndexVO.setRate(rate.stripTrailingZeros().toPlainString());
        }

        return bodyIndexVO;
    }

    /**
     * 构建体测镜身体异常信息数据
     *
     * @param baseIndexVOS
     * @param indexName
     * @return
     */
    private MirrorDataExceptionVO buildMirrorBodyExceptionInfo(List<BasicEvaluateIndexVO> baseIndexVOS, String indexName) {
        MirrorDataExceptionVO exceptionVO = new MirrorDataExceptionVO();
        exceptionVO.setIndexName(indexName);
        List<String> exceptionList = new ArrayList<>();
        for (BasicEvaluateIndexVO baseIndexVO : baseIndexVOS) {
            if (baseIndexVO.getIsError()) {
                Optional<PropertyValueVO> first = baseIndexVO.getPropProperty().getValueList().stream()
                    .filter(i -> baseIndexVO.getDetValue().equals(i.getCode()))
                    .findFirst();

                first.ifPresent(propertyValueVO -> exceptionList.add(propertyValueVO.getValue()));
            }
        }

        exceptionVO.setExceptionList(exceptionList);
        exceptionVO.setExceptionCount(exceptionList.size());

        return exceptionVO;
    }

    /**
     * 构建客户产康预约基础测量数据
     *
     * @param postpartumDataVO
     * @param recrods
     */
    private void buildCustomerProductionBodyVO(PostpartumDataVO postpartumDataVO, List<CustomerPostpartumInfoPO> recrods) {
        postpartumDataVO.setWeight(buildSingleIndexVO("体重", "kg", recrods, CustomerPostpartumInfoPO::getWeight));
        postpartumDataVO.setBust(buildSingleIndexVO("胸围", "cm", recrods, CustomerPostpartumInfoPO::getBreastCircumference));
        postpartumDataVO.setWaist(buildSingleIndexVO("腰围", "cm", recrods, CustomerPostpartumInfoPO::getWaistCircumference));
        postpartumDataVO.setHip(buildSingleIndexVO("臀围", "cm", recrods, CustomerPostpartumInfoPO::getHipCircumference));
    }

    /**
     * 构建单指标数据
     *
     * @param indexName
     * @param unit
     * @param recrods
     * @param valueExtractor
     * @return
     */
    private BodyIndexVO buildSingleIndexVO(String indexName, String unit, List<CustomerPostpartumInfoPO> recrods,  Function<CustomerPostpartumInfoPO, BigDecimal> valueExtractor) {
        BodyIndexVO bodyIndexVO = new BodyIndexVO();
        bodyIndexVO.setIndexName(indexName);
        bodyIndexVO.setIndexUnit(unit);

        if (CollectionUtil.isEmpty(recrods)) {
            return bodyIndexVO;
        }

        CustomerPostpartumInfoPO latestRecord = recrods.get(0);
        BigDecimal currentValue = valueExtractor.apply(latestRecord);
        if (Objects.nonNull(currentValue)) {
            bodyIndexVO.setCurrentIndexValue(currentValue.toString());
        }
        if (recrods.size() >= 2) {
            CustomerPostpartumInfoPO previousRecord = recrods.get(1);
            BigDecimal previousValue = valueExtractor.apply(previousRecord);
            if (Objects.nonNull(previousValue)) {
                bodyIndexVO.setLastIndexValue(previousValue.toString());

                if (Objects.nonNull(currentValue)) {
                    BigDecimal contrastValue = currentValue.subtract(previousValue);
                    if (previousValue.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal rate = contrastValue.divide(previousValue, 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                        bodyIndexVO.setRate(rate.stripTrailingZeros().toPlainString());
                    }
                }
            }
        }

        return bodyIndexVO;
    }
    private CustomerProductionAmountStreamVO convertStreamDto(AccountStreamDto dto) {
        CustomerProductionAmountStreamVO streamVO = new CustomerProductionAmountStreamVO();

        streamVO.setId(dto.getId());
        streamVO.setTitle(dto.getTitle());
        if (dto.getTitle().contains("后台赠送")
            || dto.getTitle().contains("客诉")) {
            streamVO.setTitle("赠送");
        } else if (dto.getTradeType().equals(TradeType.AVAILABLE_CKJ_ROOM_CHECK_IN.getCode())
            || dto.getTradeType().equals(TradeType.FROZEN_CKJ_ORDER_CREATED.getCode())
            || dto.getTradeType().equals(TradeType.AVAILABLE_CKJ_ORDER_UPDATE_ADD.getCode())
            || dto.getTradeType().equals(TradeType.FROZEN_CKJ_ORDER_UPDATE_ADD.getCode())) {
            // 订单履约，优先查看订单
            if (StringUtils.isNotBlank(dto.getOrderNo())) {
                HeOrderPO orderPO = heOrderService.getByOrderSn(dto.getOrderNo());
                if (Objects.nonNull(orderPO)) {
                    if (orderPO.getVersion() < 3.00) {
                        streamVO.setTitle("订单赠送");
                    } else {
                        // 新订单，优先判断asset_id, asset_id = 1 订单类型-购买 2 订单类型-定制礼赠
                        if (dto.getAssetId().equals("1")) {
                            streamVO.setTitle("订单类型-购买");
                        } else if (dto.getAssetId().equals("2")) {
                            streamVO.setTitle("订单类型-定制礼赠");
                        } else {
                            if (StringUtils.isNotBlank(dto.getAssetId())) {
                                // 去order_goods表中查
                                HeOrderGoodsPO orderGoodsPO = heOrderGoodsService.queryOrderGoodsInfoByOrderGoodsSn(dto.getAssetId());
                                if (Objects.nonNull(orderGoodsPO)) {
                                    if (orderGoodsPO.getGift().equals(0)) {
                                        streamVO.setTitle("订单类型-购买");
                                    } else if (orderGoodsPO.getGift().equals(1)) {
                                        streamVO.setTitle("订单类型-定制礼赠");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else if (dto.getTradeType().equals(TradeType.AVAILABLE_CKJ_ORDER_USED.getCode())) {
            // 购买
            streamVO.setTitle("项目规划-产康项目");
        } else if (dto.getTradeType().equals(TradeType.FROZEN_CKJ_ROOM_CHECK_IN.getCode())) {
            streamVO.setTitle("开始服务");
        }

        streamVO.setAssetType(dto.getAssetType());
        if (AssetTypeEnum.AVAILABLE_CKJ.getCode().equals(dto.getAssetType().intValue())) {
            streamVO.setAssetTypeName("可用账户");
        } else {
            streamVO.setAssetTypeName("冻结账户");
        }

        streamVO.setBalance(dto.getAmount() / 100);
        streamVO.setAfterBalance(dto.getBalanceAfter() / 100);
        streamVO.setAffectTag(dto.getTradeAffect());
        streamVO.setGmtCreate(dto.getGmtCreate());
        streamVO.setMonth(DateUtil.format(dto.getGmtCreate(), "yyyy.MM"));

        return streamVO;
    }


    @Override
    public PageVO<ClientSearchVO> clientSearch(ClientSearchRequest request) {
        log.info("clientSearch request:{}", JSON.toJSONString(request));
        if (request.getPageNum() <= 0) {
            request.setPageNum(1);
        }
        if (Objects.isNull(request.getStoreId())) {
            throw new BusinessException("请选择门店");
        }
        if (Objects.isNull(request.getKeyword())) {
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        boolean isSeller = false;
        List<SysEmployeeDTO> roleNameByMobile = iSysEmployeeRoleService.listEmployeeByRoles(Arrays.asList(request.getOperator().getOperatorPhone())
                ,Arrays.asList("frontLineSale")
        );
        log.info("探店报告用户搜索:销售:{},request:{},",isSeller,JSON.toJSONString(request));
        if(CollectionUtil.isNotEmpty(roleNameByMobile)){
            isSeller = true;
        }
        List<String> grandPhoneList = new ArrayList<>();
        if(isSeller){
            grandPhoneList = getGrandPhoneList(request);
            if(CollectionUtils.isEmpty(grandPhoneList)){
                return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
            }
        }
        GrantUserBasicQueryRequest queryRequest = new GrantUserBasicQueryRequest();
        queryRequest.setKeyword(request.getKeyword());
        queryRequest.setPageNum(request.getPageNum());
        queryRequest.setPageSize(request.getPageSize());
        if(isSeller){
            queryRequest.setGrantPhoneList(grandPhoneList);
        }
        PageVO<HeUserBasicPO> heUserBasicPOPageVO = heUserBasicService.queryGrantByCustomerNameOrPhone(queryRequest);
        if(heUserBasicPOPageVO==null || CollectionUtil.isEmpty(heUserBasicPOPageVO.getList())){
            return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
        }
        EcpStorePO storePO = cfgStoreService.queryStoreByStoreId(request.getStoreId());
        List<HeUserBasicPO> phoneList = new ArrayList<>(heUserBasicPOPageVO.getList().stream()
                .collect(Collectors.toMap(HeUserBasicPO::getPhone, Function.identity(), (a, b) -> a)).values());
        //从phoneList中拿到手机号组成list<string>
        List<String> collect = phoneList.stream().map(HeUserBasicPO::getPhone).collect(Collectors.toList());
        List<TabClientPO> tabClientPOS = tabClientService.list(new LambdaQueryWrapper<TabClientPO>().in(TabClientPO::getPhone, collect)
                .eq(TabClientPO::getStoreId, request.getStoreId())
        );
        Map<String, TabClientPO> tabClientPOMap = tabClientPOS.stream().collect(Collectors.toMap(TabClientPO::getPhone, Function.identity()));
        List<ClientSearchVO> resultList = new ArrayList<>();
        heUserBasicPOPageVO.getList().forEach(heUserBasicPO->{
            ClientSearchVO clientSearchVO = new ClientSearchVO();
            TabClientPO po = tabClientPOMap.get(heUserBasicPO.getPhone());
            clientSearchVO.setId(po==null?null:po.getId());
            clientSearchVO.setName(heUserBasicPO.getName());
            clientSearchVO.setPhone(heUserBasicPO.getPhone());
            clientSearchVO.setSex(po==null?null:po.getSex());
            clientSearchVO.setHide_phone(DesensitizedUtil.mobilePhone(heUserBasicPO.getPhone()));
            clientSearchVO.setSeller_id(po==null?0:po.getSellerId());
            clientSearchVO.setFrom_type(po==null?null:po.getFromType());
            clientSearchVO.setStore_id(po==null?null:po.getStoreId());
            clientSearchVO.setBasic_uid(heUserBasicPO.getId().intValue());
            clientSearchVO.setUp_id(po==null?null:po.getUpId());
            clientSearchVO.setPhone_type(po==null?null:po.getPhoneType());
            if(po!=null) {
                UserPO one = userService.getOne(new LambdaQueryWrapper<UserPO>().eq(UserPO::getId, po.getUpId()));
                clientSearchVO.setStaff_name(one==null?null:one.getName());
            }
            clientSearchVO.setSource(po==null?null:CustomerFromTypeEnum.getValueByCode(po.getFromType()));
            clientSearchVO.setSwitch_status("0");
            clientSearchVO.setStore_name(storePO==null?null:storePO.getStoreName());
            TabWechatUserPO wechatUserPO = tabWechatUserService.queryWechatInfoByBasicUid(heUserBasicPO.getId().intValue());
            if (Objects.nonNull(wechatUserPO)) {
                clientSearchVO.setAvatar_url(wechatUserPO.getAvatarUrl());
            }
            clientSearchVO.setUser_newest(new ClientSearchVO.User_newest(heUserBasicPO.getId(), heUserBasicPO.getPhone(), 0));
            resultList.add(clientSearchVO);
        });
        return new PageVO<>(resultList, heUserBasicPOPageVO.getTotalCount(), heUserBasicPOPageVO.getPageSize(), heUserBasicPOPageVO.getPageNo());
    }

    private @NotNull List<String> getGrandPhoneList(ClientSearchRequest request) {
        ScrmUserPO scrmUserPO = scrmUserService.queryByScrmPhone(request.getOperator().getOperatorPhone());
        if(scrmUserPO==null){
            return new ArrayList<>();
        }
        List<ClientSearchMemberVO> memberVOList = scrmTeamMemberRecordService.searchByKeyword(scrmUserPO.getScrmId(), request.getKeyword());

//        Page<ClientSearchMemberVO> memberVOList = scrmTeamMemberRecordService.searchByKeyword(new Page(request.getPageNum(), request.getPageSize()),
//                scrmUserPO.getScrmId(),
//                request.getKeyword());
        if(memberVOList!=null && CollectionUtil.isEmpty(memberVOList)){
            return new ArrayList<>();
        }
        //获取有权限的手机号
        return memberVOList.stream().map(ClientSearchMemberVO::getPhone).collect(Collectors.toList());
    }

    private PageVO<ClientSearchVO> buildClientSearchVOS(PageVO<ClientSearchMemberVO> result) {
        List<String> phoneList = result.getList().stream().map(ClientSearchMemberVO::getPhone).collect(Collectors.toList());
        List<TabClientPO> tabClientPOS = tabClientService.list(new LambdaQueryWrapper<TabClientPO>().in(TabClientPO::getPhone, phoneList));
        Map<String, TabClientPO> tabClientPOMap = tabClientPOS.stream().collect(Collectors.toMap(TabClientPO::getPhone, Function.identity()));
        List<ClientSearchVO> resultList = new ArrayList<>();
        result.getList().forEach(customerPO -> {
            ClientSearchVO clientSearchVO = new ClientSearchVO();
            TabClientPO po = tabClientPOMap.get(customerPO.getPhone());
            clientSearchVO.setId(po.getId());
            clientSearchVO.setName(customerPO.getName());
            clientSearchVO.setPhone(customerPO.getPhone());
            clientSearchVO.setSex(po.getSex());
            clientSearchVO.setHide_phone(DesensitizedUtil.mobilePhone(customerPO.getPhone()));
//                clientSearchVO.setStaff_name(customerPO.getStaffName());
//                clientSearchVO.setSource(po.getSource());
            resultList.add(clientSearchVO);
        });
        return new PageVO<ClientSearchVO>(resultList, result.getTotalCount(),  result.getPageSize(),  result.getPageNo());
    }
}
