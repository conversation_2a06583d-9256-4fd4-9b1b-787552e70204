package com.stbella.customer.server.scrm.convert;

import com.stbella.customer.server.config.DateMapper;
import com.stbella.customer.server.config.MappingConfig;
import com.stbella.customer.server.cts.enums.SitterConstellationEnum;
import com.stbella.customer.server.cts.request.BatchOpportunityRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.customer.enums.PregnancyEnum;
import com.stbella.customer.server.scrm.dto.OrderGoodsDTO;
import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerBirthDTO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerDTO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerOrderDTO;
import com.stbella.customer.server.scrm.entity.CustomerAdSubscribeListPO;
import com.stbella.customer.server.scrm.entity.ScrmBusinessOpportunityPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerBabyPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerOrderPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerOrderRefundPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.entity.ScrmOpportunityActivityRecordPO;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.request.AccountInfoRequest;
import com.stbella.customer.server.scrm.request.OpportunityRequest;
import com.stbella.customer.server.scrm.request.OrderInfoRequest;
import com.stbella.customer.server.scrm.request.OrderRefundRequest;
import com.stbella.customer.server.scrm.request.ScrmOpportunityActivityRecordRequest;
import com.stbella.customer.server.scrm.request.ScrmOrderUpdateRequest;
import com.stbella.customer.server.scrm.request.ScrmTeamMemberRecordRequest;
import com.stbella.customer.server.scrm.request.SubscribeRequest;
import com.stbella.customer.server.scrm.request.SynHistoryCustomerRequest;
import com.stbella.customer.server.scrm.vo.ScrmOpportunityDetailVO;
import com.stbella.customer.server.util.EmojiFilter;

import com.stbella.order.server.order.month.res.OrderGoodsInfoVO;
import com.stbella.order.server.order.month.res.OrderInfoNewV3VO;
import com.stbella.order.server.order.month.res.ScrmOrderReductionRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

@Mapper(config = MappingConfig.class, uses = DateMapper.class, imports = {DateUtil.class})
public interface SCRMConvert {
    @Mappings(value = {
            @Mapping(target = "province", ignore = true),
            @Mapping(target = "city", ignore = true),
            @Mapping(target = "region", ignore = true),
            @Mapping(target = "birthdate", expression = "java(DateUtil.formatDateTime(scrmCustomerPO.getBirthdate()))"),
            @Mapping(target = "predictBornDate", expression = "java(DateUtil.formatDateTime(scrmCustomerPO.getPredictBornDate()))"),
    })
    ScrmCustomerDTO scrmCustomerPO2DTO(ScrmCustomerPO scrmCustomerPO);

    /**
     * scrm客户信息转为客户PO
     * @param accountInfoRequest
     * @return
     */
    default ScrmCustomerPO accountInfoRequest2ScrmCustomerPO(AccountInfoRequest accountInfoRequest) {

        if (accountInfoRequest == null) {
            return null;
        }

        ScrmCustomerPO scrmCustomerPO = new ScrmCustomerPO();


        if (accountInfoRequest.getPhone() != null) {
            scrmCustomerPO.setPhone(accountInfoRequest.getPhone());
        }
        if (accountInfoRequest.getAddress() != null) {
            scrmCustomerPO.setAddress(accountInfoRequest.getAddress());
        }
        scrmCustomerPO.setName(accountInfoRequest.getAccountName());
        scrmCustomerPO.setEmail(accountInfoRequest.getAccountEmail());
        scrmCustomerPO.setCertType(accountInfoRequest.getCertType__c());
        scrmCustomerPO.setIdCard(accountInfoRequest.getIdCard__c());
        scrmCustomerPO.setNation(accountInfoRequest.getNation__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getBirthdate__c())) {
            scrmCustomerPO.setBirthdate(new Date(accountInfoRequest.getBirthdate__c()));
        }
        scrmCustomerPO.setConstellationType(accountInfoRequest.getConstellationType__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getConstellationType__c())) {
            scrmCustomerPO.setConstellation(SitterConstellationEnum.getValueByCode(accountInfoRequest.getConstellationType__c()));
        }

        if (ObjectUtil.isNotEmpty(accountInfoRequest.getFState())) {
            // 省市区暂时先存储scrm的id值
            scrmCustomerPO.setProvince(accountInfoRequest.getFState().toString());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getFCity())) {
            // 省市区暂时先存储scrm的id值
            scrmCustomerPO.setCity(accountInfoRequest.getFCity().toString());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getFDistrict())) {
            // 省市区暂时先存储scrm的id值
            scrmCustomerPO.setRegion(accountInfoRequest.getFDistrict().toString());
        }

        if (ObjectUtil.isNotEmpty(accountInfoRequest.getPredictBornDate__c())) {
            scrmCustomerPO.setPredictBornDate(new Date(accountInfoRequest.getPredictBornDate__c()));
        }
        scrmCustomerPO.setCustomerStage(accountInfoRequest.getCustomerStage__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem189__c())) {
            scrmCustomerPO.setPregnancy(PregnancyEnum.getCodeByValue(accountInfoRequest.getCustomItem189__c()));
        }
        scrmCustomerPO.setBornNum(accountInfoRequest.getBornNum__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getFetusNum__c())) {
            scrmCustomerPO.setFetusNum(accountInfoRequest.getFetusNum__c().intValue());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem188__c())) {
            scrmCustomerPO.setGestationWeekNow(Integer.valueOf(new DecimalFormat("0").format(accountInfoRequest.getCustomItem188__c())));
        }
        scrmCustomerPO.setHospital(accountInfoRequest.getHospital__c());
        scrmCustomerPO.setProductionMode(accountInfoRequest.getProductionMode__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getBornTime__c())) {
            scrmCustomerPO.setBornTime(new Date(accountInfoRequest.getBornTime__c()));
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem190__c())){
            scrmCustomerPO.setUrgentName(accountInfoRequest.getCustomItem190__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem191__c())){
            scrmCustomerPO.setUrgentPhone(accountInfoRequest.getCustomItem191__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getRelationWithClient__c())){
            scrmCustomerPO.setRelationWithClient(accountInfoRequest.getRelationWithClient__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getProfession__c())){
            scrmCustomerPO.setProfession(accountInfoRequest.getProfession__c());
        }
        // TODO: 2023/3/16 年龄
        scrmCustomerPO.setAge(null);
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getBloodType__c())){
            scrmCustomerPO.setBloodType(accountInfoRequest.getBloodType__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem231__c())){
            scrmCustomerPO.setFromType(accountInfoRequest.getCustomItem231__c());
        }
//        scrmCustomerPO.setTags("");
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getId())){
            scrmCustomerPO.setScrmCustomerId(accountInfoRequest.getId());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getOwnerId())){
            scrmCustomerPO.setScrmOwnerId(accountInfoRequest.getOwnerId());
        }

        if (ObjectUtil.isNotEmpty(accountInfoRequest.getOwnerPhone())){
            scrmCustomerPO.setScrmOwnerPhone(accountInfoRequest.getOwnerPhone());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem202__c())) {
            scrmCustomerPO.setFoodProhibition(Arrays.asList(accountInfoRequest.getCustomItem202__c()));
        }
        scrmCustomerPO.setOtherFoodRequirement(accountInfoRequest.getCustomItem203__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem204__c())) {
            scrmCustomerPO.setRoomProhibition(Arrays.asList(accountInfoRequest.getCustomItem204__c()));
        }
        scrmCustomerPO.setOtherRoomProhibition(accountInfoRequest.getCustomItem205__c());
        scrmCustomerPO.setGestationalDiabetes(accountInfoRequest.getCustomItem206__c());
        scrmCustomerPO.setHighBloodPressure(accountInfoRequest.getCustomItem207__c());
        scrmCustomerPO.setInfectiousDiseaseExamination(accountInfoRequest.getCustomItem234__c());
        scrmCustomerPO.setHistoryOfDrugAllergy(accountInfoRequest.getCustomItem235__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem236__c())) {
            scrmCustomerPO.setNoneTag(accountInfoRequest.getCustomItem236__c().toString());
        }
        scrmCustomerPO.setOtherMedicalPrecautions(accountInfoRequest.getCustomItem210__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCheckInDate__c())) {
            scrmCustomerPO.setCheckInDate(new Date(accountInfoRequest.getCheckInDate__c()));
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCheckOutDate__c())) {
            scrmCustomerPO.setCheckOutDate(new Date(accountInfoRequest.getCheckOutDate__c()));
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getInDays__c())) {
            scrmCustomerPO.setInDays(String.valueOf(accountInfoRequest.getInDays__c()));
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getIsCustomer())) {
            scrmCustomerPO.setSignStatus(accountInfoRequest.getIsCustomer().equals("否") ? 0 : 1);
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getTotalWonOpportunities())) {
            scrmCustomerPO.setSignNum(Integer.valueOf(new DecimalFormat("0").format(accountInfoRequest.getTotalWonOpportunities())));
        }
        scrmCustomerPO.setMonthCareMissionTime(accountInfoRequest.getCustomItem211__c());
        scrmCustomerPO.setMorningMusicHealingTime(accountInfoRequest.getCustomItem212__c());
        scrmCustomerPO.setOtherSeverNotes(accountInfoRequest.getCustomItem213__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem214__c())) {
            scrmCustomerPO.setSmell(Arrays.asList(accountInfoRequest.getCustomItem214__c()));
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem215__c())) {
            scrmCustomerPO.setMostConcernedAboutRecoverySites(Arrays.asList(accountInfoRequest.getCustomItem215__c()));
        }
        scrmCustomerPO.setMassageIntensity(accountInfoRequest.getCustomItem216__c());
        scrmCustomerPO.setDesiredTimeToParticipate(accountInfoRequest.getCustomItem217__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem218__c())) {
            scrmCustomerPO.setLikeType(Arrays.asList(accountInfoRequest.getCustomItem218__c()));
        }
        scrmCustomerPO.setEntryReminder(accountInfoRequest.getCustomItem219__c());
        scrmCustomerPO.setInvitationMode(accountInfoRequest.getCustomItem220__c());
        scrmCustomerPO.setButlerCareFrequency(accountInfoRequest.getCustomItem221__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem222__c())) {
            scrmCustomerPO.setDoNotDisturbService(Arrays.asList(accountInfoRequest.getCustomItem222__c()));
        }
        scrmCustomerPO.setOtherService(accountInfoRequest.getCustomItem223__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem224__c())) {
            scrmCustomerPO.setRoomPreparation(Arrays.asList(accountInfoRequest.getCustomItem224__c()));
        }
        scrmCustomerPO.setOtherPreparation(accountInfoRequest.getCustomItem225__c());
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem226__c())) {
            scrmCustomerPO.setSpecialIdentity(Arrays.asList(accountInfoRequest.getCustomItem226__c()));
        }
        scrmCustomerPO.setVipLevel(accountInfoRequest.getCustomItem227__c());
        scrmCustomerPO.setStoreName(accountInfoRequest.getCustomItem228__c());

        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem230__c())) {
            scrmCustomerPO.setCustomerStatus(accountInfoRequest.getCustomItem230__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem232__c())) {
            scrmCustomerPO.setOfflineFromType(accountInfoRequest.getCustomItem232__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem233__c())) {
            scrmCustomerPO.setWechat(accountInfoRequest.getCustomItem233__c());
        }
        scrmCustomerPO.setCreateBy(accountInfoRequest.getCreatedBy());
        scrmCustomerPO.setUpdateBy(accountInfoRequest.getUpdatedBy());

        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem255__c())) {
            scrmCustomerPO.setBudget(accountInfoRequest.getCustomItem255__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem262__c())) {
            scrmCustomerPO.setPhoneType(accountInfoRequest.getCustomItem262__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getGender__c())) {
            scrmCustomerPO.setSex(accountInfoRequest.getGender__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getProductionFromType__c())) {
            scrmCustomerPO.setProductionFromType(accountInfoRequest.getProductionFromType__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCtsFromType__c())) {
            scrmCustomerPO.setCtsFromType(accountInfoRequest.getCtsFromType__c());
        }
        if (ObjectUtil.isNotEmpty(accountInfoRequest.getCtsFromTypeStore__c())) {
            scrmCustomerPO.setCtsFromTypeStore(accountInfoRequest.getCtsFromTypeStore__c());
        }
        return scrmCustomerPO;

    }

    /**
     * 客户po转为scrm客户
     * @param scrmCustomerPO
     * @return
     */
    default AccountInfoRequest scrmCustomerPO2AccountInfoRequest(ScrmCustomerPO scrmCustomerPO) {
        if (scrmCustomerPO == null) {
            return null;
        }

        AccountInfoRequest accountInfoRequest = new AccountInfoRequest();
        if (scrmCustomerPO.getScrmCustomerId() != null) {
            accountInfoRequest.setId(scrmCustomerPO.getScrmCustomerId());
        }
        if (scrmCustomerPO.getAddress() != null) {
            accountInfoRequest.setAddress(scrmCustomerPO.getAddress());
        }
        if (scrmCustomerPO.getPhone() != null) {
            accountInfoRequest.setPhone(scrmCustomerPO.getPhone());
        }
        accountInfoRequest.setOwnerId(scrmCustomerPO.getScrmOwnerId());
        accountInfoRequest.setOwnerPhone(scrmCustomerPO.getScrmOwnerPhone());
        accountInfoRequest.setAccountName(scrmCustomerPO.getName());

        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getProvince()) && !scrmCustomerPO.getProvince().equals("-1")) {
            accountInfoRequest.setFState(Integer.valueOf(scrmCustomerPO.getProvince()));
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCity()) && !scrmCustomerPO.getCity().equals("-1")) {
            accountInfoRequest.setFCity(Integer.valueOf(scrmCustomerPO.getCity()));
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getRegion()) && !scrmCustomerPO.getRegion().equals("-1")) {
            accountInfoRequest.setFDistrict(Integer.valueOf(scrmCustomerPO.getRegion()));
        }

        accountInfoRequest.setAddress(scrmCustomerPO.getAddress());
        accountInfoRequest.setPhone(scrmCustomerPO.getPhone());
        accountInfoRequest.setWxUnionID(scrmCustomerPO.getWechat());
        accountInfoRequest.setAccountEmail(scrmCustomerPO.getEmail());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getGmtCreate())) {
            accountInfoRequest.setCreatedAt(scrmCustomerPO.getGmtCreate().getTime());
        }
        accountInfoRequest.setCreatedBy(scrmCustomerPO.getCreateBy());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getGmtModified())) {
            accountInfoRequest.setUpdatedAt(scrmCustomerPO.getGmtModified().getTime());
        }
        accountInfoRequest.setUpdatedBy(scrmCustomerPO.getUpdateBy());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSignNum())) {
            accountInfoRequest.setTotalWonOpportunities(scrmCustomerPO.getSignNum().doubleValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSignStatus())) {
            accountInfoRequest.setIsCustomer(scrmCustomerPO.getSignStatus().equals(1) ? "是" : "否");
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getGestationWeekNow())) {
            accountInfoRequest.setCustomItem188__c(scrmCustomerPO.getGestationWeekNow().doubleValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getPregnancy())) {
            accountInfoRequest.setCustomItem189__c(PregnancyEnum.getValueByCode(scrmCustomerPO.getPregnancy()));
        }
        accountInfoRequest.setCustomItem190__c(scrmCustomerPO.getUrgentName());
        accountInfoRequest.setCustomItem191__c(scrmCustomerPO.getUrgentPhone());
        accountInfoRequest.setRelationWithClient__c(scrmCustomerPO.getRelationWithClient());
        accountInfoRequest.setCertType__c(scrmCustomerPO.getCertType());
        accountInfoRequest.setIdCard__c(scrmCustomerPO.getIdCard());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getBirthdate())) {
            accountInfoRequest.setBirthdate__c(scrmCustomerPO.getBirthdate().getTime());
        }
        accountInfoRequest.setProfession__c(scrmCustomerPO.getProfession());
        accountInfoRequest.setBloodType__c(scrmCustomerPO.getBloodType());
        accountInfoRequest.setNation__c(scrmCustomerPO.getNation());
        accountInfoRequest.setConstellationType__c(scrmCustomerPO.getConstellationType());
        accountInfoRequest.setCustomerStage__c(scrmCustomerPO.getCustomerStage());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getPredictBornDate())) {
            accountInfoRequest.setPredictBornDate__c(scrmCustomerPO.getPredictBornDate().getTime());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCheckInDate())) {
            accountInfoRequest.setCheckInDate__c(scrmCustomerPO.getCheckInDate().getTime());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCheckOutDate())) {
            accountInfoRequest.setCheckOutDate__c(scrmCustomerPO.getCheckOutDate().getTime());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getInDays())) {
            accountInfoRequest.setInDays__c(Long.valueOf(scrmCustomerPO.getInDays()));
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getBornTime())) {
            accountInfoRequest.setBornTime__c(scrmCustomerPO.getBornTime().getTime());
        }
        accountInfoRequest.setHospital__c(scrmCustomerPO.getHospital());
        accountInfoRequest.setProductionMode__c(scrmCustomerPO.getProductionMode());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getBornNum())) {

            accountInfoRequest.setBornNum__c(scrmCustomerPO.getBornNum() >= 3 ? 3 : scrmCustomerPO.getBornNum());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getFetusNum())) {
            accountInfoRequest.setFetusNum__c(scrmCustomerPO.getFetusNum().longValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getFoodProhibition())) {
            accountInfoRequest.setCustomItem202__c(scrmCustomerPO.getFoodProhibition().toArray(new Integer[0]));
        }
        accountInfoRequest.setCustomItem203__c(scrmCustomerPO.getOtherFoodRequirement());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getRoomProhibition())) {
            accountInfoRequest.setCustomItem204__c(scrmCustomerPO.getRoomProhibition().toArray(new Integer[0]));
        }
        accountInfoRequest.setCustomItem205__c(scrmCustomerPO.getOtherRoomProhibition());
        accountInfoRequest.setCustomItem206__c(scrmCustomerPO.getGestationalDiabetes());
        accountInfoRequest.setCustomItem207__c(scrmCustomerPO.getHighBloodPressure());
        accountInfoRequest.setCustomItem234__c(scrmCustomerPO.getInfectiousDiseaseExamination());
        accountInfoRequest.setCustomItem235__c(scrmCustomerPO.getHistoryOfDrugAllergy());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getNoneTag())) {
            accountInfoRequest.setCustomItem236__c(Integer.valueOf(scrmCustomerPO.getNoneTag()));
        }
        accountInfoRequest.setCustomItem210__c(scrmCustomerPO.getOtherMedicalPrecautions());
        accountInfoRequest.setCustomItem211__c(scrmCustomerPO.getMonthCareMissionTime());
        accountInfoRequest.setCustomItem212__c(scrmCustomerPO.getMorningMusicHealingTime());
        accountInfoRequest.setCustomItem213__c(scrmCustomerPO.getOtherSeverNotes());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSmell())) {
            accountInfoRequest.setCustomItem214__c(scrmCustomerPO.getSmell().toArray(new Integer[0]));
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getMostConcernedAboutRecoverySites())) {
            accountInfoRequest.setCustomItem215__c(scrmCustomerPO.getMostConcernedAboutRecoverySites().toArray(new Integer[0]));

        }
        accountInfoRequest.setCustomItem216__c(scrmCustomerPO.getMassageIntensity());
        accountInfoRequest.setCustomItem217__c(scrmCustomerPO.getDesiredTimeToParticipate());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getLikeType())) {
            accountInfoRequest.setCustomItem218__c(scrmCustomerPO.getLikeType().toArray(new Integer[0]));
        }
        accountInfoRequest.setCustomItem219__c(scrmCustomerPO.getEntryReminder());
        accountInfoRequest.setCustomItem220__c(scrmCustomerPO.getInvitationMode());
        accountInfoRequest.setCustomItem221__c(scrmCustomerPO.getButlerCareFrequency());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getDoNotDisturbService())) {
            accountInfoRequest.setCustomItem222__c(scrmCustomerPO.getDoNotDisturbService().toArray(new Integer[0]));
        }
        accountInfoRequest.setCustomItem223__c(scrmCustomerPO.getOtherService());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getRoomPreparation())) {
            accountInfoRequest.setCustomItem224__c(scrmCustomerPO.getRoomPreparation().toArray(new Integer[0]));
        }
        accountInfoRequest.setCustomItem225__c(scrmCustomerPO.getOtherPreparation());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSpecialIdentity())) {
            accountInfoRequest.setCustomItem226__c(scrmCustomerPO.getSpecialIdentity().toArray(new Integer[0]));
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getWinSale())) {
            accountInfoRequest.setCustomItem238__c(scrmCustomerPO.getWinSale());
        }
        accountInfoRequest.setCustomItem227__c(scrmCustomerPO.getVipLevel());
        accountInfoRequest.setCustomItem228__c(scrmCustomerPO.getStoreName());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCustomItem229__c())) {
            accountInfoRequest.setCustomItem229__c(scrmCustomerPO.getCustomItem229__c());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCustomerStatus())) {
            accountInfoRequest.setCustomItem230__c(scrmCustomerPO.getCustomerStatus());
        }
        accountInfoRequest.setCustomItem231__c(scrmCustomerPO.getFromType());
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getOfflineFromType())) {
            accountInfoRequest.setCustomItem232__c(scrmCustomerPO.getOfflineFromType());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getPhoneType())) {
            accountInfoRequest.setCustomItem262__c(scrmCustomerPO.getPhoneType());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getWechatMomentSourceExtend())) {
            accountInfoRequest.setCustomItem268__c(scrmCustomerPO.getWechatMomentSourceExtend());
        }

        // 设置会员等级
        /*
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSaintLevel())) {
            accountInfoRequest.setCustomItem242__c(scrmCustomerPO.getSaintLevel().intValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getBabyLevel())) {
            accountInfoRequest.setCustomItem261__c(scrmCustomerPO.getBabyLevel().intValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getIslaLeve())) {
            accountInfoRequest.setIslaMemberLevel__c(scrmCustomerPO.getIslaLeve().intValue());
        }*/
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCustomerLevel())) {
            accountInfoRequest.setCustomerLevel__c(scrmCustomerPO.getCustomerLevel().intValue());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getSex()) && !scrmCustomerPO.getSex().equals(0)) {
            accountInfoRequest.setGender__c(scrmCustomerPO.getSex());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCtsFromType())) {
            accountInfoRequest.setCtsFromType__c(scrmCustomerPO.getCtsFromType());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getCtsFromTypeStore())) {
            accountInfoRequest.setCtsFromTypeStore__c(scrmCustomerPO.getCtsFromTypeStore());
        }
        if (ObjectUtil.isNotEmpty(scrmCustomerPO.getServeCityId())) {
            accountInfoRequest.setServeCityId__c(scrmCustomerPO.getServeCityId());
        }
        return accountInfoRequest;
    }

//    @Mappings(value = {
//            @Mapping(target = "province", ignore = true),
//            @Mapping(target = "city", ignore = true),
//            @Mapping(target = "region", ignore = true)
//    })
    ScrmCustomerPO scrmCustomerDTO2PO(ScrmCustomerDTO scrmCustomerDTO);

    ScrmCustomerPO synHistoryCustomerRequest2PO(SynHistoryCustomerRequest request);

    /**
     * scrm商机请求转为商机PO
     * @param scrmOpportunityRequest
     * @return
     */
    default ScrmBusinessOpportunityPO scrmOpportunityRequest2ScrmBusinessOpportunityPO(SCRMOpportunityRequest scrmOpportunityRequest) {
        if (scrmOpportunityRequest == null) {
            return null;
        }
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = new ScrmBusinessOpportunityPO();

        if (scrmOpportunityRequest.getId() != null) {
            scrmBusinessOpportunityPO.setScrmId(scrmOpportunityRequest.getId());
        }
        if (scrmOpportunityRequest.getOwnerId() != null) {
            scrmBusinessOpportunityPO.setOwnerId(scrmOpportunityRequest.getOwnerId());
        }
        if (scrmOpportunityRequest.getEntityType() != null) {
            scrmBusinessOpportunityPO.setEntityType(scrmOpportunityRequest.getEntityType());
        }
        if (scrmOpportunityRequest.getOpportunityName() != null) {
            scrmBusinessOpportunityPO.setOpportunityName(scrmOpportunityRequest.getOpportunityName());
        }
        if (scrmOpportunityRequest.getReason() != null) {
            scrmBusinessOpportunityPO.setReason(scrmOpportunityRequest.getReason());
        }
        if (scrmOpportunityRequest.getReasonDesc() != null) {
            scrmBusinessOpportunityPO.setReasonDesc(scrmOpportunityRequest.getReasonDesc());
        }
        if (scrmOpportunityRequest.getCloseDate() != null) {
            scrmBusinessOpportunityPO.setCloseDate(scrmOpportunityRequest.getCloseDate());
        }
        if (scrmOpportunityRequest.getCustomerId() != null) {
            scrmBusinessOpportunityPO.setCustomerId(scrmOpportunityRequest.getCustomerId());
        }
        if (scrmOpportunityRequest.getOpportunityType() != null) {
            scrmBusinessOpportunityPO.setOpportunityType(scrmOpportunityRequest.getOpportunityType());
        }
        if (scrmOpportunityRequest.getMoney() != null) {
            scrmBusinessOpportunityPO.setMoney(scrmOpportunityRequest.getMoney());
        }
        if (scrmOpportunityRequest.getSaleStageId() != null) {
            scrmBusinessOpportunityPO.setSaleStageId(scrmOpportunityRequest.getSaleStageId());
        }
        if (scrmOpportunityRequest.getLostStageId() != null) {
            scrmBusinessOpportunityPO.setLostStageId(scrmOpportunityRequest.getLostStageId());
        }
        if (scrmOpportunityRequest.getWinRate() != null) {
            scrmBusinessOpportunityPO.setWinRate(scrmOpportunityRequest.getWinRate());
        }
        if (scrmOpportunityRequest.getSourceId() != null) {
            scrmBusinessOpportunityPO.setSourceId(scrmOpportunityRequest.getSourceId());
        }
        if (scrmOpportunityRequest.getProduct() != null) {
            scrmBusinessOpportunityPO.setProduct(scrmOpportunityRequest.getProduct());
        }
        if (scrmOpportunityRequest.getStageUpdatedAt() != null) {
            scrmBusinessOpportunityPO.setStageUpdatedAt(scrmOpportunityRequest.getStageUpdatedAt());
        }
        if (scrmOpportunityRequest.getActualCost() != null) {
            scrmBusinessOpportunityPO.setActualCost(scrmOpportunityRequest.getActualCost());
        }
        if (scrmOpportunityRequest.getCommitmentFlg() != null) {
            scrmBusinessOpportunityPO.setCommitmentFlg(scrmOpportunityRequest.getCommitmentFlg());
        }
        if (scrmOpportunityRequest.getStatus() != null) {
            scrmBusinessOpportunityPO.setStatus(scrmOpportunityRequest.getStatus());
        }
        if (scrmOpportunityRequest.getRecentActivityRecordTime() != null) {
            scrmBusinessOpportunityPO.setRecentActivityRecordTime(scrmOpportunityRequest.getRecentActivityRecordTime());
        }
        if (scrmOpportunityRequest.getCreatedBy() != null) {
            scrmBusinessOpportunityPO.setCreatedBy(scrmOpportunityRequest.getCreatedBy());
        }
        if (scrmOpportunityRequest.getUpdatedBy() != null) {
            scrmBusinessOpportunityPO.setUpdatedBy(scrmOpportunityRequest.getUpdatedBy());
        }
        if (scrmOpportunityRequest.getComment() != null) {
            scrmBusinessOpportunityPO.setComment(scrmOpportunityRequest.getComment());
        }
        if (scrmOpportunityRequest.getDimDepart() != null) {
            scrmBusinessOpportunityPO.setDimDepart(scrmOpportunityRequest.getDimDepart());
        }
        if (scrmOpportunityRequest.getTerritoryId() != null) {
            scrmBusinessOpportunityPO.setTerritoryId(scrmOpportunityRequest.getTerritoryId());
        }
        if (scrmOpportunityRequest.getLockStatus() != null) {
            scrmBusinessOpportunityPO.setLockStatus(scrmOpportunityRequest.getLockStatus());
        }
        if (scrmOpportunityRequest.getCampaignId() != null) {
            scrmBusinessOpportunityPO.setCampaignId(scrmOpportunityRequest.getCampaignId());
        }
        if (scrmOpportunityRequest.getApprovalStatus() != null) {
            scrmBusinessOpportunityPO.setApprovalStatus(scrmOpportunityRequest.getApprovalStatus());
        }
        if (scrmOpportunityRequest.getLeadId() != null) {
            scrmBusinessOpportunityPO.setLeadId(scrmOpportunityRequest.getLeadId());
        }
        if (scrmOpportunityRequest.getOpportunityScore() != null) {
            scrmBusinessOpportunityPO.setOpportunityScore(scrmOpportunityRequest.getOpportunityScore());
        }
        if (scrmOpportunityRequest.getTerritoryHighSeaStatus() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaStatus(scrmOpportunityRequest.getTerritoryHighSeaStatus());
        }
        if (scrmOpportunityRequest.getTerritoryExpireTime() != null) {
            scrmBusinessOpportunityPO.setTerritoryExpireTime(scrmOpportunityRequest.getTerritoryExpireTime());
        }
        if (scrmOpportunityRequest.getTerritoryHighSeaId() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaId(scrmOpportunityRequest.getTerritoryHighSeaId());
        }
        if (scrmOpportunityRequest.getFcastMoney() != null) {
            scrmBusinessOpportunityPO.setFcastMoney(scrmOpportunityRequest.getFcastMoney());
        }
        if (scrmOpportunityRequest.getForecastCategory() != null) {
            scrmBusinessOpportunityPO.setForecastCategory(scrmOpportunityRequest.getForecastCategory());
        }
        if (scrmOpportunityRequest.getDuplicateFlg() != null) {
            scrmBusinessOpportunityPO.setDuplicateFlg(scrmOpportunityRequest.getDuplicateFlg());
        }
        if (scrmOpportunityRequest.getWinReason() != null) {
            scrmBusinessOpportunityPO.setWinReason(scrmOpportunityRequest.getWinReason());
        }
        if (scrmOpportunityRequest.getWinReasonDesc() != null) {
            scrmBusinessOpportunityPO.setWinReasonDesc(scrmOpportunityRequest.getWinReasonDesc());
        }
        if (scrmOpportunityRequest.getApplicantId() != null) {
            scrmBusinessOpportunityPO.setApplicantId(scrmOpportunityRequest.getApplicantId());
        }
        if (scrmOpportunityRequest.getProjectBudget() != null) {
            scrmBusinessOpportunityPO.setProjectBudget(scrmOpportunityRequest.getProjectBudget());
        }
        if (scrmOpportunityRequest.getPriceId() != null) {
            scrmBusinessOpportunityPO.setPriceId(scrmOpportunityRequest.getPriceId());
        }
        if (scrmOpportunityRequest.getCustomItem162__c() != null) {
            scrmBusinessOpportunityPO.setReasonReturn(scrmOpportunityRequest.getCustomItem162__c());
        }
        if (scrmOpportunityRequest.getCustomItem163__c() != null) {
            scrmBusinessOpportunityPO.setReasonComment(scrmOpportunityRequest.getCustomItem163__c());
        }
        if (scrmOpportunityRequest.getCustomItem164__c() != null) {
            scrmBusinessOpportunityPO.setDelayReason(scrmOpportunityRequest.getCustomItem164__c());
        }
        if (scrmOpportunityRequest.getCustomItem165__c() != null) {
            scrmBusinessOpportunityPO.setDelayComment(scrmOpportunityRequest.getCustomItem165__c());
        }
        if (scrmOpportunityRequest.getCustomItem168__c() != null) {
            scrmBusinessOpportunityPO.setSignInInformation(scrmOpportunityRequest.getCustomItem168__c());
        }
        if (scrmOpportunityRequest.getCustomItem171__c() != null) {
            scrmBusinessOpportunityPO.setMonthOrder(scrmOpportunityRequest.getCustomItem171__c());
        }
        if (scrmOpportunityRequest.getCustomItem172__c() != null) {
            scrmBusinessOpportunityPO.setNonMonthOrder(scrmOpportunityRequest.getCustomItem172__c());
        }
        if (scrmOpportunityRequest.getCustomItem173__c() != null) {
            scrmBusinessOpportunityPO.setAllocateTime(new Date(scrmOpportunityRequest.getCustomItem173__c()));
        }
        if (scrmOpportunityRequest.getCustomItem174__c() != null) {
            scrmBusinessOpportunityPO.setContractedStore(scrmOpportunityRequest.getCustomItem174__c());
        }
        if (scrmOpportunityRequest.getCustomItem175__c() != null) {
            scrmBusinessOpportunityPO.setOwnedStore(scrmOpportunityRequest.getCustomItem175__c());
        }
        if (scrmOpportunityRequest.getIntendedProvince__c() != null) {
            scrmBusinessOpportunityPO.setIntendedProvince(scrmOpportunityRequest.getIntendedProvince__c());
        }
        if (scrmOpportunityRequest.getIntendedCity__c() != null) {
            scrmBusinessOpportunityPO.setIntendedCity(scrmOpportunityRequest.getIntendedCity__c());
        }
        if (scrmOpportunityRequest.getIntendedArea__c() != null) {
            scrmBusinessOpportunityPO.setIntendedArea(scrmOpportunityRequest.getIntendedArea__c());
        }
        if (scrmOpportunityRequest.getIntendedStore__c() != null) {
            scrmBusinessOpportunityPO.setIntendedStore(scrmOpportunityRequest.getIntendedStore__c());
        }
        if (scrmOpportunityRequest.getIntendedBrand__c() != null) {
            scrmBusinessOpportunityPO.setIntendedBrand(scrmOpportunityRequest.getIntendedBrand__c());
        }
        if (scrmOpportunityRequest.getCustomerBudget__c() != null) {
            scrmBusinessOpportunityPO.setCustomerBudget(scrmOpportunityRequest.getCustomerBudget__c());
        }
        if (scrmOpportunityRequest.getIntendedCountry__c() != null) {
            scrmBusinessOpportunityPO.setIntendedCountry(scrmOpportunityRequest.getIntendedCountry__c());
        }
        if (scrmOpportunityRequest.getCustomItem186__c() != null) {
            scrmBusinessOpportunityPO.setOldTag(scrmOpportunityRequest.getCustomItem186__c());
        }
        if (scrmOpportunityRequest.getCustomType__c() != null) {
            scrmBusinessOpportunityPO.setCustomType(scrmOpportunityRequest.getCustomType__c());
        }
        if (scrmOpportunityRequest.getReturnVisit24Flag__c() != null) {
            scrmBusinessOpportunityPO.setReturnVisitOneDay(scrmOpportunityRequest.getReturnVisit24Flag__c());
        }
        if (scrmOpportunityRequest.getReturnVisitThreeDayFlag__c() != null) {
            scrmBusinessOpportunityPO.setReturnVisitThreeDay(scrmOpportunityRequest.getReturnVisitThreeDayFlag__c());
        }
        if (scrmOpportunityRequest.getReturnVisitWeekFlag__c() != null) {
            scrmBusinessOpportunityPO.setReturnVisitWeek(scrmOpportunityRequest.getReturnVisitWeekFlag__c());
        }
        if (scrmOpportunityRequest.getCreatedAt() != null) {
            scrmBusinessOpportunityPO.setCreatedAt(scrmOpportunityRequest.getCreatedAt());
        }
        if (scrmOpportunityRequest.getUpdatedAt() != null) {
            scrmBusinessOpportunityPO.setUpdatedAt(scrmOpportunityRequest.getUpdatedAt());
        }
        if (scrmOpportunityRequest.getStageUpdatedAt() != null) {
            scrmBusinessOpportunityPO.setStageUpdatedAt(scrmOpportunityRequest.getStageUpdatedAt());
        }

        return scrmBusinessOpportunityPO;
    }

    /**
     * scrm商机详情转为商机po
     * @param scrmOpportunityDetailVO
     * @return
     */
    default ScrmBusinessOpportunityPO scrmOpportunityDetailVO2ScrmBusinessOpportunityPO(ScrmOpportunityDetailVO scrmOpportunityDetailVO) {
        if (scrmOpportunityDetailVO == null) {
            return null;
        }
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = new ScrmBusinessOpportunityPO();

        if (scrmOpportunityDetailVO.getId() != null) {
            scrmBusinessOpportunityPO.setScrmId(scrmOpportunityDetailVO.getId());
        }
        if (scrmOpportunityDetailVO.getOwnerId() != null) {
            scrmBusinessOpportunityPO.setOwnerId(scrmOpportunityDetailVO.getOwnerId());
        }
        if (scrmOpportunityDetailVO.getEntityType() != null) {
            scrmBusinessOpportunityPO.setEntityType(scrmOpportunityDetailVO.getEntityType());
        }
        if (scrmOpportunityDetailVO.getOpportunityName() != null) {
            scrmBusinessOpportunityPO.setOpportunityName(scrmOpportunityDetailVO.getOpportunityName());
        }
        if (scrmOpportunityDetailVO.getReason() != null) {
            scrmBusinessOpportunityPO.setReason(scrmOpportunityDetailVO.getReason());
        }
        if (scrmOpportunityDetailVO.getReasonDesc() != null) {
            scrmBusinessOpportunityPO.setReasonDesc(scrmOpportunityDetailVO.getReasonDesc());
        }
        if (scrmOpportunityDetailVO.getCloseDate() != null) {
            scrmBusinessOpportunityPO.setCloseDate(new Date(scrmOpportunityDetailVO.getCloseDate()));
        }
        if (scrmOpportunityDetailVO.getAccountId() != null) {
            scrmBusinessOpportunityPO.setCustomerId(scrmOpportunityDetailVO.getAccountId());
        }
        if (scrmOpportunityDetailVO.getOpportunityType() != null) {
            scrmBusinessOpportunityPO.setOpportunityType(scrmOpportunityDetailVO.getOpportunityType());
        }
        if (scrmOpportunityDetailVO.getMoney() != null) {
            scrmBusinessOpportunityPO.setMoney(new BigDecimal(scrmOpportunityDetailVO.getMoney()));
        }
        if (scrmOpportunityDetailVO.getSaleStageId() != null) {
            scrmBusinessOpportunityPO.setSaleStageId(scrmOpportunityDetailVO.getSaleStageId());
        }
        if (scrmOpportunityDetailVO.getLostStageId() != null) {
            scrmBusinessOpportunityPO.setLostStageId(scrmOpportunityDetailVO.getLostStageId());
        }
        if (scrmOpportunityDetailVO.getWinRate() != null) {
            scrmBusinessOpportunityPO.setWinRate(scrmOpportunityDetailVO.getWinRate());
        }
        if (scrmOpportunityDetailVO.getSourceId() != null) {
            scrmBusinessOpportunityPO.setSourceId(scrmOpportunityDetailVO.getSourceId());
        }
        if (scrmOpportunityDetailVO.getProduct() != null) {
            scrmBusinessOpportunityPO.setProduct(scrmOpportunityDetailVO.getProduct());
        }
        if (scrmOpportunityDetailVO.getStageUpdatedAt() != null) {
            scrmBusinessOpportunityPO.setStageUpdatedAt(new Date(scrmOpportunityDetailVO.getStageUpdatedAt()));
        }
        if (scrmOpportunityDetailVO.getActualCost() != null) {
            scrmBusinessOpportunityPO.setActualCost(new BigDecimal(scrmOpportunityDetailVO.getActualCost()));
        }
        if (scrmOpportunityDetailVO.getCommitmentFlg() != null) {
            scrmBusinessOpportunityPO.setCommitmentFlg(scrmOpportunityDetailVO.getCommitmentFlg());
        }
        if (scrmOpportunityDetailVO.getStatus() != null) {
            scrmBusinessOpportunityPO.setStatus(scrmOpportunityDetailVO.getStatus());
        }
        if (scrmOpportunityDetailVO.getRecentActivityRecordTime() != null) {
            scrmBusinessOpportunityPO.setRecentActivityRecordTime(new Date(scrmOpportunityDetailVO.getRecentActivityRecordTime()));
        }
        if (scrmOpportunityDetailVO.getCreatedBy() != null) {
            scrmBusinessOpportunityPO.setCreatedBy(scrmOpportunityDetailVO.getCreatedBy());
        }
        if (scrmOpportunityDetailVO.getUpdatedBy() != null) {
            scrmBusinessOpportunityPO.setUpdatedBy(scrmOpportunityDetailVO.getUpdatedBy());
        }
        if (scrmOpportunityDetailVO.getComment() != null) {
            scrmBusinessOpportunityPO.setComment(scrmOpportunityDetailVO.getComment());
        }
        if (scrmOpportunityDetailVO.getDimDepart() != null) {
            scrmBusinessOpportunityPO.setDimDepart(scrmOpportunityDetailVO.getDimDepart());
        }
        if (scrmOpportunityDetailVO.getTerritoryId() != null) {
            scrmBusinessOpportunityPO.setTerritoryId(scrmOpportunityDetailVO.getTerritoryId());
        }
        if (scrmOpportunityDetailVO.getLockStatus() != null) {
            scrmBusinessOpportunityPO.setLockStatus(scrmOpportunityDetailVO.getLockStatus());
        }
        if (scrmOpportunityDetailVO.getCampaignId() != null) {
            scrmBusinessOpportunityPO.setCampaignId(scrmOpportunityDetailVO.getCampaignId());
        }
        if (scrmOpportunityDetailVO.getApprovalStatus() != null) {
            scrmBusinessOpportunityPO.setApprovalStatus(scrmOpportunityDetailVO.getApprovalStatus());
        }
        if (scrmOpportunityDetailVO.getLeadId() != null) {
            scrmBusinessOpportunityPO.setLeadId(scrmOpportunityDetailVO.getLeadId());
        }
        if (scrmOpportunityDetailVO.getOpportunityScore() != null) {
            scrmBusinessOpportunityPO.setOpportunityScore(new BigDecimal(scrmOpportunityDetailVO.getOpportunityScore()));
        }
        if (scrmOpportunityDetailVO.getTerritoryHighSeaStatus() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaStatus(scrmOpportunityDetailVO.getTerritoryHighSeaStatus());
        }
        if (scrmOpportunityDetailVO.getTerritoryExpireTime() != null) {
            scrmBusinessOpportunityPO.setTerritoryExpireTime(new Date(scrmOpportunityDetailVO.getTerritoryExpireTime()));
        }
        if (scrmOpportunityDetailVO.getTerritoryHighSeaId() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaId(scrmOpportunityDetailVO.getTerritoryHighSeaId());
        }
        if (scrmOpportunityDetailVO.getFcastMoney() != null) {
            scrmBusinessOpportunityPO.setFcastMoney(new BigDecimal(scrmOpportunityDetailVO.getFcastMoney()));
        }
        if (scrmOpportunityDetailVO.getForecastCategory() != null) {
            scrmBusinessOpportunityPO.setForecastCategory(scrmOpportunityDetailVO.getForecastCategory());
        }
        if (scrmOpportunityDetailVO.getDuplicateFlg() != null) {
            scrmBusinessOpportunityPO.setDuplicateFlg(scrmOpportunityDetailVO.getDuplicateFlg());
        }
        if (scrmOpportunityDetailVO.getWinReason() != null) {
            scrmBusinessOpportunityPO.setWinReason(scrmOpportunityDetailVO.getWinReason());
        }
        if (scrmOpportunityDetailVO.getWinReasonDesc() != null) {
            scrmBusinessOpportunityPO.setWinReasonDesc(scrmOpportunityDetailVO.getWinReasonDesc());
        }
        if (scrmOpportunityDetailVO.getApplicantId() != null) {
            scrmBusinessOpportunityPO.setApplicantId(scrmOpportunityDetailVO.getApplicantId());
        }
        if (scrmOpportunityDetailVO.getProjectBudget() != null) {
            scrmBusinessOpportunityPO.setProjectBudget(new BigDecimal(scrmOpportunityDetailVO.getProjectBudget()));
        }
        if (scrmOpportunityDetailVO.getPriceId() != null) {
            scrmBusinessOpportunityPO.setPriceId(scrmOpportunityDetailVO.getPriceId());
        }
        if (scrmOpportunityDetailVO.getCustomItem162__c() != null) {
            scrmBusinessOpportunityPO.setReasonReturn(scrmOpportunityDetailVO.getCustomItem162__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem163__c() != null) {
            scrmBusinessOpportunityPO.setReasonComment(scrmOpportunityDetailVO.getCustomItem163__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem164__c() != null) {
            scrmBusinessOpportunityPO.setDelayReason(scrmOpportunityDetailVO.getCustomItem164__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem165__c() != null) {
            scrmBusinessOpportunityPO.setDelayComment(scrmOpportunityDetailVO.getCustomItem165__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem173__c() != null) {
            scrmBusinessOpportunityPO.setAllocateTime(new Date(scrmOpportunityDetailVO.getCustomItem173__c()));
        }
        if (scrmOpportunityDetailVO.getCustomItem168__c() != null) {
            scrmBusinessOpportunityPO.setSignInInformation(scrmOpportunityDetailVO.getCustomItem168__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem169__c() != null) {
            scrmBusinessOpportunityPO.setConsultationTime(new Date(scrmOpportunityDetailVO.getCustomItem169__c()));
        }
        if (scrmOpportunityDetailVO.getCustomItem170__c() != null) {
            scrmBusinessOpportunityPO.setArrivalTime(scrmOpportunityDetailVO.getCustomItem170__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem171__c() != null) {
            scrmBusinessOpportunityPO.setMonthOrder(scrmOpportunityDetailVO.getCustomItem171__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem172__c() != null) {
            scrmBusinessOpportunityPO.setNonMonthOrder(scrmOpportunityDetailVO.getCustomItem172__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem174__c() != null) {
            scrmBusinessOpportunityPO.setContractedStore(scrmOpportunityDetailVO.getCustomItem174__c());
        }
        if (scrmOpportunityDetailVO.getIntendedProvince__c() != null) {
            scrmBusinessOpportunityPO.setIntendedProvince(scrmOpportunityDetailVO.getIntendedProvince__c());
        }
        if (scrmOpportunityDetailVO.getIntendedCity__c() != null) {
            scrmBusinessOpportunityPO.setIntendedCity(scrmOpportunityDetailVO.getIntendedCity__c());
        }
        if (scrmOpportunityDetailVO.getIntendedArea__c() != null) {
            scrmBusinessOpportunityPO.setIntendedArea(scrmOpportunityDetailVO.getIntendedArea__c());
        }
        if (scrmOpportunityDetailVO.getIntendedStore__c() != null) {
            scrmBusinessOpportunityPO.setIntendedStore(scrmOpportunityDetailVO.getIntendedStore__c());
        }
        if (scrmOpportunityDetailVO.getIntendedBrand__c() != null) {
            scrmBusinessOpportunityPO.setIntendedBrand(scrmOpportunityDetailVO.getIntendedBrand__c());
        }
        if (scrmOpportunityDetailVO.getCustomerBudget__c() != null) {
            scrmBusinessOpportunityPO.setCustomerBudget(scrmOpportunityDetailVO.getCustomerBudget__c());
        }
        if (scrmOpportunityDetailVO.getIntendedCountry__c() != null) {
            scrmBusinessOpportunityPO.setIntendedCountry(scrmOpportunityDetailVO.getIntendedCountry__c());
        }
        if (scrmOpportunityDetailVO.getCustomItem186__c() != null) {
            scrmBusinessOpportunityPO.setOldTag(scrmOpportunityDetailVO.getCustomItem186__c());
        }
        return scrmBusinessOpportunityPO;
    }

    /**
     * scrm商机详情转为scrm商机请求
     * @param scrmOpportunityDetailVO
     * @return
     */
    SCRMOpportunityRequest scrmOpportunityDetailVO2SCRMOpportunityRequest(ScrmOpportunityDetailVO scrmOpportunityDetailVO);

    /**
     * scrm联系人转为宝宝PO
     * @param babys
     * @return
     */
    List<ScrmCustomerBabyPO> babyDTO2ScrmCustomerBabyPOList(List<ScrmCustomerBirthDTO.BabyDTO> babys);


    @Mappings(value = {
            @Mapping(target = "scrmId", source = "id")
    })
    ScrmOpportunityActivityRecordPO scrmOpportunityActivityRecordRequest2ScrmBusinessOpportunityPO(ScrmOpportunityActivityRecordRequest scrmOpportunityActivityRecordRequest);

    /**
     * 商机po转为scrm商机请求
     * @param scrmBusinessOpportunityPO
     * @return
     */
    default OpportunityRequest scrmBusinessOpportunityPO2OpportunityRequest(ScrmBusinessOpportunityPO scrmBusinessOpportunityPO) {
        if (scrmBusinessOpportunityPO == null) {
            return null;
        }
        OpportunityRequest opportunityRequest = new OpportunityRequest();

        if (scrmBusinessOpportunityPO.getOpportunityScore() != null) {
            opportunityRequest.setOpportunityScore(scrmBusinessOpportunityPO.getOpportunityScore().doubleValue());
        }
        if (scrmBusinessOpportunityPO.getOwnerId() != null) {
            opportunityRequest.setOwnerId(scrmBusinessOpportunityPO.getOwnerId());
        }
        if (scrmBusinessOpportunityPO.getFcastMoney() != null) {
            opportunityRequest.setFcastMoney(scrmBusinessOpportunityPO.getFcastMoney().doubleValue());
        }
        if (scrmBusinessOpportunityPO.getScrmId() != null) {
            opportunityRequest.setId(scrmBusinessOpportunityPO.getScrmId());
        }
        if (scrmBusinessOpportunityPO.getOpportunityName() != null) {
            opportunityRequest.setOpportunityName(scrmBusinessOpportunityPO.getOpportunityName());
        }
        if (scrmBusinessOpportunityPO.getLockStatus() != null) {
            opportunityRequest.setLockStatus(scrmBusinessOpportunityPO.getLockStatus());
        }
        if (scrmBusinessOpportunityPO.getTerritoryHighSeaId() != null) {
            opportunityRequest.setTerritoryHighSeaId(scrmBusinessOpportunityPO.getTerritoryHighSeaId());
        }
        if (scrmBusinessOpportunityPO.getUpdatedBy() != null) {
            opportunityRequest.setUpdatedBy(scrmBusinessOpportunityPO.getUpdatedBy());
        }
        if (scrmBusinessOpportunityPO.getMoney() != null) {
            opportunityRequest.setMoney(scrmBusinessOpportunityPO.getMoney().doubleValue());
        }
        if (scrmBusinessOpportunityPO.getTerritoryHighSeaStatus() != null) {
            opportunityRequest.setTerritoryHighSeaStatus(scrmBusinessOpportunityPO.getTerritoryHighSeaStatus());
        }
        if (scrmBusinessOpportunityPO.getEntityType() != null) {
            opportunityRequest.setEntityType(scrmBusinessOpportunityPO.getEntityType());
        }
        if (scrmBusinessOpportunityPO.getDuplicateFlg() != null) {
            opportunityRequest.setDuplicateFlg(scrmBusinessOpportunityPO.getDuplicateFlg().shortValue());
        }
        if (scrmBusinessOpportunityPO.getSaleStageId() != null) {
            opportunityRequest.setSaleStageId(scrmBusinessOpportunityPO.getSaleStageId());
        }
        if (scrmBusinessOpportunityPO.getApprovalStatus() != null) {
            opportunityRequest.setApprovalStatus(scrmBusinessOpportunityPO.getApprovalStatus());
        }
        if (scrmBusinessOpportunityPO.getCreatedAt() != null) {
            opportunityRequest.setCreatedAt(scrmBusinessOpportunityPO.getCreatedAt().getTime());
        }
        if (scrmBusinessOpportunityPO.getDimDepart() != null) {
            opportunityRequest.setDimDepart(scrmBusinessOpportunityPO.getDimDepart());
        }
        if (scrmBusinessOpportunityPO.getWinRate() != null) {
            opportunityRequest.setWinRate(scrmBusinessOpportunityPO.getWinRate());
        }
        if (scrmBusinessOpportunityPO.getPriceId() != null) {
            opportunityRequest.setPriceId(scrmBusinessOpportunityPO.getPriceId());
        }
        if (scrmBusinessOpportunityPO.getUpdatedAt() != null) {
            opportunityRequest.setUpdatedAt(scrmBusinessOpportunityPO.getUpdatedAt().getTime());
        }
        if (scrmBusinessOpportunityPO.getCommitmentFlg() != null) {
            opportunityRequest.setCommitmentFlg(scrmBusinessOpportunityPO.getCommitmentFlg());
        }
        if (scrmBusinessOpportunityPO.getCreatedBy() != null) {
            opportunityRequest.setCreatedBy(scrmBusinessOpportunityPO.getCreatedBy());
        }
        if (scrmBusinessOpportunityPO.getStatus() != null) {
            opportunityRequest.setStatus(scrmBusinessOpportunityPO.getStatus());
        }
        if (scrmBusinessOpportunityPO.getCustomerId() != null) {
            opportunityRequest.setAccountId(scrmBusinessOpportunityPO.getCustomerId());
        }
        if (scrmBusinessOpportunityPO.getCloseDate() != null) {
            opportunityRequest.setCloseDate(scrmBusinessOpportunityPO.getCloseDate().getTime());
        }
        if (scrmBusinessOpportunityPO.getOpportunityType() != null) {
            opportunityRequest.setOpportunityType(scrmBusinessOpportunityPO.getOpportunityType());
        }
        if (scrmBusinessOpportunityPO.getLostStageId() != null) {
            opportunityRequest.setLostStageId(scrmBusinessOpportunityPO.getLostStageId());
        }
        if (scrmBusinessOpportunityPO.getReason() != null) {
            opportunityRequest.setReason(scrmBusinessOpportunityPO.getReason());
        }
        if (scrmBusinessOpportunityPO.getReasonDesc() != null) {
            opportunityRequest.setReasonDesc(scrmBusinessOpportunityPO.getReasonDesc());
        }
        if (scrmBusinessOpportunityPO.getSourceId() != null) {
            opportunityRequest.setSourceId(scrmBusinessOpportunityPO.getSourceId());
        }
        if (scrmBusinessOpportunityPO.getProjectBudget() != null) {
            opportunityRequest.setProjectBudget(scrmBusinessOpportunityPO.getProjectBudget().doubleValue());
        }
        if (scrmBusinessOpportunityPO.getActualCost() != null) {
            opportunityRequest.setActualCost(scrmBusinessOpportunityPO.getActualCost().doubleValue());
        }
        if (scrmBusinessOpportunityPO.getProduct() != null) {
            opportunityRequest.setProduct(scrmBusinessOpportunityPO.getProduct());
        }
        if (scrmBusinessOpportunityPO.getStageUpdatedAt() != null) {
            opportunityRequest.setStageUpdatedAt(scrmBusinessOpportunityPO.getStageUpdatedAt().getTime());
        }
        if (scrmBusinessOpportunityPO.getRecentActivityRecordTime() != null) {
            opportunityRequest.setRecentActivityRecordTime(scrmBusinessOpportunityPO.getRecentActivityRecordTime().getTime());
        }
        if (scrmBusinessOpportunityPO.getComment() != null) {
            opportunityRequest.setComment(scrmBusinessOpportunityPO.getComment());
        }
        if (scrmBusinessOpportunityPO.getTerritoryId() != null) {
            opportunityRequest.setTerritoryId(scrmBusinessOpportunityPO.getTerritoryId());
        }
        if (scrmBusinessOpportunityPO.getCampaignId() != null) {
            opportunityRequest.setCampaignId(scrmBusinessOpportunityPO.getCampaignId());
        }
        if (scrmBusinessOpportunityPO.getLeadId() != null) {
            opportunityRequest.setLeadId(scrmBusinessOpportunityPO.getLeadId());
        }
        if (scrmBusinessOpportunityPO.getTerritoryExpireTime() != null) {
            opportunityRequest.setTerritoryExpireTime(scrmBusinessOpportunityPO.getTerritoryExpireTime().getTime());
        }
        if (scrmBusinessOpportunityPO.getForecastCategory() != null) {
            opportunityRequest.setForecastCategory(scrmBusinessOpportunityPO.getForecastCategory());
        }
        if (scrmBusinessOpportunityPO.getWinReason() != null) {
            opportunityRequest.setWinReason(scrmBusinessOpportunityPO.getWinReason());
        }
        if (scrmBusinessOpportunityPO.getWinReasonDesc() != null) {
            opportunityRequest.setWinReasonDesc(scrmBusinessOpportunityPO.getWinReasonDesc());
        }
        if (scrmBusinessOpportunityPO.getApplicantId() != null) {
            opportunityRequest.setApplicantId(scrmBusinessOpportunityPO.getApplicantId());
        }
        if (scrmBusinessOpportunityPO.getReasonReturn() != null) {
            opportunityRequest.setCustomItem162__c(scrmBusinessOpportunityPO.getReasonReturn());
        }
        if (scrmBusinessOpportunityPO.getReasonComment() != null) {
            opportunityRequest.setCustomItem163__c(scrmBusinessOpportunityPO.getReasonComment());
        }
        if (scrmBusinessOpportunityPO.getDelayReason() != null) {
            opportunityRequest.setCustomItem164__c(scrmBusinessOpportunityPO.getDelayReason());
        }
        if (scrmBusinessOpportunityPO.getDelayComment() != null) {
            opportunityRequest.setCustomItem165__c(scrmBusinessOpportunityPO.getDelayComment());
        }
        if (scrmBusinessOpportunityPO.getAllocateTime() != null) {
            opportunityRequest.setCustomItem173__c(scrmBusinessOpportunityPO.getAllocateTime().getTime());
        }
        if (scrmBusinessOpportunityPO.getSignInInformation() != null) {
            opportunityRequest.setCustomItem168__c(scrmBusinessOpportunityPO.getSignInInformation());
        }
        if (scrmBusinessOpportunityPO.getConsultationTime() != null) {
            opportunityRequest.setCustomItem169__c(scrmBusinessOpportunityPO.getConsultationTime().getTime());
        }
        if (scrmBusinessOpportunityPO.getArrivalTime() != null) {
            opportunityRequest.setCustomItem170__c(scrmBusinessOpportunityPO.getArrivalTime());
        }
        if (scrmBusinessOpportunityPO.getMonthOrder() != null) {
            opportunityRequest.setCustomItem171__c(scrmBusinessOpportunityPO.getMonthOrder());
        }
        if (scrmBusinessOpportunityPO.getNonMonthOrder() != null) {
            opportunityRequest.setCustomItem172__c(scrmBusinessOpportunityPO.getNonMonthOrder());
        }
        if (scrmBusinessOpportunityPO.getContractedStore() != null) {
            opportunityRequest.setCustomItem174__c(scrmBusinessOpportunityPO.getContractedStore());
        }
        if (scrmBusinessOpportunityPO.getIntendedProvince() != null) {
            opportunityRequest.setIntendedProvince__c(scrmBusinessOpportunityPO.getIntendedProvince());
        }
        if (scrmBusinessOpportunityPO.getIntendedCity() != null) {
            opportunityRequest.setIntendedCity__c(scrmBusinessOpportunityPO.getIntendedCity());
        }
        if (scrmBusinessOpportunityPO.getIntendedArea() != null) {
            opportunityRequest.setIntendedArea__c(scrmBusinessOpportunityPO.getIntendedArea());
        }
        if (scrmBusinessOpportunityPO.getIntendedBrand() != null) {
            opportunityRequest.setIntendedBrand__c(scrmBusinessOpportunityPO.getIntendedBrand());
        }
        if (scrmBusinessOpportunityPO.getIntendedStore() != null) {
            opportunityRequest.setIntendedStore__c(scrmBusinessOpportunityPO.getIntendedStore());
        }
        if (scrmBusinessOpportunityPO.getCustomerBudget() != null) {
            opportunityRequest.setCustomerBudget__c(scrmBusinessOpportunityPO.getCustomerBudget());
        }
        if (scrmBusinessOpportunityPO.getIntendedCountry() != null) {
            opportunityRequest.setIntendedCountry__c(scrmBusinessOpportunityPO.getIntendedCountry());
        }
        if (scrmBusinessOpportunityPO.getOldTag() != null) {
            opportunityRequest.setCustomItem186__c(scrmBusinessOpportunityPO.getOldTag());
        }
        if (scrmBusinessOpportunityPO.getCustomType() != null) {
            opportunityRequest.setCustomType__c(scrmBusinessOpportunityPO.getCustomType());
        }

        return opportunityRequest;
    }

    /**
     * 房态信息转为scrm客户po
     * @param scrmCustomerBirthDTO
     * @return
     */
    ScrmCustomerPO scrmCustomerRoomDTO2ScrmCustomerPO(ScrmCustomerBirthDTO scrmCustomerBirthDTO);


    /**
     * ScrmCustomerOrderDTO ->  ScrmCustomerOrderPO
     *
     * @param scrmCustomerOrderDTO
     * @return
     */
    ScrmCustomerOrderPO scrmCustomerOrderDTO2PO(ScrmCustomerOrderDTO scrmCustomerOrderDTO);


    /**
     * ScrmCustomerOrderPO -> OrderInfoRequest
     *
     * @param scrmCustomerOrderPO
     * @return
     */
    default OrderInfoRequest scrmCustomerOrderPO2OrderInfoRequest(ScrmCustomerOrderPO scrmCustomerOrderPO) {
        if (ObjectUtil.isEmpty(scrmCustomerOrderPO)) {
            return null;
        }

        OrderInfoRequest orderInfoRequest = new OrderInfoRequest();

        //订单编号
        orderInfoRequest.setId(scrmCustomerOrderPO.getScrmId());
        orderInfoRequest.setName(scrmCustomerOrderPO.getOrderSn());
        orderInfoRequest.setOwnerId(scrmCustomerOrderPO.getScrmOwnerId());
        orderInfoRequest.setDimDepart(scrmCustomerOrderPO.getScrmDimDepart());
        orderInfoRequest.setCustomItem2__c(scrmCustomerOrderPO.getScrmCustomerId());
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getOrderAmount())) {
            orderInfoRequest.setCustomItem12__c(scrmCustomerOrderPO.getOrderAmount().doubleValue());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getPayAmount())) {
            orderInfoRequest.setCustomItem4__c(scrmCustomerOrderPO.getPayAmount().toString());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getPaidAmount())) {
            orderInfoRequest.setCustomItem5__c(scrmCustomerOrderPO.getPaidAmount().toString());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRefundAmount())) {
            orderInfoRequest.setCustomItem6__c(scrmCustomerOrderPO.getRefundAmount().toString());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getGoodsAmount())) {
            orderInfoRequest.setCustomItem19__c(scrmCustomerOrderPO.getGoodsAmount().toString());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getGoodsDays())) {
            orderInfoRequest.setCustomItem7__c(scrmCustomerOrderPO.getGoodsDays().longValue());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getNetMargin())) {
            orderInfoRequest.setCustomItem8__c(scrmCustomerOrderPO.getNetMargin().doubleValue());
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getDiscountMargin())) {
            orderInfoRequest.setCustomItem20__c(scrmCustomerOrderPO.getDiscountMargin().doubleValue());
        }

        orderInfoRequest.setCustomItem9__c(scrmCustomerOrderPO.getExtraGift());
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRemark())) {
            orderInfoRequest.setCustomItem10__c(EmojiFilter.filterEmoji(scrmCustomerOrderPO.getRemark()));
        }

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getFirstPayTime())) {
            orderInfoRequest.setCustomItem11__c(scrmCustomerOrderPO.getFirstPayTime().getTime());
        }
        orderInfoRequest.setCustomItem13__c(scrmCustomerOrderPO.getOrderH5());
        orderInfoRequest.setCustomItem14__c(scrmCustomerOrderPO.getGoodsName());

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getPercentFirstTime())) {
            orderInfoRequest.setCustomItem15__c(scrmCustomerOrderPO.getPercentFirstTime().getTime());
        }
        //待定
//        orderInfoRequest.setCustomItem16__c(scrmCustomerOrderPO.getPercentFirstTime().getTime());
        orderInfoRequest.setCustomItem18__c(scrmCustomerOrderPO.getProductionName());
        //是否已关联销售机会
        orderInfoRequest.setCustomItem16__c(scrmCustomerOrderPO.getSaleChange());

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getGmtCreate())) {
            orderInfoRequest.setCreatedAt(scrmCustomerOrderPO.getGmtCreate().getTime());
        }
        orderInfoRequest.setCreatedBy(scrmCustomerOrderPO.getCreateBy());

        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getGmtModified())) {
            orderInfoRequest.setUpdatedAt(scrmCustomerOrderPO.getGmtModified().getTime());
        }
        orderInfoRequest.setUpdatedBy(scrmCustomerOrderPO.getUpdateBy());
        orderInfoRequest.setLockStatus(2);
        orderInfoRequest.setOrderType(scrmCustomerOrderPO.getOrderType());

        //签约门店id
        orderInfoRequest.setCustomItem23__c(scrmCustomerOrderPO.getUserStoreRecordId());

        //产康订单抵扣产康金
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getProductionAmountPay())) {
            orderInfoRequest.setCustomItem28__c(scrmCustomerOrderPO.getProductionAmountPay().toString());
        }

        //业绩是否删除
        if(ObjectUtil.isEmpty(scrmCustomerOrderPO.getPerformanceStatus())){
            orderInfoRequest.setPerformanceStatus__c(0);
        } else {
            orderInfoRequest.setPerformanceStatus__c(scrmCustomerOrderPO.getPerformanceStatus());
        }

        // 毛利率
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getGrossMargin())) {
            orderInfoRequest.setGrossMargin__c(scrmCustomerOrderPO.getGrossMargin().doubleValue());
        }

        // 剩余应收金额
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRemainingAmount())) {
            orderInfoRequest.setRemainingAmount__c(scrmCustomerOrderPO.getRemainingAmount().doubleValue());
        }
        // 审阅中金额
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getReviewAmount())) {
            orderInfoRequest.setReviewAmount__c(scrmCustomerOrderPO.getReviewAmount().doubleValue());
        }
        // 实际已付金额
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRealAmount())) {
            orderInfoRequest.setRealAmount__c(scrmCustomerOrderPO.getRealAmount().doubleValue());
        }
        // 退款金额
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRefundingAmount())) {
            orderInfoRequest.setRefundingAmount__c(scrmCustomerOrderPO.getRefundingAmount().doubleValue());
        }
        // 订单状态
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getOrderStatus())) {
            orderInfoRequest.setOrderStatus__c(scrmCustomerOrderPO.getOrderStatus());
        }
        // 退款状态
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getRefundStatus())) {
            orderInfoRequest.setRefundStatus__c(scrmCustomerOrderPO.getRefundStatus());
        }
        // 是否包含月子护理服务, 1-是 2-否
        if (ObjectUtil.isNotEmpty(scrmCustomerOrderPO.getMonthOrder())) {
            orderInfoRequest.setMonthOrder__c(scrmCustomerOrderPO.getMonthOrder());
        }

        return orderInfoRequest;
    }

    /**
     * 原初始化数据需要，已停止维护
     * @param scrmOpportunityRequests
     * @return
     */
    @Deprecated
    default List<ScrmBusinessOpportunityPO> scrmOpportunityRequestList2ScrmBusinessOpportunityPO(List<BatchOpportunityRequest> scrmOpportunityRequests){
        List<ScrmBusinessOpportunityPO> result = new ArrayList<>();
        for (BatchOpportunityRequest scrmOpportunityRequest : scrmOpportunityRequests) {
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = batchOpportunityRequest2ScrmBusinessOpportunityPO(scrmOpportunityRequest);
            result.add(scrmBusinessOpportunityPO);
        }
        return result;
    }

    /**
     * 原初始化数据需要，已停止维护
     * @param scrmOpportunityRequest
     * @return
     */
    @Deprecated
    default ScrmBusinessOpportunityPO batchOpportunityRequest2ScrmBusinessOpportunityPO(BatchOpportunityRequest scrmOpportunityRequest){
        if (scrmOpportunityRequest == null) {
            return null;
        }
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = new ScrmBusinessOpportunityPO();

        if (scrmOpportunityRequest.getId() != null) {
            scrmBusinessOpportunityPO.setScrmId(scrmOpportunityRequest.getId());
        }
        if (scrmOpportunityRequest.getOwnerId() != null) {
            scrmBusinessOpportunityPO.setOwnerId(scrmOpportunityRequest.getOwnerId());
        }
        if (scrmOpportunityRequest.getEntityType() != null) {
            scrmBusinessOpportunityPO.setEntityType(scrmOpportunityRequest.getEntityType());
        }
        if (scrmOpportunityRequest.getOpportunityName() != null) {
            scrmBusinessOpportunityPO.setOpportunityName(scrmOpportunityRequest.getOpportunityName());
        }
        if (scrmOpportunityRequest.getReason() != null) {
            scrmBusinessOpportunityPO.setReason(scrmOpportunityRequest.getReason());
        }
        if (scrmOpportunityRequest.getReasonDesc() != null) {
            scrmBusinessOpportunityPO.setReasonDesc(scrmOpportunityRequest.getReasonDesc());
        }
        if (scrmOpportunityRequest.getCloseDate() != null) {
            scrmBusinessOpportunityPO.setCloseDate(scrmOpportunityRequest.getCloseDate());
        }
        if (scrmOpportunityRequest.getAccountId() != null) {
            scrmBusinessOpportunityPO.setCustomerId(scrmOpportunityRequest.getAccountId());
        }
        if (scrmOpportunityRequest.getOpportunityType() != null) {
            scrmBusinessOpportunityPO.setOpportunityType(scrmOpportunityRequest.getOpportunityType());
        }
        if (scrmOpportunityRequest.getMoney() != null) {
            scrmBusinessOpportunityPO.setMoney(scrmOpportunityRequest.getMoney());
        }
        if (scrmOpportunityRequest.getSaleStageId() != null) {
            scrmBusinessOpportunityPO.setSaleStageId(scrmOpportunityRequest.getSaleStageId());
        }
        if (scrmOpportunityRequest.getLostStageId() != null) {
            scrmBusinessOpportunityPO.setLostStageId(scrmOpportunityRequest.getLostStageId());
        }
        if (scrmOpportunityRequest.getWinRate() != null) {
            scrmBusinessOpportunityPO.setWinRate(scrmOpportunityRequest.getWinRate());
        }
        if (scrmOpportunityRequest.getSourceId() != null) {
            scrmBusinessOpportunityPO.setSourceId(scrmOpportunityRequest.getSourceId());
        }
        if (scrmOpportunityRequest.getProduct() != null) {
            scrmBusinessOpportunityPO.setProduct(scrmOpportunityRequest.getProduct());
        }
        if (scrmOpportunityRequest.getStageUpdatedAt() != null) {
            scrmBusinessOpportunityPO.setStageUpdatedAt(scrmOpportunityRequest.getStageUpdatedAt());
        }
        if (scrmOpportunityRequest.getActualCost() != null) {
            scrmBusinessOpportunityPO.setActualCost(scrmOpportunityRequest.getActualCost());
        }
        if (scrmOpportunityRequest.getCommitmentFlg() != null) {
            scrmBusinessOpportunityPO.setCommitmentFlg(scrmOpportunityRequest.getCommitmentFlg());
        }
        if (scrmOpportunityRequest.getStatus() != null) {
            scrmBusinessOpportunityPO.setStatus(scrmOpportunityRequest.getStatus());
        }
        if (scrmOpportunityRequest.getRecentActivityRecordTime() != null) {
            scrmBusinessOpportunityPO.setRecentActivityRecordTime(scrmOpportunityRequest.getRecentActivityRecordTime());
        }
        if (scrmOpportunityRequest.getCreatedBy() != null) {
            scrmBusinessOpportunityPO.setCreatedBy(scrmOpportunityRequest.getCreatedBy());
        }
        if (scrmOpportunityRequest.getUpdatedBy() != null) {
            scrmBusinessOpportunityPO.setUpdatedBy(scrmOpportunityRequest.getUpdatedBy());
        }
        if (scrmOpportunityRequest.getComment() != null) {
            scrmBusinessOpportunityPO.setComment(scrmOpportunityRequest.getComment());
        }
        if (scrmOpportunityRequest.getDimDepart() != null) {
            scrmBusinessOpportunityPO.setDimDepart(scrmOpportunityRequest.getDimDepart());
        }
        if (scrmOpportunityRequest.getTerritoryId() != null) {
            scrmBusinessOpportunityPO.setTerritoryId(scrmOpportunityRequest.getTerritoryId());
        }
        if (scrmOpportunityRequest.getLockStatus() != null) {
            scrmBusinessOpportunityPO.setLockStatus(scrmOpportunityRequest.getLockStatus());
        }
        if (scrmOpportunityRequest.getCampaignId() != null) {
            scrmBusinessOpportunityPO.setCampaignId(scrmOpportunityRequest.getCampaignId());
        }
        if (scrmOpportunityRequest.getApprovalStatus() != null) {
            scrmBusinessOpportunityPO.setApprovalStatus(scrmOpportunityRequest.getApprovalStatus());
        }
        if (scrmOpportunityRequest.getLeadId() != null) {
            scrmBusinessOpportunityPO.setLeadId(scrmOpportunityRequest.getLeadId());
        }
        if (scrmOpportunityRequest.getOpportunityScore() != null) {
            scrmBusinessOpportunityPO.setOpportunityScore(scrmOpportunityRequest.getOpportunityScore());
        }
        if (scrmOpportunityRequest.getTerritoryHighSeaStatus() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaStatus(scrmOpportunityRequest.getTerritoryHighSeaStatus());
        }
        if (scrmOpportunityRequest.getTerritoryExpireTime() != null) {
            scrmBusinessOpportunityPO.setTerritoryExpireTime(scrmOpportunityRequest.getTerritoryExpireTime());
        }
        if (scrmOpportunityRequest.getTerritoryHighSeaId() != null) {
            scrmBusinessOpportunityPO.setTerritoryHighSeaId(scrmOpportunityRequest.getTerritoryHighSeaId());
        }
        if (scrmOpportunityRequest.getFcastMoney() != null) {
            scrmBusinessOpportunityPO.setFcastMoney(scrmOpportunityRequest.getFcastMoney());
        }
        if (scrmOpportunityRequest.getForecastCategory() != null) {
            scrmBusinessOpportunityPO.setForecastCategory(scrmOpportunityRequest.getForecastCategory());
        }
//        if (scrmOpportunityRequest.getDuplicateFlg() != null) {
//            scrmBusinessOpportunityPO.setDuplicateFlg(scrmOpportunityRequest.getDuplicateFlg());
//        }
        if (scrmOpportunityRequest.getWinReason() != null) {
            scrmBusinessOpportunityPO.setWinReason(scrmOpportunityRequest.getWinReason());
        }
        if (scrmOpportunityRequest.getWinReasonDesc() != null) {
            scrmBusinessOpportunityPO.setWinReasonDesc(scrmOpportunityRequest.getWinReasonDesc());
        }
        if (scrmOpportunityRequest.getApplicantId() != null) {
            scrmBusinessOpportunityPO.setApplicantId(scrmOpportunityRequest.getApplicantId());
        }
        if (scrmOpportunityRequest.getProjectBudget() != null) {
            scrmBusinessOpportunityPO.setProjectBudget(scrmOpportunityRequest.getProjectBudget());
        }
        if (scrmOpportunityRequest.getPriceId() != null) {
            scrmBusinessOpportunityPO.setPriceId(scrmOpportunityRequest.getPriceId());
        }
        if (scrmOpportunityRequest.getCustomItem162__c() != null) {
            scrmBusinessOpportunityPO.setReasonReturn(scrmOpportunityRequest.getCustomItem162__c());
        }
        if (scrmOpportunityRequest.getCustomItem163__c() != null) {
            scrmBusinessOpportunityPO.setReasonComment(scrmOpportunityRequest.getCustomItem163__c());
        }
        if (scrmOpportunityRequest.getCustomItem164__c() != null) {
            scrmBusinessOpportunityPO.setDelayReason(scrmOpportunityRequest.getCustomItem164__c());
        }
        if (scrmOpportunityRequest.getCustomItem165__c() != null) {
            scrmBusinessOpportunityPO.setDelayComment(scrmOpportunityRequest.getCustomItem165__c());
        }
        if (scrmOpportunityRequest.getCustomItem168__c() != null) {
            scrmBusinessOpportunityPO.setSignInInformation(scrmOpportunityRequest.getCustomItem168__c());
        }
        if (scrmOpportunityRequest.getCustomItem171__c() != null) {
            scrmBusinessOpportunityPO.setMonthOrder(scrmOpportunityRequest.getCustomItem171__c());
        }
        if (scrmOpportunityRequest.getCustomItem172__c() != null) {
            scrmBusinessOpportunityPO.setNonMonthOrder(scrmOpportunityRequest.getCustomItem172__c());
        }
        if (scrmOpportunityRequest.getCustomItem173__c() != null) {
            scrmBusinessOpportunityPO.setAllocateTime(new Date(scrmOpportunityRequest.getCustomItem173__c()));
        }
        if (scrmOpportunityRequest.getCustomItem174__c() != null) {
            scrmBusinessOpportunityPO.setContractedStore(scrmOpportunityRequest.getCustomItem174__c());
        }
        if (scrmOpportunityRequest.getCustomItem175__c() != null) {
            scrmBusinessOpportunityPO.setOwnedStore(scrmOpportunityRequest.getCustomItem175__c());
        }
        return scrmBusinessOpportunityPO;
    }

    /**
     * 订单退款请求转为po
     *
     * @param req
     * @return
     */
    ScrmCustomerOrderRefundPO orderRefundRequest2CustomerOrderRefundPO(OrderRefundRequest req);

    /**
     * 将广告平台客户关注公众号消息推送转为PO
     * @param req
     * @return
     */
    default CustomerAdSubscribeListPO adSubscribeRequest2CustomerAdSubscribeListPO(SubscribeRequest req) {
        if (req == null) {
            return null;
        }

        CustomerAdSubscribeListPO customerAdSubscribeListPO = new CustomerAdSubscribeListPO();
        customerAdSubscribeListPO.setTraceId(req.getTrace_id());
        customerAdSubscribeListPO.setWechatOpenid(req.getWechat_openid());
        customerAdSubscribeListPO.setAdgroupId(req.getAdgroup_id());
        customerAdSubscribeListPO.setCampaignId(req.getCampaign_id());
        customerAdSubscribeListPO.setWechatAccountId(req.getWechat_account_id());
        customerAdSubscribeListPO.setPositionId(req.getPosition_id());
        customerAdSubscribeListPO.setAdId(req.getAd_id());
        if (req.getAct_time() != null) {
            customerAdSubscribeListPO.setActTime(DateUtil.date(Long.parseLong(req.getAct_time()) * 1000));
        }

        return customerAdSubscribeListPO;
    }

    @Mappings({
        @Mapping(source = "signOrderDiscountMargin", target = "discountMargin")
    })
    ScrmCustomerOrderDTO orderInfoV3VO2ScrmCustomerOrderDTO(OrderInfoNewV3VO orderVO);

    default OrderGoodsDTO orderGoodsInfoVO2OrderGoodsDTO(OrderGoodsInfoVO vo) {
        if (vo == null) {
            return null;
        }

        OrderGoodsDTO dto = new OrderGoodsDTO();
        dto.setId(vo.getId().longValue());
        dto.setGoodsName(vo.getGoodsName());
        dto.setSkuName(vo.getSkuName());
        dto.setCategoryId(vo.getBackCategoryId());
        dto.setCategoryName(vo.getCategoryName());
        dto.setGoodsPrice(vo.getGoodsPriceOrgin());
        dto.setGoodsNum(vo.getGoodsNum());
        dto.setGoodsType(vo.getGift());
        dto.setRoomTypeName(vo.getRoomTypeName());
        dto.setNurseTypeName(vo.getCareServiceName());

        return dto;
    }

    OrderReductionRecordDTO orderReductionRecordVO2DTO(ScrmOrderReductionRecordVO vo);

    ScrmTeamMemberRecordPO teamMemberRecordRequest2PO(ScrmTeamMemberRecordRequest request);

    default ScrmCustomerOrderPO scrmOrderUpdateRequestToScrmOrderPO(ScrmOrderUpdateRequest request) {
        if (request == null) {
            return null;
        }

        ScrmCustomerOrderPO po = new ScrmCustomerOrderPO();
        po.setScrmId(request.getScrmId());
        po.setScrmCustomerId(request.getAccountId());
        po.setScrmOwnerId(request.getOwnerId());
        po.setScrmDimDepart(request.getDimDepart());
        if (request.getPercentFirstTime() != null) {
            po.setPercentFirstTime(DateUtil.date(request.getPercentFirstTime()));
        }
        po.setMonthOrder(request.getMonthOrder());

        return po;
    }
}


