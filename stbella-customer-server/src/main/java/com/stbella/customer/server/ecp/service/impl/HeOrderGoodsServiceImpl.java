package com.stbella.customer.server.ecp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.customer.server.ecp.entity.HeOrderGoodsPO;
import com.stbella.customer.server.ecp.mapper.helper.HeOrderGoodsMapper;
import com.stbella.customer.server.ecp.service.HeOrderGoodsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单商品表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-11-08
 */
@Service
public class HeOrderGoodsServiceImpl extends ServiceImpl<HeOrderGoodsMapper, HeOrderGoodsPO> implements HeOrderGoodsService {

    /**
     * 根据订单商品编号查询订单商品信息
     *
     * @param orderGoodsSn
     * @return
     */
    @Override
    public HeOrderGoodsPO queryOrderGoodsInfoByOrderGoodsSn(String orderGoodsSn) {
        if (StringUtils.isNotBlank(orderGoodsSn)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<HeOrderGoodsPO>()
            .eq(HeOrderGoodsPO::getOrderGoodsSn, orderGoodsSn)
            .last("limit 1"));
    }
}
