package com.stbella.customer.server.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.care.api.php.PhpApiService;
import com.stbella.care.server.care.service.postpartum.CustomerPostpartumEvaluateReportService;
import com.stbella.care.server.care.service.postpartum.CustomerPostpartumEvaluateService;
import com.stbella.care.server.care.vo.api.ListStoreAuthVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateReportVO;
import com.stbella.care.server.care.vo.postpartum.CustomerPostpartumEvaluateVO;
import com.stbella.core.base.LoginUserDTO;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CareManager {

    @DubboReference(timeout = 5000)
    private PhpApiService phpApiService;

    @DubboReference(timeout = 5000)
    private CustomerPostpartumEvaluateService customerPostpartumEvaluateService;

    @DubboReference(timeout = 5000)
    private CustomerPostpartumEvaluateReportService customerPostpartumEvaluateReportService;

    /**
     * 获取当前登录用户的门店权限列表
     *
     * @param phone
     * @return
     */
    public List<Long> getAuthStoreByUser(String phone) {
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setMobile(phone);
        List<ListStoreAuthVO> authStoreList = phpApiService.listStoreAuth(loginUserDTO);

        return authStoreList.stream()
            .map(ListStoreAuthVO::getStoreId)
            .collect(Collectors.toList());
    }

    /**
     * 查询用户的产康评估表列表
     *
     * @param basicUid
     * @return
     */
    public List<CustomerPostpartumEvaluateVO> queryPostpartumEvaluateListByBasicId(Long basicUid) {
        if (Objects.isNull(basicUid)) {
            return Lists.newArrayList();
        }

        List<CustomerPostpartumEvaluateVO> list = customerPostpartumEvaluateService.listByBasicUid(basicUid);
        return CollectionUtil.isEmpty(list) ? Lists.newArrayList() : list;
    }

    /**
     * 通过评估表id查询评估内容详情
     *
     * @param evaluateId
     * @return
     */
    public CustomerPostpartumEvaluateReportVO queryPostpartumEvaluateReport(Long evaluateId) {
        if (Objects.isNull(evaluateId)) {
            return null;
        }

        return customerPostpartumEvaluateReportService.detail(evaluateId);
    }
}
