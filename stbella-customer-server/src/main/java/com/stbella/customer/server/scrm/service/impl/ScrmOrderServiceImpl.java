package com.stbella.customer.server.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.customer.enums.ScrmCustomerStatusEnum;
import com.stbella.customer.server.ecp.entity.EcpStorePO;
import com.stbella.customer.server.ecp.entity.HeClientExpandPeriodPO;
import com.stbella.customer.server.ecp.entity.HeUserCardPO;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.service.CfgStoreService;
import com.stbella.customer.server.ecp.service.HeClientExpandPeriodService;
import com.stbella.customer.server.ecp.service.HeUserCardService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.scrm.constant.ScrmQwCustomerMessageConstant;
import com.stbella.customer.server.scrm.convert.SCRMConvert;
import com.stbella.customer.server.scrm.dto.OrderGoodsDTO;
import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerOrderDTO;
import com.stbella.customer.server.scrm.entity.ScrmBusinessOpportunityPO;
import com.stbella.customer.server.scrm.entity.ScrmConfigPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerOrderPO;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.entity.ScrmUserPO;
import com.stbella.customer.server.scrm.entity.ScrmUserStoreConfigPO;
import com.stbella.customer.server.scrm.enums.CustomerStatusEnum;
import com.stbella.customer.server.scrm.enums.OpportunityLoseReasonEnum;
import com.stbella.customer.server.scrm.enums.OpportunityOldCustomerTagEnum;
import com.stbella.customer.server.scrm.enums.OpportunityStatusSaleStageEnum;
import com.stbella.customer.server.scrm.enums.ScrmConfigBizTypeEnum;
import com.stbella.customer.server.scrm.request.OpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmCheckinStoreRequest;
import com.stbella.customer.server.scrm.request.ScrmCustomerCreateRequest;
import com.stbella.customer.server.scrm.request.ScrmOrderUpdateRequest;
import com.stbella.customer.server.scrm.service.ScrmBusinessOpportunityService;
import com.stbella.customer.server.scrm.service.ScrmConfigService;
import com.stbella.customer.server.scrm.service.ScrmCustomerOrderService;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.scrm.service.ScrmOrderService;
import com.stbella.customer.server.scrm.service.ScrmUserService;
import com.stbella.customer.server.scrm.service.ScrmUserStoreConfigService;
import com.stbella.customer.server.scrm.service.XsyScrmService;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.notice.server.NoticeService;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.order.month.req.OrderInfoNewV3Query;
import com.stbella.order.server.order.month.res.OrderInfoNewV3VO;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.order.api.OrderInfoService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RefreshScope
@DubboService
public class ScrmOrderServiceImpl implements ScrmOrderService {

    @Resource
    private ScrmConfigService scrmConfigService;

    @Resource
    private TabClientService tabClientService;

    @Resource
    private HeUserCardService heUserCardService;

    @Resource
    private HeClientExpandPeriodService heClientExpandPeriodService;

    @Resource
    private ScrmUserService scrmUserService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private ScrmUserStoreConfigService scrmUserStoreConfigService;

    @Resource
    private ScrmCustomerOrderService scrmCustomerOrderService;

    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;

    @Resource
    private CfgStoreService cfgStoreService;

    @Resource
    private XsyScrmService xsyScrmService;

    @Resource
    private SCRMConvert scrmConvert;

    @DubboReference
    private OrderInfoService orderInfoService;

    @DubboReference
    private NoticeService noticeService;

    @DubboReference
    private OrderV3Service orderV3Service;

    @Value("${webActivityHost.url}")
    private String webActivityHost;

    /**
     * picp订单同步到scrm中
     *
     * @param picpOrderDTO
     * @return
     */
    @Override
    public Boolean orderSyncScrm(ScrmCustomerOrderDTO picpOrderDTO) {
        log.info("picp订单同步到scrm中, request:{}", JSONUtil.toJsonStr(picpOrderDTO));

        // 获取客户在scrm中的信息
        ScrmCustomerPO scrmCustomerInfo = getScrmCustomerInfo(picpOrderDTO.getClientUid());
        if (Objects.isNull(scrmCustomerInfo)) {
            log.error("picp订单同步到scrm中，未找到scrm客户信息, clientUid:{}", picpOrderDTO.getClientUid());
            return false;
        }

        picpOrderDTO.setScrmCustomerId(scrmCustomerInfo.getScrmCustomerId());

        // 获取订单绑定的销售信息
        ScrmUserPO scrmUserInfo = getScrmUserInfo(picpOrderDTO.getStoreId(), picpOrderDTO.getStaffPhone());

        picpOrderDTO.setScrmOwnerId(scrmUserInfo.getScrmId());
        picpOrderDTO.setScrmDimDepart(scrmUserInfo.getDimDepart());
        picpOrderDTO.setCreateBy(scrmUserInfo.getScrmId());
        picpOrderDTO.setUpdateBy(scrmUserInfo.getScrmId());

        // 先将订单信息存入到数据库及scrm中
        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.savePicpOrder(picpOrderDTO);
        if (Objects.nonNull(scrmCustomerOrderPO.getUserStoreRecordId()) || scrmCustomerOrderPO.getSaleChange().equals(2)) {
            // 订单已有门店信息或者已绑定商机，无需处理了。
            log.info("订单已有门店信息或者已绑定商机，无需后续处理, orderPO:{}", JSONUtil.toJsonStr(scrmCustomerOrderPO));
            return true;
        }

        if (scrmCustomerOrderPO.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.getCode())) {
            // 查找到订单要关联的商机, 并关联商机和订单
            ScrmBusinessOpportunityPO opportunityPO = findScrmBusinessOpportunity(
                scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO);

            // 处理商机与打卡记录的绑定
            checkinStoreBindOpportunity(scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO, opportunityPO);

            // 处理商机与订单的绑定
            orderBindOpportunity(scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO, opportunityPO);
        } else {
            // 只需要找到销售与门店关系就可以了
            nonMonthOrderBindStore(scrmUserInfo, scrmCustomerOrderPO);

            if (scrmCustomerOrderPO.getOrderType().equals(OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode())) {
                // 如果是小月子订单，客户状态要改为小月子
                scrmCustomerInfo.setCustomerStatus(CustomerStatusEnum.MISCARRIAGE.getCode());
                scrmCustomerService.updateScrmCustomer(scrmCustomerInfo);
            }
        }

        return true;
    }

    /**
     * 订单标准v1.0 同步到scrm中
     *
     * @param orderId
     * @return
     */
    @Override
    public Boolean omniOrderSyncScrm(Long orderId) {
        log.info("【订单标准v1.00】订单同步至scrm, orderId:{}", orderId);
        if (Objects.isNull(orderId)) {
            return false;
        }

        OrderInfoNewV3Query query = new OrderInfoNewV3Query();
        query.setOrderId(orderId.intValue());
        OrderInfoNewV3VO orderInfoNewV3VO = orderV3Service.queryOrderInfo(query);
        log.info("【订单标准v1.00】订单同步至scrm, 获取订单详情:{}", JSONUtil.toJsonStr(orderInfoNewV3VO));

        ScrmCustomerOrderDTO picpOrderDTO = scrmConvert.orderInfoV3VO2ScrmCustomerOrderDTO(orderInfoNewV3VO);
        picpOrderDTO.setMonthOrder(2);
        picpOrderDTO.setGrossMargin(picpOrderDTO.getGrossMargin().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        picpOrderDTO.setNetMargin(picpOrderDTO.getNetMargin().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        if (Objects.nonNull(picpOrderDTO.getDiscountMargin())) {
            picpOrderDTO.setDiscountMargin(picpOrderDTO.getDiscountMargin().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        }

        // 通过百分之五十的支付时间判断订单是否计入业绩
        if (Objects.nonNull(picpOrderDTO.getPercentFirstTime())) {
            picpOrderDTO.setPerformanceStatus(1);
        } else {
            picpOrderDTO.setPerformanceStatus(2);
        }

        String orderH5Url = webActivityHost + "/#/newOrderDetail?orderId=" + orderId + "&source=pi&t=" + DateUtil.date().getTime();
        picpOrderDTO.setOrderH5(orderH5Url);

        // 解析订单商品信息
        boolean monthOrder = false;
        boolean smallMonthOrder = false;
        Set<Integer> orderGoodsTypeSet = new HashSet<>();

        List<OrderGoodsDTO> orderGoodsDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderInfoNewV3VO.getOrderGoodsInfoVOS())) {
            orderInfoNewV3VO.getOrderGoodsInfoVOS().forEach(orderGoodsInfoVO -> {
                OrderGoodsDTO orderGoodsDTO = scrmConvert.orderGoodsInfoVO2OrderGoodsDTO(orderGoodsInfoVO);
                if (Objects.nonNull(orderGoodsDTO)) {
                    orderGoodsDTO.setOrderId(orderId);
                    orderGoodsDTOList.add(orderGoodsDTO);
                    orderGoodsTypeSet.add(orderGoodsInfoVO.getGoodsType());
                }
            });
        }

        if (CollectionUtil.isNotEmpty(orderGoodsTypeSet)) {
            if (orderGoodsTypeSet.contains(0)) {
                monthOrder = true;
                // 只要有标准月子套餐，即为月子服务
                picpOrderDTO.setMonthOrder(1);
            }

            if (orderGoodsTypeSet.contains(1) && !orderGoodsTypeSet.contains(0)) {
                smallMonthOrder = true;
            }
        }
        picpOrderDTO.setOrderGoodsList(orderGoodsDTOList);

        // 解析订单的减免列表
        List<OrderReductionRecordDTO> orderReductionRecordDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderInfoNewV3VO.getScrmOrderReductionRecordVOS())) {
            orderInfoNewV3VO.getScrmOrderReductionRecordVOS().forEach(orderReductionRecordVO -> {
                OrderReductionRecordDTO orderReductionRecordDTO = scrmConvert.orderReductionRecordVO2DTO(orderReductionRecordVO);
                if (Objects.nonNull(orderReductionRecordDTO)) {
                    orderReductionRecordDTO.setOrderId(orderId);
                    orderReductionRecordDTOList.add(orderReductionRecordDTO);
                }
            });
        }

        // 获取客户在scrm中的信息
        ScrmCustomerPO scrmCustomerInfo = getScrmCustomerInfo(picpOrderDTO.getClientUid());
        if (Objects.isNull(scrmCustomerInfo)) {
            log.error("picp订单同步到scrm中，未找到scrm客户信息, clientUid:{}", picpOrderDTO.getClientUid());
            return false;
        }

        picpOrderDTO.setScrmCustomerId(scrmCustomerInfo.getScrmCustomerId());

        // 获取订单绑定的销售信息
        ScrmUserPO scrmUserInfo = getScrmUserInfo(picpOrderDTO.getStoreId(), picpOrderDTO.getStaffPhone());

        picpOrderDTO.setScrmOwnerId(scrmUserInfo.getScrmId());
        picpOrderDTO.setScrmDimDepart(scrmUserInfo.getDimDepart());
        picpOrderDTO.setCreateBy(scrmUserInfo.getScrmId());
        picpOrderDTO.setUpdateBy(scrmUserInfo.getScrmId());

        // 先将订单信息存入到数据库及scrm中
        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.savePicpOrder(picpOrderDTO);

        // 同步商品信息
        omniOrderGoodsSyncScrm(picpOrderDTO.getOrderGoodsList());

        // 处理订单与商机的关系
        if (Objects.nonNull(scrmCustomerOrderPO.getUserStoreRecordId()) || scrmCustomerOrderPO.getSaleChange().equals(2)) {
            // 订单已有门店信息或者已绑定商机，无需处理了。
            log.info("订单已有门店信息或者已绑定商机，无需后续处理, orderPO:{}", JSONUtil.toJsonStr(scrmCustomerOrderPO));
            return true;
        }

        if (scrmCustomerOrderPO.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.getCode())) {
            // 新的订单同步过来，都需要绑定标准月子商机
            // 查找到订单要关联的商机, 并关联商机和订单
            ScrmBusinessOpportunityPO opportunityPO = findScrmBusinessOpportunity(
                scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO);

            // 处理商机与打卡记录的绑定, 只有标准月子才需要绑定到店
            if (monthOrder) {
                checkinStoreBindOpportunity(scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO, opportunityPO);
            }

            // 处理商机与订单的绑定
            orderBindOpportunity(scrmCustomerInfo, scrmUserInfo, scrmCustomerOrderPO, opportunityPO);
        } else {
            // 只需要找到销售与门店关系就可以了
            nonMonthOrderBindStore(scrmUserInfo, scrmCustomerOrderPO);

            if (smallMonthOrder) {
                // 如果是小月子订单，客户状态要改为小月子
                scrmCustomerInfo.setCustomerStatus(CustomerStatusEnum.MISCARRIAGE.getCode());
                scrmCustomerService.updateScrmCustomer(scrmCustomerInfo);
            }
        }

        // 更新一下减免记录
        orderReductionRecordDTOList.forEach(this::ominOrderReducionRecordSyncScrm);

        return true;
    }

    /**
     * 订单商品列表同步到scrm
     *
     * @param orderGoodsList
     */
    private void omniOrderGoodsSyncScrm(List<OrderGoodsDTO> orderGoodsList) {
        log.info("【订单标准v1.00】订单商品同步至scrm, orderGoods:{}", JSONUtil.toJsonStr(orderGoodsList));
        if (CollectionUtil.isEmpty(orderGoodsList)) {
            return;
        }

        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.queryScrmOrderByOrderId(orderGoodsList.get(0).getOrderId());
        if (Objects.isNull(scrmCustomerOrderPO)) {
            // 订单未同步至scrm
            log.error("订单商品列表同步至scrm时发现订单未同步至scrm，需先同步订单，picpOrderId:{}", orderGoodsList.get(0).getOrderId());
            return;
        }

        orderGoodsList = orderGoodsList.stream().peek(i -> {
            i.setScrmOrderId(scrmCustomerOrderPO.getScrmId());
            i.setDimDepart(scrmCustomerOrderPO.getScrmDimDepart());
            i.setScrmOwnerId(i.getScrmOwnerId());
        }).collect(Collectors.toList());

        // 一次同步商品信息，只能同步16个
        List<List<OrderGoodsDTO>> orderGoodsListPartition = ListUtil.partition(orderGoodsList, 16);
        orderGoodsListPartition.forEach(item -> {
            xsyScrmService.orderGoodsSync(item);
        });
    }

    /**
     * 订单减免记录同步到scrm
     *
     * @param recordDTO
     * @return
     */
    @Override
    public Boolean ominOrderReducionRecordSyncScrm(OrderReductionRecordDTO recordDTO) {
        log.info("【订单标准v1.00】减免记录同步至scrm, request:{}", JSONUtil.toJsonStr(recordDTO));
        if (Objects.isNull(recordDTO)) {
            return true;
        }

        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.queryScrmOrderByOrderId(recordDTO.getOrderId());
        if (Objects.isNull(scrmCustomerOrderPO)) {
            // 订单未同步至scrm
            log.error("订单减免记录同步至scrm时发现订单未同步至scrm，需先同步订单，picpOrderId:{}", recordDTO.getOrderId());
            return false;
        }

        recordDTO.setScrmOrderId(scrmCustomerOrderPO.getScrmId());
        recordDTO.setDimDepart(scrmCustomerOrderPO.getScrmDimDepart());
        recordDTO.setAuthState("审核通过");
        if (StringUtils.isBlank(recordDTO.getChangeType())) {
            recordDTO.setChangeType("收款减免");
        }

        // 通过手机号查询到本次减免记录所属人的scrm信息
        if (StringUtils.isNotBlank(recordDTO.getStaffPhone())) {
            ScrmUserPO scrmUserInfo = getScrmUserInfo(scrmCustomerOrderPO.getStoreId(), recordDTO.getStaffPhone());
            if (Objects.nonNull(scrmUserInfo)) {
                recordDTO.setScrmOwnerId(scrmUserInfo.getScrmId());
            }
        }

        xsyScrmService.orderReductionRecordSync(recordDTO);
        return true;
    }

    /**
     * 获取订单的scrm客户信息
     *
     * @param clientUid 客户的clientid
     * @return
     */
    private ScrmCustomerPO getScrmCustomerInfo(Integer clientUid) {
        TabClientPO tabClientPO = tabClientService.queryClientById(clientUid);
        if (Objects.isNull(tabClientPO)) {
            log.error("通过clientUid获取tab_client表中对应的数据，未找到, clientUid:{}", clientUid);
            return null;
        }

        ScrmCustomerPO scrmCustomerPO = null;
        if (StringUtils.isNotBlank(tabClientPO.getScrmId())) {
            scrmCustomerPO = scrmCustomerService.getByScrmCustomerId(Long.valueOf(tabClientPO.getScrmId()));
        }

        if (Objects.isNull(scrmCustomerPO)) {
            // 原客户对应的scrm信息不存在了
            //scrmCustomerPO = scrmCustomerService.queryCustomerByPhone(tabClientPO.getPhone());
            log.info("手机号:{} 对应的scrm客户信息不存在，需要去scrm中新增", tabClientPO.getPhone());
            ScrmCustomerCreateRequest scrmCustomerCreateRequest = new ScrmCustomerCreateRequest();
            scrmCustomerCreateRequest.setPhoneType(tabClientPO.getPhoneType());
            scrmCustomerCreateRequest.setPhone(tabClientPO.getPhone());
            scrmCustomerCreateRequest.setName(tabClientPO.getName());
            scrmCustomerCreateRequest.setFromType(tabClientPO.getFromType());
            scrmCustomerCreateRequest.setProvince(tabClientPO.getProvince());
            scrmCustomerCreateRequest.setCity(tabClientPO.getCity());
            scrmCustomerCreateRequest.setRegion(tabClientPO.getRegion());
            scrmCustomerCreateRequest.setAddress(tabClientPO.getAddress());
            scrmCustomerCreateRequest.setBloodType(tabClientPO.getBloodType());
            scrmCustomerCreateRequest.setUrgentName(tabClientPO.getUrgentName());
            scrmCustomerCreateRequest.setUrgentPhone(tabClientPO.getUrgentPhone());
            scrmCustomerCreateRequest.setRelationWithClient(tabClientPO.getRelationWithClient());
            scrmCustomerCreateRequest.setConstellationType(tabClientPO.getConstellationType());
            scrmCustomerCreateRequest.setProfession(tabClientPO.getProfession());
            scrmCustomerCreateRequest.setIdCard(tabClientPO.getIdCard());
            scrmCustomerCreateRequest.setCertType(tabClientPO.getCertType());
            scrmCustomerCreateRequest.setHospital(tabClientPO.getHospital());

            HeUserCardPO userCardPO = heUserCardService.queryUserCardInfoByCertType(tabClientPO.getBasicUid(), tabClientPO.getCertType(), 1);
            if (Objects.nonNull(userCardPO)) {
                scrmCustomerCreateRequest.setName(userCardPO.getName());
                scrmCustomerCreateRequest.setCertType(userCardPO.getCertType());
                scrmCustomerCreateRequest.setIdCard(userCardPO.getIdCard());
            }

            HeClientExpandPeriodPO clientExpandPeriodPO = heClientExpandPeriodService.queryOneByEcpUid(tabClientPO.getId().intValue());
            if (Objects.nonNull(clientExpandPeriodPO)) {
                if (Objects.nonNull(clientExpandPeriodPO.getPredictBornTime()) && clientExpandPeriodPO.getPredictBornTime() > 0) {
                    DateTime predictBornDate = Objects.nonNull(clientExpandPeriodPO.getPredictBornTime()) ? DateUtil.date(clientExpandPeriodPO.getPredictBornTime() * 1000) : null;
                    scrmCustomerCreateRequest.setPredictBornDate(predictBornDate);
                }
            }

            scrmCustomerPO = scrmCustomerService.createCustomer2Scrm(scrmCustomerCreateRequest);

            // 更新一下tab_client表中的scrm_id
            tabClientPO.setScrmId(scrmCustomerPO.getScrmCustomerId().toString());
            tabClientService.updateById(tabClientPO);
        }

        return scrmCustomerPO;
    }

    /**
     * 获取订单的scrm销售信息
     *
     * @param storeId 订单的门店id
     * @param staffPhone 销售手机号
     * @return
     */
    private ScrmUserPO getScrmUserInfo(Integer storeId, String staffPhone) {
        ScrmUserPO scrmUserPO = scrmUserService.queryByScrmPhone(staffPhone);
        if (Objects.isNull(scrmUserPO)) {
            // 该员工在scrm中没有账号，则查找该门店下其他的销售或者店长
            Long saleId = scrmUserStoreConfigService.querySaleUserScrmIdByStoreId(storeId);
            if (Objects.isNull(saleId)) {
                log.info("门店:{} 下没有销售或者店长, 订单指向管理员", storeId);
                ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
                saleId = Long.parseLong(scrmConfigPO.getBizContent());
            }

            scrmUserPO = scrmUserService.queryByScrmId(saleId);
        }

        return scrmUserPO;
    }

    /**
     * 查找符合订单类型的进行中的商机
     *
     * @param customer
     * @param sale
     * @param order
     * @return
     */
    private ScrmBusinessOpportunityPO findScrmBusinessOpportunity(ScrmCustomerPO customer, ScrmUserPO sale, ScrmCustomerOrderPO order) {
        /*
        1. 查找客户所有的标准月子商机
        2. 查看是否有进行中的商机
        3. 查看是否有历史未签约或已签约的商机
        4. 找不到进行中的商机，则创建一个新的商机，商机所属人为销售
        5. 判断商机所属人和销售是否一致
            5.1. 是同一人, 看门店是否一致，门店不一致，则修改为订单门店
            5.2. 不是同一人，看门店是否一致，门店一致，则修改商机所属人为销售；门店不一致，则创建一个新商机，商机所属人为销售
         */
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = null;

        List<ScrmBusinessOpportunityPO> opportunityList = scrmBusinessOpportunityService.queryCustomerOpportunityList(
            customer.getScrmCustomerId(), order.getOrderType());

        boolean createNewOpportunity = false;
        if (CollectionUtil.isEmpty(opportunityList)) {
            log.info("客户customerId:{}未发现对应订单类型orderType:{}的商机，需要新增商机", customer.getScrmCustomerId(), order.getOrderType());
            createNewOpportunity = true;
        } else {
            // 查找是否有进行中的商机
            List<Long> inProcessStatusList = scrmBusinessOpportunityService.getOpportunityInProcessStatusByOrderType(
                order.getOrderType());

            if (CollectionUtil.isEmpty(inProcessStatusList)) {
                log.error("订单orderType:{}未找到对应的商机类型", order.getOrderType());
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "订单未找到对应的商机类型");
            }

            List<ScrmBusinessOpportunityPO> inProcessOpportunityList = opportunityList.stream()
                .filter(opportunity -> inProcessStatusList.contains(opportunity.getSaleStageId()))
                .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(inProcessOpportunityList)) {
                log.info("客户customerId:{}未发现对应订单类型orderType:{}有进行中的商机，需要新增商机",
                    customer.getScrmCustomerId(), order.getOrderType());
                createNewOpportunity = true;
            } else {
                // 查找商机是否属于销售的商机
                List<ScrmBusinessOpportunityPO> saleOpportunityList = inProcessOpportunityList.stream()
                    .filter(opportunity -> opportunity.getOwnerId().equals(sale.getScrmId()))
                    .sorted(Comparator.comparing(ScrmBusinessOpportunityPO::getGmtCreate).reversed())
                    .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(saleOpportunityList)) {
                    // 没有该销售的商机，需要找同一门店的商机
                    List<ScrmUserStoreConfigPO> userStoreConfigPOList = scrmUserStoreConfigService.listByStore(order.getStoreId());
                    if (CollectionUtil.isNotEmpty(userStoreConfigPOList)) {
                        List<Long> userStoreConfigIdList = userStoreConfigPOList.stream()
                            .map(ScrmUserStoreConfigPO::getScrmRecordId)
                            .collect(Collectors.toList());

                        List<ScrmBusinessOpportunityPO> storeOpportunityList = inProcessOpportunityList.stream()
                            .filter(opportunity -> userStoreConfigIdList.contains(opportunity.getContractedStore()))
                            .sorted(Comparator.comparing(ScrmBusinessOpportunityPO::getGmtCreate).reversed())
                            .collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(storeOpportunityList)) {
                            log.info("客户customerId:{}, 订单类型orderType:{}, storeId:{}, 未发现对应门店的商机，需要新增商机",
                                customer.getScrmCustomerId(), order.getOrderType(), order.getStoreId());
                            createNewOpportunity = true;
                        } else {
                            scrmBusinessOpportunityPO = storeOpportunityList.get(0);
                            // 查找是否包含有邀约到店阶段的商机
                            Long checkinStoreStageId = scrmBusinessOpportunityService.opportunityStatus2StageIdByEntityType(
                                scrmBusinessOpportunityPO.getEntityType(), OpportunityStatusSaleStageEnum.ARRIVAL.getCode());
                            if (Objects.nonNull(checkinStoreStageId)) {
                                Optional<ScrmBusinessOpportunityPO> firstOptional = storeOpportunityList.stream()
                                    .filter(opportunity -> opportunity.getSaleStageId().equals(checkinStoreStageId))
                                    .findFirst();
                                if (firstOptional.isPresent()) {
                                    scrmBusinessOpportunityPO = firstOptional.get();
                                }
                            }
                        }
                    } else {
                        log.info("客户customerId:{}, 订单类型orderType:{}, storeId:{}, 未发现对应门店的商机，需要新增商机",
                            customer.getScrmCustomerId(), order.getOrderType(), order.getStoreId());
                        createNewOpportunity = true;
                    }
                } else {
                    scrmBusinessOpportunityPO = saleOpportunityList.get(0);
                }
            }
        }

        if (Objects.isNull(scrmBusinessOpportunityPO) && createNewOpportunity) {
            // 创建新的商机
            scrmBusinessOpportunityPO = createOpportunity(customer, sale, order);
        }

        // 检查商机对应的所属人及门店是否和销售的一致，不一致则需要更新转移
        if (Objects.nonNull(scrmBusinessOpportunityPO)) {
            if (!scrmBusinessOpportunityPO.getOwnerId().equals(sale.getScrmId())) {
                // 商机的所属人非销售本人，则需要商机转移
                scrmBusinessOpportunityPO = scrmBusinessOpportunityService.transformScrmOpportunity(scrmBusinessOpportunityPO, sale.getScrmId());
            }

            // 查看商机的所属门店
            ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(sale, order.getStoreId());
            if (Objects.nonNull(scrmUserStoreConfig) && !scrmBusinessOpportunityPO.getContractedStore().equals(scrmUserStoreConfig.getScrmRecordId())) {
                // 商机的所属门店和订单的所属门店不一致，则需要更新
                scrmBusinessOpportunityPO.setContractedStore(scrmUserStoreConfig.getScrmRecordId());
            }
        }

        return scrmBusinessOpportunityPO;
    }

    /**
     * 门店打卡记录与商机关联起来
     *
     * @param customer
     * @param sale
     * @param order
     * @param opportunity
     */
    private void checkinStoreBindOpportunity(ScrmCustomerPO customer, ScrmUserPO sale, ScrmCustomerOrderPO order, ScrmBusinessOpportunityPO opportunity) {
        List<ScrmCheckinStoreRequest> checkinStoreList = xsyScrmService.queryScrmCheckinStoreListByPhone(customer.getPhone());
        if (CollectionUtil.isNotEmpty(checkinStoreList)) {
            // 查找未绑定商机的门店打卡记录
            List<Long> scrmUserStoreConfigIds = checkinStoreList.stream()
                .filter(i -> i.getRelationOpportunity().equals(1))
                .map(ScrmCheckinStoreRequest::getStoreId)
                .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(scrmUserStoreConfigIds)) {
                List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOList = scrmUserStoreConfigService.getByRecordIdList(scrmUserStoreConfigIds);
                if (CollectionUtil.isNotEmpty(scrmUserStoreConfigPOList)) {
                    List<ScrmUserStoreConfigPO> filterStoreList = scrmUserStoreConfigPOList.stream()
                        .filter(i -> i.getStoreId().equals(order.getStoreId().longValue()))
                        .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(filterStoreList)) {
                        ScrmUserStoreConfigPO scrmUserStoreConfig = filterStoreList.get(0);
                        Optional<ScrmCheckinStoreRequest> checkinStoreOptional = checkinStoreList.stream()
                            .filter(i -> i.getStoreId().equals(scrmUserStoreConfig.getScrmRecordId()))
                            .findFirst();

                        if (checkinStoreOptional.isPresent()) {
                            // 查找对应业务类型的探店打卡阶段
                            Long checkinStoreStageId = scrmBusinessOpportunityService.opportunityStatus2StageIdByEntityType(
                                opportunity.getEntityType(),
                                OpportunityStatusSaleStageEnum.ARRIVAL.getCode());

                            if (Objects.nonNull(checkinStoreStageId)) {
                                ScrmCheckinStoreRequest checkinStore = checkinStoreOptional.get();
                                opportunity.setSignInInformation(checkinStore.getScrmId());
                                opportunity.setSaleStageId(checkinStoreStageId);

                                scrmBusinessOpportunityService.updateOpportunity(opportunity, checkinStore.getCheckinTime());

                                // 在scrm中更新探店打卡的商机绑定状态
                                xsyScrmService.signInInformationBindOrUnBindOpportunity(opportunity.getSignInInformation(),2);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 订单与商机关联起来
     *
     * @param customer
     * @param sale
     * @param order
     */
    private void orderBindOpportunity(ScrmCustomerPO customer, ScrmUserPO sale, ScrmCustomerOrderPO order, ScrmBusinessOpportunityPO opportunity) {
        // 商机开始和订单关联起来，并做赢单处理
        if (Objects.isNull(opportunity)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单未找到对应的商机类型, 绑定失败");
        }

        // 定义月子订单类型
        List<Integer> monthOrderTypeList = Collections.singletonList(OmniOrderTypeEnum.MONTH_ORDER.getCode());

        if (monthOrderTypeList.contains(order.getOrderType())) {
            opportunity.setMonthOrder(order.getScrmId());
        } else {
            opportunity.setNonMonthOrder(order.getScrmId());
        }
        opportunity.setMoney(order.getPayAmount());
        opportunity.setLockStatus(2);
        opportunity.setStatus(2);
        opportunity.setOldTag(OpportunityOldCustomerTagEnum.NONE.getCode());

        // 需要自动输单的商机列表
        List<ScrmBusinessOpportunityPO> inProcessOpportunityList = new ArrayList<>();

        // 查找所有的商机
        List<ScrmBusinessOpportunityPO> allOpportunityList = scrmBusinessOpportunityService.queryCustomerOpportunityList(
            customer.getScrmCustomerId(), order.getOrderType());

        // 查找对应业务类型的赢单阶段id和输单阶段id
        Long winStageId = scrmBusinessOpportunityService.opportunityStatus2StageIdByEntityType(
            opportunity.getEntityType(),
            OpportunityStatusSaleStageEnum.WIN.getCode());

        Long loseStageId = scrmBusinessOpportunityService.opportunityStatus2StageIdByEntityType(
            opportunity.getEntityType(),
            OpportunityStatusSaleStageEnum.LOSE.getCode());

        if (CollectionUtil.isNotEmpty(allOpportunityList)) {
            // 查到其他进行中的商机
            List<Long> inProcessStatusList = scrmBusinessOpportunityService.getOpportunityInProcessStatusByOrderType(order.getOrderType());
            if (CollectionUtil.isNotEmpty(inProcessStatusList)) {
                inProcessOpportunityList = allOpportunityList.stream()
                    .filter(i -> inProcessStatusList.contains(i.getSaleStageId()) && !(i.getScrmId().equals(opportunity.getScrmId())))
                    .collect(Collectors.toList());
            }

            // 查看该业务类型的商机是否存在历史输单商机
            List<ScrmBusinessOpportunityPO> loseOpportunityList = allOpportunityList.stream()
                .filter(i -> i.getSaleStageId().equals(loseStageId))
                .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(loseOpportunityList)) {
                opportunity.setOldTag(OpportunityOldCustomerTagEnum.HISTORY_NO_SIGN.getCode());
            }

            // // 查看该业务类型的商机是否存在历史赢单商机
            List<ScrmBusinessOpportunityPO> winOpportunityList = allOpportunityList.stream()
                .filter(i -> i.getSaleStageId().equals(winStageId))
                .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(winOpportunityList)) {
                opportunity.setOldTag(OpportunityOldCustomerTagEnum.HISTORY_SIGNED.getCode());
            }
        }

        // 保存数据库，同步到scrm
        // 同步到scrm
        // 保存商机中间表，同步到scrm
        opportunity.setSaleStageId(winStageId);
        scrmBusinessOpportunityService.updateOpportunity(opportunity, order.getPercentFirstTime());

        // 订单中的商机状态变更
        order.setSaleChange(2);
        order.setUserStoreRecordId(opportunity.getContractedStore());
        scrmCustomerOrderService.updateScrmOrder(order);

        // 标准月子的订单，需要把其他进行中的商机设置为输单
        if (order.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.code())) {
            // 其他的同类型商机变成输单
            inProcessOpportunityList.forEach(i -> {
                i.setReason(OpportunityLoseReasonEnum.OTHER_STORES_HAVE_BEEN_SIGNED.getCode());
                i.setSaleStageId(loseStageId);
                i.setStatus(3);

                scrmBusinessOpportunityService.updateOpportunity(i, null);
            });

            // 更新客户的状态，如果客户是小于签单状态，则更新为已签单
            Integer customerStage = customer.getCustomerStage();
            if (Objects.isNull(customerStage) || customerStage < ScrmCustomerStatusEnum.SIGN.getCode()) {
                customer.setCustomerStage(ScrmCustomerStatusEnum.SIGN.getCode());
            }
            // 判断该订单的预产期，如果是0或者2099年，则为备孕中，否则为怀孕
            Result<OrderUserSnapshotVO> orderUserSnapshotVOResult = orderInfoService.queryOrderUserSnapshot(order.getOrderId().intValue());
            if (orderUserSnapshotVOResult.getSuccess() && Objects.nonNull(orderUserSnapshotVOResult.getData())) {
                Date predictBornDate = orderUserSnapshotVOResult.getData().getPredictBornDate();
                if (Objects.isNull(predictBornDate) || predictBornDate.getTime() == 0 || DateUtil.year(predictBornDate) == 2099) {
                    customer.setCustomerStatus(CustomerStatusEnum.PREPARATION_FOR_PREGNANCY.getCode());
                } else {
                    customer.setCustomerStatus(CustomerStatusEnum.PREGNANT.getCode());
                    customer.setPredictBornDate(predictBornDate);
                }
            }
            customer.setWinSale(sale.getScrmId());
            scrmCustomerService.updateScrmCustomer(customer);
        }
    }

    /**
     * 创建一个新的商机
     *
     * @param customer
     * @param sale
     * @param order
     * @return
     */
    private ScrmBusinessOpportunityPO createOpportunity(ScrmCustomerPO customer, ScrmUserPO sale, ScrmCustomerOrderPO order) {
        // 查找销售与门店关系的配置
        Long scrmUserStoreConfigId = null;
        ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(sale, order.getStoreId());
        if (Objects.nonNull(scrmUserStoreConfig)) {
            scrmUserStoreConfigId = scrmUserStoreConfig.getScrmRecordId();
        }

        OpportunityRequest opportunityRequest = new OpportunityRequest();
        opportunityRequest.setOwnerId(sale.getScrmId());
        opportunityRequest.setDimDepart(sale.getDimDepart());
        opportunityRequest.setAccountId(customer.getScrmCustomerId());
        opportunityRequest.setStageUpdatedAt(System.currentTimeMillis());
        opportunityRequest.setCreatedAt(System.currentTimeMillis());
        opportunityRequest.setUpdatedAt(System.currentTimeMillis());
        opportunityRequest.setIntendedStore__c(order.getStoreId());

        // 商机所属门店关系
        opportunityRequest.setCustomItem174__c(scrmUserStoreConfigId);

        //获取客户名称
        String orderName = order.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.code()) ? "标准月子" : OmniOrderTypeEnum.getValueByCode(order.getOrderType());
        opportunityRequest.setOpportunityName(customer.getName() + orderName + "商机");

        // 获取门店类型
        EcpStorePO ecpStorePO = cfgStoreService.queryStoreByStoreId(order.getStoreId());
        opportunityRequest.setStoreType(0);
        if (Objects.nonNull(ecpStorePO)) {
            opportunityRequest.setStoreType(ecpStorePO.getType());
        }

        // 新建的商机，需要把tab_client中的创建时间作为首次触达的时间
        TabClientPO tabClientPO = tabClientService.queryClientByPhoneStore(customer.getPhone(),
            order.getStoreId());
        if (Objects.nonNull(tabClientPO)) {
            opportunityRequest.setStageUpdatedAt(DateUtils.toDateTime(tabClientPO.getRecordTime()).getTime());
            opportunityRequest.setCustomItem173__c(DateUtils.toDateTime(tabClientPO.getRecordTime()).getTime());
        }

        ScrmBusinessOpportunityPO scrmOpportunity = scrmBusinessOpportunityService.createScrmOpportunity(opportunityRequest, null);
        if (Objects.nonNull(scrmOpportunity)) {
            // 发送消息至企微群里
            String messageContent = String.format(
                ScrmQwCustomerMessageConstant.ORDER_SYNC_CREATE_OPPORTUNITY_MESSAGE,
                StringUtils.isNotBlank(customer.getName()) ? customer.getName() : "",
                StringUtils.isNotBlank(customer.getPhone()) ? customer.getPhone() : "",
                StringUtils.isNotBlank(customer.getWechat()) ? customer.getWechat() : "",
                Objects.nonNull(ecpStorePO.getStoreName()) ? ecpStorePO.getStoreName() : "",
                StringUtils.isNotBlank(sale.getName()) ? sale.getName() : "",
                DateUtil.format(new Date(), "yyyy年MM月dd日 HH时mm分ss秒"));

            WechatNailNailRobotDeclarationRequest wechatMessageRequest = new WechatNailNailRobotDeclarationRequest();
            wechatMessageRequest.setBizType(60001);
            wechatMessageRequest.setStoreId(0);
            wechatMessageRequest.setType(1);
            wechatMessageRequest.setContent(messageContent);
            log.info("企微客资分配通知内容{}", messageContent);
            Result<Boolean> notice = noticeService.notice(wechatMessageRequest);
            log.info("企微客资分配通知结果：{}", JSONUtil.toJsonStr(notice));
        }

        return scrmOpportunity;
    }

    /**
     * 非标准月子订单绑定销售与门店关系
     *
     * @param sale
     * @param order
     */
    private void nonMonthOrderBindStore(ScrmUserPO sale, ScrmCustomerOrderPO order) {
        ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(sale, order.getStoreId());
        if (Objects.nonNull(scrmUserStoreConfig)) {
            order.setUserStoreRecordId(scrmUserStoreConfig.getScrmRecordId());
            order.setSaleChange(2);
            scrmCustomerOrderService.updateScrmOrder(order);
        }
    }

    /**
     * scrm修改订单同步至picp
     *
     * @param request
     * @return
     */
    @Override
    public Boolean orderUpdateFromScrm(ScrmOrderUpdateRequest request) {
        log.info("scrm修改订单, request:{}", JSONUtil.toJsonStr(request));
        ScrmCustomerOrderPO scrmCustomerOrderPO = scrmConvert.scrmOrderUpdateRequestToScrmOrderPO(request);
        if (Objects.isNull(scrmCustomerOrderPO)) {
            return false;
        }

        List<ScrmCustomerOrderPO> scrmCustomerOrderPOS = scrmCustomerOrderService.selectByScrmIdList(
            Collections.singletonList(scrmCustomerOrderPO.getScrmId()));
        if (CollectionUtil.isEmpty(scrmCustomerOrderPOS)) {
            log.info("scrm修改订单, 订单在picp中不存在");
            return false;
        }

        Long id = scrmCustomerOrderPOS.get(0).getId();
        Date oldPercentFirstTime = scrmCustomerOrderPOS.get(0).getPercentFirstTime();

        scrmCustomerOrderPO.setId(id);
        if (scrmCustomerOrderService.updateById(scrmCustomerOrderPO)) {
            if (!scrmCustomerOrderPO.getPercentFirstTime().equals(oldPercentFirstTime)) {
                // 修改商机中间表的时间
                ScrmBusinessOpportunityPO opportunityPO = scrmBusinessOpportunityService.queryOpportunityByScrmOrderId(scrmCustomerOrderPO.getScrmId());
                if (Objects.nonNull(opportunityPO)) {
                    scrmBusinessOpportunityService.saveOpportunityStatus(
                        opportunityPO.getScrmId(),
                        opportunityPO.getOpportunityName(),
                        opportunityPO.getSaleStageId(),
                        null,
                        opportunityPO.getOwnerId(),
                        opportunityPO.getDimDepart(),
                        opportunityPO.getEntityType(),
                        opportunityPO.getLockStatus(),
                        scrmCustomerOrderPO.getPercentFirstTime()
                    );
                }
            }

            return true;
        }

        return false;
    }
}
