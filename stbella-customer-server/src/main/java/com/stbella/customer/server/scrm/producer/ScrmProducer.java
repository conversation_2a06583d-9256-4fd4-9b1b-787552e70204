package com.stbella.customer.server.scrm.producer;

import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.activity.request.ActivitySignupEmailRequest;
import com.stbella.customer.server.cts.request.SCRMClockStoreRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.customer.request.growth.CustomerProductionGiftRequest;
import com.stbella.customer.server.scrm.dto.ScrmCustomerBirthDTO;
import com.stbella.customer.server.scrm.dto.ScrmDistributionCustomerDTO;
import com.stbella.customer.server.scrm.dto.ScrmPulsarMessageDTO;
import com.stbella.customer.server.scrm.enums.ScrmPulsarMessageTypeEnum;
import com.stbella.customer.server.scrm.request.AccountInfoRequest;
import com.stbella.customer.server.scrm.request.ContactInfoRequest;
import com.stbella.customer.server.scrm.request.CustomerStatusChangeEventRequest;
import com.stbella.customer.server.scrm.request.OpportunityTempRequest;
import com.stbella.customer.server.scrm.request.ScrmCheckinStoreRequest;
import com.stbella.customer.server.scrm.request.ScrmCustomerCreateRequest;
import com.stbella.customer.server.scrm.request.ScrmOpportunityActivityRecordRequest;
import com.stbella.customer.server.scrm.request.ScrmUserStoreConfigOperateRequest;
import com.stbella.customer.server.scrm.request.ServiceOpportunityWinRequest;
import com.stbella.customer.server.scrm.vo.ScrmUserInfoVO;
import com.stbella.pulsar.template.PulsarTemplate;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ScrmProducer {

    @Resource
    private PulsarTemplate pulsarTemplate;

    private String PAY_TOPIC = "";

    @Value("${pulsar.nameSpace}")
    private void setNameSpace(String nameSpace) {
        PAY_TOPIC = "persistent://pulsar-44k4paxzz5vg/" + nameSpace + "/scrm_notify";
    }

    /**
     * 客户信息变更
     *
     * @param accountInfoRequest
     * @return
     */
    public Integer accountInfoUpdate(AccountInfoRequest accountInfoRequest) {
        String request = JSONUtil.toJsonStr(accountInfoRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.ACCOUNT_INFO_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm更新用户信息, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);

        return 1;
    }

    /**
     * 活动记录变更
     *
     * @param scrmOpportunityActivityRecordRequest
     */
    public void followUpRecordUpdate(ScrmOpportunityActivityRecordRequest scrmOpportunityActivityRecordRequest) {
        String request = JSONUtil.toJsonStr(scrmOpportunityActivityRecordRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.FOLLOW_UP_RECORD_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm更新活动记录, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 联系人信息变更
     *
     * @param contactInfoRequestList
     * @return
     */
    public Boolean contactUpdate(List<ContactInfoRequest> contactInfoRequestList) {
        String request = JSONUtil.toJsonStr(contactInfoRequestList);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.CONTACT_INFO_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm更新联系人, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
        return true;
    }

    /**
     * 新增商机
     *
     * @param scrmOpportunityRequest
     */
    public void opportunityAdd(SCRMOpportunityRequest scrmOpportunityRequest) {
        String request = JSONUtil.toJsonStr(scrmOpportunityRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_ADD.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm新增商机, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 修改商机
     *
     * @param scrmOpportunityRequest
     */
    public void opportunityUpdate(SCRMOpportunityRequest scrmOpportunityRequest) {
        String request = JSONUtil.toJsonStr(scrmOpportunityRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm修改商机, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 删除商机
     *
     * @param scrmOpportunityRequest
     */
    public void opportunityDelete(SCRMOpportunityRequest scrmOpportunityRequest) {
        String request = JSONUtil.toJsonStr(scrmOpportunityRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_DEL.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm删除商机, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 分配客户团队成员
     * @param distributionCustomerList
     * @return
     */
    public Long distributionCustomerTeamMember(List<ScrmDistributionCustomerDTO> distributionCustomerList) {
        String request = JSONUtil.toJsonStr(distributionCustomerList);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.DISTRIBUTION_CUSTOMER_TEAM_MEMBER.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm分配客户团队成员, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);

        return 1L;
    }

    /**
     * 销售与门店关联关系变更
     *
     * @param scrmUserStoreConfigOperateRequest
     * @return
     */
    public Boolean saleStoreConfigUpdate(ScrmUserStoreConfigOperateRequest scrmUserStoreConfigOperateRequest) {
        String request = JSONUtil.toJsonStr(scrmUserStoreConfigOperateRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.SALE_STORE_CONFIG_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm销售与门店关联关系变更, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);

        return true;
    }

    /**
     * scrm打卡记录信息变更
     *
     * @param scrmCheckinStoreRequest
     * @return
     */
    public Boolean checkinStoreUpdate(ScrmCheckinStoreRequest scrmCheckinStoreRequest) {
        String request = JSONUtil.toJsonStr(scrmCheckinStoreRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.CHECKIN_STORE_CHANGE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm打卡记录变更, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);

        return true;
    }

    /**
     * scrm员工信息变更
     *
     * @param scrmUserInfoRequest
     */
    public void userInfoUpdate(ScrmUserInfoVO scrmUserInfoRequest) {
        String request = JSONUtil.toJsonStr(scrmUserInfoRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.USER_INFO_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm员工信息变更, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 400新增商机请求
     *
     * @param scrmOpportunityRequest
     */
    public void customerAddOpportunity(SCRMOpportunityRequest scrmOpportunityRequest) {
        String request = JSONUtil.toJsonStr(scrmOpportunityRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.CUSTOMER_ADD_OPPORTUNITY.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:400新增商机请求, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 创建客户到scrm
     *
     * @param scrmCustomerCreateRequest
     */
    public void createCustomer2Scrm(ScrmCustomerCreateRequest scrmCustomerCreateRequest) {
        String request = JSONUtil.toJsonStr(scrmCustomerCreateRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.CREATE_CUSTOMER_TO_SCRM.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:创建客户到scrm, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * occ客户探店打卡信息上传
     *
     * @param scrmClockStoreRequest
     * @return
     */
    public Boolean clockStore(SCRMClockStoreRequest scrmClockStoreRequest) {
        String request = JSONUtil.toJsonStr(scrmClockStoreRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OCC_CLOCK_STORE_SIGNIN.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:occ探店打卡上传至scrm, 消息id:{}, 消息内容:{}", messageId,
            pulsarMessageDTO);
        return true;
    }

    /**
     * 商机中间表变更同步
     *
     * @param opportunityTempRequest
     */
    public void opportunityRecordTempUpdate(OpportunityTempRequest opportunityTempRequest) {
        String request = JSONUtil.toJsonStr(opportunityTempRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_RECORD_TEMP_UPDATE.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:scrm商机中间表变更, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 会员赠送产康资产
     * @param customerProductionGiftRequest
     */
    public void memberBirthdayProductionGift(CustomerProductionGiftRequest customerProductionGiftRequest) {
        String request = JSONUtil.toJsonStr(customerProductionGiftRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.MEMBER_BIRTHDAY_PRODUCTION_GIFT.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:会员赠送产康资产, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 同步分娩喜报至scrm
     *
     * @param scrmCustomerBirthDTO
     * @return
     */
    public Long synCustomerChildbirthRoom(ScrmCustomerBirthDTO scrmCustomerBirthDTO) {
        String request = JSONUtil.toJsonStr(scrmCustomerBirthDTO);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.SYNC_CUSTOMER_CHILDBIRTH_ROOM.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:同步分娩喜报, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);

        return 1L;
    }

    /**
     * 发送活动报名列表邮件
     *
     * @param emailRequest
     */
    public void sendEmailActivityPartSignupDetailList(ActivitySignupEmailRequest emailRequest) {
        String request = JSONUtil.toJsonStr(emailRequest);

        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.ACTIVITY_SIGNUP_EMAIL.getCode());
        pulsarMessageDTO.setMessage(request);

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:发送活动报名列表邮件, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 客户商机状态变更
     * @param eventRequest
     */
    public void customerOpportunityStatusChange(CustomerStatusChangeEventRequest eventRequest) {
        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.CUSTOMER_OPPORTUNITY_STATUS_CHANGE.getCode());
        pulsarMessageDTO.setMessage(JSONUtil.toJsonStr(eventRequest));

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:客户商机状态变更, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 400商机更新
     *
     * @param scrmOpportunityRequest
     */
    public void opportunityFor400Update(SCRMOpportunityRequest scrmOpportunityRequest) {
        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_400_UPDATE.getCode());
        pulsarMessageDTO.setMessage(JSONUtil.toJsonStr(scrmOpportunityRequest));

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:400商机更新, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 400商机添加团队成员处理
     *
     * @param dtoList
     */
    public void opportunityTeamMemberHandle(List<ScrmDistributionCustomerDTO> dtoList) {
        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_TEAM_MEMBER_UPDATE.getCode());
        pulsarMessageDTO.setMessage(JSONUtil.toJsonStr(dtoList));

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:400商机更新团队成员, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }

    /**
     * 400商机赢单处理
     *
     * @param request
     */
    public void serviceOpportunityWinHandle(ServiceOpportunityWinRequest request) {
        ScrmPulsarMessageDTO pulsarMessageDTO = new ScrmPulsarMessageDTO();
        pulsarMessageDTO.setType(ScrmPulsarMessageTypeEnum.OPPORTUNITY_400_WIN.getCode());
        pulsarMessageDTO.setMessage(JSONUtil.toJsonStr(request));

        MessageId messageId = pulsarTemplate.send(PAY_TOPIC, JSONUtil.toJsonStr(pulsarMessageDTO));
        log.info("scrmPulsarProducer:400商机赢单处理, 消息id:{}, 消息内容:{}", messageId, pulsarMessageDTO);
    }
}
