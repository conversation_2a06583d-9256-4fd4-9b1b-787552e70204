package com.stbella.customer.server.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.care.server.care.entity.TabClientPO;
import com.stbella.care.server.ecp.TabClientService;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.cts.request.BatchOpportunityRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunitiesWinOrderRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.customer.dto.CustomerAdAppointmentDTO;
import com.stbella.customer.server.customer.enums.ScrmCustomerStatusEnum;
import com.stbella.customer.server.customer.request.ClientSearchRequest;
import com.stbella.customer.server.customer.request.CustomerWechatFansListRequest;
import com.stbella.customer.server.customer.request.store.ClientCreateRequest;
import com.stbella.customer.server.customer.service.CustomerAdAppointmentService;
import com.stbella.customer.server.customer.service.CustomerUserService;
import com.stbella.customer.server.customer.service.CustomerWechatFansService;
import com.stbella.customer.server.customer.vo.ClientSearchVO;
import com.stbella.customer.server.customer.vo.CustomerWechatFansListVO;
import com.stbella.customer.server.ecp.dto.HeStoreMailConfigDTO;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.customer.server.ecp.entity.TabWechatUserPO;
import com.stbella.customer.server.ecp.enums.StoreTypeEnum;
import com.stbella.customer.server.ecp.request.GrantUserBasicQueryRequest;
import com.stbella.customer.server.ecp.service.HeUserBasicService;
import com.stbella.customer.server.ecp.service.TabWechatUserService;
import com.stbella.customer.server.manager.BaseManager;
import com.stbella.customer.server.scrm.component.SnowballEngine;
import com.stbella.customer.server.scrm.component.assembler.NoticeMessageAssembler;
import com.stbella.customer.server.scrm.constant.ScrmQwCustomerMessageConstant;
import com.stbella.customer.server.scrm.convert.SCRMConvert;
import com.stbella.customer.server.scrm.dto.*;
import com.stbella.customer.server.scrm.entity.*;
import com.stbella.customer.server.scrm.enums.*;
import com.stbella.customer.server.scrm.manager.ScrmManager;
import com.stbella.customer.server.scrm.manager.SmsManager;
import com.stbella.customer.server.scrm.mapper.ScrmBusinessOpportunityMapper;
import com.stbella.customer.server.scrm.producer.ScrmProducer;
import com.stbella.customer.server.scrm.request.*;
import com.stbella.customer.server.scrm.service.*;
import com.stbella.customer.server.scrm.utils.FieldConvertUtil;
import com.stbella.customer.server.scrm.vo.*;
import com.stbella.customer.server.scrm.vo.ScrmDescriptionResultVO.FieldData.SelectItemData;
import com.stbella.customer.server.util.BeanMapper;
import com.stbella.customer.server.util.DateUtils;
import com.stbella.notice.enums.NoticeTypeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.redis.service.RedisService;
import com.stbella.scrm.enums.WeWorkAppEnum;
import com.stbella.scrm.request.ScrmSendMessageRequest;
import com.stbella.scrm.service.ScrmMessageService;
import com.stbella.store.server.ecp.entity.CfgStore;
import com.stbella.store.server.ecp.service.CfgStoreService;
import java.text.MessageFormat;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 商机表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-03-13
 */
@Service
@Slf4j
@DubboService
public class ScrmBusinessOpportunityServiceImpl extends ServiceImpl<ScrmBusinessOpportunityMapper, ScrmBusinessOpportunityPO> implements ScrmBusinessOpportunityService {

    @Resource
    private SCRMConvert scrmConvert;
    @Resource
    private FieldConvertUtil fieldConvertUtil;
    @Resource
    private XsyScrmService xsyScrmService;
    @Resource
    private ScrmOpportunityStatusRecordService scrmOpportunityStatusRecordService;
    @Resource
    private ScrmUserStoreConfigService scrmUserStoreConfigService;
    @Resource
    private ScrmOpportunityActivityRecordService scrmOpportunityActivityRecordService;
    @Resource
    private ScrmConfigService scrmConfigService;
    @Resource
    private ScrmCustomerService scrmCustomerService;
    @Resource
    private HeUserBasicService heUserBasicService;
    @DubboReference
    private ScrmMessageService scrmMessageService;
    @DubboReference
    private CfgStoreService cfgStoreService;
    @Resource
    @Lazy
    private ScrmManager scrmManager;
    @Value("${scrm.php_distribution_customer}")
    private String scrmDistributionCustomer;
    @Value("${scrm.php_get_user_info_by_phone}")
    private String getUserInfoByPhone;
    @Resource
    private ScrmCustomerOrderService scrmCustomerOrderService;
    @Resource
    private RedisService redisService;
    @Resource
    private ScrmUserAssignAccountTempRecordService scrmUserAssignAccountTempRecordService;
    @DubboReference
    private TabClientService tabClientService;
    @Resource
    private CustomerUserService userService;
    @Resource
    private com.stbella.customer.server.ecp.service.TabClientService ecpTabClientService;
    @Resource
    private ScrmStaffPhoneConfigService scrmStaffPhoneConfigService;
    @Resource
    private SnowballEngine snowballEngine;
    @Resource
    private SmsManager smsManager;
    @Resource
    private ScrmUserService scrmUserService;
    @Resource
    private NoticeMessageAssembler noticeMessageAssembler;

    @Resource
    private ScrmBrandStoreConfigService scrmBrandStoreConfigService;

    @Resource
    private ScrmTeamMemberRecordService scrmTeamMemberRecordService;

    @Resource
    private ScrmProducer scrmProducer;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private TabWechatUserService tabWechatUserService;

    @Resource
    private CustomerWechatFansService customerWechatFansService;

    @Resource
    private CustomerAdAppointmentService customerAdAppointmentService;

    @Resource
    private BaseManager baseManager;

    @Override
    public void updateOpportunity(SCRMOpportunityRequest scrmOpportunityRequest) {
        log.info("更新商机入参：{}",JSONUtil.toJsonStr(scrmOpportunityRequest));
        //入参
        // 先做一层转换，将scrm的一些值转换为picp所需要的值
        scrmOpportunityRequest = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(scrmOpportunityRequest);
        ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(scrmOpportunityRequest);
        //数据库本地数据
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = this.baseMapper.selectOne(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>().eq(ScrmBusinessOpportunityPO::getScrmId, convertScrmBusinessOpportunityPO.getScrmId()));


        //是否手动绑定订单（如入参）
        boolean manuallyBindOrderReq = ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getMonthOrder()) || ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getNonMonthOrder());
        //是否手动绑定订单（旧订单）
        boolean manuallyBindOrderOld = ObjectUtil.isNotEmpty(scrmBusinessOpportunityPO.getMonthOrder()) || ObjectUtil.isNotEmpty(scrmBusinessOpportunityPO.getNonMonthOrder());
        //新的商机状态
        Long saleStageIdReq = convertScrmBusinessOpportunityPO.getSaleStageId();
        //数据库中的商机状态
        Long saleStageIdOld = scrmBusinessOpportunityPO.getSaleStageId();

        // 旧的商机签约门店, 因为在商机回收或者输单(预算不足或其他情况时)时会将商机签约门店置为0，所以要有个字段来缓存商机的签约门店，以便保存到中间表的时候使用
        Long oldContractedStore = 0L;
        // 是否要使用旧的商机签约门店,
        Boolean needUseOldContractedStore = false;

        // 数据库中存储的商机所有人和部门
        Long oldOwnerId = scrmBusinessOpportunityPO.getOwnerId();
        Long oldDimDepart = scrmBusinessOpportunityPO.getDimDepart();

        // scrm中传递的商机所有人和部门
        Long nowOwnerId = convertScrmBusinessOpportunityPO.getOwnerId();
        Long nowDimDepart = convertScrmBusinessOpportunityPO.getDimDepart();

        //配置表
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        //标准月子-首次触达
        Long saleStagFirst = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()));

        //标准月子-邀约到店
        Long saleStagCheckinStore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()));
        //小月子-邀约到店
        Long salesPhaseLittlemonthInvitationToTheStore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode()));

        // 产康-首次触达
        Long productionSaleStagFirst = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode()));
        // 产康-邀约到店
        Long productionSaleStagCheckinStore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode()));

        /* 2023.06.29 商机的门店不会和本地的对比了
        Boolean needHandleUserAssignTempRecord = false;
        if (ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getContractedStore()) && !convertScrmBusinessOpportunityPO.getContractedStore().equals(scrmBusinessOpportunityPO.getContractedStore())) {
            // 如果商机的门店和本地存储不一样，通知
            needHandleUserAssignTempRecord = true;
        }*/


        //是否创建客户
        Boolean informCustomer = false;
        //重新分配
        boolean reassignment = false;
        //将商机更新到scrm
        boolean updateOpportunity = false;
        ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO = new ScrmOpportunityStatusRecordPO();

        //回收商机：原先是非未领取，现在是未领取
        if(!TerritoryHighSeaStatusEnum.UNCLAIMED.getCode().equals(scrmBusinessOpportunityPO.getTerritoryHighSeaStatus()) && TerritoryHighSeaStatusEnum.UNCLAIMED.getCode().equals(convertScrmBusinessOpportunityPO.getTerritoryHighSeaStatus())){
            log.info("回收商机：原先是非未领取，现在是未领取");
            //needUseOldContractedStore = true;
            //oldContractedStore = convertScrmBusinessOpportunityPO.getContractedStore();
            //清除签约门店字段
            //convertScrmBusinessOpportunityPO.setContractedStore(0L);
            //删除团队成员中的销售
            //xsyScrmService.delTeamMember(scrmBusinessOpportunityPO.getCustomerId(),scrmBusinessOpportunityPO.getOwnerId());
            //将商机的所有者改成管理员
            convertScrmBusinessOpportunityPO.setOwnerId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.HIGH_SEAAS_POOL_MANAGER.getCode())));
            convertScrmBusinessOpportunityPO.setLockStatus(2);
            updateOpportunity = true;
        }


        //重新分配（原先未领取，现在变成已领取就是重新分配）-将当前的销售添加到商机的团队成员中
        /* 2023.06.29 和运营约定，商机不会再分配了。
        if(TerritoryHighSeaStatusEnum.UNCLAIMED.getCode().equals(scrmBusinessOpportunityPO.getTerritoryHighSeaStatus()) && TerritoryHighSeaStatusEnum.RECEIVED.getCode().equals(convertScrmBusinessOpportunityPO.getTerritoryHighSeaStatus())){
            log.info("重新分配（原先未领取，现在变成已领取就是重新分配）-将当前的销售添加到商机的团队成员中");
            //商机销售阶段变成初始状态
            Long entityType = convertScrmBusinessOpportunityPO.getEntityType();
            if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())));
            }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode())));
            }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode())));
            }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode())));
            }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode())));
            }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode())));
            }


            //如果原先有扫码信息需要将扫码信息改成未绑定
            if(ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getSignInInformation()) && convertScrmBusinessOpportunityPO.getSignInInformation()!=0){
                xsyScrmService.signInInformationBindOrUnBindOpportunity(convertScrmBusinessOpportunityPO.getSignInInformation(),1);
            }
            //清除扫码信息
            convertScrmBusinessOpportunityPO.setSignInInformation(0L);
            //分配时间变最新
            convertScrmBusinessOpportunityPO.setAllocateTime(new Date());
            //需要将当前的销售添加到商机的团队成员中
            ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
            scrmAddTeamMemberRequest.setUserId(convertScrmBusinessOpportunityPO.getOwnerId());
            scrmAddTeamMemberRequest.setRecordFrom_data(convertScrmBusinessOpportunityPO.getCustomerId());
            scrmAddTeamMemberRequest.setRecordFrom(Long.parseLong(scrmConfig.getOrDefault(ScrmConfigBizTypeEnum.ACCOUNT_OBJECT_ID.getCode(), "1")));
            scrmAddTeamMemberRequest.setOwnerFlag(1);
            scrmAddTeamMemberRequest.setEntityType(Long.parseLong(scrmConfig.get(ScrmConfigBizTypeEnum.TEAM_MEMBER_DEFAULT_TYPE.getCode())));
            xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);
            updateOpportunity = true;
            reassignment = true;

            //给当前销售和销售的上级发送消息
            log.info("重新分配发送消息");
            Long ownerId = convertScrmBusinessOpportunityPO.getOwnerId();
            ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(ownerId);
            String phone = userInfo.getPhone();
            String managerPhone = userInfo.getManagerPhone();
            ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
            scrmSendMessageRequest.setCustomerName("");
            if(StringUtils.isNotEmpty(phone) && phone.equals(managerPhone)){
                scrmSendMessageRequest.setTouser(Arrays.asList(phone));
            }else{
                scrmSendMessageRequest.setTouser(Arrays.asList(phone,managerPhone));
            }
            Long customerId = convertScrmBusinessOpportunityPO.getCustomerId();
            ScrmCustomerPO byScrmCustomerId = scrmCustomerService.getByScrmCustomerId(customerId);
            if(ObjectUtil.isNotEmpty(byScrmCustomerId)){
                String content = String.format(ScrmQwCustomerMessageConstant.SEND_OPPORTUNITY_MESSAGE, byScrmCustomerId.getName(), convertScrmBusinessOpportunityPO.getOpportunityName(), byScrmCustomerId.getPhone());
                scrmSendMessageRequest.setContent(content);
            }else{
                scrmSendMessageRequest.setContent("你有一条新的销售机会，请立即点击查看并跟进客户。");
            }
            scrmMessageService.sendMessage(scrmSendMessageRequest);

            //获取这个商机所有的活动记录，并将这些活动记录的ownerId改成新的销售
            xsyScrmService.batchUpdateActivityOwnerId(convertScrmBusinessOpportunityPO.getScrmId(),convertScrmBusinessOpportunityPO.getOwnerId());

            List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.infoListByUserId(convertScrmBusinessOpportunityPO.getOwnerId());
            if(CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS)){
                if(scrmUserStoreConfigPOS.size()==1){
                    convertScrmBusinessOpportunityPO.setContractedStore(scrmUserStoreConfigPOS.get(0).getScrmRecordId());
                }
            }

            needHandleUserAssignTempRecord = true;
        }*/

        // 需要管理400分配客资中间表
        /* 2023.06.29 和运营约定，商机编辑时不会更新客资分配表了，如有需要，则直接读商机中的相关字段
        if (needHandleUserAssignTempRecord && Objects.equals(convertScrmBusinessOpportunityPO.getEntityType(), new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))) {
            scrmUserAssignAccountTempRecordService.handleTempRecord(scrmOpportunityRequest.getUpdatedBy(),
                convertScrmBusinessOpportunityPO.getOwnerId(),
                scrmOpportunityRequest.getCustomerId(),
                scrmOpportunityRequest.getId(),
                convertScrmBusinessOpportunityPO.getContractedStore());
        }*/

        //商机邀约到店-商机有签到的信息，且销售状态是首次触达，需要将商机阶段改为邀约到店
        if(!reassignment && ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getSignInInformation())
            && (saleStagFirst.equals(convertScrmBusinessOpportunityPO.getSaleStageId()) || productionSaleStagFirst.equals(convertScrmBusinessOpportunityPO.getSaleStageId()))) {
            log.info("商机邀约到店-商机有签到的信息，且销售状态是首次触达");
            if (convertScrmBusinessOpportunityPO.getEntityType().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))) {
                // 产康商机的到店
                convertScrmBusinessOpportunityPO.setSaleStageId(productionSaleStagCheckinStore);
            } else {
                convertScrmBusinessOpportunityPO.setSaleStageId(saleStagCheckinStore);
            }

            //将这条签到信息的绑定商机状态改成已绑定
            xsyScrmService.signInInformationBindOrUnBindOpportunity(convertScrmBusinessOpportunityPO.getSignInInformation(),2);
            updateOpportunity = true;
        }

        //编辑商机，扫码信息A-扫码信息B
        Long reqSign = convertScrmBusinessOpportunityPO.getSignInInformation();
        Long sqlSign = scrmBusinessOpportunityPO.getSignInInformation();
        //两次扫码信息不一致
        if(ObjectUtil.isAllNotEmpty(reqSign,sqlSign) && !reqSign.equals(sqlSign)){
            log.info("两次扫码信息不一致");
            //将这条签到信息的绑定商机状态改成已绑定
            xsyScrmService.signInInformationBindOrUnBindOpportunity(reqSign,2);
            //将原先的签到信息改成未绑定
            xsyScrmService.signInInformationBindOrUnBindOpportunity(sqlSign,1);
            updateOpportunity = true;

            ScrmCreateClockInStoreDTO scrmCreateClockInStoreRequest = xsyScrmService.getSignInInformation(reqSign);
            //同时更新商机中间表中的到店时间
            updateOpportunityStatusFinishTime(convertScrmBusinessOpportunityPO.getScrmId(), OpportunityStatusSaleStageEnum.ARRIVAL.getCode(), scrmCreateClockInStoreRequest.getSignTime());
        }

        //商机手动赢单
        if(manuallyBindOrderReq){
            log.info("商机手动赢单");

            Long order = null;
            Long oldOrder = null;

            //根据商机类型判断当前的赢单
            Long entityType = convertScrmBusinessOpportunityPO.getEntityType();
            if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                //获取所有这个客户正在进行中的商机（标准月子）
                List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = this.listByCustomerId(convertScrmBusinessOpportunityPO.getCustomerId());
                for (ScrmBusinessOpportunityPO businessOpportunityPO : scrmBusinessOpportunityPOS) {
                    if(!Objects.equals(businessOpportunityPO.getScrmId(), convertScrmBusinessOpportunityPO.getScrmId())){
                        //走输单逻辑
                        //输单
                        //默认输单：已签其他门店
                        businessOpportunityPO.setReason(OpportunityLoseReasonEnum.OTHER_STORES_HAVE_BEEN_SIGNED.getCode());
                        businessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode())));
                        businessOpportunityPO.setStatus(3);
                    }
                }

                //查询需要更新为输单的销售机会
                List<ScrmBusinessOpportunityPO> updateScrmBusinessOpportunityPOS = scrmBusinessOpportunityPOS.stream().filter(s -> !Objects.equals(s.getScrmId(), convertScrmBusinessOpportunityPO.getScrmId())).collect(Collectors.toList());
                log.info("查询需要更新为输单的销售机会=>{}", JSONUtil.toJsonStr(updateScrmBusinessOpportunityPOS));
                if (CollectionUtil.isNotEmpty(updateScrmBusinessOpportunityPOS)) {
                    this.updateBatchById(updateScrmBusinessOpportunityPOS);
                }

                //推送scrm 更新输单
                for (ScrmBusinessOpportunityPO businessOpportunityPO : scrmBusinessOpportunityPOS) {

                    if(!Objects.equals(businessOpportunityPO.getScrmId(), convertScrmBusinessOpportunityPO.getScrmId())){
                        //推送到SCRM
                        OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(businessOpportunityPO);
                        opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);
                        xsyScrmService.opportunityEdit(opportunityRequest);
                        //创建中间表
                        saveOpportunityStatus(
                                businessOpportunityPO.getScrmId(),
                                businessOpportunityPO.getOpportunityName(),
                                businessOpportunityPO.getSaleStageId(),
                                businessOpportunityPO.getContractedStore(),
                                businessOpportunityPO.getOwnerId(),
                                businessOpportunityPO.getDimDepart(),
                                businessOpportunityPO.getEntityType(),
                                businessOpportunityPO.getLockStatus(),
                                null
                        );
                    }
                }

                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())));
                convertScrmBusinessOpportunityPO.setWinRate(100L);
                convertScrmBusinessOpportunityPO.setStatus(2);
                order = convertScrmBusinessOpportunityPO.getMonthOrder();
                oldOrder = scrmBusinessOpportunityPO.getMonthOrder();
            }else{
                order = convertScrmBusinessOpportunityPO.getNonMonthOrder();
                oldOrder = scrmBusinessOpportunityPO.getNonMonthOrder();
            }

            //修改数据库的订单的是否关联商机数据
            ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.selectByScrmId(order);
            //更新数据库的订单的时候
            scrmCustomerOrderPO.setSaleChange(2);
            scrmCustomerOrderService.updateById(scrmCustomerOrderPO);
            //同步到scrm
            scrmManager.order2Scrm(scrmCustomerOrderPO);

            //赢单后将商机的预计签单金额改成订单的签单金额
            convertScrmBusinessOpportunityPO.setMoney(scrmCustomerOrderPO.getPayAmount());

            //判断当前的订单和之前的是否是同一个，不是同一个需要解锁之前的订单
            if(ObjectUtil.isNotEmpty(oldOrder) && !oldOrder.equals(order)){
                ScrmCustomerOrderPO oldOrderPO = scrmCustomerOrderService.selectByScrmId(oldOrder);
                //更新数据库的订单的时候
                oldOrderPO.setSaleChange(1);
                scrmCustomerOrderService.updateById(oldOrderPO);
                //同步到scrm
                scrmManager.order2Scrm(oldOrderPO);

                //同时更新商机中间表中的签单时间
                updateOpportunityStatusFinishTime(convertScrmBusinessOpportunityPO.getScrmId(), OpportunityStatusSaleStageEnum.WIN.getCode(), scrmCustomerOrderPO.getPercentFirstTime());
            }

            updateOpportunity = true;
        }

        //数据库的商机不是输单，入参是输单，赢率变0
        if(
                !scrmBusinessOpportunityPO.getSaleStageId().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()))) &&
                convertScrmBusinessOpportunityPO.getSaleStageId().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()))) &&
                convertScrmBusinessOpportunityPO.getWinRate() == 0L
        ){
            log.info("数据库的商机不是输单，入参是输单，赢率变0");
            //预算不足或者其他
            if(
                    OpportunityLoseReasonEnum.BUDGET_SHORTFALL.getCode().equals(convertScrmBusinessOpportunityPO.getReason()) ||
                            OpportunityLoseReasonEnum.OTHER.getCode().equals(convertScrmBusinessOpportunityPO.getReason())
            ){
                //放到公海池
                convertScrmBusinessOpportunityPO.setTerritoryHighSeaStatus(TerritoryHighSeaStatusEnum.UNCLAIMED.getCode());
                xsyScrmService.reclaimBusinessOpportunity(convertScrmBusinessOpportunityPO.getScrmId());

                //将商机的所有者改成管理员
                convertScrmBusinessOpportunityPO.setOwnerId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.HIGH_SEAAS_POOL_MANAGER.getCode())));
                convertScrmBusinessOpportunityPO.setLockStatus(2);
                 /* 2023.06.29 和运营约定，商机在手动输单并回收到公海池时，不会再清空签约门店等字段
                //清除签约门店字段
                needUseOldContractedStore = true;
                oldContractedStore = convertScrmBusinessOpportunityPO.getContractedStore();
                convertScrmBusinessOpportunityPO.setContractedStore(0L);
                 */
            }
            updateOpportunity = true;
        }

        convertScrmBusinessOpportunityPO.setId(scrmBusinessOpportunityPO.getId());

        //原先状态为赢单
        if(convertScrmBusinessOpportunityPO.getStatus()==2){
            //锁定商机
            convertScrmBusinessOpportunityPO.setLockStatus(2);
        }

        // 商机阶段是否是属于退回的状态
        Boolean isReturnOpportunity = false;

        // 原先商机有扫码信息，且状态是邀约到店（小月子、标注月子），现在没有扫码信息且状态是邀约到店，需要将商机接单改成初始状态，赢率改成10
        List<Long> checkinStoreStagList = Arrays.asList(saleStagCheckinStore, salesPhaseLittlemonthInvitationToTheStore, productionSaleStagCheckinStore);
        if (ObjectUtil.isNotEmpty(scrmBusinessOpportunityPO.getSignInInformation())
            && checkinStoreStagList.contains(scrmBusinessOpportunityPO.getSaleStageId())
            && ObjectUtil.isEmpty(convertScrmBusinessOpportunityPO.getSignInInformation())
            && checkinStoreStagList.contains(convertScrmBusinessOpportunityPO.getSaleStageId())
        ) {
            log.info("原先商机有扫码信息，且状态是邀约到店（小月子、标准月子、产康），现在没有扫码信息且状态是邀约到店，需要将商机接单改成初始状态，赢率改成10");
            Long entityType = convertScrmBusinessOpportunityPO.getEntityType();

            if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(saleStagFirst);
            }

            if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                convertScrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode())));
            }

            if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))) {
                convertScrmBusinessOpportunityPO.setSaleStageId(productionSaleStagFirst);
            }

            convertScrmBusinessOpportunityPO.setWinRate(10L);
            //解绑原先的扫码信息
            xsyScrmService.signInInformationBindOrUnBindOpportunity(scrmBusinessOpportunityPO.getSignInInformation(),1);
            updateOpportunity = true;

            //同时删除商机中间表中的到店状态
            deleteOpportunityStatus(convertScrmBusinessOpportunityPO.getScrmId(), OpportunityStatusSaleStageEnum.ARRIVAL.getCode());
            isReturnOpportunity = true;
        }

        //原先商机有订单信息、且为赢单（全部订单），更新后没有订单信息，如果有扫码信息，退回到邀约到店（80%），没有则初始状态（10%）
        if (manuallyBindOrderOld && !manuallyBindOrderReq) {
            log.info("原先商机有订单信息、且为赢单（全部订单），更新后没有订单信息，如果有扫码信息，退回到邀约到店（80%），没有则初始状态（10%）");

            // 是否删除到店中间表
            Boolean deleteArrival = false;
            if (ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getSignInInformation())) {
                //改成邀约到店
                convertScrmBusinessOpportunityPO.setSaleStageId(getSaleStageBySaleStage(scrmBusinessOpportunityPO.getSaleStageId(), scrmConfig, 2));
                //赢率80%
                convertScrmBusinessOpportunityPO.setWinRate(80L);
            }else{
                //改成首次触达
                convertScrmBusinessOpportunityPO.setSaleStageId(getSaleStageBySaleStage(scrmBusinessOpportunityPO.getSaleStageId(), scrmConfig, 1));
                //赢率10%
                convertScrmBusinessOpportunityPO.setWinRate(10L);
                deleteArrival = true;
            }

            //解绑订单
            Long order = ObjectUtil.isNotEmpty(scrmBusinessOpportunityPO.getMonthOrder()) ? scrmBusinessOpportunityPO.getMonthOrder() : scrmBusinessOpportunityPO.getNonMonthOrder();
            ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.selectByScrmId(order);
            //更新数据库的订单的时候
            scrmCustomerOrderPO.setSaleChange(1);
            scrmCustomerOrderService.updateById(scrmCustomerOrderPO);
            //同步到scrm
            scrmManager.order2Scrm(scrmCustomerOrderPO);

            updateOpportunity = true;

            //同时删除商机中间表中的签单状态或者到店状态
            if (deleteArrival) {
                deleteOpportunityStatus(convertScrmBusinessOpportunityPO.getScrmId(), OpportunityStatusSaleStageEnum.ARRIVAL.getCode());
            }

            deleteOpportunityStatus(convertScrmBusinessOpportunityPO.getScrmId(), OpportunityStatusSaleStageEnum.WIN.getCode());

            isReturnOpportunity = true;
        }

        // 如果商机的所属人变更，则更新对应商机中间表中所有的所属人
        if (!oldOwnerId.equals(nowOwnerId)) {
            scrmOpportunityStatusRecordService.updateOwnerId(scrmBusinessOpportunityPO.getScrmId(), nowOwnerId, nowDimDepart);

            // 将新的商机所属人拉入客户的团队成员
            // 将销售加到客户的团队成员中
            ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
            scrmAddTeamMemberRequest.setUserId(nowOwnerId);
            // 客户默认为1
            scrmAddTeamMemberRequest.setRecordFrom(1L);
            scrmAddTeamMemberRequest.setRecordFrom_data(scrmBusinessOpportunityPO.getCustomerId());
            scrmAddTeamMemberRequest.setOwnerFlag(1);
            log.info("商机转移，将新的所有人拉入客户的团队成员={}", JSONUtil.parse(scrmAddTeamMemberRequest));
            xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);

            // 添加团队成员到记录表中
            ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
                .recordId(scrmAddTeamMemberRequest.getRecordFrom_data())
                .userId(scrmAddTeamMemberRequest.getUserId())
                .type(1)
                .build();

            scrmTeamMemberRecordService.edit(recordRequest);

            // 修改商机所属门店
            if (ObjectUtil.isNotNull(scrmBusinessOpportunityPO.getContractedStore())) {
                ScrmUserStoreConfigPO oldUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(
                    scrmBusinessOpportunityPO.getContractedStore());
                if (ObjectUtil.isNotNull(oldUserStoreConfigPO)) {
                    // 门店关系不存在，需要新增
                    ScrmUserPO nowScrmUserPO = scrmUserService.queryByScrmId(nowOwnerId);
                    if (ObjectUtil.isNotNull(nowScrmUserPO)) {
                        ScrmUserStoreConfigPO newScrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(nowScrmUserPO, oldUserStoreConfigPO.getStoreId().intValue());
                        if (ObjectUtil.isNotNull(newScrmUserStoreConfig)) {
                            convertScrmBusinessOpportunityPO.setContractedStore(newScrmUserStoreConfig.getScrmRecordId());
                            updateOpportunity = true;
                        }
                    }
                }
            }
        }

        //更新到我们的数据库
        this.baseMapper.updateById(convertScrmBusinessOpportunityPO);

        if(updateOpportunity){
            //更新商机状态
            OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(convertScrmBusinessOpportunityPO);
            opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);
            xsyScrmService.opportunityEdit(opportunityRequest);
        }

        //判断如果数据库的销售阶段和最新的销售是否不一致
        if(!convertScrmBusinessOpportunityPO.getSaleStageId().equals(scrmBusinessOpportunityPO.getSaleStageId())){
            Date realFinishTime = null;

            if (Arrays.asList(saleStagCheckinStore, productionSaleStagCheckinStore).contains(convertScrmBusinessOpportunityPO.getSaleStageId())
                && Objects.nonNull(convertScrmBusinessOpportunityPO.getSignInInformation())) {
                // 当前最新的商机状态为已到店，则商机的到店时间为打卡的时间
                ScrmCreateClockInStoreDTO scrmCreateClockInStoreRequest = xsyScrmService.getSignInInformation(convertScrmBusinessOpportunityPO.getSignInInformation());
                realFinishTime = scrmCreateClockInStoreRequest.getSignTime();
            }

            if (convertScrmBusinessOpportunityPO.getSaleStageId().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())))
                && ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getMonthOrder())) {
                // 当前最新的商机状态为已签单，则商机的签约时间为订单的计入业绩时间
                ScrmCustomerOrderPO scrmCustomerOrderPO = scrmCustomerOrderService.selectByScrmId(convertScrmBusinessOpportunityPO.getMonthOrder());
                realFinishTime = scrmCustomerOrderPO.getPercentFirstTime();
            }

            // 需要判断是不是回退到首次触达
            if (isReturnOpportunity && convertScrmBusinessOpportunityPO.getSaleStageId().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())))) {
                log.info("该商机id:{}的首次触达为退回商机导致，故不做触达时间的变更", convertScrmBusinessOpportunityPO.getScrmId());
            } else {
                saveOpportunityStatus(
                    scrmOpportunityRequest.getId(),
                    convertScrmBusinessOpportunityPO.getOpportunityName(),
                    convertScrmBusinessOpportunityPO.getSaleStageId(),
                    needUseOldContractedStore ? oldContractedStore : convertScrmBusinessOpportunityPO.getContractedStore(),
                    convertScrmBusinessOpportunityPO.getOwnerId(),
                    convertScrmBusinessOpportunityPO.getDimDepart(),
                    convertScrmBusinessOpportunityPO.getEntityType(),
                    convertScrmBusinessOpportunityPO.getLockStatus(),
                    realFinishTime
                );
            }
        }

        //如果数据库的签约门店和入参的签约门店不一致, 但是如果是因为输单原因导致的商机的所属门店为空，则不更新中间表
        if (!needUseOldContractedStore
            && (ObjectUtil.isEmpty(scrmBusinessOpportunityPO.getContractedStore()) || !scrmBusinessOpportunityPO.getContractedStore().equals(convertScrmBusinessOpportunityPO.getContractedStore()))) {
            log.info("如果数据库的签约门店和入参的签约门店不一致");
            //通知客户
            scrmAddOrUpdateClient(convertScrmBusinessOpportunityPO);
            /*
            //取商机变更记录表最新的记录进行更新
            ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO1 = scrmOpportunityStatusRecordService.selectByIdAndStageId(scrmOpportunityRequest.getId(), getOpportunityStatus(scrmOpportunityRequest.getSaleStageId()).longValue());
            if(ObjectUtil.isNotEmpty(scrmOpportunityStatusRecordPO1)){
                scrmOpportunityStatusRecordPO1.setScrmUserStoreConfigId(convertScrmBusinessOpportunityPO.getContractedStore());
                //更新到scrm
                updateOpportunityStatus(scrmOpportunityStatusRecordPO1);
                //更新到我们的数据库
                scrmOpportunityStatusRecordService.updateById(scrmOpportunityStatusRecordPO1);
            }*/
        }
    }


    /**
     * 判断商机是否签单
     * @return
     */
    private Boolean opportunityWin(Long saleStage,Map<Integer, String> scrmConfig){
       return saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())))||
               saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode())))||
               saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode())))||
               saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode())))||
               saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode())))||
               saleStage.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode())));
    }

    /**
     * 根据原来的商机阶段，获取对应新的商机阶段
     * @param saleStage
     * @param scrmConfig
     * @return
     */
    private Long getSaleStageBySaleStage(Long saleStage,Map<Integer, String> scrmConfig,Integer type){

        Long saleStagFirst = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()));
        Long saleStagCheckinStore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()));
        Long saleStagWinOrder = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode()));
        Long saleStagLoseOrder = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()));

        Long salesPhaseSmallmonthFirsttouch = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode()));
        Long salesPhaseLittlemonthInvitationtothestore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode()));
        Long salesPhaseSmallmonthWinsingle = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode()));
        Long salesStageSmallmonthLosethebill = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_SMALLMONTH_LOSETHEBILL.getCode()));

        Long salesPhaseNurseassignmentFirsttouch = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode()));
        Long salesPhaseNurseassignmentWinasingle = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode()));
        Long salesPhaseNurseassignmentLosingorders = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS.getCode()));

        Long salesPhaseProductionFirsttouch = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode()));
        Long salesPhaseProductionInvitationtothestore = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode()));
        Long salesPhaseProductionWinsingle = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode()));
        Long salesStageProductionDelivery = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_PRODUCTION_DELIVERY.getCode()));

        Long salesPhaseSbarFirsttouch = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode()));
        Long salesPhaseSbarWinningorder = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode()));
        Long salesPhaseSbarTolosetheorder = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_TOLOSETHEORDER.getCode()));

        Long salesPhaseOtherFirsttouch = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode()));
        Long salesPhaseOtherWinningsingle = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode()));
        Long salesPhaseOtherLosingorders = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_LOSINGORDERS.getCode()));



        if(saleStage.equals(saleStagFirst) || saleStage.equals(saleStagCheckinStore) || saleStage.equals(saleStagWinOrder) || saleStage.equals(saleStagLoseOrder) ){
            switch (type){
                case 1:return saleStagFirst;
                case 2:return saleStagCheckinStore;
                case 3:return saleStagWinOrder;
                case 4:return saleStagLoseOrder;
            }
        }

        if(saleStage.equals(salesPhaseSmallmonthFirsttouch) || saleStage.equals(salesPhaseLittlemonthInvitationtothestore) || saleStage.equals(salesPhaseSmallmonthWinsingle) || saleStage.equals(salesStageSmallmonthLosethebill) ){
            switch (type){
                case 1:return salesPhaseSmallmonthFirsttouch;
                case 2:return salesPhaseLittlemonthInvitationtothestore;
                case 3:return salesPhaseSmallmonthWinsingle;
                case 4:return salesStageSmallmonthLosethebill;
            }
        }

        if(saleStage.equals(salesPhaseNurseassignmentFirsttouch) || saleStage.equals(salesPhaseNurseassignmentWinasingle) || saleStage.equals(salesPhaseNurseassignmentLosingorders) ){
            switch (type){
                case 1:return salesPhaseNurseassignmentFirsttouch;
                case 3:return salesPhaseNurseassignmentWinasingle;
                case 4:return salesPhaseNurseassignmentLosingorders;
            }
        }

        if(saleStage.equals(salesPhaseProductionFirsttouch) || saleStage.equals(salesPhaseProductionInvitationtothestore) || saleStage.equals(salesPhaseProductionWinsingle) || saleStage.equals(salesStageProductionDelivery) ){
            switch (type){
                case 1:return salesPhaseProductionFirsttouch;
                case 2:return salesPhaseProductionInvitationtothestore;
                case 3:return salesPhaseProductionWinsingle;
                case 4:return salesStageProductionDelivery;
            }
        }

        if(saleStage.equals(salesPhaseSbarFirsttouch) || saleStage.equals(salesPhaseSbarWinningorder) || saleStage.equals(salesPhaseSbarTolosetheorder) ){
            switch (type){
                case 1:return salesPhaseSbarFirsttouch;
                case 3:return salesPhaseSbarWinningorder;
                case 4:return salesPhaseSbarTolosetheorder;
            }
        }

        if(saleStage.equals(salesPhaseOtherFirsttouch) || saleStage.equals(salesPhaseOtherWinningsingle) || saleStage.equals(salesPhaseOtherLosingorders) ){
            switch (type){
                case 1:return salesPhaseOtherFirsttouch;
                case 3:return salesPhaseOtherWinningsingle;
                case 4:return salesPhaseOtherLosingorders;
            }
        }
        return null;
    }

    @Override
    public Result distributeSaleScreateOpportunities(List<ScrmDistributionCustomerDTO> scrmDistributionCustomerDTOList) {
        try{

            Set<String> phoneList = new HashSet<>();
            for (ScrmDistributionCustomerDTO scrmAutoOpportunityRequest : scrmDistributionCustomerDTOList) {

                Long ownerId = scrmAutoOpportunityRequest.getOwnerId();
                ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(ownerId);

                if(ObjectUtil.isNotNull(userInfo)){
                    List<String> responsibility = userInfo.getResponsibility();

                    List<String> sale = responsibility.stream().filter(r -> r.contains("销售")).collect(Collectors.toList());

                    if(CollectionUtil.isNotEmpty(sale)){

                        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

                        Long saleId = scrmAutoOpportunityRequest.getOwnerId();
                        Long customerId = scrmAutoOpportunityRequest.getUserId();

                        //判断这个销售目前有多少个门店，如果只有一个门店就去记录表id，如果多个就空着
                        String phone = null;
                        Long saleStoreRecordId = null;
                        Long storeId = null;
                        List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.infoListByUserId(saleId);
                        if (CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS)) {
                            phone = scrmUserStoreConfigPOS.get(0).getPhone();
                            // 将scrmPhone转换为picpPhone
                            String picpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(phone);
                            if (ObjectUtil.isNotNull(picpPhone)) {
                                phone = picpPhone;
                            }

                            ScrmUserStoreConfigPO scrmUserStoreConfigPO = null;
                            if (scrmUserStoreConfigPOS.size() == 1) {
                                scrmUserStoreConfigPO = scrmUserStoreConfigPOS.get(0);
                            } else {
                                List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS1 = scrmUserStoreConfigPOS.stream()
                                    .filter(i -> i.getIsAffiliatedStore().equals(2))
                                    .collect(Collectors.toList());

                                if (CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS1)) {
                                    scrmUserStoreConfigPO = scrmUserStoreConfigPOS1.get(0);
                                }
                            }

                            if (ObjectUtil.isNotNull(scrmUserStoreConfigPO)) {
                                // opportunityRequest.setCustomItem174__c(scrmUserStoreConfigPOS.get(0).getScrmRecordId());
                                saleStoreRecordId = scrmUserStoreConfigPO.getScrmRecordId();
                                storeId = scrmUserStoreConfigPO.getStoreId();

                                //通知客户（自建商机的时候如果销售只有一条记录，就通知客户）
                                ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO  = new ScrmBusinessOpportunityPO();
                                convertScrmBusinessOpportunityPO.setCustomerId(customerId);
                                convertScrmBusinessOpportunityPO.setContractedStore(scrmUserStoreConfigPO.getScrmRecordId());
                                scrmAddOrUpdateClient(convertScrmBusinessOpportunityPO);
                            }
                        }


                        //同个客户、同个销售如果有进行中的商机就不再创建商机
                        List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = null;
                        /* 2024.03.11 取消客户商机与门店直接的关联，只与客户和销售有关联
                        if (ObjectUtil.isNotNull(saleStoreRecordId)) {
                            // 销售只有一个门店
                            // 自动创建逻辑：只要【同一门店、同一客户】有商机，则不自动创建；
                            List<ScrmUserStoreConfigPO> userStoreList = scrmUserStoreConfigService.listByStore(storeId.intValue());
                            List<Long> storeScrmRecordIds = userStoreList.stream()
                                .map(ScrmUserStoreConfigPO::getScrmRecordId)
                                .collect(Collectors.toList());

                            scrmBusinessOpportunityPOS = this.baseMapper.selectList(
                                new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                                    .eq(ScrmBusinessOpportunityPO::getCustomerId, customerId)
                                    .in(ScrmBusinessOpportunityPO::getContractedStore, storeScrmRecordIds)
                                    .in(ScrmBusinessOpportunityPO::getSaleStageId,Arrays.asList(
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()),
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()),
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())
                                    ))
                            );
                        } else {
                            // 销售负责多门店
                            // 自动创建逻辑：只要【同一客户、同一销售】有商机，则不会创建
                            scrmBusinessOpportunityPOS = this.baseMapper.selectList(
                                new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                                    .eq(ScrmBusinessOpportunityPO::getCustomerId, customerId)
                                    .eq(ScrmBusinessOpportunityPO::getOwnerId,saleId)
                                    .in(ScrmBusinessOpportunityPO::getSaleStageId,Arrays.asList(
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()),
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()),
                                        scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())
                                    ))
                            );
                        }*/
                        // 自动创建逻辑：只要【同一客户、同一销售】有商机，则不会创建
                        scrmBusinessOpportunityPOS = this.baseMapper.selectList(
                            new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                                .eq(ScrmBusinessOpportunityPO::getCustomerId, customerId)
                                .eq(ScrmBusinessOpportunityPO::getOwnerId,saleId)
                                .in(ScrmBusinessOpportunityPO::getSaleStageId,Arrays.asList(
                                    scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()),
                                    scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())
                                ))
                        );
                        if(CollectionUtil.isEmpty(scrmBusinessOpportunityPOS)){
                            OpportunityRequest opportunityRequest = new OpportunityRequest();
                            opportunityRequest.setOwnerId(saleId);
                            opportunityRequest.setAccountId(customerId);
                            opportunityRequest.setStageUpdatedAt(System.currentTimeMillis());
                            opportunityRequest.setCreatedAt(System.currentTimeMillis());
                            opportunityRequest.setUpdatedAt(System.currentTimeMillis());
                            opportunityRequest.setDimDepart(userInfo.getDimDepart());
                            opportunityRequest.setCustomItem174__c(null);

                            //获取客户名称
                            ScrmCustomerPO byScrmCustomerId = scrmCustomerService.getByScrmCustomerId(scrmAutoOpportunityRequest.getUserId());
                            if(ObjectUtil.isNotNull(byScrmCustomerId)){
                                opportunityRequest.setOpportunityName(byScrmCustomerId.getName()+"标准月子订单");
                            }

                            opportunityRequest.setCustomItem174__c(saleStoreRecordId);

                            //找不到手机号就默认圣贝拉
                            opportunityRequest.setStoreType(0);

                            if(StringUtils.isNotEmpty(phone)){
                                EcpUserInfoVO ccpUserInfoByPhone = getCcpUserInfoByPhone(phone);//根据销售手机号获取ecp中的门店
                                if(ObjectUtil.isNotEmpty(ccpUserInfoByPhone)){
                                    opportunityRequest.setStoreType(ccpUserInfoByPhone.getType()==0?0:1);
                                }
                            }

                            // 如果所属门店不为空，则获取所属门店对应的品牌门店
                            if (ObjectUtil.isNotNull(saleStoreRecordId)) {
//                                Long brandStoreId = xsyScrmService.queryStoreBrandRecordIdBySaleStoreId(saleStoreRecordId);
//                                opportunityRequest.setStore__c(brandStoreId);
                                ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(saleStoreRecordId);
                                if (Objects.nonNull(scrmUserStoreConfigPO)) {
                                    ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(
                                        scrmUserStoreConfigPO.getStoreId().intValue());
                                    if (Objects.nonNull(scrmBrandStoreConfigPO)) {
                                        opportunityRequest.setStore__c(scrmBrandStoreConfigPO.getScrmId());
                                    }
                                }
                            }

                            Long opportunityId = xsyScrmService.opportunityEdit(opportunityRequest);
                            //获取商机详情保存到我们的数据库
                            ScrmOpportunityDetailVO scrmOpportunityDetailVO = xsyScrmService.queryOpportunityDetail(opportunityId);
                            scrmOpportunityDetailVO.setCustomerId(scrmAutoOpportunityRequest.getUserId());
                            SCRMOpportunityRequest scrmOpportunityRequest = scrmConvert.scrmOpportunityDetailVO2SCRMOpportunityRequest(scrmOpportunityDetailVO);
                            //存状态变更记录表
                            add(scrmOpportunityRequest);

                            // 保存400分配客资记录
                            // 先获取团队成员记录，从而获取分配人id，也就是团队成员中的createdBy
                            log.info("查看团队成员的分配信息，获取分配人id");
                            ScrmTeamMemberObjectVO scrmTeamMemberObjectVO = xsyScrmService.queryTeamMemberInfo(
                                Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.ACCOUNT_OBJECT_ID.getCode())),
                                saleId, customerId);

                            if (ObjectUtil.isNotEmpty(scrmTeamMemberObjectVO)) {
                                scrmUserAssignAccountTempRecordService.handleTempRecord(scrmTeamMemberObjectVO.getCreatedBy(),
                                    saleId, customerId, opportunityId, opportunityRequest.getCustomItem174__c(), null);
                            }
                        }
                        //给当前销售和销售的上级发送消息
                        //将scrmPhone转为picpPhone
                        String selfPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getPhone());
                        if (ObjectUtil.isNotNull(selfPicpPhone)) {
                            phoneList.add(selfPicpPhone);
                        } else {
                            phoneList.add(userInfo.getPhone());
                        }

                        String managerPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getManagerPhone());
                        if (ObjectUtil.isNotNull(managerPicpPhone)) {
                            phoneList.add(managerPicpPhone);
                        } else {
                            phoneList.add(userInfo.getManagerPhone());
                        }
                    }

                }

            }

            ArrayList<String> strings = new ArrayList<>(phoneList);
            if(CollectionUtil.isNotEmpty(strings)){
                ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
                scrmSendMessageRequest.setCustomerName("");
                scrmSendMessageRequest.setContent("你有一条新的销售机会，请立即点击查看并跟进客户。");
                scrmSendMessageRequest.setTouser(new ArrayList<>(phoneList));
                scrmMessageService.sendMessage(scrmSendMessageRequest);
            }

            return Result.success();
        }catch (Exception e){
            log.error(e.getMessage());
            return Result.failed(e.getMessage());
        }
    }

    /**
     * 商机自动赢单匹配逻辑：销售id+客户id+关联表id（如果同一个客户+同一个销售+同一个关联表id有多条数据，取最新一条）
     * @param scrmOpportunitiesWinOrderRequest
     * @return
     */
    @Override
    public Long opportunitiesAutoWinOrders(SCRMOpportunitiesWinOrderRequest scrmOpportunitiesWinOrderRequest) {
        log.info("商机自动赢单入参：{}"+JsonUtil.write(scrmOpportunitiesWinOrderRequest));

        //处理过的订单不再处理
        Long scrmOrderId = scrmOpportunitiesWinOrderRequest.getScrmOrderId();

        Object cacheObject = redisService.getCacheObject("WinOrder:" + scrmOrderId);

        if(ObjectUtil.isNotEmpty(cacheObject)){
            //已经处理过了
            return null;
        }else{
            redisService.setCacheObject("WinOrder:" + scrmOrderId,"1");
        }


        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        //用我们的销售id换scrm的销售id
        Long scrmStaffId = null;
        List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.listByPhone(Arrays.asList(scrmOpportunitiesWinOrderRequest.getStaffPhone()));
        if(CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS)){
            scrmStaffId = scrmUserStoreConfigPOS.get(0).getUserId();
        }

        //能找到销售
        if(ObjectUtil.isNotEmpty(scrmStaffId)){

            //获取所有这个客户正在进行中的商机（标准月子）
            List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = this.listByCustomerId(scrmOpportunitiesWinOrderRequest.getScrmId());

            Long recordId = null;
            ScrmCustomUserStoreQuery scrmCustomUserStoreQuery = new ScrmCustomUserStoreQuery();
            scrmCustomUserStoreQuery.setStoreId(scrmOpportunitiesWinOrderRequest.getStoreId().longValue());
            scrmCustomUserStoreQuery.setSaleId(scrmStaffId);
            List<ScrmCustomUserStoreVO> scrmCustomUserStoreVOS = xsyScrmService.queryScrmCustomUserStoreList(scrmCustomUserStoreQuery);
            log.info("通过员工查询scrm的门店列表结果:{}", JSONUtil.toJsonStr(scrmCustomUserStoreVOS));
            if(CollectionUtil.isNotEmpty(scrmCustomUserStoreVOS)){
                scrmCustomUserStoreVOS = scrmCustomUserStoreVOS.stream().filter(s->s.getStoreId().equals(scrmOpportunitiesWinOrderRequest.getStoreId().longValue())).collect(Collectors.toList());
                //记录id
                if(CollectionUtil.isNotEmpty(scrmCustomUserStoreVOS)){
                    recordId = scrmCustomUserStoreVOS.get(0).getId();
                }
            }

            //如果有签约门店
            if(ObjectUtil.isNotEmpty(recordId)){

                ScrmBusinessOpportunityPO winBusinessOpportunity = null;
                //精准匹配商机
                for (ScrmBusinessOpportunityPO s : scrmBusinessOpportunityPOS) {
                    //同个门店 同个销售 取最新的一条
                    if(
                        //客户id
                            s.getCustomerId().equals(scrmOpportunitiesWinOrderRequest.getScrmId())&&
                                    //销售id
                                    s.getOwnerId().equals(scrmStaffId) &&
                                    //关联表id
                                    (ObjectUtil.isNotEmpty(s.getContractedStore())&&s.getContractedStore().equals(recordId))
                    ){
                        winBusinessOpportunity = s;
                        break;
                    }
                }

                //自动赢单输单
                if(ObjectUtil.isNotNull(winBusinessOpportunity)){
                    for (ScrmBusinessOpportunityPO scrmBusinessOpportunityPO : scrmBusinessOpportunityPOS) {
                        if(scrmBusinessOpportunityPO.getScrmId().equals(winBusinessOpportunity.getScrmId())){
                            //更改状态
                            scrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())));
                            //绑定
                            scrmBusinessOpportunityPO.setMonthOrder(scrmOpportunitiesWinOrderRequest.getScrmOrderId());

                            //赢单后将商机的预计签单金额改成订单的签单金额
                            scrmBusinessOpportunityPO.setMoney(scrmOpportunitiesWinOrderRequest.getPayMoney());

                            //输单不锁
                            scrmBusinessOpportunityPO.setLockStatus(2);
                            // 赢单将状态改为赢单
                            scrmBusinessOpportunityPO.setStatus(2);
                        }else{
                            //输单
                            //默认输单：已签其他门店
                            scrmBusinessOpportunityPO.setReason(OpportunityLoseReasonEnum.OTHER_STORES_HAVE_BEEN_SIGNED.getCode());
                            scrmBusinessOpportunityPO.setSaleStageId(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode())));
                            scrmBusinessOpportunityPO.setStatus(3);
                        }
                        this.updateBatchById(scrmBusinessOpportunityPOS);
                    }

                    for (ScrmBusinessOpportunityPO scrmBusinessOpportunityPO : scrmBusinessOpportunityPOS) {
                        //推送到SCRM
                        OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(scrmBusinessOpportunityPO);
                        opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);
                        xsyScrmService.opportunityEdit(opportunityRequest);
                        //创建中间表
                        // 判断是否是赢单
                        Date realFinishTime = DateUtil.date();
                        if (scrmBusinessOpportunityPO.getSaleStageId().equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())))) {
                            realFinishTime = scrmOpportunitiesWinOrderRequest.getPercentFirstTime();
                        }
                        saveOpportunityStatus(
                                scrmBusinessOpportunityPO.getScrmId(),
                                scrmBusinessOpportunityPO.getOpportunityName(),
                                scrmBusinessOpportunityPO.getSaleStageId(),
                                scrmBusinessOpportunityPO.getContractedStore(),
                                scrmBusinessOpportunityPO.getOwnerId(),
                                scrmBusinessOpportunityPO.getDimDepart(),
                                scrmBusinessOpportunityPO.getEntityType(),
                                scrmBusinessOpportunityPO.getLockStatus(),
                                realFinishTime
                        );
                    }
                    ScrmCustomerPO byScrmCustomerId = scrmCustomerService.getByScrmCustomerId(scrmOpportunitiesWinOrderRequest.getScrmId());
                    if(ObjectUtil.isNotNull(byScrmCustomerId)){
                        Integer customerStage = byScrmCustomerId.getCustomerStage();
                        if(customerStage< ScrmCustomerStatusEnum.SIGN.getCode()){
                            byScrmCustomerId.setCustomerStage(ScrmCustomerStatusEnum.SIGN.getCode());
                        }
                        byScrmCustomerId.setWinSale(winBusinessOpportunity.getOwnerId());
                        scrmCustomerService.updateById(byScrmCustomerId);
                        //更新scrm客户的数据
                        scrmManager.custom2Scrm(byScrmCustomerId);
                    }
                    return recordId;
                }
            }
        }
        return null;
    }

    @Override
    public Result updateFollowUpRecord(ScrmOpportunityActivityRecordRequest scrmOpportunityActivityRecordRequest) {
        ScrmOpportunityActivityRecordPO scrmOpportunityActivityRecordPO = scrmOpportunityActivityRecordService.getBaseMapper().selectOne(new LambdaQueryWrapper<ScrmOpportunityActivityRecordPO>().eq(ScrmOpportunityActivityRecordPO::getScrmId, scrmOpportunityActivityRecordRequest.getId()));
        ScrmOpportunityActivityRecordPO scrmOpportunityActivityRecordPOReq = scrmConvert.scrmOpportunityActivityRecordRequest2ScrmBusinessOpportunityPO(scrmOpportunityActivityRecordRequest);
        if(ObjectUtil.isNotNull(scrmOpportunityActivityRecordPO)){
            scrmOpportunityActivityRecordPOReq.setId(scrmOpportunityActivityRecordPO.getId());
            scrmOpportunityActivityRecordService.updateById(scrmOpportunityActivityRecordPOReq);
        }else{
            scrmOpportunityActivityRecordService.save(scrmOpportunityActivityRecordPOReq);
        }
        return Result.success();
    }

    @Override
    public List<ScrmBusinessOpportunityPO> listByCustomerId(Long scrmId) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        //获取所有进行中的商机
        return this.baseMapper.selectList(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                .eq(ScrmBusinessOpportunityPO::getCustomerId,scrmId)
                .eq(ScrmBusinessOpportunityPO::getEntityType,scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode()))
                .in(ScrmBusinessOpportunityPO::getSaleStageId, Arrays.asList(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()),scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())))
                .orderByDesc(ScrmBusinessOpportunityPO::getGmtCreate)
        );
    }

    @Override
    public List<ScrmBusinessOpportunityPO> listByCustomerIdAndSaleIds(Long customerId, List<Long> saleIdList) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        //获取所有进行中的商机
        return this.baseMapper.selectList(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                .eq(ScrmBusinessOpportunityPO::getCustomerId,customerId)
                .eq(ScrmBusinessOpportunityPO::getEntityType,scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode()))
                .eq(ScrmBusinessOpportunityPO::getSaleStageId, scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()))
                .in(ScrmBusinessOpportunityPO::getOwnerId,saleIdList)
                .orderByDesc(ScrmBusinessOpportunityPO::getGmtCreate)
        );
    }

    @Override
    public List<ScrmBusinessOpportunityPO> listByCustomerIdAndStoreConfigIds(Long customerId, List<Long> storeConfigId) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        //获取所有进行中的商机

        List<Long> entityTypeList = Arrays.asList(
            Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())),
            Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())));

        // 月子商机和产康商机中处于首次触达的
        List<Long> firstSaleStageList = Arrays.asList(
            Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())),
            Long.valueOf(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode())));

        return this.baseMapper.selectList(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                .eq(ScrmBusinessOpportunityPO::getCustomerId,customerId)
                .in(ScrmBusinessOpportunityPO::getEntityType, entityTypeList)
                .in(ScrmBusinessOpportunityPO::getSaleStageId, firstSaleStageList)
                .isNull(ScrmBusinessOpportunityPO::getOwnedStore)
//                .in(ScrmBusinessOpportunityPO::getContractedStore,storeConfigId)
                .orderByDesc(ScrmBusinessOpportunityPO::getGmtCreate)
        );
    }

    @Override
    public void add(SCRMOpportunityRequest scrmOpportunityRequest) {
        //入参
        scrmOpportunityRequest = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(scrmOpportunityRequest);
        ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(scrmOpportunityRequest);

        //新增状态、新增商机
        Long scrmUserStoreConfigId = null;
        if (ObjectUtil.isNotEmpty(scrmOpportunityRequest.getCustomItem174__c()) && !scrmOpportunityRequest.getCustomItem174__c().equals(0L)) {
            scrmUserStoreConfigId = scrmOpportunityRequest.getCustomItem174__c();
        } else {
            //根据销售id获取记录了表id，如果一个销售只有一条记录，那么记录就取这一条
            List<ScrmUserStoreConfigPO> scrmUserStoreConfigPOS = scrmUserStoreConfigService.infoListByUserId(convertScrmBusinessOpportunityPO.getOwnerId());
            if(CollectionUtil.isNotEmpty(scrmUserStoreConfigPOS)){
                if(scrmUserStoreConfigPOS.size()==1){
                    scrmUserStoreConfigId = scrmUserStoreConfigPOS.get(0).getScrmRecordId();
                }
            }
        }

        //通知客户
        scrmAddOrUpdateClient(convertScrmBusinessOpportunityPO);
        saveOpportunityStatus(
                scrmOpportunityRequest.getId(),
                convertScrmBusinessOpportunityPO.getOpportunityName(),
                convertScrmBusinessOpportunityPO.getSaleStageId(),
                scrmUserStoreConfigId,
                convertScrmBusinessOpportunityPO.getOwnerId(),
                convertScrmBusinessOpportunityPO.getDimDepart(),
                convertScrmBusinessOpportunityPO.getEntityType(),
                convertScrmBusinessOpportunityPO.getLockStatus(),
                null
        );
        //创建商机的时候添加新增、更新、阶段时间、商机的部门
        if(ObjectUtil.isEmpty(convertScrmBusinessOpportunityPO.getDimDepart())){
            ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(convertScrmBusinessOpportunityPO.getOwnerId());
            if(ObjectUtil.isNotEmpty(userInfo)){
                convertScrmBusinessOpportunityPO.setDimDepart(userInfo.getDimDepart());
            }
        }
        convertScrmBusinessOpportunityPO.setCreatedAt(new Date());
        convertScrmBusinessOpportunityPO.setUpdatedAt(new Date());
        convertScrmBusinessOpportunityPO.setStageUpdatedAt(new Date());
        this.save(convertScrmBusinessOpportunityPO);

    }

    @Override
    public void createOpportunity(ScrmCrateOpportunityRequest scrmCrateOpportunityRequest,ScrmCrateWinOpportunityRequest scrmCrateWinOpportunityRequest) {
        ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(scrmCrateOpportunityRequest.getOwnerId());
        OpportunityRequest opportunityRequest = new OpportunityRequest();
        opportunityRequest.setOwnerId(scrmCrateOpportunityRequest.getOwnerId());
        opportunityRequest.setAccountId(scrmCrateOpportunityRequest.getCustomerId());
        opportunityRequest.setStageUpdatedAt(System.currentTimeMillis());
        opportunityRequest.setUpdatedAt(System.currentTimeMillis());
        opportunityRequest.setCreatedAt(System.currentTimeMillis());

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        //自动赢单逻辑
        if(ObjectUtil.isNotEmpty(scrmCrateWinOpportunityRequest)){
            if(ObjectUtil.isNotEmpty(scrmCrateWinOpportunityRequest.getOrderType())){
                opportunityRequest.setEntityType(Long.parseLong(scrmConfig.get(scrmCrateWinOpportunityRequest.getOrderType())));
            }

            //直接赢单
            String saleStagWinOrder = scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode());
            opportunityRequest.setSaleStageId(new Long(saleStagWinOrder));

            if(ObjectUtil.isNotEmpty(scrmCrateWinOpportunityRequest.getSignId())){
                //签到信息
                opportunityRequest.setCustomItem168__c(scrmCrateWinOpportunityRequest.getSignId());
            }

        }


        if(ObjectUtil.isNotEmpty(userInfo)){
            opportunityRequest.setDimDepart(userInfo.getDimDepart());
        }
        //获取客户名称
        ScrmCustomerPO byScrmCustomerId = scrmCustomerService.getByScrmCustomerId(scrmCrateOpportunityRequest.getCustomerId());
        if(ObjectUtil.isNotNull(byScrmCustomerId)){
            opportunityRequest.setOpportunityName(byScrmCustomerId.getName()+"标准月子订单");
        }
        opportunityRequest.setCustomItem174__c(scrmCrateOpportunityRequest.getRecordId());
        opportunityRequest.setStoreType(scrmCrateOpportunityRequest.getCustomerType());

        Long opportunityId = xsyScrmService.opportunityEdit(opportunityRequest);
        //获取商机详情保存到我们的数据库
        ScrmOpportunityDetailVO scrmOpportunityDetailVO = xsyScrmService.queryOpportunityDetail(opportunityId);
        scrmOpportunityDetailVO.setCustomerId(scrmCrateOpportunityRequest.getCustomerId());
        SCRMOpportunityRequest scrmOpportunityRequest = scrmConvert.scrmOpportunityDetailVO2SCRMOpportunityRequest(scrmOpportunityDetailVO);
        scrmOpportunityRequest = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(scrmOpportunityRequest);
        ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(scrmOpportunityRequest);
        this.save(convertScrmBusinessOpportunityPO);

        //保存变更记录表
        saveOpportunityStatus(
                opportunityId,
                convertScrmBusinessOpportunityPO.getOpportunityName(),
                convertScrmBusinessOpportunityPO.getSaleStageId(),
                convertScrmBusinessOpportunityPO.getContractedStore(),
                convertScrmBusinessOpportunityPO.getOwnerId(),
                convertScrmBusinessOpportunityPO.getDimDepart(),
                convertScrmBusinessOpportunityPO.getEntityType(),
                convertScrmBusinessOpportunityPO.getLockStatus(),
                null
        );
    }

    @Override
    public void createWinOpportunity(ScrmCrateWinOpportunityRequest scrmCrateWinOpportunityRequest) {
        ScrmCrateOpportunityRequest scrmCrateOpportunityRequest = new ScrmCrateOpportunityRequest();
        scrmCrateOpportunityRequest.setCustomerId(scrmCrateWinOpportunityRequest.getCustomerId());
        scrmCrateOpportunityRequest.setOwnerId(scrmCrateWinOpportunityRequest.getOwnerId());
        scrmCrateOpportunityRequest.setCustomerType(scrmCrateWinOpportunityRequest.getCustomerType());
        scrmCrateOpportunityRequest.setRecordId(scrmCrateWinOpportunityRequest.getRecordId());
        this.createOpportunity(scrmCrateOpportunityRequest,scrmCrateWinOpportunityRequest);
    }

    @Override
    public void del(SCRMOpportunityRequest scrmOpportunityRequest) {
        List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = this.baseMapper.selectList(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>().eq(ScrmBusinessOpportunityPO::getScrmId, scrmOpportunityRequest.getId()));
        if(CollectionUtil.isNotEmpty(scrmBusinessOpportunityPOS)){
            for (ScrmBusinessOpportunityPO scrmBusinessOpportunityPO : scrmBusinessOpportunityPOS) {
                this.baseMapper.deleteById(scrmBusinessOpportunityPO.getId());
            }
        }
    }

    @Override
    public void batchInsertOpportunity(List<BatchOpportunityRequest> scrmOpportunityRequests) {
        List<ScrmBusinessOpportunityPO> convertScrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequestList2ScrmBusinessOpportunityPO(scrmOpportunityRequests);

        List<Long> collect = convertScrmBusinessOpportunityPO.stream().map(ScrmBusinessOpportunityPO::getScrmId).collect(Collectors.toList());

        List<ScrmBusinessOpportunityPO> scrmBusinessOpportunityPOS = this.baseMapper.selectList(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>().in(ScrmBusinessOpportunityPO::getScrmId, collect));

        List<ScrmBusinessOpportunityPO> insert = new ArrayList<>();
        List<ScrmBusinessOpportunityPO> update = new ArrayList<>();

        if(CollectionUtil.isNotEmpty(scrmBusinessOpportunityPOS)){

            for (ScrmBusinessOpportunityPO scrmBusinessOpportunityPO : convertScrmBusinessOpportunityPO) {

                Boolean same = false;

                for (ScrmBusinessOpportunityPO businessOpportunityPO : scrmBusinessOpportunityPOS) {
                    if(scrmBusinessOpportunityPO.getScrmId().equals(businessOpportunityPO.getScrmId())){
                        scrmBusinessOpportunityPO.setId(businessOpportunityPO.getId());
                        update.add(scrmBusinessOpportunityPO);
                        same = true;
                        break;
                    }
                }
                if(!same){
                    insert.add(scrmBusinessOpportunityPO);
                }
            }

        }else{
            insert = convertScrmBusinessOpportunityPO;
        }
        if(CollectionUtil.isNotEmpty(insert)){
            this.saveBatch(insert);
        }
        if(CollectionUtil.isNotEmpty(update)){
            this.updateBatchById(update);
        }
    }


    /**
     * 添加用户
     *
     * @param
     * @return
     */
    @Override
    public ClientSearchVO createScrmUser(ClientCreateRequest request) {
        ClientSearchVO vo = new ClientSearchVO();
        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.queryCustomerByPhone(request.getUserPhone());
        if (scrmCustomerPO == null) {
            GrantUserBasicQueryRequest queryRequest = new GrantUserBasicQueryRequest();
            queryRequest.setKeyword(request.getUserPhone());
            queryRequest.setPageNum(1);
            queryRequest.setPageSize(100);
            PageVO<HeUserBasicPO> heUserBasicPOPageVO = heUserBasicService.queryByCustomerNameOrPhone(queryRequest);
            if (heUserBasicPOPageVO == null || CollectionUtil.isEmpty(heUserBasicPOPageVO.getList())) {
                return null;
            }
            HeUserBasicPO heUserBasicPO = heUserBasicPOPageVO.getList().get(0);
            List<com.stbella.customer.server.ecp.entity.TabClientPO> list = ecpTabClientService.list(new LambdaQueryWrapper<com.stbella.customer.server.ecp.entity.TabClientPO>()
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getPhone, request.getUserPhone())
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getStoreId, request.getStoreId())
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getActive, 1)
            );
            log.info("createScrmUser-scrmCustomerPO查不到，创建:{},list:{}", request.getUserPhone(), JSON.toJSONString(list));
            if (CollectionUtil.isEmpty(list)) {
                TabClientPO tabClientPO = new TabClientPO();
                tabClientPO.setBasicUid(heUserBasicPO.getId().intValue());
                tabClientPO.setPhone(request.getUserPhone());
                tabClientPO.setName(heUserBasicPO.getName());
                tabClientPO.setStoreId(request.getStoreId());
                tabClientPO.setRecordTime(LocalDateTime.now());
                tabClientPO.setActive(1);
                tabClientPO.setPhoneType(heUserBasicPO.getPhoneType());
                tabClientPO.setUpdateTime(LocalDateTime.now());
                tabClientService.save(tabClientPO);
            }
            List<com.stbella.customer.server.ecp.entity.TabClientPO> newList = ecpTabClientService.list(new LambdaQueryWrapper<com.stbella.customer.server.ecp.entity.TabClientPO>()
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getPhone, request.getUserPhone())
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getStoreId, request.getStoreId())
                    .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getActive, 1)
            );
            if (CollectionUtil.isNotEmpty(newList)) {
                com.stbella.customer.server.ecp.entity.TabClientPO byId = newList.get(0);
                if (byId != null) {
                    ClientSearchRequest clientSearchRequest = new ClientSearchRequest();
                    clientSearchRequest.setKeyword(request.getUserPhone());
                    clientSearchRequest.setStoreId(request.getStoreId());
                    clientSearchRequest.setOperator(request.getOperator());
                    clientSearchRequest.setPageNum(1);
                    clientSearchRequest.setPageSize(10);
                    PageVO<ClientSearchVO> clientSearch = userService.clientSearch(clientSearchRequest);
                    if (clientSearch != null && CollectionUtil.isNotEmpty(clientSearch.getList())) {
                        return clientSearch.getList().get(0);
                    }
                }
            }
        }
        GrantUserBasicQueryRequest queryRequest = new GrantUserBasicQueryRequest();
        queryRequest.setKeyword(request.getUserPhone());
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(100);
        PageVO<HeUserBasicPO> heUserBasicPOPageVO = heUserBasicService.queryByCustomerNameOrPhone(queryRequest);
        if (heUserBasicPOPageVO == null || CollectionUtil.isEmpty(heUserBasicPOPageVO.getList())) {
            return null;
        }
        HeUserBasicPO heUserBasicPO = heUserBasicPOPageVO.getList().get(0);
        TabClientPO tabClientPO = new TabClientPO();
        tabClientPO.setBasicUid(heUserBasicPO.getId().intValue());
        tabClientPO.setPhone(request.getUserPhone());
        tabClientPO.setName(heUserBasicPO.getName());
        tabClientPO.setStoreId(request.getStoreId());
        tabClientPO.setRecordTime(LocalDateTime.now());
        tabClientPO.setActive(1);
        tabClientPO.setPhoneType(heUserBasicPO.getPhoneType());
        tabClientPO.setUpdateTime(LocalDateTime.now());
        log.info("createScrmUser-scrmCustomerPO查得到到，创建:{}", request.getUserPhone());
        tabClientService.save(tabClientPO);

//        ScrmCustomerDTO scrmCustomerDTO = scrmConvert.scrmCustomerPO2DTO(scrmCustomerPO);
//        ScrmCustomerDTO.ScrmCustomerStaff scrmCustomerStaff = new ScrmCustomerDTO.ScrmCustomerStaff();
//        scrmCustomerStaff.setPhone(request.getUserPhone());
//        scrmCustomerStaff.setStoreId(request.getStoreId());
//        scrmCustomerDTO.setStaffList(Arrays.asList(scrmCustomerStaff));
//        log.info("客户分配销售调用php,url={},参数={}", scrmDistributionCustomer, JSONUtil.toJsonStr(scrmCustomerDTO));
//        String post = HttpUtil.post(scrmDistributionCustomer, JSONUtil.toJsonStr(scrmCustomerDTO));
//        log.info("客户分配销售调用php,结果={}", post);
        List<com.stbella.customer.server.ecp.entity.TabClientPO> newList = ecpTabClientService.list(new LambdaQueryWrapper<com.stbella.customer.server.ecp.entity.TabClientPO>()
                .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getPhone, request.getUserPhone())
                .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getStoreId, request.getStoreId())
                .eq(com.stbella.customer.server.ecp.entity.TabClientPO::getActive, 1)
        );
        if (CollectionUtil.isNotEmpty(newList)) {
            com.stbella.customer.server.ecp.entity.TabClientPO byId = newList.get(0);
            if (byId != null) {
                ClientSearchRequest clientSearchRequest = new ClientSearchRequest();
                clientSearchRequest.setKeyword(request.getUserPhone());
                clientSearchRequest.setStoreId(request.getStoreId());
                clientSearchRequest.setOperator(request.getOperator());
                clientSearchRequest.setPageNum(1);
                clientSearchRequest.setPageSize(10);
                PageVO<ClientSearchVO> clientSearch = userService.clientSearch(clientSearchRequest);
                if (clientSearch != null && CollectionUtil.isNotEmpty(clientSearch.getList())) {
                    return clientSearch.getList().get(0);
                }
            }
        }
        return null;
    }

    /**
     * @param convertScrmBusinessOpportunityPO 商机入参
     */
    private void scrmAddOrUpdateClient(ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO){
        Long userId = convertScrmBusinessOpportunityPO.getCustomerId();
        Long contractedStore = convertScrmBusinessOpportunityPO.getContractedStore();
        ScrmUserStoreConfigPO byRecordId = scrmUserStoreConfigService.getByRecordId(contractedStore);
        if(ObjectUtil.isNotEmpty(byRecordId)){
            LambdaQueryWrapper<ScrmCustomerPO> lq = new LambdaQueryWrapper<>();
            lq.eq(ScrmCustomerPO::getScrmCustomerId, userId);
            lq.last("limit 1");
            ScrmCustomerPO one = scrmCustomerService.getOne(lq);
            ScrmAccountQuery query = new ScrmAccountQuery();
            query.setScrmCustomerId(userId);
            AccountInfoRequest request = xsyScrmService.queryScrmAccount(query);
            ScrmCustomerPO scrmCustomerPO = scrmManager.accountInfoRequest2ScrmCustomerPO(request);
            scrmCustomerPO.setId(one.getId());
            ScrmCustomerDTO scrmCustomerDTO = scrmConvert.scrmCustomerPO2DTO(scrmCustomerPO);
            if (Objects.nonNull(one) && Objects.nonNull(one.getProductionNewleadTime())) {
                scrmCustomerDTO.setProductionNewleadTime(one.getProductionNewleadTime());
            }
            ScrmCustomerDTO.ScrmCustomerStaff scrmCustomerStaff = new ScrmCustomerDTO.ScrmCustomerStaff();
            // 把scrmPhone转为picpPhone
            String staffPhone = byRecordId.getPhone();
            String picpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(staffPhone);
            if (ObjectUtil.isNotNull(picpPhone)) {
                log.info("客户分配销售, 销售scrmPhone:{}转为picpPhone:{}", staffPhone, picpPhone);
                staffPhone = picpPhone;
            }
            scrmCustomerStaff.setPhone(staffPhone);
            scrmCustomerStaff.setStoreId(byRecordId.getStoreId().intValue());
            scrmCustomerDTO.setStaffList(Arrays.asList(scrmCustomerStaff));
            log.info("客户分配销售调用php,url={},参数={}", scrmDistributionCustomer, JSONUtil.toJsonStr(scrmCustomerDTO));
            String post = HttpUtil.post(scrmDistributionCustomer, JSONUtil.toJsonStr(scrmCustomerDTO));
            log.info("客户分配销售调用php,结果={}", post);
        }
    }

    private EcpUserInfoVO getCcpUserInfoByPhone(String phone){
        HashMap hashMap = new HashMap();
        hashMap.put("phoneList",Arrays.asList(phone));
        log.info("根据手机号获取ecp信息,url={},参数={}", getUserInfoByPhone, JSONUtil.toJsonStr(hashMap));
        String post = HttpUtil.post(getUserInfoByPhone, JSONUtil.toJsonStr(hashMap));
        log.info("根据手机号获取ecp信息,resp={}", post);
        LinkedHashMap read = JsonUtil.read(post, LinkedHashMap.class);
        if("10000".equals(read.get("code")+"")){
            ArrayList<LinkedHashMap> data = (ArrayList<LinkedHashMap>) read.get("data");
            if(CollectionUtil.isNotEmpty(data)){
                LinkedHashMap linkedHashMap = data.get(0);
                return JsonUtil.read(JsonUtil.write(linkedHashMap), EcpUserInfoVO.class);
            }
        }else{
            log.error("根据手机号获取销售信息失败，resp:{}",post);
        }
        return null;
    }


    @Override
    public void saveOpportunityStatus(
            Long scrmOpportunityId,
            String scrmOpportunityName,
            Long saleStageId,
            Long contractedStore,
            Long ownerId,
            Long dimDepart,
            Long entityType,
            Integer lockStatus,
            Date realFinishTime
            ){
        try{
            if (ObjectUtil.isNull(realFinishTime)) {
                realFinishTime = new Date();
            }

            Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
            Long opportunityEntityStatus = new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_TEMP_DEFAULT_TYPE.getCode()));
            Long opportunityStatus = getOpportunityStatus(saleStageId).longValue();

            // 判断当前状态下的商机中间表是否存在，如果不存在，才会新增
            ScrmOpportunityStatusRecordPO opportunityTempRecord = scrmOpportunityStatusRecordService.getOne(
                new LambdaQueryWrapper<ScrmOpportunityStatusRecordPO>()
                    .eq(ScrmOpportunityStatusRecordPO::getOpportunityScrmId, scrmOpportunityId)
                    .eq(ScrmOpportunityStatusRecordPO::getSaleStageId, opportunityStatus)
                    .last("limit 1"));

            if (ObjectUtil.isNull(opportunityTempRecord)) {
                // 新增商机中间表记录
                ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO = new ScrmOpportunityStatusRecordPO();
                scrmOpportunityStatusRecordPO.setOpportunityScrmId(scrmOpportunityId);
                scrmOpportunityStatusRecordPO.setOpportunityName(scrmOpportunityName);
                scrmOpportunityStatusRecordPO.setDimDepart(dimDepart);
                scrmOpportunityStatusRecordPO.setOwnerId(ownerId);
                scrmOpportunityStatusRecordPO.setSaleStageId(opportunityStatus);
                //scrmOpportunityStatusRecordPO.setScrmUserStoreConfigId(contractedStore);
                scrmOpportunityStatusRecordPO.setEntityType(opportunityEntityStatus);
                scrmOpportunityStatusRecordPO.setLockStatus(2);
                scrmOpportunityStatusRecordPO.setRealFinishTime(realFinishTime);

                OpportunityTempRequest req = new OpportunityTempRequest();
                req.setName(DateUtil.formatDateTime(new Date()));
                req.setCustomItem1__c(scrmOpportunityId);
                req.setCustomItem2__c(opportunityStatus.intValue());
                //req.setCustomItem3__c(contractedStore);
                req.setEntityType(opportunityEntityStatus);
                req.setOwnerId(ownerId);
                req.setDimDepart(dimDepart);
                req.setCreatedBy(ownerId);
                req.setCreatedAt(System.currentTimeMillis());
                req.setUpdatedBy(ownerId);
                req.setUpdatedAt(System.currentTimeMillis());
                req.setLockStatus(2);
                req.setCustomItem8__c(realFinishTime.getTime());

                /*
                if (ObjectUtil.isNotEmpty(contractedStore) && !contractedStore.equals(0L)) {
                    Long storeBrandRecordId = xsyScrmService.queryStoreBrandRecordIdBySaleStoreId(contractedStore);
                    if (!storeBrandRecordId.equals(0L)) {
                        req.setStore__c(storeBrandRecordId);
                    }
                }*/

                Long aLong = xsyScrmService.opportunityTempEdit(req);
                scrmOpportunityStatusRecordPO.setScrmId(aLong);
                scrmOpportunityStatusRecordService.save(scrmOpportunityStatusRecordPO);

                // 通知客资状态变化消息
                log.info("发送客资商机状态变更到消息队列, 商机id:{}, 状态:{}", scrmOpportunityId, opportunityStatus);
                CompletableFuture.runAsync(() -> sendCustomerStatusChangeMessage(scrmOpportunityId, opportunityStatus.intValue()), taskExecutor);
            } else {
                if (!opportunityTempRecord.getRealFinishTime().equals(realFinishTime)) {
                    // 如果两个阶段到达时间不一致，则需要更新中间表中的时间
                    opportunityTempRecord.setRealFinishTime(realFinishTime);
                    scrmOpportunityStatusRecordService.updateById(opportunityTempRecord);

                    // 更新scrm中的商机中间表时间
                    OpportunityTempRequest opportunityTempRequest = new OpportunityTempRequest();
                    opportunityTempRequest.setId(opportunityTempRecord.getScrmId());
                    opportunityTempRequest.setCustomItem8__c(opportunityTempRecord.getRealFinishTime().getTime());
                    opportunityTempRequest.setCustomItem1__c(opportunityTempRecord.getOpportunityScrmId());
                    opportunityTempRequest.setEntityType(opportunityTempRecord.getEntityType());
                    opportunityTempRequest.setOwnerId(opportunityTempRecord.getOwnerId());
                    xsyScrmService.opportunityTempEdit(opportunityTempRequest);
                }
            }
        }catch (Exception e){
            log.error("商机中间表存储失败，scrmOpportunityId:{},scrmOpportunityName:{},saleStageId:{},contractedStore:{},ownerId:{},dimDepart:{},entityType:{},lockStatus:{},e:{}",
                    scrmOpportunityId,
                    scrmOpportunityName,
                    saleStageId,
                    contractedStore,
                    ownerId,
                    dimDepart,
                    entityType,
                    lockStatus,
                    e
            );
        }
    }

    /**
     * 更新商机中间表对应阶段的时间
     * @param scrmOpportunityId 商机id
     * @param saleStageId 商机中间表中的阶段
     * @param realFinishTime 商机实际完成时间
     */
    public void updateOpportunityStatusFinishTime(Long scrmOpportunityId, Integer saleStageId, Date realFinishTime) {
        ScrmOpportunityStatusRecordPO opportunityTempRecord = scrmOpportunityStatusRecordService.getOne(
            new LambdaQueryWrapper<ScrmOpportunityStatusRecordPO>()
                .eq(ScrmOpportunityStatusRecordPO::getOpportunityScrmId, scrmOpportunityId)
                .eq(ScrmOpportunityStatusRecordPO::getSaleStageId, saleStageId)
                .last("limit 1"));

        // 删除scrm中间表的数据
        // 如果两个阶段到达时间不一致，则需要更新中间表中的时间
        if (ObjectUtil.isNotNull(opportunityTempRecord) && !opportunityTempRecord.getRealFinishTime().equals(realFinishTime)) {
            opportunityTempRecord.setRealFinishTime(realFinishTime);
            scrmOpportunityStatusRecordService.updateById(opportunityTempRecord);

            // 更新scrm中的商机中间表时间
            OpportunityTempRequest opportunityTempRequest = new OpportunityTempRequest();
            opportunityTempRequest.setId(opportunityTempRecord.getScrmId());
            opportunityTempRequest.setCustomItem8__c(opportunityTempRecord.getRealFinishTime().getTime());
            opportunityTempRequest.setCustomItem1__c(opportunityTempRecord.getOpportunityScrmId());
            opportunityTempRequest.setEntityType(opportunityTempRecord.getEntityType());
            opportunityTempRequest.setOwnerId(opportunityTempRecord.getOwnerId());
            xsyScrmService.opportunityTempEdit(opportunityTempRequest);
        }
    }

    /**
     * 删除商机中间表的记录
     * @param scrmOpportunityId 商机中间表中的阶段
     * @param saleStageId 商机阶段id
     */
    public void deleteOpportunityStatus(Long scrmOpportunityId, Integer saleStageId) {
        ScrmOpportunityStatusRecordPO opportunityTempRecord = scrmOpportunityStatusRecordService.getOne(
            new LambdaQueryWrapper<ScrmOpportunityStatusRecordPO>()
                .eq(ScrmOpportunityStatusRecordPO::getOpportunityScrmId, scrmOpportunityId)
                .eq(ScrmOpportunityStatusRecordPO::getSaleStageId, saleStageId)
                .last("limit 1"));

        if (ObjectUtil.isNotNull(opportunityTempRecord)) {
            // 删除scrm中间表的数据
            xsyScrmService.opportunityTempDelete(opportunityTempRecord.getScrmId());
            // 删除中间表中的数据
            scrmOpportunityStatusRecordService.removeById(opportunityTempRecord.getId());
        }
    }

    @Override
    public boolean checkOpportunityStatusForUser(Long basicUid) {
        // 获取配置信息
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        // 首次触达状态码列表
        List<String> saleStagFirstList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())
        );

        // 邀约到店状态码列表
        List<String> checkStoreList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())
        );

        // 合并首次触达和邀约到店的状态码列表
        List<Long> validSaleStageIds = Stream.concat(
                saleStagFirstList.stream().map(Long::parseLong),
                checkStoreList.stream().map(Long::parseLong)
        ).collect(Collectors.toList());

        //查询
        HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoById(basicUid);
        if (ObjectUtil.isNull(heUserBasicPO)) {
            return false;
        }
        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.queryCustomerByPhone(heUserBasicPO.getPhone());
        if (ObjectUtil.isNull(scrmCustomerPO)) {
            return false;
        }
        // 查询用户相关的商机
        LambdaQueryWrapper<ScrmBusinessOpportunityPO> wrapper = new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
                .eq(ScrmBusinessOpportunityPO::getCustomerId, scrmCustomerPO.getScrmCustomerId())
                .in(ScrmBusinessOpportunityPO::getSaleStageId, validSaleStageIds);

        List<ScrmBusinessOpportunityPO> opportunities = this.getBaseMapper().selectList(wrapper);

        // 判断是否存在符合条件的商机
        return !opportunities.isEmpty();
    }

    /**
     * 1、获取所有数据库商机、商机中间表数据
     * 2、获取所有商机中客户的id并去tab_client表中查询
     * 3、获取所有商机的签约门店，并store_config表中查询所有数据
     * 4、根据阶段类型单独处理
     * 首次触达：判断中间表中是否有首次触达的数据，如果没有就新增，阶段实际实际时间取tab_client的record时间record_time（是邀约到店请求的，且无法根据商机的客户id无法直接在tab_client找到用户的，会根据打卡信息中的手机号+签约门店（需要通过store_config表转成系统门店id）搜索客户）
     * 邀约到店：去scrm获取邀约到店那条记录的数详情（获取手机号+签约门店），之后进行首次触达逻辑，邀约到店阶段实际时间取打卡信息时间
     * 签单：先进行邀约到店逻辑，阶段实际实际时间取订单支付50%的时间
     * 输单：先进行邀约到店逻辑，阶段实际时间取的是商机最后更新时间？？
     */
    @Override
    public void updateAllOpportunityIntermediateTable() {

        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        //获取所有的中间表（我们的数据库）
        List<ScrmOpportunityStatusRecordPO> scrmOpportunityTempList = scrmOpportunityStatusRecordService.getAll();

        // 已存在商机中间表中的商机id
        List<Long> hasExistOpportunityIds = scrmOpportunityTempList.stream()
            .map(ScrmOpportunityStatusRecordPO::getOpportunityScrmId).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<ScrmBusinessOpportunityPO> wrapper = new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .notIn(ScrmBusinessOpportunityPO::getScrmId, hasExistOpportunityIds)
            .orderByAsc(ScrmBusinessOpportunityPO::getGmtCreate);

        //获取所有的商机（我们的数据库）
        Page page = this.page(new Page(1, 100), wrapper);
        List<ScrmBusinessOpportunityPO> opportunityPOList = page.getRecords();

        if (page.getRecords().size() <= 0) {
            log.info("初始化历史商机数据已经完成了，没有需要更新的了");
            return;
        }
        List<Long> idList = opportunityPOList.stream().map(ScrmBusinessOpportunityPO::getCustomerId).collect(Collectors.toList());
        //所有商机对应的客户的表中的数据（用于首次触达的实际时间）
        List<TabClientPO> tabClientListByCondition = tabClientService.queryClientListByBasicIdList(idList);

        //批量新增中间表
        List<SaveOpportunityStatusDTO> saveOpportunityStatusDTOList = new ArrayList();

        //批量更新中间表
        List<ScrmOpportunityStatusRecordPO> updateOpportunityStatusRecordPOList = new ArrayList();

        List<Long> collect = opportunityPOList.stream().map(ScrmBusinessOpportunityPO::getContractedStore).collect(Collectors.toList());
        //商机的签约门店的列表
        List<ScrmUserStoreConfigPO> scrmStoreConfigList = scrmUserStoreConfigService.getByRecordIdList(collect);


        List<Long> scrmOrderIdLIst = new ArrayList();
        opportunityPOList.stream().forEach(o->{
            Long monthOrder = o.getMonthOrder();
            Long nonMonthOrder = o.getNonMonthOrder();
            if(ObjectUtil.isNotEmpty(monthOrder)){
                scrmOrderIdLIst.add(monthOrder);
            }
            if(ObjectUtil.isNotEmpty(nonMonthOrder)){
                scrmOrderIdLIst.add(nonMonthOrder);
            }
        });

        List<ScrmCustomerOrderPO> scrmCustomerOrderPOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(scrmOrderIdLIst)){
            //订单信息（支付一半的时间用于实际时间）
            scrmCustomerOrderPOList = scrmCustomerOrderService.selectByScrmIdList(scrmOrderIdLIst);
        }

        //首次触达
        List<String> saleStagFirstList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode())
        );

        //邀约到店
        List<String> checkStoreList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode())
        );

        //签单
        List<String> winOrderList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode())
        );

        //输单
        List<String> loseOrderList = Arrays.asList(
                scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_SMALLMONTH_LOSETHEBILL.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_TOLOSETHEORDER.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_LOSINGORDERS.getCode()),
                scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_PRODUCTION_DELIVERY.getCode())
        );

        for (ScrmBusinessOpportunityPO scrmBusinessOpportunityPO : opportunityPOList) {

            //当前销售阶段
            Long saleStageId = scrmBusinessOpportunityPO.getSaleStageId();

            if(saleStagFirstList.contains(saleStageId.toString())){
                //首次触达
                batchUpdateOpportunityTempByFirsttouch(
                        scrmBusinessOpportunityPO,
                        scrmOpportunityTempList,
                        saveOpportunityStatusDTOList,
                        updateOpportunityStatusRecordPOList,
                        tabClientListByCondition,
                        scrmStoreConfigList,
                        null,
                        scrmConfig);
            }else if(checkStoreList.contains(saleStageId.toString())){
                batchUpdateOpportunityTempByInvitationToTheStore(
                        scrmBusinessOpportunityPO,
                        scrmOpportunityTempList,
                        saveOpportunityStatusDTOList,
                        updateOpportunityStatusRecordPOList,
                        tabClientListByCondition,
                        scrmStoreConfigList,
                        scrmConfig
                );
            }else if(winOrderList.contains(saleStageId.toString())){
                batchUpdateOpportunityTempByWinningSingle(
                        scrmBusinessOpportunityPO,
                        scrmOpportunityTempList,
                        saveOpportunityStatusDTOList,
                        updateOpportunityStatusRecordPOList,
                        tabClientListByCondition,
                        scrmCustomerOrderPOList,
                        scrmStoreConfigList,
                        scrmConfig
                );
            }else if(loseOrderList.contains(saleStageId.toString())){
                batchUpdateOpportunityTempByLosingOrders(
                        scrmBusinessOpportunityPO,
                        scrmOpportunityTempList,
                        saveOpportunityStatusDTOList,
                        updateOpportunityStatusRecordPOList,
                        tabClientListByCondition,
                        scrmStoreConfigList,
                        scrmConfig
                );
            }
        }

        log.info("批量检查商机状态，批量新增：{}",saveOpportunityStatusDTOList);
        log.info("批量检查商机状态，批量修改：{}",updateOpportunityStatusRecordPOList);

        //批量插入中间表到数据库
        if(CollectionUtil.isNotEmpty(saveOpportunityStatusDTOList)){
            for (SaveOpportunityStatusDTO saveOpportunityStatusDTO : saveOpportunityStatusDTOList) {
                saveOpportunityStatus(
                        saveOpportunityStatusDTO.getScrmOpportunityId(),
                        saveOpportunityStatusDTO.getScrmOpportunityName(),
                        saveOpportunityStatusDTO.getSaleStageId(),
                        saveOpportunityStatusDTO.getContractedStore(),
                        saveOpportunityStatusDTO.getOwnerId(),
                        saveOpportunityStatusDTO.getDimDepart(),
                        saveOpportunityStatusDTO.getEntityType(),
                        saveOpportunityStatusDTO.getLockStatus(),
                        saveOpportunityStatusDTO.getRealFinishTime()
                );
            }
        }
        //批量更新中间表到数据库
        if(CollectionUtil.isNotEmpty(updateOpportunityStatusRecordPOList)){
            for (ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO : updateOpportunityStatusRecordPOList) {
                //更新到scrm
                updateOpportunityStatus(scrmOpportunityStatusRecordPO);
                //更新到我们的数据库
                scrmOpportunityStatusRecordService.updateById(scrmOpportunityStatusRecordPO);
            }
        }
    }

    /**
     * 根据scrm商机id新增或更新商机
     *
     * @param scrmId
     */
    @Override
    public void updateOpportunityByScrmId(Long scrmId) {
        ScrmOpportunityDetailVO scrmOpportunityDetailVO = xsyScrmService.queryOpportunityDetail(scrmId);
        SCRMOpportunityRequest request = scrmConvert.scrmOpportunityDetailVO2SCRMOpportunityRequest(scrmOpportunityDetailVO);
        request = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(request);
        ScrmBusinessOpportunityPO scrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(request);
        saveOrUpdate(scrmBusinessOpportunityPO);
    }

    /**
     * 通知所有门店到店数
     */
    @Override
    public void reminderCheckinStoreUsers() {
        // 获取本月的开始时间，结束时间
        Map<String, Date> firstDayAndLastDayByMonth = DateUtils.getFirstDayAndLastDayByMonth(new Date());
        Date monthStart = firstDayAndLastDayByMonth.get("dateStart");
        Date monthEnd = firstDayAndLastDayByMonth.get("dateEnd");

        DateTime lastMonth = DateUtil.offsetMonth(monthStart, -1);

        log.info("通知所有门店到店数, 本月开始时间:{}, 本月结束时间:{}", DateUtil.formatDateTime(monthStart), DateUtil.formatDateTime(monthEnd));

        List<ScrmCheckinUserInfoDTO> checkinUserInfoDTOS = baseMapper.queryCheckinUsersByDate(lastMonth, monthEnd);

        Date currentDate = new Date();
        // 获取当天的开始时间，结束时间
        DateTime todayStart = DateUtil.beginOfDay(currentDate);
        DateTime todayEnd = DateUtil.endOfDay(currentDate);
        log.info("通知所有门店到店数, 今日开始时间:{}, 今日结束时间:{}", DateUtil.formatDateTime(todayStart), DateUtil.formatDateTime(todayEnd));
        // 获取本周的开始时间，结束时间
        DateTime weekStart = DateUtil.beginOfWeek(currentDate, true);
        DateTime weekEnd = DateUtil.endOfWeek(currentDate, true);
        log.info("通知所有门店到店数, 本周开始时间:{}, 本周结束时间:{}", DateUtil.formatDateTime(weekStart), DateUtil.formatDateTime(weekEnd));
        // 获取每个门店当天到店数
        Map<Long, Long> todayStoreCheckinUsers = checkinUserInfoDTOS.stream()
            .filter(i -> DateUtil.isIn(i.getCheckinTime(), todayStart, todayEnd))
            .collect(Collectors.groupingBy(ScrmCheckinUserInfoDTO::getStoreId, Collectors.counting()));

        // 获取每个门店本周到店数
        Map<Long, Long> weekStoreCheckinUsers = checkinUserInfoDTOS.stream()
            .filter(i -> DateUtil.isIn(i.getCheckinTime(), weekStart, weekEnd))
            .collect(Collectors.groupingBy(ScrmCheckinUserInfoDTO::getStoreId, Collectors.counting()));

        // 获取每个门店本月到店数
        Map<Long, Long> monthStoreCheckinUsers = checkinUserInfoDTOS.stream()
            .filter(i -> DateUtil.isIn(i.getCheckinTime(), monthStart, monthEnd))
            .collect(Collectors.groupingBy(ScrmCheckinUserInfoDTO::getStoreId, Collectors.counting()));

        // 获取门店店长手机号配置
        List<HeStoreMailConfigDTO> storeMailConfigDTOS = heUserBasicService.queryStoreMailConfig();
        Map<Long, String> storePhoneConfigMap = storeMailConfigDTOS.stream()
            .filter(i -> i.getType().equals(0))
            .collect(Collectors.toMap(HeStoreMailConfigDTO::getStoreId, HeStoreMailConfigDTO::getPhone));

        // 获取所有的门店
        List<CfgStore> allStoreList = cfgStoreService.getStoreList();

        for (CfgStore cfgStore : allStoreList) {
            if (cfgStore.getType().equals(0) || cfgStore.getType().equals(1)) {
                String messageContent = "截止%s（今日）20时00分00秒，%s门店，今日累计打卡数【%d】，本周累计打卡数【%d】，本月累计打卡数【%d】.";
                messageContent = String.format(messageContent,
                    DateUtil.format(currentDate, DatePattern.CHINESE_DATE_PATTERN),
                    cfgStore.getStoreName(),
                    todayStoreCheckinUsers.getOrDefault(cfgStore.getStoreId().longValue(), 0L),
                    weekStoreCheckinUsers.getOrDefault(cfgStore.getStoreId().longValue(), 0L),
                    monthStoreCheckinUsers.getOrDefault(cfgStore.getStoreId().longValue(), 0L)
                );

                String phone = storePhoneConfigMap.getOrDefault(cfgStore.getStoreId().longValue(), "");
                if (ObjectUtil.isEmpty(phone)) {
                    log.info("{}门店到店客资数消息未发送，原因:未配置接收人, 消息内容:{}", cfgStore.getStoreName(), messageContent);
                    continue;
                }

                List<String> phoneList = Arrays.asList(phone.split(","));
                ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
                scrmSendMessageRequest.setContent(messageContent);
                scrmSendMessageRequest.setTouser(phoneList);
                scrmSendMessageRequest.setAppId(WeWorkAppEnum.SERVICE_NOTIFY.getCode());
                scrmMessageService.sendMessage(scrmSendMessageRequest);

                log.info("{}门店到店客资数消息发送成功,接收人:{}，消息内容:{}", cfgStore.getStoreName(), JSONUtil.toJsonStr(phoneList), messageContent);
            }
        }
    }

    /**
     * 400角色新增商机
     *
     * @param scrmOpportunityRequest
     */
    @Override
    public void customerAddOpportunity(SCRMOpportunityRequest scrmOpportunityRequest) {
        //入参
        SCRMOpportunityRequest initialRequest = new SCRMOpportunityRequest();
        BeanUtils.copyProperties(scrmOpportunityRequest, initialRequest);

        // 先将scrm传参中的值转为对应的picp的值
        SCRMOpportunityRequest request = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(initialRequest);
        ScrmBusinessOpportunityPO convertScrmBusinessOpportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(request);

        // 设置商机中间表的所有人, 默认为商机创建人
        Long opportunityTempUserId = convertScrmBusinessOpportunityPO.getOwnerId();
        Long opportunityTempUserDimDepart = convertScrmBusinessOpportunityPO.getDimDepart();

        // 创建商机的时候添加新增、更新、阶段时间、商机的部门
        if(ObjectUtil.isEmpty(convertScrmBusinessOpportunityPO.getDimDepart())){
            ScrmUserPO scrmUserPO = scrmUserService.queryByScrmId(convertScrmBusinessOpportunityPO.getOwnerId());
            if (Objects.nonNull(scrmUserPO)) {
                convertScrmBusinessOpportunityPO.setDimDepart(scrmUserPO.getDimDepart());
            }
        }
        convertScrmBusinessOpportunityPO.setCreatedAt(new Date());
        convertScrmBusinessOpportunityPO.setUpdatedAt(new Date());
        convertScrmBusinessOpportunityPO.setStageUpdatedAt(new Date());

        // 根据商机id，查询商机在不在
        ScrmBusinessOpportunityPO oldOpportunityPO = getOne(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .select(ScrmBusinessOpportunityPO::getId)
            .eq(ScrmBusinessOpportunityPO::getScrmId, convertScrmBusinessOpportunityPO.getScrmId())
            .last("limit 1"));
        if (ObjectUtil.isNotNull(oldOpportunityPO)) {
            convertScrmBusinessOpportunityPO.setId(oldOpportunityPO.getId());
        }

        // 先保存一次商机
        this.saveOrUpdate(convertScrmBusinessOpportunityPO);

        // 获取意向套餐的名称
        String intendedGoods = "";
        if (ObjectUtil.isNotNull(scrmOpportunityRequest.getCustomItem201__c())) {
            intendedGoods = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "customItem201__c", Long.valueOf(scrmOpportunityRequest.getCustomItem201__c()));
        }

        // 获取意向套餐的名称
        String intendedNursingMode = "";
        if (ObjectUtil.isNotNull(scrmOpportunityRequest.getCustomItem202__c())) {
            intendedNursingMode = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "customItem202__c", Long.valueOf(scrmOpportunityRequest.getCustomItem202__c()));
        }

        // 获取客户身上的预算字段值
        String budget = "";
        if (ObjectUtil.isNotNull(scrmOpportunityRequest.getCustomerBudgetValue())) {
            budget = xsyScrmService.convertScrmOptionIdToLabel("account", "customItem270__c", Long.valueOf(scrmOpportunityRequest.getCustomerBudgetValue()));
        }

        // 获取客户希望联系方式
        String contactWay = ScrmContactWayEnum.fromCode(scrmOpportunityRequest.getContactway__c());

        // 保存完商机了，需要去根据规则，获取销售信息
        OpportunityFact fact = new OpportunityFact();

        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.getOne(new LambdaQueryWrapper<ScrmCustomerPO>()
            .eq(ScrmCustomerPO::getScrmCustomerId, scrmOpportunityRequest.getCustomerId())
            .last("limit 1"));

        Integer orderType = getOrderTypeByOpportunityEntityType(convertScrmBusinessOpportunityPO.getEntityType());

        // 如果是产康商机，则要标记客户为产康客户
        if (PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode().equals(orderType)
            && Objects.isNull(scrmCustomerPO.getProductionNewleadTime())) {
            scrmCustomerPO.setProductionNewleadTime(new Date());
            scrmCustomerService.updateById(scrmCustomerPO);

            // 商机自定义类型标记为2-馆外产康商机
            convertScrmBusinessOpportunityPO.setCustomType(2);
        }

        fact.setOrderType(orderType);
        fact.setScrmOpportunityId(convertScrmBusinessOpportunityPO.getScrmId());
        fact.setIntendedGoods(intendedGoods);
        fact.setIntendedNursingMode(intendedNursingMode);
        fact.setCustomerBudget(budget);
        fact.setContactWay(contactWay);

        if (ObjectUtil.isNotNull(scrmCustomerPO)) {
            fact.setClientName(scrmCustomerPO.getName());
            fact.setClientPhone(scrmCustomerPO.getPhone());
            fact.setScrmMemberId(scrmCustomerPO.getScrmCustomerId());
            fact.setFromType(scrmCustomerPO.getCorrectFromType());
            fact.setProductionFromType(scrmCustomerPO.getProductionFromType());
        }

        // 获取客户预算的名称
        /*
        if (ObjectUtil.isNotNull(scrmOpportunityRequest.getCustomerBudget__c())) {
            String customerBudgetName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "customerBudget__c",
                Long.valueOf(scrmOpportunityRequest.getCustomerBudget__c()));
            fact.setCustomerBudget(customerBudgetName);
        }*/

        // 获取意向国家的名称
        if (ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getIntendedCountry())) {
            fact.setCountry(convertScrmBusinessOpportunityPO.getIntendedCountry().toString());

            String countryName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "intendedCountry__c",
                Long.valueOf(scrmOpportunityRequest.getIntendedCountry__c()));
            fact.setCountryName(countryName);
        }

        // 获取意向省份的名称
        if (ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getIntendedProvince())) {
            fact.setProvince(convertScrmBusinessOpportunityPO.getIntendedProvince().toString());

            String provinceName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "intendedProvince__c",
                Long.valueOf(scrmOpportunityRequest.getIntendedProvince__c()));
            log.info("获取意向省份的id为:{}, 名称为:{}", scrmOpportunityRequest.getIntendedProvince__c(), provinceName);
            fact.setProvinceName(provinceName);
        }
        // 获取意向城市的名称
        if (ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getIntendedCity())) {
            fact.setCity(convertScrmBusinessOpportunityPO.getIntendedCity().toString());

            String cityName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "intendedCity__c",
                Long.valueOf(scrmOpportunityRequest.getIntendedCity__c()));
            log.info("获取意向城市的id为:{}, 名称为:{}", scrmOpportunityRequest.getIntendedCity__c(), cityName);
            fact.setCityName(cityName);
        }
        // 获取意向区/县的名称
        if (ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getIntendedArea())) {
            fact.setDistrict(convertScrmBusinessOpportunityPO.getIntendedArea().toString());

            String districtName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "intendedArea__c",
                Long.valueOf(scrmOpportunityRequest.getIntendedArea__c()));
            fact.setDistrictName(districtName);
            log.info("获取意向区、县的id为:{}, 名称为:{}", scrmOpportunityRequest.getIntendedArea__c(), districtName);
        }
        if (ObjectUtil.isNotNull(convertScrmBusinessOpportunityPO.getIntendedStore())) {
            fact.setStoreId(convertScrmBusinessOpportunityPO.getIntendedStore());
            CfgStore cfgStore = cfgStoreService.queryByStoreId(
                convertScrmBusinessOpportunityPO.getIntendedStore());
            if (ObjectUtil.isNotNull(cfgStore)) {
                fact.setStoreName(cfgStore.getStoreName());
            }
        }
        if (ObjectUtil.isNotEmpty(scrmOpportunityRequest.getIntendedBrand__c())) {
            fact.setStoreType(convertScrmBusinessOpportunityPO.getIntendedBrand().toString());
            String brandName = xsyScrmService.convertScrmOptionIdToLabel("opportunity", "intendedBrand__c",
                Long.valueOf(scrmOpportunityRequest.getIntendedBrand__c()));
            fact.setBrandName(brandName);
        }

        log.info("400创建商机，查询要分配的销售id, 参数:{}", JSONUtil.toJsonStr(fact));
        Result<OpportunityFact> opportunityFactResult = snowballEngine.assignOpportunity(fact);
        log.info("400创建商机，查询要分配的销售id, 结果:{}", JSONUtil.toJsonStr(opportunityFactResult));

        if (opportunityFactResult.getSuccess()) {
            Long scrmMemberId = opportunityFactResult.getData().getScrmMemberId();
            Long teamId = opportunityFactResult.getData().getTeamId();

            // 获取销售
            ScrmUserPO scrmUserPO = scrmUserService.queryByScrmId(scrmMemberId);

            // 如果能找到一个销售，则商机中间表中的所属人id为销售id
            opportunityTempUserId = scrmMemberId;
            if (ObjectUtil.isNotEmpty(scrmUserPO)) {
                opportunityTempUserDimDepart = scrmUserPO.getDimDepart();
            }

            convertScrmBusinessOpportunityPO.setOwnerId(scrmMemberId);
            convertScrmBusinessOpportunityPO.setDimDepart(opportunityTempUserDimDepart);

            // 将销售加到客户的团队成员中
            ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
            scrmAddTeamMemberRequest.setUserId(scrmMemberId);
            //客户默认为1
            scrmAddTeamMemberRequest.setRecordFrom(1L);
            scrmAddTeamMemberRequest.setRecordFrom_data(convertScrmBusinessOpportunityPO.getCustomerId());
            scrmAddTeamMemberRequest.setOwnerFlag(1);
            log.info("400创建商机, 找到分配的销售, 添加销售到客户团队成员={}", JSONUtil.parse(scrmAddTeamMemberRequest));
            xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);

            // 添加团队成员到记录表中
            ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
                .recordId(scrmAddTeamMemberRequest.getRecordFrom_data())
                .userId(scrmAddTeamMemberRequest.getUserId())
                .type(1)
                .build();

            scrmTeamMemberRecordService.edit(recordRequest);

            // 更新用户的staffPhone列表
            List<String> staffPhoneList = scrmCustomerPO.getStaffPhone();
            if (ObjectUtil.isEmpty(staffPhoneList)) {
                staffPhoneList = new ArrayList<>();
            }
            staffPhoneList.add(scrmUserPO.getPhone());
            scrmCustomerPO.setStaffPhone(staffPhoneList);
            scrmCustomerService.updateById(scrmCustomerPO);

            // 获取销售与门店关系
            ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getScrmUserStoreConfig(
                scrmUserPO, convertScrmBusinessOpportunityPO.getIntendedStore());

            // 商机所属的门店id（取ecp的门店id值）
            Long opportunityContractedStore = null;
            if (ObjectUtil.isNotNull(scrmUserStoreConfigPO)) {
                convertScrmBusinessOpportunityPO.setContractedStore(scrmUserStoreConfigPO.getScrmRecordId());
                opportunityContractedStore = scrmUserStoreConfigPO.getStoreId();
            }

            // 不需要了，已经直接在scrm中新增了销售与对应门店的关系
            /*
            if (!Objects.equals(opportunityContractedStore, Long.valueOf(convertScrmBusinessOpportunityPO.getIntendedStore()))) {
                // 如果选中销售的门店非意向门店，则需要告警
                CfgStore actualStore = Objects.nonNull(opportunityContractedStore) ? cfgStoreService.queryByStoreId(opportunityContractedStore.intValue()) : null;
                String storeNotMatchNotice = String.format(
                    ScrmQwCustomerMessageConstant.CUSTOMER_DISTRIBUTION_STORE_NOT_MATCH_NOTICE,
                    StringUtils.defaultIfBlank(fact.getClientName(), ""),
                    StringUtils.defaultIfBlank(fact.getClientPhone(), ""),
                    StringUtils.defaultIfBlank(fact.getStoreName(), ""),
                    ObjectUtil.isNotNull(actualStore) ? actualStore.getStoreName() : "");

                noticeMessageAssembler.pushMsg(storeNotMatchNotice, NoticeTypeEnum.SCRM_CUSTOMER_DISTRIBUTION_NOTICE.getBizType());
            }*/

            Long brandStoreId = null;
            if (ObjectUtil.isNotEmpty(convertScrmBusinessOpportunityPO.getContractedStore())) {
                // 能确定一个门店，要获取到该门店对应的品牌
                //brandStoreId = xsyScrmService.queryStoreBrandRecordIdBySaleStoreId(convertScrmBusinessOpportunityPO.getContractedStore());
                if (Objects.nonNull(opportunityContractedStore)) {
                    ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(opportunityContractedStore.intValue());
                    if (Objects.nonNull(scrmBrandStoreConfigPO)) {
                        brandStoreId = scrmBrandStoreConfigPO.getScrmId();
                    }
                }

                // 更新并分配用户到php
                scrmAddOrUpdateClient(convertScrmBusinessOpportunityPO);
            }

            // 更新商机到scrm
            OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(convertScrmBusinessOpportunityPO);
            opportunityRequest.setStore__c(brandStoreId);
            opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);
            xsyScrmService.opportunityEdit(opportunityRequest);

            // 新增400客资分配表
            scrmUserAssignAccountTempRecordService.handleTempRecord(opportunityRequest.getCreatedBy(),
                scrmMemberId, opportunityRequest.getAccountId(), opportunityRequest.getId(), opportunityRequest.getCustomItem174__c(), teamId);

            // 发送企微消息通知
            List<Integer> intendedProductIds = new ArrayList<>();
            if (Objects.nonNull(scrmOpportunityRequest.getIntendedProduct__c())) {
                intendedProductIds = Arrays.stream(scrmOpportunityRequest.getIntendedProduct__c()).collect(Collectors.toList());
            }
            OpportunityDistributionMessage distributionMessage = OpportunityDistributionMessage.builder()
                .customerId(convertScrmBusinessOpportunityPO.getCustomerId())
                .saleId(scrmMemberId)
                .contractedStoreId(opportunityContractedStore)
                .intendedGoods(intendedGoods)
                .intendedNursingMode(intendedNursingMode)
                .contactWay(contactWay)
                .orderType(orderType)
                .visitAppointmentTime(scrmOpportunityRequest.getVisitAppointmentTime__c())
                .visitProject(scrmOpportunityRequest.getVisitProject__c())
                .intendedProductIds(intendedProductIds)
                .sendManager(true)
                .build();
            scrmDistributionCustomerSuccessMessageNotify(distributionMessage);
        }

        // 添加商机的中间表，记录商机创建时间
        saveOpportunityStatus(
            scrmOpportunityRequest.getId(),
            convertScrmBusinessOpportunityPO.getOpportunityName(),
            convertScrmBusinessOpportunityPO.getSaleStageId(),
            null,
            opportunityTempUserId,
            opportunityTempUserDimDepart,
            convertScrmBusinessOpportunityPO.getEntityType(),
            convertScrmBusinessOpportunityPO.getLockStatus(),
            null
        );

        // 再次更新
        this.saveOrUpdate(convertScrmBusinessOpportunityPO);
    }

    /**
     * 根据时间戳查询scrm中门店分配的客户数量
     *
     * @param dateStartTime
     * @param dateEndTime
     * @return
     */
    @Override
    public List<StoreUserNumVO> queryScrmUserNum(Long dateStartTime, Long dateEndTime) {
        DateTime dateStart = DateUtil.date(dateStartTime);
        DateTime dateEnd = DateUtil.date(dateEndTime);

        List<ScrmCheckinUserInfoDTO> distributeUserInfoDTOS = baseMapper.queryDistributeUsersByDate(dateStart, dateEnd);
        Map<Long, Long> storeDistributeNums = distributeUserInfoDTOS.stream().collect(
            Collectors.groupingBy(ScrmCheckinUserInfoDTO::getStoreId, Collectors.counting()));

        // 获取所有的门店
        List<CfgStore> allStoreList = cfgStoreService.getStoreList();

        List<StoreUserNumVO> storeUserNumVOList = new ArrayList<>();
        for (CfgStore cfgStore : allStoreList) {
            if (cfgStore.getType().equals(StoreTypeEnum.SAINT_BELLA.code())
                || cfgStore.getType().equals(StoreTypeEnum.BABY_BELLA.code())
                || cfgStore.getType().equals(StoreTypeEnum.ISLA_BELLA.code())) {
                StoreUserNumVO storeUserNumVO = new StoreUserNumVO();
                storeUserNumVO.setStoreId(Long.valueOf(cfgStore.getStoreId()));
                storeUserNumVO.setStoreName(cfgStore.getStoreName());
                storeUserNumVO.setType(cfgStore.getType());
                storeUserNumVO.setUserNum(0);
                if (ObjectUtil.isNotEmpty(storeDistributeNums)) {
                    Long userNum = storeDistributeNums.getOrDefault(storeUserNumVO.getStoreId(), 0L);
                    storeUserNumVO.setUserNum(userNum.intValue());
                }

                storeUserNumVOList.add(storeUserNumVO);
            }
        }
        return storeUserNumVOList;
    }

    /**
     * 查询用户指定订单类型的商机列表 如果订单类型为空，则默认获取该用户所有的商机
     *
     * @param customerId
     * @param orderType  订单类型，参考(com.stbella.order.common.enums.core.OmniOrderTypeEnum)
     * @return
     */
    @Override
    public List<ScrmBusinessOpportunityPO> queryCustomerOpportunityList(Long customerId, Integer orderType) {
        Long entityType = orderType2OpportunityEntityType(orderType);

        return list(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .eq(ScrmBusinessOpportunityPO::getCustomerId, customerId)
            .eq(ObjectUtil.isNotNull(entityType), ScrmBusinessOpportunityPO::getEntityType, entityType)
            .orderByDesc(ScrmBusinessOpportunityPO::getGmtCreate));
    }

    /**
     * 订单类型转为商机业务类型
     *
     * @param orderType
     * @return
     */
    @Override
    public Long orderType2OpportunityEntityType(Integer orderType) {
        ScrmConfigBizTypeEnum bizTypeEnum = null;

        OmniOrderTypeEnum orderTypeEnum = OmniOrderTypeEnum.getByCode(orderType);
        switch (orderTypeEnum) {
            case MONTH_ORDER: // 标准月子
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER;
                break;
            case SMALL_MONTH_ORDER: // 小月子
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER;
                break;
            case OTHER_MONTH_ORDER: // 其他月子订单
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER;
                break;
            case NURSE_OUTSIDE_ORDER: // 护士外派
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER;
                break;
            case PRODUCTION_ORDER: // 产康
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER;
                break;
            case SBRA_ORDER: // sbra
                bizTypeEnum = ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER;
                break;
            default:
                break;
        }

        if (ObjectUtil.isNotNull(bizTypeEnum)) {
            ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(bizTypeEnum.getCode());
            if (ObjectUtil.isNotNull(scrmConfigPO)) {
                return Long.valueOf(scrmConfigPO.getBizContent());
            }
        }
        return null;
    }

    /**
     * 获取scrm商机类型对应进行中的状态id列表
     *
     * @param entityType scrm商机类型
     * @return
     */
    @Override
    public List<Long> getOpportunityInProcessStatus(Long entityType) {
        if (ObjectUtil.isNull(entityType)) {
            return null;
        }

        List<Long> statusList = new ArrayList<>();

        Map<Integer, String> config = scrmConfigService.queryAllConfig();
        Long standardMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode()));
        Long smallMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode()));
        Long otherMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode()));
        Long nurseOutsideOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode()));
        Long productionOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode()));
        Long sbraOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode()));

        if (entityType.equals(standardMonthOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())));
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())));
        } else if (entityType.equals(smallMonthOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode())));
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode())));
        } else if (entityType.equals(otherMonthOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode())));
        } else if (entityType.equals(nurseOutsideOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode())));
        } else if (entityType.equals(productionOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode())));
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode())));
        } else if (entityType.equals(sbraOrder)) {
            statusList.add(Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode())));
        }

        return statusList;
    }

    /**
     * 通过订单类型获取scrm商机类型对应进行中的状态id列表
     *
     * @param orderType 订单类型，参考(com.stbella.order.common.enums.core.OmniOrderTypeEnum)
     * @return
     */
    @Override
    public List<Long> getOpportunityInProcessStatusByOrderType(Integer orderType) {
        Long entityType = orderType2OpportunityEntityType(orderType);
        return getOpportunityInProcessStatus(entityType);
    }

    /**
     * 根据商机业务类型获取商机状态对应的商机阶段
     *
     * @param entityType
     * @param opportunityStatus
     * @return
     */
    @Override
    public Long opportunityStatus2StageIdByEntityType(Long entityType, Integer opportunityStatus) {
        if (ObjectUtil.isNull(entityType)) {
            return null;
        }

        Map<Integer, Long> statusMap = new HashMap<>();

        Map<Integer, String> config = scrmConfigService.queryAllConfig();
        Long standardMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode()));
        Long smallMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode()));
        Long otherMonthOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode()));
        Long nurseOutsideOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode()));
        Long productionOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode()));
        Long sbraOrder = Long.valueOf(config.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode()));

        if (entityType.equals(standardMonthOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.ARRIVAL.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode())));
        } else if (entityType.equals(smallMonthOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.ARRIVAL.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESSTAGE_SMALLMONTH_LOSETHEBILL.getCode())));
        } else if (entityType.equals(otherMonthOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_LOSINGORDERS.getCode())));
        } else if (entityType.equals(nurseOutsideOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS.getCode())));
        } else if (entityType.equals(productionOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.ARRIVAL.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESSTAGE_PRODUCTION_DELIVERY.getCode())));
        } else if (entityType.equals(sbraOrder)) {
            statusMap.put(OpportunityStatusSaleStageEnum.ALLOCATION.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.WIN.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode())));
            statusMap.put(OpportunityStatusSaleStageEnum.LOSE.getCode(), Long.valueOf(config.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_TOLOSETHEORDER.getCode())));
        }

        return statusMap.getOrDefault(opportunityStatus, null);
    }

    /**
     * 根据订单类型获取商机状态对应的商机阶段
     *
     * @param orderType
     * @param opportunityStatus
     * @return
     */
    @Override
    public Long opportunityStatus2StageIdByOrderType(Integer orderType, Integer opportunityStatus) {
        Long entityType = orderType2OpportunityEntityType(orderType);
        return opportunityStatus2StageIdByEntityType(entityType, opportunityStatus);
    }

    /**
     * 在scrm中新增商机
     *
     * @param request
     * @param distributedUserId 分配人id
     * @return
     */
    @Override
    public ScrmBusinessOpportunityPO createScrmOpportunity(OpportunityRequest request, Long distributedUserId) {
        // 更新一下品牌门店字段
        if (ObjectUtil.isNotNull(request.getCustomItem174__c())) {
            ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(request.getCustomItem174__c());
            if (Objects.nonNull(scrmUserStoreConfigPO)) {
                ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(scrmUserStoreConfigPO.getStoreId().intValue());
                if (Objects.nonNull(scrmBrandStoreConfigPO)) {
                    request.setStore__c(scrmBrandStoreConfigPO.getScrmId());
                }
            }
        }

        if (Objects.nonNull(request.getIntendedStore__c())) {
            Long scrmIntendedStore = xsyScrmService.convertPicpIdToScrmOptionId("opportunity", "intendedStore__c", request.getIntendedStore__c().toString());
            request.setIntendedStore__c(scrmIntendedStore.intValue());
        }

        Long opportunityId = xsyScrmService.opportunityEdit(request);

        // 获取商机详情保存到我们的数据库
        ScrmOpportunityDetailVO scrmOpportunityDetailVO = xsyScrmService.queryOpportunityDetail(opportunityId);
        ScrmBusinessOpportunityPO opportunityPO = scrmConvert.scrmOpportunityDetailVO2ScrmBusinessOpportunityPO(scrmOpportunityDetailVO);

        Date realFinshTime = null;
        if (Objects.nonNull(request.getStageUpdatedAt())) {
            realFinshTime = DateUtil.date(request.getStageUpdatedAt());
        }
        // 保存到商机中间表
        saveOpportunityStatus(
            opportunityPO.getScrmId(),
            opportunityPO.getOpportunityName(),
            opportunityPO.getSaleStageId(),
            null,
            opportunityPO.getOwnerId(),
            opportunityPO.getDimDepart(),
            opportunityPO.getEntityType(),
            opportunityPO.getLockStatus(),
            realFinshTime
        );

        // 将新的商机所属人拉入客户的团队成员
        ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
        scrmAddTeamMemberRequest.setUserId(request.getOwnerId());
        // 客户默认为1
        scrmAddTeamMemberRequest.setRecordFrom(1L);
        scrmAddTeamMemberRequest.setRecordFrom_data(request.getAccountId());
        scrmAddTeamMemberRequest.setOwnerFlag(1);
        xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);

        // 添加团队成员到记录表中
        ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
            .recordId(scrmAddTeamMemberRequest.getRecordFrom_data())
            .userId(scrmAddTeamMemberRequest.getUserId())
            .type(1)
            .build();

        scrmTeamMemberRecordService.edit(recordRequest);

        // 新增400客资分配表
        if (Objects.isNull(distributedUserId)) {
            ScrmConfigPO managerConfig = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SCRM_SYSTEM_MANAGER_ID.getCode());
            distributedUserId = Long.valueOf(managerConfig.getBizContent());
        }

        scrmUserAssignAccountTempRecordService.handleTempRecord(
            distributedUserId,
            request.getOwnerId(),
            request.getAccountId(),
            opportunityId,
            request.getCustomItem174__c(),
            null
        );

        opportunityPO.setCreatedAt(new Date());
        opportunityPO.setUpdatedAt(new Date());
        opportunityPO.setStageUpdatedAt(new Date());

        if (save(opportunityPO)) {
            return opportunityPO;
        }

        return null;
    }

    /**
     * 处理更新商机的操作,并同步到scrm
     *
     * @param opportunityPO 商机po
     * @param stageTime 阶段到达时间
     * @return
     */
    @Override
    public Boolean updateOpportunity(ScrmBusinessOpportunityPO opportunityPO, Date stageTime) {
        if (Objects.isNull(opportunityPO)) {
            return false;
        }

        if (updateById(opportunityPO)) {
            OpportunityRequest opportunityRequest = scrmConvert.scrmBusinessOpportunityPO2OpportunityRequest(opportunityPO);
            opportunityRequest = xsyScrmService.convertOpportunityPicpRequestToScrmRequest(opportunityRequest);

            // 更新一下品牌门店字段
            if (ObjectUtil.isNotNull(opportunityRequest.getCustomItem174__c())) {
                ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(
                    opportunityRequest.getCustomItem174__c());
                if (Objects.nonNull(scrmUserStoreConfigPO)) {
                    ScrmBrandStoreConfigPO scrmBrandStoreConfigPO = scrmBrandStoreConfigService.queryByScrmId(
                        scrmUserStoreConfigPO.getStoreId().intValue());
                    if (Objects.nonNull(scrmBrandStoreConfigPO)) {
                        opportunityRequest.setStore__c(scrmBrandStoreConfigPO.getScrmId());
                    }
                }
            }
            xsyScrmService.opportunityEdit(opportunityRequest);

            if (Objects.isNull(stageTime)) {
                stageTime = DateUtil.date();
            }

            saveOpportunityStatus(
                opportunityPO.getScrmId(),
                opportunityPO.getOpportunityName(),
                opportunityPO.getSaleStageId(),
                opportunityPO.getContractedStore(),
                opportunityPO.getOwnerId(),
                opportunityPO.getDimDepart(),
                opportunityPO.getEntityType(),
                opportunityPO.getLockStatus(),
                stageTime
            );

            return true;
        }
        return false;
    }

    /**
     * 转移商机
     *
     * @param opportunityPO
     * @param newOwnerId
     * @return
     */
    @Override
    public ScrmBusinessOpportunityPO transformScrmOpportunity(ScrmBusinessOpportunityPO opportunityPO, Long newOwnerId) {
        OpportunityTransferVO opportunityTransferVO = xsyScrmService.opportunityTransfer(opportunityPO.getScrmId(), newOwnerId);
        if (ObjectUtil.isNull(opportunityTransferVO)) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "商机转移失败");
        }

        opportunityPO.setOwnerId(opportunityTransferVO.getOpportunity().getOwnerId());
        opportunityPO.setDimDepart(opportunityTransferVO.getOpportunity().getDimDepart());
        if (updateById(opportunityPO)) {
            if (CollectionUtil.isNotEmpty(opportunityTransferVO.getOpportunityTempList())) {
                opportunityTransferVO.getOpportunityTempList().forEach(opportunityTemp -> {
                    scrmOpportunityStatusRecordService.update(new LambdaUpdateWrapper<ScrmOpportunityStatusRecordPO>()
                        .eq(ScrmOpportunityStatusRecordPO::getScrmId, opportunityTemp.getId())
                        .set(ScrmOpportunityStatusRecordPO::getOwnerId, opportunityTemp.getOwnerId())
                        .set(ScrmOpportunityStatusRecordPO::getDimDepart, opportunityTemp.getDimDepart()));
                });
            }

            return opportunityPO;
        }

        return null;
    }

    /**
     * 获取scrm中指定时间段内的打卡人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    @Override
    public List<ScrmCheckinUserInfoDTO> queryCheckinUsersByDate(Date dateStart, Date dateEnd) {
        return baseMapper.queryCheckinUsersByDate(dateStart, dateEnd);
    }

    /**
     * 获取scrm中指定时间段内的分配人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    @Override
    public List<ScrmCheckinUserInfoDTO> queryDistributeUsersByDate(Date dateStart, Date dateEnd) {
        return baseMapper.queryDistributeUsersByDate(dateStart, dateEnd);
    }

    /**
     * 获取scrm中指定时间段内的首签人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    @Override
    public List<ScrmCheckinUserInfoDTO> queryFirstSignUsersByDate(Date dateStart, Date dateEnd) {
        return baseMapper.queryFirstSignUsersByDate(dateStart, dateEnd);
    }

    /**
     * 判断该门店是否有商机
     *
     * @param storeId
     * @return
     */
    @Override
    public Boolean existOpportunityByStoreId(Long storeId) {
        List<ScrmUserStoreConfigPO> storeConfigList = scrmUserStoreConfigService.listByStore(storeId.intValue());
        if (CollectionUtil.isEmpty(storeConfigList)) {
            return false;
        }

        List<Long> storeConfigIds = storeConfigList.stream()
            .map(ScrmUserStoreConfigPO::getScrmRecordId)
            .collect(Collectors.toList());

        int total = count(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .in(ScrmBusinessOpportunityPO::getContractedStore, storeConfigIds));
        return total > 0;
    }

    /**
     * 获取绑定订单的对应商机
     *
     * @param scrmOrderId
     * @return
     */
    @Override
    public ScrmBusinessOpportunityPO queryOpportunityByScrmOrderId(Long scrmOrderId) {
        return getOne(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .eq(ScrmBusinessOpportunityPO::getMonthOrder, scrmOrderId)
            .last("limit 1"));
    }

    //首次触达
    private void batchUpdateOpportunityTempByFirsttouch(
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO,
            List<ScrmOpportunityStatusRecordPO> scrmOpportunityTempList,
            List<SaveOpportunityStatusDTO> saveOpportunityStatusDTOList,
            List<ScrmOpportunityStatusRecordPO> updateOpportunityStatusRecordPOList,
            List<TabClientPO> tabClientListByCondition,
            List<ScrmUserStoreConfigPO> scrmStoreConfigList,
            ScrmCreateClockInStoreDTO scrmCreateClockInStoreRequest,
            Map<Integer, String> scrmConfig){

        Long customerId = scrmBusinessOpportunityPO.getCustomerId();

        Optional<ScrmUserStoreConfigPO> storeConfigFirst = scrmStoreConfigList.stream().filter(s -> s.getScrmRecordId().equals(scrmBusinessOpportunityPO.getContractedStore())).findFirst();

        TabClientPO first = null;

        if (storeConfigFirst.isPresent()) {
            first = tabClientListByCondition.stream().filter(t -> ObjectUtil.equal(t.getScrmId(),customerId+"") && t.getStoreId().equals(storeConfigFirst.get().getStoreId().intValue())).findFirst().get();
        }else{
            if(ObjectUtil.isNotEmpty(scrmCreateClockInStoreRequest)){
                //如果用户的scrm不在tab_client表中，手动去查
                ScrmUserStoreConfigPO byRecordId = scrmUserStoreConfigService.getByRecordId(scrmCreateClockInStoreRequest.getStoreId());
                if(ObjectUtil.isNotEmpty(byRecordId) && ObjectUtil.isNotEmpty(byRecordId.getStoreId())){
                    TabClientPO tabClientPO = tabClientService.queryClientListByPhoneAndStoreId(scrmCreateClockInStoreRequest.getPhone(), byRecordId.getStoreId().intValue());
                    if(ObjectUtil.isNotEmpty(tabClientPO)){
                        first = tabClientPO;
                    }
                }

            }
        }



        List<ScrmOpportunityStatusRecordPO> statusList = scrmOpportunityTempList.stream().filter(s -> s.getOpportunityScrmId().equals(scrmBusinessOpportunityPO.getScrmId()) && s.getSaleStageId() == 1).collect(Collectors.toList());

        if(CollectionUtil.isEmpty(statusList)){
            //不存在，需要手动添加1条记录
            SaveOpportunityStatusDTO saveOpportunityStatusDTO = new SaveOpportunityStatusDTO();
            saveOpportunityStatusDTO.setScrmOpportunityId(scrmBusinessOpportunityPO.getScrmId());
            saveOpportunityStatusDTO.setScrmOpportunityName(scrmBusinessOpportunityPO.getOpportunityName());
            saveOpportunityStatusDTO.setSaleStageId(getOppStageIdByEntityTypeAndOpportunityStatusSaleStage(scrmBusinessOpportunityPO.getEntityType(),OpportunityStatusSaleStageEnum.ALLOCATION,scrmConfig));
            saveOpportunityStatusDTO.setContractedStore(scrmBusinessOpportunityPO.getContractedStore());
            saveOpportunityStatusDTO.setOwnerId(scrmBusinessOpportunityPO.getOwnerId());
            saveOpportunityStatusDTO.setDimDepart(scrmBusinessOpportunityPO.getDimDepart());
            saveOpportunityStatusDTO.setEntityType(scrmBusinessOpportunityPO.getEntityType());
            saveOpportunityStatusDTO.setLockStatus(scrmBusinessOpportunityPO.getLockStatus());
            //首次触达的实际阶段到达时间：客户的 tab_client的record的时间
            if(ObjectUtil.isNotEmpty(first)){
                saveOpportunityStatusDTO.setRealFinishTime(new Date(first.getRecordTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
            }
            saveOpportunityStatusDTOList.add(saveOpportunityStatusDTO);
        }else{
            //判断有没有新字段
            for (ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO : statusList) {
                Long storeId = scrmOpportunityStatusRecordPO.getScrmUserStoreConfigId();
                if(ObjectUtil.isEmpty(storeId)){
                    //需要手动添加中间表的门店（新）这个字段
                    if(ObjectUtil.isNotEmpty(first)){
                        if(ObjectUtil.isEmpty(scrmOpportunityStatusRecordPO.getRealFinishTime())){
                            scrmOpportunityStatusRecordPO.setRealFinishTime(new Date(first.getRecordTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                        }
                    }
                    updateOpportunityStatusRecordPOList.add(scrmOpportunityStatusRecordPO);
                }
            }
        }
    }

    //邀约到店
    private void batchUpdateOpportunityTempByInvitationToTheStore(
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO,
            List<ScrmOpportunityStatusRecordPO> scrmOpportunityTempList,
            List<SaveOpportunityStatusDTO> saveOpportunityStatusDTOList,
            List<ScrmOpportunityStatusRecordPO> updateOpportunityStatusRecordPOList,
            List<TabClientPO> tabClientListByCondition,
            List<ScrmUserStoreConfigPO> scrmStoreConfigList,
            Map<Integer, String> scrmConfig
    ){

        List<Long> oppEntity = Arrays.asList(
                new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())),
                        new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode()))
        );

        if(!oppEntity.contains(scrmBusinessOpportunityPO.getEntityType())){
            //邀约到店只有标准跟小月子，其他商机类型不需要处理
            return;
        }

        Long signInInformation = scrmBusinessOpportunityPO.getSignInInformation();
        List<ScrmOpportunityStatusRecordPO> statusList = scrmOpportunityTempList.stream().filter(s -> s.getOpportunityScrmId().equals(scrmBusinessOpportunityPO.getScrmId()) && s.getSaleStageId() == 2).collect(Collectors.toList());
        ScrmCreateClockInStoreDTO scrmCreateClockInStoreRequest = null;
        if(ObjectUtil.isNotEmpty(signInInformation)){
            //首次触达的实际阶段到达时间：邀约到店那条记录的时间
            scrmCreateClockInStoreRequest = xsyScrmService.getSignInInformation(signInInformation);
        }

        //首先判断是否有首次触达的记录
        batchUpdateOpportunityTempByFirsttouch(scrmBusinessOpportunityPO,scrmOpportunityTempList,saveOpportunityStatusDTOList,updateOpportunityStatusRecordPOList,tabClientListByCondition,scrmStoreConfigList,scrmCreateClockInStoreRequest,scrmConfig);

        if(CollectionUtil.isEmpty(statusList)){
            //不存在，需要手动添加1条记录
            SaveOpportunityStatusDTO saveOpportunityStatusDTO = new SaveOpportunityStatusDTO();
            saveOpportunityStatusDTO.setScrmOpportunityId(scrmBusinessOpportunityPO.getScrmId());
            saveOpportunityStatusDTO.setScrmOpportunityName(scrmBusinessOpportunityPO.getOpportunityName());
            saveOpportunityStatusDTO.setSaleStageId(getOppStageIdByEntityTypeAndOpportunityStatusSaleStage(scrmBusinessOpportunityPO.getEntityType(),OpportunityStatusSaleStageEnum.ARRIVAL,scrmConfig));
            saveOpportunityStatusDTO.setContractedStore(scrmBusinessOpportunityPO.getContractedStore());
            saveOpportunityStatusDTO.setOwnerId(scrmBusinessOpportunityPO.getOwnerId());
            saveOpportunityStatusDTO.setDimDepart(scrmBusinessOpportunityPO.getDimDepart());
            saveOpportunityStatusDTO.setEntityType(scrmBusinessOpportunityPO.getEntityType());
            saveOpportunityStatusDTO.setLockStatus(scrmBusinessOpportunityPO.getLockStatus());
            if(ObjectUtil.isNotEmpty(scrmCreateClockInStoreRequest)){
                saveOpportunityStatusDTO.setRealFinishTime(scrmCreateClockInStoreRequest.getSignTime());
            }
            saveOpportunityStatusDTOList.add(saveOpportunityStatusDTO);
        }else{
            //判断有没有新字段
            for (ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO : statusList) {
                Long storeId = scrmOpportunityStatusRecordPO.getScrmUserStoreConfigId();
                if(ObjectUtil.isEmpty(storeId) && ObjectUtil.isEmpty(scrmOpportunityStatusRecordPO.getRealFinishTime())){
                    //需要手动添加中间表的门店（新）这个字段
                    scrmOpportunityStatusRecordPO.setRealFinishTime(scrmCreateClockInStoreRequest.getSignTime());
                    updateOpportunityStatusRecordPOList.add(scrmOpportunityStatusRecordPO);
                }
            }
        }
    }

    //签单
    private void batchUpdateOpportunityTempByWinningSingle(
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO,
            List<ScrmOpportunityStatusRecordPO> scrmOpportunityTempList,
            List<SaveOpportunityStatusDTO> saveOpportunityStatusDTOList,
            List<ScrmOpportunityStatusRecordPO> updateOpportunityStatusRecordPOList,
            List<TabClientPO> tabClientListByCondition,
            List<ScrmCustomerOrderPO> scrmCustomerOrderPOList,
            List<ScrmUserStoreConfigPO> scrmStoreConfigList,
            Map<Integer, String> scrmConfig
    ){
        //首先判断是否有邀约到店、首次触达的数据
        batchUpdateOpportunityTempByInvitationToTheStore(scrmBusinessOpportunityPO,scrmOpportunityTempList,saveOpportunityStatusDTOList,updateOpportunityStatusRecordPOList,tabClientListByCondition,scrmStoreConfigList,scrmConfig);

        List<ScrmOpportunityStatusRecordPO> statusList = scrmOpportunityTempList.stream().filter(s -> s.getOpportunityScrmId().equals(scrmBusinessOpportunityPO.getScrmId()) && s.getSaleStageId() == 3).collect(Collectors.toList());

        Optional<ScrmCustomerOrderPO> first;
        if(ObjectUtil.isNotEmpty(scrmBusinessOpportunityPO.getMonthOrder())){
            first = scrmCustomerOrderPOList.stream().filter(s -> s.getScrmId().equals(scrmBusinessOpportunityPO.getMonthOrder())).findFirst();
        }else{
            first = scrmCustomerOrderPOList.stream().filter(s -> s.getScrmId().equals(scrmBusinessOpportunityPO.getNonMonthOrder())).findFirst();
        }

        Date date = null;
        if(first.isPresent()){
            //签到的实际是时间是支付一半的时间
            date = first.get().getPercentFirstTime();
        }


        if(CollectionUtil.isEmpty(statusList)){
            //不存在，需要手动添加1条记录
            SaveOpportunityStatusDTO saveOpportunityStatusDTO = new SaveOpportunityStatusDTO();
            saveOpportunityStatusDTO.setScrmOpportunityId(scrmBusinessOpportunityPO.getScrmId());
            saveOpportunityStatusDTO.setScrmOpportunityName(scrmBusinessOpportunityPO.getOpportunityName());
            saveOpportunityStatusDTO.setSaleStageId(getOppStageIdByEntityTypeAndOpportunityStatusSaleStage(scrmBusinessOpportunityPO.getEntityType(),OpportunityStatusSaleStageEnum.WIN,scrmConfig));
            saveOpportunityStatusDTO.setContractedStore(scrmBusinessOpportunityPO.getContractedStore());
            saveOpportunityStatusDTO.setOwnerId(scrmBusinessOpportunityPO.getOwnerId());
            saveOpportunityStatusDTO.setDimDepart(scrmBusinessOpportunityPO.getDimDepart());
            saveOpportunityStatusDTO.setEntityType(scrmBusinessOpportunityPO.getEntityType());
            saveOpportunityStatusDTO.setLockStatus(scrmBusinessOpportunityPO.getLockStatus());
            saveOpportunityStatusDTO.setRealFinishTime(date);
            saveOpportunityStatusDTOList.add(saveOpportunityStatusDTO);
        }else{
            //判断有没有新字段
            for (ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO : statusList) {
                Long storeId = scrmOpportunityStatusRecordPO.getScrmUserStoreConfigId();
                if(ObjectUtil.isEmpty(storeId) && ObjectUtil.isEmpty(scrmOpportunityStatusRecordPO.getRealFinishTime())){
                    //需要手动添加中间表的门店（新）这个字段
                    scrmOpportunityStatusRecordPO.setRealFinishTime(date);
                    updateOpportunityStatusRecordPOList.add(scrmOpportunityStatusRecordPO);
                }
            }
        }

    }

    private void batchUpdateOpportunityTempByLosingOrders(
            ScrmBusinessOpportunityPO scrmBusinessOpportunityPO,
            List<ScrmOpportunityStatusRecordPO> scrmOpportunityTempList,
            List<SaveOpportunityStatusDTO> saveOpportunityStatusDTOList,
            List<ScrmOpportunityStatusRecordPO> updateOpportunityStatusRecordPOList,
            List<TabClientPO> tabClientListByCondition,
            List<ScrmUserStoreConfigPO> scrmStoreConfigList,
            Map<Integer, String> scrmConfig
    ){
        //首先判断是否有邀约到店、首次触达的数据
        batchUpdateOpportunityTempByInvitationToTheStore(scrmBusinessOpportunityPO,scrmOpportunityTempList,saveOpportunityStatusDTOList,updateOpportunityStatusRecordPOList,tabClientListByCondition,scrmStoreConfigList,scrmConfig);

        List<ScrmOpportunityStatusRecordPO> statusList = scrmOpportunityTempList.stream().filter(s -> s.getOpportunityScrmId().equals(scrmBusinessOpportunityPO.getScrmId()) && s.getSaleStageId() == 4).collect(Collectors.toList());


        if(CollectionUtil.isEmpty(statusList)){
            //不存在，需要手动添加1条记录
            SaveOpportunityStatusDTO saveOpportunityStatusDTO = new SaveOpportunityStatusDTO();
            saveOpportunityStatusDTO.setScrmOpportunityId(scrmBusinessOpportunityPO.getScrmId());
            saveOpportunityStatusDTO.setScrmOpportunityName(scrmBusinessOpportunityPO.getOpportunityName());
            saveOpportunityStatusDTO.setSaleStageId(getOppStageIdByEntityTypeAndOpportunityStatusSaleStage(scrmBusinessOpportunityPO.getEntityType(),OpportunityStatusSaleStageEnum.LOSE,scrmConfig));
            saveOpportunityStatusDTO.setContractedStore(scrmBusinessOpportunityPO.getContractedStore());
            saveOpportunityStatusDTO.setOwnerId(scrmBusinessOpportunityPO.getOwnerId());
            saveOpportunityStatusDTO.setDimDepart(scrmBusinessOpportunityPO.getDimDepart());
            saveOpportunityStatusDTO.setEntityType(scrmBusinessOpportunityPO.getEntityType());
            saveOpportunityStatusDTO.setLockStatus(scrmBusinessOpportunityPO.getLockStatus());
            saveOpportunityStatusDTO.setRealFinishTime(scrmBusinessOpportunityPO.getGmtModified());
            saveOpportunityStatusDTOList.add(saveOpportunityStatusDTO);
        }else{
            //判断有没有新字段
            for (ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO : statusList) {
                Long storeId = scrmOpportunityStatusRecordPO.getScrmUserStoreConfigId();
                if(ObjectUtil.isEmpty(storeId) && ObjectUtil.isEmpty(scrmOpportunityStatusRecordPO.getRealFinishTime())){
                    //需要手动添加中间表的门店（新）这个字段
                    scrmOpportunityStatusRecordPO.setRealFinishTime(scrmBusinessOpportunityPO.getGmtModified());
                    updateOpportunityStatusRecordPOList.add(scrmOpportunityStatusRecordPO);
                }
            }
        }
    }


    public void updateOpportunityStatus(ScrmOpportunityStatusRecordPO scrmOpportunityStatusRecordPO1){
        try{
            OpportunityTempRequest req = new OpportunityTempRequest();
            if(ObjectUtil.isNotEmpty(scrmOpportunityStatusRecordPO1.getScrmId())){
                req.setId(scrmOpportunityStatusRecordPO1.getScrmId());
            } else {
                req.setCustomItem8__c(System.currentTimeMillis());
            }
            req.setCustomItem1__c(scrmOpportunityStatusRecordPO1.getOpportunityScrmId());
            req.setCustomItem2__c(scrmOpportunityStatusRecordPO1.getSaleStageId().intValue());
            //req.setCustomItem3__c(scrmOpportunityStatusRecordPO1.getScrmUserStoreConfigId());
            req.setEntityType(scrmOpportunityStatusRecordPO1.getEntityType());
            req.setOwnerId(scrmOpportunityStatusRecordPO1.getOwnerId());
            req.setDimDepart(scrmOpportunityStatusRecordPO1.getDimDepart());
            req.setUpdatedBy(scrmOpportunityStatusRecordPO1.getOwnerId());
            req.setUpdatedAt(System.currentTimeMillis());
            req.setLockStatus(2);

            /*
            if (ObjectUtil.isNotEmpty(scrmOpportunityStatusRecordPO1.getScrmUserStoreConfigId()) && !scrmOpportunityStatusRecordPO1.getScrmUserStoreConfigId().equals(0L)) {
                Long storeBrandRecordId = xsyScrmService.queryStoreBrandRecordIdBySaleStoreId(scrmOpportunityStatusRecordPO1.getScrmUserStoreConfigId());
                if (!storeBrandRecordId.equals(0L)) {
                    req.setStore__c(storeBrandRecordId);
                }
            }*/

            xsyScrmService.opportunityTempEdit(req);
        }catch (Exception e){
            log.error("商机中间表更新失败，scrmOpportunityStatusRecordPO1:{},e:{}", scrmOpportunityStatusRecordPO1,e);
        }

    }


    private Integer getOpportunityStatus(Long opportunitySaleStageId){
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();
        if(
                opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode())))
        ){
            return OpportunityStatusSaleStageEnum.ALLOCATION.getCode();
        }
        if(
                opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode())))
                    || opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode())))
                    || opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode())))
        ){
            return OpportunityStatusSaleStageEnum.ARRIVAL.getCode();
        }
        if(
                opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode())))
        ){
            return OpportunityStatusSaleStageEnum.WIN.getCode();
        }
        if(
                opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_SMALLMONTH_LOSETHEBILL.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_PRODUCTION_DELIVERY.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_TOLOSETHEORDER.getCode()))) ||
                        opportunitySaleStageId.equals(new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_LOSINGORDERS.getCode())))
        ){
            return OpportunityStatusSaleStageEnum.LOSE.getCode();
        }
        return 0;
    }

    private Long getOppStageIdByEntityTypeAndOpportunityStatusSaleStage(Long entityType,OpportunityStatusSaleStageEnum opportunityStatusSaleStageEnum,Map<Integer, String> scrmConfig){
        switch (opportunityStatusSaleStageEnum){
            case ALLOCATION:
                if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_FIRST.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_FIRSTTOUCH.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_FIRSTTOUCH.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_FIRSTTOUCH.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_FIRSTTOUCH.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_FIRSTTOUCH.getCode()));
                }
                break;
            case ARRIVAL:
                if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_CHECKIN_STORE.getCode()));
                } else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_LITTLEMONTH_INVITATIONTOTHESTORE.getCode()));
                } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))) {
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_INVITATIONTOTHESTORE.getCode()));
                }
                break;
            case WIN:
                if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_WIN_ORDER.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SMALLMONTH_WINSINGLE.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_WINASINGLE.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_PRODUCTION_WINSINGLE.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_WINNINGORDER.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_WINNINGSINGLE.getCode()));
                }
                break;
            case LOSE:
                if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALE_STAG_LOSE_ORDER.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_SMALLMONTH_LOSETHEBILL.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_NURSEASSIGNMENT_LOSINGORDERS.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESSTAGE_PRODUCTION_DELIVERY.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_SBAR_TOLOSETHEORDER.getCode()));
                }else if(Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode())))){
                    return new Long(scrmConfig.get(ScrmConfigBizTypeEnum.SALESPHASE_OTHER_LOSINGORDERS.getCode()));
                }
                break;
        }
        return 0L;
    }

    /**
     * 根据商机的业务类型，返回商机对应的订单类型
     *
     * @param entityType
     * @return
     */
    private Integer getOrderTypeByOpportunityEntityType(Long entityType) {
        Map<Integer, String> scrmConfig = scrmConfigService.queryAllConfig();

        if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.MONTH_ORDER.getCode();
        } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SMALL_MONTH_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.SMALL_MONTH_ORDER.getCode();
        } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_NURSE_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.NURSE_OUTSIDE_ORDER.getCode();
        } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_PRODUCTION_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode();
        } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_SBRA_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.SBRA_ORDER.getCode();
        } else if (Objects.equals(entityType, new Long(scrmConfig.get(ScrmConfigBizTypeEnum.OPPORTUNITY_OTHER_ORDER.getCode())))) {
            return PicpOrderTpyeEnum.OTHER_MONTH_ORDER.getCode();
        }

        return null;
    }

    /**
     * scrm分配团队成员成功后发送企微消息通知
     *
     * @param message
     */
    @Override
    public void scrmDistributionCustomerSuccessMessageNotify(OpportunityDistributionMessage message) {
        log.info("scrm分配团队成员成功后发送企微消息通知，入参：customerId:{}, saleId:{}, storeId:{}", message.getCustomerId(), message.getSaleId(), message.getContractedStoreId());
        ScrmAccountQuery query = new ScrmAccountQuery();
        query.setScrmCustomerId(message.getCustomerId());
        AccountInfoRequest accountInfoRequest = xsyScrmService.queryScrmAccount(query);
        log.info("客户分配销售的时候获取客户信息，入参：{},出参：{}", message.getCustomerId(), JSONUtil.toJsonStr(accountInfoRequest));

        if (ObjectUtil.isNotEmpty(accountInfoRequest)) {
            LocalQwMsgRequest localQwMsgRequest = new LocalQwMsgRequest();

            String accountName = ObjectUtil.isEmpty(accountInfoRequest.getAccountName()) ? "" : accountInfoRequest.getAccountName();
            String customerPhone = ObjectUtil.isEmpty(accountInfoRequest.getPhone()) ? "" : accountInfoRequest.getPhone();

            localQwMsgRequest.setCustomerPhone(customerPhone);
            localQwMsgRequest.setCustomerName(accountName);

            String customerStatusStr = "";
            if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem230__c())) {
                String customerStatus = xsyScrmService.convertScrmOptionIdToPicpId("account",
                    "customItem230__c", accountInfoRequest.getCustomItem230__c().longValue());
                if (ObjectUtil.isNotEmpty(customerStatus)) {
                    customerStatusStr = CustomerStatusEnum.getValueByCode(new Integer(customerStatus));
                }
            }

            Long predictBornDate__c = accountInfoRequest.getPredictBornDate__c();
            String predictBornDateStr = "";
            if (ObjectUtil.isNotEmpty(predictBornDate__c)) {
                predictBornDateStr = DateUtil.formatDate(new Date(predictBornDate__c));
            }

            String fromTypeStr = "";
            if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem231__c())) {
                String fromTypeInt = xsyScrmService.convertScrmOptionIdToPicpId("account",
                    "customItem231__c", accountInfoRequest.getCustomItem231__c().longValue());
                if (ObjectUtil.isNotEmpty(fromTypeInt)) {
                    fromTypeStr = CustomerFromTypeEnum.getValueByCode(new Integer(fromTypeInt));
                }
            }

            String productionFromTypeStr = "";
            if (Objects.nonNull(accountInfoRequest.getProductionFromType__c())) {
                productionFromTypeStr = CustomerProductionFromTypeEnum.getDescByScrmCode(accountInfoRequest.getProductionFromType__c());
            }

            String hospital__c = accountInfoRequest.getHospital__c();
            hospital__c = ObjectUtil.isEmpty(hospital__c) ? "" : hospital__c;

            // 胎数
            String fetusNum__c = ObjectUtil.isEmpty(accountInfoRequest.getFetusNum__c()) ? "" : accountInfoRequest.getFetusNum__c().toString();

            String remark = accountInfoRequest.getCustomItem251__c__c();
            remark = ObjectUtil.isEmpty(remark) ? "" : remark;

            // 月子套餐预算
//            String budgetStr = "";
//            if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem270__c())) {
//                String budgetInt = xsyScrmService.convertScrmOptionIdToPicpId("account",
//                    "customItem255__c", accountInfoRequest.getCustomItem255__c().longValue());
//                if (ObjectUtil.isNotEmpty(budgetInt)) {
//                    budgetStr = BudgetTypeEnum.getValueByCode(new Integer(budgetInt));
//                }

//                budgetStr = xsyScrmService.convertScrmOptionIdToLabel("account",
//                    "customItem270__c", accountInfoRequest.getCustomItem270__c().longValue());
//            }

            // 客户意向产品（产康）
            String intendedProductStr = "";
            if (CollectionUtil.isNotEmpty(message.getIntendedProductIds())) {
                intendedProductStr = StringUtils.join(OpportunityIntendedProductEnum.getDescByScrmCode(message.getIntendedProductIds()), "，");
            }

            // 产康阶段
            String productionStageStr = "";
            if (Objects.nonNull(accountInfoRequest.getCustomItem277__c())) {
                productionStageStr = CustomerProductionStageEnum.getDescByScrmCode(accountInfoRequest.getCustomItem277__c());
            }

            // 一胎月子方式
            String firstBirthType = accountInfoRequest.getCustomItem265__c();
            firstBirthType = ObjectUtil.isEmpty(firstBirthType) ? "" : firstBirthType;

            // 客户关注点
            String customerConcern = "";
            if (ObjectUtil.isNotEmpty(accountInfoRequest.getCustomItem266__c())) {
                Map<String, List<SelectItemData>> scrmObjectOptionList = xsyScrmService.getScrmObjectOptionList("account");
                Map<Long, String> selectItemMap = new HashMap<>();
                if (scrmObjectOptionList.containsKey("customItem266__c")) {
                    log.info("客户关注点选项列表:{}", scrmObjectOptionList.get("customItem266__c"));
                    for (SelectItemData selectItemData : scrmObjectOptionList.get("customItem266__c")) {
                        selectItemMap.put(selectItemData.getValue(), selectItemData.getLabel());
                    }
                }

                List<String> customerConcernList = Arrays.stream(accountInfoRequest.getCustomItem266__c())
                    .map(i -> selectItemMap.getOrDefault(i.longValue(), ""))
                    .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(customerConcernList)) {
                    customerConcern = StringUtils.join(customerConcernList, "，");
                }
            }

            // 销售信息
            ScrmUserInfoVO userInfo = xsyScrmService.getUserInfo(message.getSaleId());
            // 设置接收人
            // 将scrm手机号转为picp手机号
            List<String> touser = new ArrayList<>();
            String selfPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getPhone());
            if (ObjectUtil.isNotNull(selfPicpPhone)) {
                touser.add(selfPicpPhone);
            } else {
                touser.add(userInfo.getPhone());
            }

            if (message.getSendManager()) {
                String managerPicpPhone = scrmStaffPhoneConfigService.scrmPhoneToPicpPhone(userInfo.getManagerPhone());
                if (ObjectUtil.isNotNull(managerPicpPhone)) {
                    touser.add(managerPicpPhone);
                } else {
                    touser.add(userInfo.getManagerPhone());
                }
            }

            localQwMsgRequest.setTouser(touser);

            // 获取销售的姓名和门店信息
            String saleName = userInfo.getName();
            String storeName = "";

            if (ObjectUtil.isNotNull(message.getContractedStoreId())) {
                CfgStore cfgStore = cfgStoreService.queryStoreInfoByStoreId(message.getContractedStoreId().intValue());
                if (ObjectUtil.isNotEmpty(cfgStore)) {
                    storeName = cfgStore.getStoreName();
                }
            }

            String content = "";
            if (PicpOrderTpyeEnum.MONTH_ORDER.getCode().equals(message.getOrderType())) {
                content = String.format(ScrmQwCustomerMessageConstant.SEND_MESSAGE,
                    accountName,
                    customerStatusStr,
                    customerPhone,
                    Optional.ofNullable(accountInfoRequest.getCustomItem233__c()).orElse(""),
                    predictBornDateStr,
                    //budgetStr,
                    fromTypeStr,
                    //intendedGoods,
                    //intendedNursingMode,
                    hospital__c,
                    fetusNum__c,
                    //firstBirthType,
                    //customerConcern,
                    saleName,
                    storeName,
                    StringUtils.isNotBlank(message.getContactWay()) ? message.getContactWay() : "",
                    remark,
                    StringUtils.isNotBlank(message.getVisitAppointmentTime()) ? message.getVisitAppointmentTime() : "",
                    StringUtils.isNotBlank(message.getVisitProject()) ? message.getVisitProject() : ""
                );
            } else if (PicpOrderTpyeEnum.PRODUCTION_ORDER.getCode().equals(message.getOrderType())) {
                content = String.format(ScrmQwCustomerMessageConstant.SEND_PRODUCTION_OPPORTUNITY_MESSAGE,
                    accountName,
                    customerPhone,
                    Optional.ofNullable(accountInfoRequest.getCustomItem233__c()).orElse(""),
                    productionFromTypeStr,
                    customerStatusStr,
                    saleName,
                    storeName,
                    StringUtils.isNotBlank(message.getContactWay()) ? message.getContactWay() : "",
                    productionStageStr,
                    intendedProductStr,
                    remark
                );
            }

            if (StringUtils.isNotBlank(content)) {
                localQwMsgRequest.setContent(content);
                log.info("发送企微消息通知,参数={}", JSONUtil.parse(localQwMsgRequest));
                try {
                    smsManager.localBatchSendMsg(localQwMsgRequest);

                    if (ObjectUtil.isNotEmpty(touser)) {
                        ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
                        scrmSendMessageRequest.setCustomerName("");
                        scrmSendMessageRequest.setContent("你有一条新的销售机会，请立即点击查看并跟进客户。");
                        scrmSendMessageRequest.setTouser(touser);
                        scrmMessageService.sendMessage(scrmSendMessageRequest);
                    }
                } catch(Exception e) {
                    log.error("发送企微消息通知失败，失败原因:{}", e.getMessage());
                }
            }
        } else {
            log.error("未在scrm中查找到客户, id:{}", message.getCustomerId());
        }
    }

    @Override
    public void opportunityFor400Update(SCRMOpportunityRequest request) {
        // 转换后的request
        SCRMOpportunityRequest convertRequest = xsyScrmService.convertOpportunityScrmRequestToPicpRequest(request);
        ScrmBusinessOpportunityPO opportunityPO = scrmConvert.scrmOpportunityRequest2ScrmBusinessOpportunityPO(convertRequest);

        // 先查询商机是否已经同步
        ScrmBusinessOpportunityPO oldOpportunityPO = queryOpportunityByScrmId(convertRequest.getId());
        if (Objects.nonNull(oldOpportunityPO)) {
            opportunityPO.setId(oldOpportunityPO.getId());
        }

        saveOrUpdate(opportunityPO);
    }

    @Override
    public ScrmBusinessOpportunityPO queryOpportunityByScrmId(Long scrmId) {
        return getOne(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .eq(ScrmBusinessOpportunityPO::getScrmId, scrmId)
            .last("limit 1"));
    }

    /**
     * 商机团队成员处理
     *
     * @param dtoList
     */
    @Override
    public void opportunityTeamMemberHandle(List<ScrmDistributionCustomerDTO> dtoList) {
        for (ScrmDistributionCustomerDTO dto : dtoList) {
            Long staffId = dto.getOwnerId();
            Long recordId = dto.getUserId();

            ScrmTeamMemberRecordRequest recordRequest = ScrmTeamMemberRecordRequest.builder()
                .recordId(recordId)
                .userId(staffId)
                .type(dto.getRecordType().intValue())
                .build();

            if (!dto.getOperateType().equals(2)) {
                scrmTeamMemberRecordService.edit(recordRequest);
            } else {
                scrmTeamMemberRecordService.delete(recordRequest);
            }

            // 判断是否是400商机，且是否是销售角色，如果是的话，则要拉销售进客户的团队成员中，且发送消息通知
            ScrmConfigPO opportunityEntityTypePO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.OPPORTUNITY_400_BUSINESS.getCode());
            ScrmConfigPO opportunityStagePO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.SALESPHASE_400_BUSINESS_INITIAL_COMMUNICATION.getCode());
            ScrmBusinessOpportunityPO opportunityPO = queryOpportunityByScrmId(recordId);
            ScrmUserPO scrmUserPO = scrmUserService.queryByScrmId(staffId);
            if (!dto.getOperateType().equals(2)
                && Objects.nonNull(scrmUserPO)
                && dto.getSaleResponsibilities().contains("线索跟进")
                && Objects.nonNull(opportunityPO)
                && opportunityPO.getEntityType().equals(Long.valueOf(opportunityEntityTypePO.getBizContent()))
                && opportunityPO.getSaleStageId().equals(Long.valueOf(opportunityStagePO.getBizContent()))
            ) {
                // 将员工加到客户的团队成员中
                ScrmAddTeamMemberRequest scrmAddTeamMemberRequest = new ScrmAddTeamMemberRequest();
                scrmAddTeamMemberRequest.setUserId(staffId);
                //客户默认为1
                scrmAddTeamMemberRequest.setRecordFrom(1L);
                scrmAddTeamMemberRequest.setRecordFrom_data(opportunityPO.getCustomerId());
                scrmAddTeamMemberRequest.setOwnerFlag(1);
                log.info("400商机添加销售至团队成员,添加销售到客户团队成员={}", JSONUtil.parse(scrmAddTeamMemberRequest));
                xsyScrmService.addTeamMember(scrmAddTeamMemberRequest);

                // 添加团队成员到记录表中
                ScrmTeamMemberRecordRequest scrmTeamMemberRecordRequest = ScrmTeamMemberRecordRequest.builder()
                    .recordId(scrmAddTeamMemberRequest.getRecordFrom_data())
                    .userId(scrmAddTeamMemberRequest.getUserId())
                    .type(1)
                    .build();

                scrmTeamMemberRecordService.edit(scrmTeamMemberRecordRequest);

                // 发送消息
                OpportunityDistributionMessage distributionMessage = OpportunityDistributionMessage.builder()
                    .customerId(opportunityPO.getCustomerId())
                    .saleId(staffId)
                    .contractedStoreId(opportunityPO.getIntendedStore().longValue())
                    .orderType(PicpOrderTpyeEnum.MONTH_ORDER.getCode())
                    .sendManager(false)
                    .build();
                scrmDistributionCustomerSuccessMessageNotify(distributionMessage);

                /*
                // 发送群消息
                String storeName = "";
                if (Objects.nonNull(opportunityPO.getIntendedStore())) {
                    CfgStore cfgStore = cfgStoreService.queryByStoreId(opportunityPO.getIntendedStore());
                    if (Objects.nonNull(cfgStore)) {
                        storeName = cfgStore.getStoreName();
                    }
                }

                ScrmCustomerPO scrmCustomerPO = scrmCustomerService.getByScrmCustomerId(opportunityPO.getCustomerId());
                String customerClientName = Optional.ofNullable(scrmCustomerPO).map(ScrmCustomerPO::getName).orElse("");
                String customerClientPhone = Optional.ofNullable(scrmCustomerPO).map(ScrmCustomerPO::getPhone).orElse("");
                String fromTypeDesc = CustomerFromTypeEnum.getValueByCode(Optional.ofNullable(scrmCustomerPO).map(ScrmCustomerPO::getFromType).orElse(null));

                String messageTemplate = ScrmQwCustomerMessageConstant.CUSTOMER_DISTRIBUTION;
                String serviceMessage = MessageFormat.format(messageTemplate,
                    "成功",
                    customerClientName,
                    customerClientPhone,
                    fromTypeDesc,
                    storeName,
                    "",
                    "已分配成功给【"+ scrmUserPO.getName() +"】");

                WechatNailNailRobotDeclarationRequest serviceMessageRequest = new WechatNailNailRobotDeclarationRequest();
                serviceMessageRequest.setType(1);
                serviceMessageRequest.setBizType(NoticeTypeEnum.SCRM_CUSTOMER_DISTRIBUTION_SUCCESS.getBizType());
                serviceMessageRequest.setStoreId(0);
                serviceMessageRequest.setContent(serviceMessage);
                baseManager.notice(serviceMessageRequest);

                if (Objects.nonNull(opportunityPO.getIntendedStore())) {
                    String saleMessage = MessageFormat.format(messageTemplate,
                        "成功",
                        customerClientName,
                        PhoneUtil.hideBetween(customerClientPhone).toString(),
                        fromTypeDesc,
                        storeName,
                        "",
                        "已分配成功给【"+ scrmUserPO.getName() +"】");

                    WechatNailNailRobotDeclarationRequest saleMessageRequest = new WechatNailNailRobotDeclarationRequest();
                    saleMessageRequest.setType(1);
                    saleMessageRequest.setBizType(NoticeTypeEnum.DIANPING_RESERVATION_STORE_DIMENSION.getBizType());
                    saleMessageRequest.setStoreId(opportunityPO.getIntendedStore());
                    saleMessageRequest.setContent(saleMessage);
                    saleMessageRequest.setMentionedMobileList(scrmUserPO.getPhone());
                    baseManager.notice(saleMessageRequest);
                }*/
            }
        }
    }

    /**
     * 400商机查询
     *
     * @param request
     * @return
     */
    @Override
    public List<ScrmBusinessOpportunityPO> queryServiceOpportunityList(ServiceOpportunityQuery request) {
        log.info("400商机查询, request:{}", JSONUtil.toJsonStr(request));

        ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(ScrmConfigBizTypeEnum.OPPORTUNITY_400_BUSINESS.getCode());
        if (Objects.isNull(scrmConfigPO)) {
            return Lists.newArrayList();
        }

        Long serviceOpportunityEntityType = Long.valueOf(scrmConfigPO.getBizContent());

        return list(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .eq(ScrmBusinessOpportunityPO::getEntityType, serviceOpportunityEntityType)
            .eq(ScrmBusinessOpportunityPO::getSaleStageId, request.getStageId())
            .ge(Objects.nonNull(request.getStartTime()), ScrmBusinessOpportunityPO::getCreatedAt, request.getStartTime())
            .le(Objects.nonNull(request.getEndTime()), ScrmBusinessOpportunityPO::getCreatedAt, request.getEndTime()));
    }

    /**
     * 400商机赢单处理
     *
     * @param request
     */
    @Override
    public void serviceOpportunityWinHandle(ServiceOpportunityWinRequest request) {
        log.info("400商机赢单-创建销售商机, request:{}", JSONUtil.toJsonStr(request));
        // 根据业务类型，创建销售的商机
        ScrmBusinessOpportunityPO opportunityPO = queryOpportunityByScrmId(request.getOpportunityId());
        if (Objects.isNull(opportunityPO)) {
            log.error("未找到400商机，opportunityId:{}", request.getOpportunityId());
            return;
        }

        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.getByScrmCustomerId(opportunityPO.getCustomerId());
        if (Objects.isNull(scrmCustomerPO)) {
            log.error("未找到400客户，customerId:{}", opportunityPO.getCustomerId());
            return;
        }

        Long now = System.currentTimeMillis();
        for (Long saleId : request.getSaleIds()) {
            Long scrmUserStoreConfigId = null;

            ScrmUserPO sale = scrmUserService.queryByScrmId(saleId);
            ScrmUserStoreConfigPO scrmUserStoreConfig = scrmUserStoreConfigService.getScrmUserStoreConfig(sale, opportunityPO.getIntendedStore());
            if (Objects.nonNull(scrmUserStoreConfig)) {
                scrmUserStoreConfigId = scrmUserStoreConfig.getScrmRecordId();
            }

            OpportunityRequest opportunityRequest = new OpportunityRequest();
            opportunityRequest.setOwnerId(sale.getScrmId());
            opportunityRequest.setDimDepart(sale.getDimDepart());
            opportunityRequest.setAccountId(opportunityPO.getCustomerId());
            opportunityRequest.setStageUpdatedAt(now);
            opportunityRequest.setCreatedAt(now);
            opportunityRequest.setUpdatedAt(now);
            opportunityRequest.setIntendedStore__c(opportunityPO.getIntendedStore());
            opportunityRequest.setCustomItem174__c(scrmUserStoreConfigId);
            opportunityRequest.setOpportunityName(scrmCustomerPO.getName() + request.getBusinessTypeName() + "商机");
            opportunityRequest.setStoreType(0);

            Integer orderType = request.getOrderType().getCode();
            Long entityType = orderType2OpportunityEntityType(orderType);
            opportunityRequest.setEntityType(entityType);

            CfgStore cfgStore = cfgStoreService.queryByStoreId(opportunityPO.getIntendedStore());
            if (Objects.nonNull(cfgStore)) {
                opportunityRequest.setStoreType(cfgStore.getType());
            }

            ScrmBusinessOpportunityPO newOpportunityPO = createScrmOpportunity(opportunityRequest, request.getOwnerId());
            if (Objects.nonNull(newOpportunityPO)) {
                // 发送消息
                OpportunityDistributionMessage distributionMessage = OpportunityDistributionMessage.builder()
                    .customerId(newOpportunityPO.getCustomerId())
                    .saleId(saleId)
                    .contractedStoreId(newOpportunityPO.getIntendedStore().longValue())
                    .orderType(orderType)
                    .sendManager(true)
                    .build();

                CompletableFuture.runAsync(() -> scrmDistributionCustomerSuccessMessageNotify(distributionMessage), taskExecutor);
                CompletableFuture.runAsync(() -> scrmAddOrUpdateClient(newOpportunityPO), taskExecutor);
            }
        }
    }

    /**
     * 客资商机状态变更，发到消息队列
     *
     * @param scrmOpportunityId
     * @param opportunityStatus
     */
    private void sendCustomerStatusChangeMessage(Long scrmOpportunityId, Integer opportunityStatus) {
        log.info("发送客资商机状态变更到消息队列, 商机id:{}, 状态:{}", scrmOpportunityId, opportunityStatus);
        ScrmBusinessOpportunityPO opportunityPO = getOne(new LambdaQueryWrapper<ScrmBusinessOpportunityPO>()
            .eq(ScrmBusinessOpportunityPO::getScrmId, scrmOpportunityId)
            .last("limit 1"));

        ScrmConfigPO scrmConfigPO = scrmConfigService.queryConfigByBizType(
            ScrmConfigBizTypeEnum.OPPORTUNITY_STANDARD_MONTH_ORDER.getCode());
        if (!opportunityPO.getEntityType().equals(Long.valueOf(scrmConfigPO.getBizContent()))) {
            return;
        }

        Integer clientId = null;
        ScrmCustomerPO customerPO = scrmCustomerService.getByScrmCustomerId(opportunityPO.getCustomerId());
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoByPhone(customerPO.getPhone());
        if (Objects.nonNull(userBasicPO)) {
            List<Long> clientIds = tabClientService.getTabClientIdListByBasicId(userBasicPO.getId().intValue());
            clientId = CollectionUtil.isNotEmpty(clientIds) ? clientIds.get(0).intValue() : null;
        }


        // 获取商机的绑定门店和意向门店
        Integer storeId = null;
        Integer brandType = null;
        if (Objects.nonNull(opportunityPO.getContractedStore())) {
            ScrmUserStoreConfigPO scrmUserStoreConfigPO = scrmUserStoreConfigService.getByRecordId(opportunityPO.getContractedStore());
            if (Objects.nonNull(scrmUserStoreConfigPO)) {
                storeId = scrmUserStoreConfigPO.getStoreId().intValue();
            }
        }

        if (Objects.isNull(storeId) && Objects.nonNull(opportunityPO.getIntendedStore())) {
            storeId = opportunityPO.getIntendedStore();
        }

        if (Objects.nonNull(storeId)) {
            CfgStore cfgStore = cfgStoreService.queryByStoreId(storeId);
            if (Objects.nonNull(cfgStore)) {
                brandType = cfgStore.getType();
            }
        }

        // 获取该手机号关联的openid
        Set<String> openIds = new HashSet<>();
        if (Objects.nonNull(userBasicPO)) {
            List<TabWechatUserPO> tabWechatUserPOList = tabWechatUserService.queryWechatInfoByBasicUidList(userBasicPO.getId().intValue());
            openIds = tabWechatUserPOList.stream()
                .map(TabWechatUserPO::getOpenid)
                .collect(Collectors.toSet());

            if (CollectionUtil.isNotEmpty(tabWechatUserPOList) && StringUtils.isNotBlank(tabWechatUserPOList.get(0).getUnionid())) {
                CustomerWechatFansListRequest customerWechatFansListRequest = new CustomerWechatFansListRequest();
                customerWechatFansListRequest.setUnionId(tabWechatUserPOList.get(0).getUnionid());
                List<CustomerWechatFansListVO> customerWechatFansListVOList = customerWechatFansService.queryWechatFans(customerWechatFansListRequest);

                openIds.addAll(customerWechatFansListVOList.stream()
                    .map(CustomerWechatFansListVO::getOpenId)
                    .collect(Collectors.toSet()));
            }
        }

        List<CustomerAdAppointmentDTO> customerAdAppointmentDTOList = customerAdAppointmentService.queryListByPhone(customerPO.getPhone());
        openIds.addAll(customerAdAppointmentDTOList.stream().map(CustomerAdAppointmentDTO::getMiniOpenid).collect(Collectors.toSet()));

        CustomerStatusChangeEventRequest customerStatusChangeEventRequest = new CustomerStatusChangeEventRequest();
        customerStatusChangeEventRequest.setPhone(customerPO.getPhone());
        customerStatusChangeEventRequest.setBasicUid(Objects.nonNull(userBasicPO) ? userBasicPO.getId().intValue() : null);
        customerStatusChangeEventRequest.setClientId(clientId);
        customerStatusChangeEventRequest.setStoreId(storeId);
        customerStatusChangeEventRequest.setBrandType(brandType);
        customerStatusChangeEventRequest.setStatus(opportunityStatus);
        customerStatusChangeEventRequest.setOpenIds(openIds);
        scrmProducer.customerOpportunityStatusChange(customerStatusChangeEventRequest);
    }
}



