package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.facade.TradeCmdService;
import com.stbella.asset.api.req.trade.UserTradeReq;
import com.stbella.base.server.security.ISysRoleService;
import com.stbella.base.server.sms.Sms;
import com.stbella.base.server.sms.SmsService;
import com.stbella.care.api.php.PhpApiService;
import com.stbella.care.server.care.vo.api.ListStoreAuthVO;
import com.stbella.core.base.LoginUserDTO;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.cts.enums.CustomerAssetsBrandEnum;
import com.stbella.customer.server.cts.enums.CustomerAssetsTypeEnum;
import com.stbella.customer.server.cts.enums.CustomerIntegralSceneEnum;
import com.stbella.customer.server.customer.constant.ClientTypeConstant;
import com.stbella.customer.server.customer.constant.RedisConstant;
import com.stbella.customer.server.customer.dto.StbellaStorePackageGoodsConfigDTO;
import com.stbella.customer.server.customer.dto.StbellaStorePackageGoodsConfigDTO.PackageDetailDTO;
import com.stbella.customer.server.customer.entity.CustomerCheckInStoreReportViewRecordPO;
import com.stbella.customer.server.customer.entity.CustomerCheckinStoreReportPO;
import com.stbella.customer.server.customer.entity.SystemOperateLogPO;
import com.stbella.customer.server.customer.enums.SystemOperateTypeEnum;
import com.stbella.customer.server.customer.enums.SystemOperatorTypeEnum;
import com.stbella.customer.server.customer.mapper.CustomerCheckInStoreReportViewRecordMapper;
import com.stbella.customer.server.customer.mapper.CustomerCheckinStoreReportMapper;
import com.stbella.customer.server.customer.request.CustomerCheckinStoreReportCreateRequest;
import com.stbella.customer.server.customer.request.CustomerCheckinStoreReportCreateV2Request;
import com.stbella.customer.server.customer.request.CustomerCheckinStoreReportCreateV2Request.QuotationItem;
import com.stbella.customer.server.customer.request.CustomerCheckinStoreReportRequest;
import com.stbella.customer.server.customer.request.IdFormRequest;
import com.stbella.customer.server.customer.request.growth.CustomerIntegralReq;
import com.stbella.customer.server.customer.service.CustomerCheckInStoreReportViewRecordService;
import com.stbella.customer.server.customer.service.CustomerCheckinStoreReportService;
import com.stbella.customer.server.customer.service.CustomerGrowthService;
import com.stbella.customer.server.customer.service.SystemOperateLogService;
import com.stbella.customer.server.customer.vo.*;
import com.stbella.customer.server.customer.vo.StorePackageGoodsDescVO.DetailImageVO;
import com.stbella.customer.server.customer.vo.growth.CustomerIntegralVO;
import com.stbella.customer.server.ecp.entity.HeInviteRelationPO;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.entity.TabMiniProgramTokenPO;
import com.stbella.customer.server.ecp.service.*;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.scrm.service.XsyScrmService;
import com.stbella.customer.server.util.SensitiveInfoUtil;
import com.stbella.order.server.order.month.req.quotation.CreateQuotationReq;
import com.stbella.order.server.order.month.service.QuotationService;
import com.stbella.platform.order.api.res.CreateOrderRes;
import com.stbella.redis.service.RedisService;
import com.stbella.rule.api.RuleHitService;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.scrm.enums.WeWorkAppEnum;
import com.stbella.scrm.request.ScrmSendMessageRequest;
import com.stbella.scrm.service.ScrmMessageService;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import com.stbella.store.server.ecp.entity.CfgStore;
import com.stbella.store.server.ecp.service.CfgStoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * C端客户探店打卡报告 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-06-20
 */
@Service
@Slf4j
public class CustomerCheckinStoreReportServiceImpl extends ServiceImpl<CustomerCheckinStoreReportMapper, CustomerCheckinStoreReportPO> implements CustomerCheckinStoreReportService {

    @Resource
    private HeCustomerClientConfigService heCustomerClientConfigService;

    @DubboReference
    private DdEmployeeService ddEmployeeService;

    @DubboReference
    private CfgStoreService cfgStoreService;

    @DubboReference
    private PhpApiService phpApiService;


    @DubboReference
    private RuleHitService ruleHitService;

    @Resource
    private XsyScrmService xsyScrmService; //销售易scrm实现

    @Resource
    private TabClientService tabClientService;//ecp-tabClient

    @Resource
    private HeUserBasicService heUserBasicService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private SystemOperateLogService systemOperateLogService;

    @Resource
    private CustomerGrowthService customerGrowthService;

    @DubboReference
    private TradeCmdService tradeCmdService;//用户积分

    @Resource
    private RedisService redisService;

    @DubboReference
    private SmsService smsService;//短信

    @Resource
    private HeInviteRelationService heInviteRelationService;

    @DubboReference
    private QuotationService quotationService;

    @DubboReference
    private ISysRoleService iSysRoleService;

    @Resource
    private CustomerCheckInStoreReportViewRecordService customerCheckInStoreReportViewRecordService;

    @DubboReference
    private ScrmMessageService scrmMessageService;
    @Autowired
    private CustomerCheckinStoreReportMapper customerCheckinStoreReportMapper;
    @Autowired
    private CustomerCheckInStoreReportViewRecordMapper customerCheckInStoreReportViewRecordMapper;

    @Resource
    private TableUserTokenService tableUserTokenService;
    /**
     * 以销售id或客户手机号和status 查询对应的探店打卡报告列表
     *
     * @param request
     * @return page vo< customer checkin store report list vo>
     * <AUTHOR>
     * @date 2023/10/09 11:56:38
     * @since v2
     */
    @Override
    public PageVO<CustomerCheckinStoreReportListVO> queryReportByRequest(
        CustomerCheckinStoreReportRequest request) {

        LambdaQueryWrapper<CustomerCheckinStoreReportPO> wrapper = new LambdaQueryWrapper<CustomerCheckinStoreReportPO>()
            .eq(StringUtils.isNotBlank(request.getClientPhone()),
                CustomerCheckinStoreReportPO::getClientPhone, request.getClientPhone())
            .orderByDesc(CustomerCheckinStoreReportPO::getGmtCreate);

        if (ObjectUtil.isNotNull(request.getNameOrPhone()) && StringUtils.isNotBlank(
            request.getNameOrPhone())) {
            wrapper.and(
                s -> s.like(CustomerCheckinStoreReportPO::getClientPhone, request.getNameOrPhone())
                    .or()
                    .like(CustomerCheckinStoreReportPO::getClientName, request.getNameOrPhone()));
        }

        if (ObjectUtil.isNotNull(request.getSaleId())) {
            // 判断当前用户是否是店长，如果是的话，则获取拥有门店权限的记录
            boolean isStoreManager = false;
            DdEmployeePO ddEmployeePO = ddEmployeeService.getByMobile(request.getSalePhone());
            if (ObjectUtil.isNotNull(ddEmployeePO)) {
                //isStoreManager = ddEmployeePO.getTitle().contains("店长");
                isStoreManager = iSysRoleService.checkExistRoleCode("room-state-chief", ddEmployeePO.getId());
            }

            if (isStoreManager) {
                LoginUserDTO loginUserDTO = new LoginUserDTO();
                loginUserDTO.setMobile(request.getSalePhone());
                List<ListStoreAuthVO> listStoreAuthVOS = phpApiService.listStoreAuth(loginUserDTO);

                List<Long> storeAuthList = new ArrayList<>();
                if (ObjectUtil.isNotNull(listStoreAuthVOS)) {
                    storeAuthList = listStoreAuthVOS.stream()
                        .map(ListStoreAuthVO::getStoreId)
                        .collect(Collectors.toList());
                } else {
                    storeAuthList.add(request.getSaleStoreId().longValue());
                }

                wrapper.in(CustomerCheckinStoreReportPO::getStoreId, storeAuthList);
            } else {
                wrapper.eq(CustomerCheckinStoreReportPO::getSaleId, request.getSaleId());
            }
        }

        Page<CustomerCheckinStoreReportPO> pageList = page(
            new Page<>(request.getPageNum(), request.getPageSize()), wrapper);

        if (ObjectUtil.isNotNull(pageList)) {

            List<CfgStore> storeList = cfgStoreService.getStoreList();
            Map<Integer, CfgStore> storeMap = storeList.stream()
                .collect(Collectors.toMap(CfgStore::getStoreId, i -> i));

            List<CustomerCheckinStoreReportListVO> dataList = new ArrayList<>();
            for (CustomerCheckinStoreReportPO record : pageList.getRecords()) {
                CustomerCheckinStoreReportListVO customerCheckinStoreReportListVO = new CustomerCheckinStoreReportListVO();
                BeanUtil.copyProperties(record, customerCheckinStoreReportListVO);

                if (record.getClientGender().equals(1)) {
                    customerCheckinStoreReportListVO.setClientName(record.getLastName() + "先生");
                } else {
                    customerCheckinStoreReportListVO.setClientName(record.getLastName() + "女士");
                }

                customerCheckinStoreReportListVO.setStoreName(
                    storeMap.containsKey(record.getStoreId()) ? storeMap.get(record.getStoreId())
                        .getNameAlias() : "未知门店");

                dataList.add(customerCheckinStoreReportListVO);
            }

            return new PageVO<>(dataList, (int) pageList.getTotal(), (int) pageList.getSize(),
                (int) pageList.getCurrent());
        }

        return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * query report list
     *
     * @param request request
     * @return page vo< customer checkin store report list vo>
     * <AUTHOR>
     * @date 2023/10/11 01:29:40
     * @since v2
     */
    @Override
    public PageVO<CustomerCheckinStoreReportListVO> queryReportList(
        CustomerCheckinStoreReportRequest request) {
        LambdaQueryWrapper<CustomerCheckinStoreReportPO> wrapper = new LambdaQueryWrapper<CustomerCheckinStoreReportPO>()
                .eq(request.getBrandType()!=null,CustomerCheckinStoreReportPO::getStoreType, request.getBrandType())
                .eq(StringUtils.isNotBlank(request.getClientPhone()), CustomerCheckinStoreReportPO::getClientPhone, request.getClientPhone())
                .eq(ObjectUtil.isNotNull(request.getStatus()), CustomerCheckinStoreReportPO::getStatus,
                        request.getStatus())
                .eq(CustomerCheckinStoreReportPO::getDeleted, 0)
            .orderByDesc(CustomerCheckinStoreReportPO::getGmtCreate);

        if (ObjectUtil.isNotNull(request.getNameOrPhone()) && StringUtils.isNotBlank(
            request.getNameOrPhone())) {
            wrapper.and(
                s -> s.like(CustomerCheckinStoreReportPO::getClientPhone, request.getNameOrPhone())
                    .or()
                    .like(CustomerCheckinStoreReportPO::getClientName, request.getNameOrPhone()));
        }

        if (ObjectUtil.isNotNull(request.getSaleId())) {
            // 判断当前用户是否是店长，如果是的话，则获取拥有门店权限的记录
            boolean isStoreManager = false;
            DdEmployeePO ddEmployeePO = ddEmployeeService.getByMobile(request.getSalePhone());
            if (ObjectUtil.isNotNull(ddEmployeePO)) {
                //isStoreManager = ddEmployeePO.getTitle().contains("店长");
                isStoreManager = iSysRoleService.checkExistRoleCode("room-state-chief", ddEmployeePO.getId());
            }

            if (isStoreManager) {
                LoginUserDTO loginUserDTO = new LoginUserDTO();
                loginUserDTO.setMobile(request.getSalePhone());
                List<ListStoreAuthVO> listStoreAuthVOS = phpApiService.listStoreAuth(loginUserDTO);

                List<Long> storeAuthList = new ArrayList<>();
                if (ObjectUtil.isNotNull(listStoreAuthVOS)) {
                    storeAuthList = listStoreAuthVOS.stream()
                        .map(ListStoreAuthVO::getStoreId)
                        .collect(Collectors.toList());
                } else {
                    storeAuthList.add(request.getSaleStoreId().longValue());
                }

                wrapper.in(CustomerCheckinStoreReportPO::getStoreId, storeAuthList);
            } else {
                wrapper.eq(CustomerCheckinStoreReportPO::getSaleId, request.getSaleId());
            }
        }

        Page<CustomerCheckinStoreReportPO> pageList = page(new Page<>(request.getPageNum(), request.getPageSize()), wrapper);

        if (ObjectUtil.isNotNull(pageList)) {

            List<CfgStore> storeList = cfgStoreService.getStoreList();
            Map<Integer, CfgStore> storeMap = storeList.stream()
                .collect(Collectors.toMap(CfgStore::getStoreId, i -> i));

            List<CustomerCheckinStoreReportListVO> dataList = new ArrayList<>();
            for (CustomerCheckinStoreReportPO record : pageList.getRecords()) {
                CustomerCheckinStoreReportListVO customerCheckinStoreReportListVO = new CustomerCheckinStoreReportListVO();
                BeanUtil.copyProperties(record, customerCheckinStoreReportListVO);
                customerCheckinStoreReportListVO.setClientName(record.getClientName());
                customerCheckinStoreReportListVO.setStoreName(storeMap.containsKey(record.getStoreId()) ? storeMap.get(record.getStoreId()).getNameAlias() : "未知门店");
                customerCheckinStoreReportListVO.setClientPhone(SensitiveInfoUtil.mobileEncrypt(customerCheckinStoreReportListVO.getClientPhone()));
                dataList.add(customerCheckinStoreReportListVO);
            }

            return new PageVO<>(dataList, (int) pageList.getTotal(), (int) pageList.getSize(), (int) pageList.getCurrent());
        }

        return new PageVO<>(Lists.newArrayList(), 0, request.getPageSize(), request.getPageNum());
    }

    /**
     * 根据id查询报告
     *
     * @param id
     * @param token
     * @return
     */
    @Override
    public CustomerCheckinStoreReportVO queryReportById(Long id, String token) {
        if(StringUtils.isNotBlank(token)) {
            saveAndNoticeReport(id, token);
        }
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = getById(id);
        if (ObjectUtil.isNull(customerCheckinStoreReportPO)) {
            return null;
        }

        CustomerCheckinStoreReportVO customerCheckinStoreReportVO = new CustomerCheckinStoreReportVO();
        BeanUtil.copyProperties(customerCheckinStoreReportPO, customerCheckinStoreReportVO);

        if (customerCheckinStoreReportPO.getClientGender().equals(1)) {
            customerCheckinStoreReportVO.setClientName(customerCheckinStoreReportPO.getLastName() + "先生");
        } else {
            customerCheckinStoreReportVO.setClientName(customerCheckinStoreReportPO.getLastName() + "女士");
        }

        // 获取该门店的套餐图片
        List<StorePackageGoodsDescVO> storePackageGoodsDescVOList = queryStorePackageGoodsInfoByStoreId(
            customerCheckinStoreReportPO.getStoreId(),
            customerCheckinStoreReportPO.getStoreType());

        Map<String, StorePackageGoodsDescVO> storePackageGoodsDescVOMap = storePackageGoodsDescVOList.stream()
            .collect(Collectors.toMap(StorePackageGoodsDescVO::getPackageName, i -> i));

        // 解析参观套餐
        if (ObjectUtil.isNotNull(customerCheckinStoreReportPO.getVisitGoods())
            && StringUtils.isNotBlank(customerCheckinStoreReportPO.getVisitGoods())) {

            List<StorePackageGoodsDescVO> visitGoodsList = new ArrayList<>();

            List<String> visitGoods = JSONUtil.toList(
                customerCheckinStoreReportPO.getVisitGoods(), String.class);
            for (String visitGood : visitGoods) {
                if (storePackageGoodsDescVOMap.containsKey(visitGood)) {
                    visitGoodsList.add(storePackageGoodsDescVOMap.get(visitGood));
                }
            }

            customerCheckinStoreReportVO.setVisitGoods(JSONUtil.toJsonStr(visitGoodsList));
        }

        // 解析意向套餐的图片
        if (ObjectUtil.isNotNull(customerCheckinStoreReportPO.getIntentionalGoods())
            && StringUtils.isNotBlank(customerCheckinStoreReportPO.getIntentionalGoods())) {

            List<StorePackageGoodsDescVO> intentionalGoodsList = new ArrayList<>();

            List<String> intentionalGoods = JSONUtil.toList(
                customerCheckinStoreReportPO.getIntentionalGoods(), String.class);
            for (String intentionalGood : intentionalGoods) {
                if (storePackageGoodsDescVOMap.containsKey(intentionalGood)) {
                    intentionalGoodsList.add(storePackageGoodsDescVOMap.get(intentionalGood));
                }
            }

            customerCheckinStoreReportVO.setIntentionalGoods(JSONUtil.toJsonStr(intentionalGoodsList));
        }

        CfgStore cfgStore = cfgStoreService.queryStoreInfoByStoreId(customerCheckinStoreReportPO.getStoreId());
        customerCheckinStoreReportVO.setStoreName(cfgStore.getNameAlias());
        customerCheckinStoreReportVO.setStorePackagePriceUrl(cfgStore.getStorePackagePriceUrl());
        customerCheckinStoreReportVO.setStorePackageGiftPriceUrl(cfgStore.getStorePackageGiftPriceUrl());

        return customerCheckinStoreReportVO;
    }

    @Override
    public CustomerCheckinStoreReportV2VO queryReportByIdV3(IdFormRequest request) {
        return null;
//        //判断保存view record并发送消息提醒销售
//        if(request.getOperator()!=null) {
//            saveAndNoticeReport(request);
//        }
//        Long id = request.getId();
//        return queryReportByIdV2(id, token);
    }

    /**
     * 根据id查询报告
     *
     * @param id    id
     * @param token
     * @return customer checkin store report vo
     * <AUTHOR>
     * @date 2023/10/09 03:05:32
     * @since v2
     */
    @Override
    public CustomerCheckinStoreReportV2VO queryReportByIdV2(Long id, String token) {
        if(StringUtils.isNotBlank(token)) {
            saveAndNoticeReport(id, token);
        }
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = getById(id);
        if (ObjectUtil.isNull(customerCheckinStoreReportPO)) {
            return new CustomerCheckinStoreReportV2VO();
        }

        CustomerCheckinStoreReportV2VO reportVO = new CustomerCheckinStoreReportV2VO();
        BeanUtil.copyProperties(customerCheckinStoreReportPO, reportVO, "intentionalQuotation");
        reportVO.setBrandActivityIsNew(true);
        //草稿状态并展示每月利益
        if (ObjectUtil.equals(customerCheckinStoreReportPO.getStatus(), 0) && ObjectUtil.equals(
                customerCheckinStoreReportPO.getBrandActivityShow(), 1)) {
            String brandActivity = queryBrandActivity(customerCheckinStoreReportPO.getStoreType());
            reportVO.setBrandActivityIsNew(
                    ObjectUtil.equals(brandActivity, customerCheckinStoreReportPO.getBrandActivityContent()));
        }
        // 获取该门店的套餐图片
        List<StorePackageGoodsDescVO> storePackageGoodsDescVOList = queryStorePackageGoodsInfoByStoreId(
                customerCheckinStoreReportPO.getStoreId(),
                customerCheckinStoreReportPO.getStoreType());

        // 解析参观套餐
        if (customerCheckinStoreReportPO.getVersion().equals(3)) {
            // 版本为3的，则显示门店所有的套餐数据
            reportVO.setVisitGoods(JSONUtil.toJsonStr(storePackageGoodsDescVOList));
        } else {
            if (ObjectUtil.isNotNull(customerCheckinStoreReportPO.getVisitGoods())
                && StringUtils.isNotBlank(customerCheckinStoreReportPO.getVisitGoods())) {

                List<StorePackageGoodsDescVO> visitGoodsList = new ArrayList<>();

                Map<String, StorePackageGoodsDescVO> storePackageGoodsDescVOMap = storePackageGoodsDescVOList.stream()
                    .collect(Collectors.toMap(StorePackageGoodsDescVO::getPackageName, i -> i));

                List<String> visitGoods = JSONUtil.toList(
                    customerCheckinStoreReportPO.getVisitGoods(), String.class);
                for (String visitGood : visitGoods) {
                    if (storePackageGoodsDescVOMap.containsKey(visitGood)) {
                        visitGoodsList.add(storePackageGoodsDescVOMap.get(visitGood));
                    }
                }

                reportVO.setVisitGoods(JSONUtil.toJsonStr(visitGoodsList));
            }
        }

        // 解析报价单
        if (StringUtils.isNotBlank(customerCheckinStoreReportPO.getIntentionalQuotation())) {
            reportVO.setIntentionalQuotation(JSONUtil.toList(customerCheckinStoreReportPO.getIntentionalQuotation(), QuotationItem.class));
        }

        CfgStore cfgStore = cfgStoreService.queryStoreInfoByStoreId(
                customerCheckinStoreReportPO.getStoreId());
        reportVO.setStoreName(cfgStore.getNameAlias());
        reportVO.setStorePackagePriceUrl(cfgStore.getStorePackagePriceUrl());
        reportVO.setStorePackageGiftPriceUrl(cfgStore.getStorePackageGiftPriceUrl());
        reportVO.setLat(cfgStore.getLat());
        reportVO.setLng(cfgStore.getLng());
        reportVO.setAddress(cfgStore.getAddress());
        return reportVO;
    }

    private void saveAndNoticeReport(Long id, String token) {
        try {
            if (StringUtils.isBlank(token)) {
                return;
            }
            TabMiniProgramTokenPO one = tableUserTokenService.getOne(new LambdaQueryWrapper<TabMiniProgramTokenPO>().eq(TabMiniProgramTokenPO::getToken, token)
                    .isNull(TabMiniProgramTokenPO::getDeletedAt));
            if (one == null) {
                log.info("token查不到用户:{}不存在", token);
                return;
            }
            //构造IdFormRequest request
            IdFormRequest idFormRequest = new IdFormRequest();
            idFormRequest.setId(id);
            Operator operator = new Operator();
            operator.setOperatorGuid(one.getBasicUid().toString());
            operator.setOperatorPhone(one.getPhone());
            idFormRequest.setOperator(operator);
            log.info("根据token构造构造IdFormRequest:{}", JSON.toJSONString(idFormRequest));
            saveAndNoticeReport(idFormRequest);
        }catch (Exception e){
            log.error("根据token构造构造IdFormRequest异常:{}", e.getMessage());
        }
    }
    private void saveAndNoticeReport(IdFormRequest request) {
        log.info("判断探店报告创建者是否是本人,request:{}", JSON.toJSONString(request));
        if(request.getOperator()==null || StringUtils.isBlank(request.getOperator().getOperatorGuid())){
            return ;
        }
        TabClientPO client = tabClientService.getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getBasicUid, Integer.parseInt(request.getOperator().getOperatorGuid()))
                .eq(TabClientPO::getActive, 1)
                .last("limit 1"));
        if (Objects.isNull(client)) {
            return ;
        }
        //根据id获取探店报告
        CustomerCheckinStoreReportPO report = getById(request.getId());
        if(report == null){
            return ;
        }
        log.info("判断探店报告创建者是否是本人,探店报告id:{},操作人id:{},操作人手机号:{},探店报告用户手机号:{}"
                ,request.getId()
                ,request.getOperator().getOperatorGuid()
                ,client.getPhone()
                ,report.getClientPhone()
        );
        if(!report.getClientPhone().equals(client.getPhone())){
            return ;
        }
        CustomerCheckInStoreReportViewRecordPO one = customerCheckInStoreReportViewRecordService.getOne(
                new LambdaQueryWrapper<CustomerCheckInStoreReportViewRecordPO>()
                        .eq(CustomerCheckInStoreReportViewRecordPO::getBasicUid, request.getOperator().getOperatorGuid())
                        .eq(CustomerCheckInStoreReportViewRecordPO::getReportId, request.getId())
        );
        if (ObjectUtil.isNull(one)) {
            customerCheckInStoreReportViewRecordService.save(new CustomerCheckInStoreReportViewRecordPO()
                    .setReportId(request.getId())
                    .setBasicUid(Integer.parseInt(request.getOperator().getOperatorGuid()))
                    .setClientName(request.getOperator().getOperatorName())
                    .setClientPhone(request.getOperator().getOperatorPhone())
                    .setOperationTime(LocalDateTime.now())
            );
            try {
                String messageContent = "客户【%s】已查看探店报告，请及时跟进～";
                messageContent = String.format(messageContent, report.getClientName());
                log.info("判断探店报告创建者是否是本人,通知销售，销售手机号:{}", report.getSalePhone());
                List<String> phoneList = Arrays.asList(report.getSalePhone());
                ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
                scrmSendMessageRequest.setContent(messageContent);
                scrmSendMessageRequest.setTouser(phoneList);
                scrmSendMessageRequest.setAppId(WeWorkAppEnum.SERVICE_NOTIFY.getCode());
                scrmMessageService.sendMessage(scrmSendMessageRequest);
                log.info("判断探店报告创建者是否是本人消息发送成功,接收人:{}，消息内容:{}", JSONUtil.toJsonStr(phoneList), messageContent);
            }catch (Exception e){
                log.error("发送探店报告通知销售失败,销售手机号:{}", report.getSalePhone(), e);
            }
        }
    }

    /**
     * 查询品牌利益列表图
     *
     * <AUTHOR>
     * @date 2023/10/09 05:59:24
     * @since v2
     */
    @Override
    public String queryBrandActivity(Integer brandType) {
        ExecuteRuleV2Req executeRuleReq = null;
        if(ObjectUtil.isNull(brandType) || brandType.equals(0)){
            //走老逻辑
            executeRuleReq = getExecuteRuleV2Req("SAINT_BELLA_BENEFIT_URL","configCode","saint_bella");
        }else{
            String senneCode = "BENEFIT_URL";
            executeRuleReq = getExecuteRuleV2Req(senneCode,"configCode",brandType+"");
        }
        log.info("queryBrandActivity请求规则引擎req:{}", JSON.toJSONString(executeRuleReq));
        try {
            Result<List<HitRuleVo>> listResult = ruleHitService.hitRuleV2(executeRuleReq);
            log.info("返回结果为:{}", listResult);
            if (ObjectUtil.isEmpty(listResult) || CollUtil.isEmpty(listResult.getData()) || ObjectUtil.isEmpty(listResult.getData().get(0))) {
                return "";
            }
            String benefitUrl = listResult.getData().get(0).getSimpleRuleValue();
            if (StringUtils.isNotBlank(benefitUrl)) {
                return benefitUrl;
            }
        } catch (BusinessException e) {
            log.info("未查到配置");
        }
        return "";
    }

    private static @NotNull ExecuteRuleV2Req getExecuteRuleV2Req(String sceneCode,String dataCode,String dataValue) {
        ExecuteRuleV2Req executeRuleReq = new ExecuteRuleV2Req();
        executeRuleReq.setSceneCode(sceneCode);
        Map<String, Object> factObj = new HashMap<>();
        factObj.put(dataCode, dataValue);
        executeRuleReq.setFactObj(factObj);
        return executeRuleReq;
    }


    /**
     * 创建一个新的探店打卡报告
     *
     * @param request
     * @return
     */
    @Override
    public Long createReport(CustomerCheckinStoreReportCreateRequest request) {
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = new CustomerCheckinStoreReportPO();
        BeanUtil.copyProperties(request, customerCheckinStoreReportPO);

        // 设置姓名
        if (StringUtils.isNotBlank(request.getFirstName()) && StringUtils.isNotBlank(request.getLastName())) {
            customerCheckinStoreReportPO.setClientName(request.getLastName() + request.getFirstName());
        }

        // 判断是否是备孕中
        customerCheckinStoreReportPO.setPregnancyStatus(0);
        if (ObjectUtil.isNotNull(request.getPredictBornDate()) && DateUtil.format(request.getPredictBornDate(), "yyyy-MM-dd").equals("2099-12-01")) {
            customerCheckinStoreReportPO.setPregnancyStatus(1);
        }

        if (ObjectUtil.isNotNull(request.getConcernedIssueList())) {
            customerCheckinStoreReportPO.setConcernedIssue(JSONUtil.toJsonStr(request.getConcernedIssueList()));
        }

        if (ObjectUtil.isNotNull(request.getVisitGoods())) {
            customerCheckinStoreReportPO.setVisitGoods(JSONUtil.toJsonStr(request.getVisitGoods()));
        }

        if (ObjectUtil.isNotNull(request.getIntentionalGoods())) {
            customerCheckinStoreReportPO.setIntentionalGoods(JSONUtil.toJsonStr(request.getIntentionalGoods()));
        }

        // 保存报告到数据库
        boolean result = save(customerCheckinStoreReportPO);

        if (result) {
            return customerCheckinStoreReportPO.getId();
        }

        return null;
    }

    /**
     * 创建探店报告
     *
     * @param request request
     * @return long
     * <AUTHOR>
     * @date 2023/10/09 10:45:34
     * @since v2
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNewReport(CustomerCheckinStoreReportCreateV2Request request) {
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = setCustomerCheckinStoreReportPO(request);

        // 保存报告到数据库
        boolean result = save(customerCheckinStoreReportPO);
        if (result) {
            if (ObjectUtil.equals(customerCheckinStoreReportPO.getStatus(), 1)) {
                // 如果意向报价单不为空，则通知订单中心，激活报价单
                if (StringUtils.isNotBlank(customerCheckinStoreReportPO.getIntentionalQuotation())) {
                    List<QuotationItem> quotationItems = JSONUtil.toList(
                        customerCheckinStoreReportPO.getIntentionalQuotation(), QuotationItem.class);
                    if (CollectionUtil.isNotEmpty(quotationItems)) {
                        quotationActive(quotationItems, request.getSaleId(), request.getSaleName(), request.getSalePhone());
                        customerCheckinStoreReportPO.setIntentionalQuotation(JSONUtil.toJsonStr(quotationItems));
                        updateById(customerCheckinStoreReportPO);
                    }
                }

                //同步用户名字和性别到scrm
                notifyScrmCustomerNameAndGender(customerCheckinStoreReportPO.getClientPhone(),
                    customerCheckinStoreReportPO.getClientName(),
                    customerCheckinStoreReportPO.getClientGender());
                //添加操作日志
                addSystemLog(customerCheckinStoreReportPO.getSaleId(),
                    customerCheckinStoreReportPO.getClientName(),
                    customerCheckinStoreReportPO.getClientGender());

                //同步用户名字和性别到数据库 ecp saas customer
                notifyCustomerNameAndGender(customerCheckinStoreReportPO.getClientPhone(),
                    customerCheckinStoreReportPO.getClientName(),
                    customerCheckinStoreReportPO.getClientGender());
            }
            return customerCheckinStoreReportPO.getId();
        }

        return null;
    }

    /**
     * 修改探店报告
     *
     * @param request request
     * <AUTHOR>
     * @date 2023/10/09 11:25:11
     * @since v2
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateReport(CustomerCheckinStoreReportCreateV2Request request) {

        //修改的信息
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = setCustomerCheckinStoreReportPO(
                request);

        boolean isUpdate = 0 != baseMapper.updateById(customerCheckinStoreReportPO);

        if (ObjectUtil.equals(customerCheckinStoreReportPO.getStatus(), 1)) {
            // 如果意向报价单不为空，则通知订单中心，激活报价单
            if (StringUtils.isNotBlank(customerCheckinStoreReportPO.getIntentionalQuotation())) {
                List<QuotationItem> quotationItems = JSONUtil.toList(
                    customerCheckinStoreReportPO.getIntentionalQuotation(), QuotationItem.class);
                if (CollectionUtil.isNotEmpty(quotationItems)) {
                    quotationActive(quotationItems, request.getSaleId(), request.getSaleName(), request.getSalePhone());
                    customerCheckinStoreReportPO.setIntentionalQuotation(JSONUtil.toJsonStr(quotationItems));
                    updateById(customerCheckinStoreReportPO);
                }
            }

            //同步用户名字和性别到scrm
            notifyScrmCustomerNameAndGender(customerCheckinStoreReportPO.getClientPhone(),
                customerCheckinStoreReportPO.getClientName(),
                customerCheckinStoreReportPO.getClientGender());
            //添加操作日志
            addSystemLog(customerCheckinStoreReportPO.getSaleId(),
                customerCheckinStoreReportPO.getClientName(),
                customerCheckinStoreReportPO.getClientGender());

            //同步用户名字和性别到数据库 ecp saas customer
            notifyCustomerNameAndGender(customerCheckinStoreReportPO.getClientPhone(),
                customerCheckinStoreReportPO.getClientName(),
                customerCheckinStoreReportPO.getClientGender());
        }
        //修改
        return isUpdate ? customerCheckinStoreReportPO.getId() : null;
    }

    /**
     * 激活报价单
     *
     * @param quotationItemList
     * @param saleId
     * @param saleName
     * @param salePhone
     */
    private void quotationActive(List<QuotationItem> quotationItemList, Integer  saleId, String saleName, String salePhone) {
        if (CollectionUtil.isEmpty(quotationItemList)) {
            return;
        }

        List<Integer> cartIdList = quotationItemList.stream()
            .map(QuotationItem::getCartId)
            .collect(Collectors.toList());

        Operator operator = new Operator();
        operator.setOperatorGuid(saleId.toString());
        operator.setOperatorName(saleName);
        operator.setOperatorPhone(salePhone);

        CreateQuotationReq req = new CreateQuotationReq();
        req.setCartIdList(cartIdList);
        req.setOperator(operator);
        log.info("去订单中心生成报价单, request:{}", JSONUtil.toJsonStr(req));
        List<CreateOrderRes> quotationResult = quotationService.createQuotation(req);
        log.info("去订单中心生成报价单, result:{}", JSONUtil.toJsonStr(quotationResult));
        if (CollectionUtil.isEmpty(quotationResult)) {
            throw new BusinessException(ResultEnum.SYSTEM_API_NOT_SUPPORT.getCode(), "报价单生成失败");
        }

        Map<Integer, Integer> quotationOrderMap = quotationResult.stream()
            .collect(Collectors.toMap(CreateOrderRes::getCartId, CreateOrderRes::getOrderId, (i, j) -> j));

        quotationItemList.forEach(i -> {
            i.setOrderId(quotationOrderMap.get(i.getCartId()));
        });
    }


    private CustomerCheckinStoreReportPO setCustomerCheckinStoreReportPO(CustomerCheckinStoreReportCreateV2Request request) {
        CustomerCheckinStoreReportPO customerCheckinStoreReportPO = new CustomerCheckinStoreReportPO();
        BeanUtil.copyProperties(request, customerCheckinStoreReportPO);
        //关注的问题
        if (ObjectUtil.isNotNull(request.getConcernedIssueList())) {
            customerCheckinStoreReportPO.setConcernedIssue(JSONUtil.toJsonStr(request.getConcernedIssueList()));
        }
        //参观的套餐
        if (ObjectUtil.isNotNull(request.getVisitGoods())) {
            customerCheckinStoreReportPO.setVisitGoods(JSONUtil.toJsonStr(request.getVisitGoods()));
        }

        // 报价单
        if (CollectionUtil.isNotEmpty(request.getIntentionalQuotation())) {
            customerCheckinStoreReportPO.setIntentionalQuotation(JSONUtil.toJsonStr(request.getIntentionalQuotation()));
        }

        // 如果是直接发给客户，则记录发送时间
        if (ObjectUtil.equals(request.getStatus(), 1)) {
            customerCheckinStoreReportPO.setSendTime(new Date());
        }
        return customerCheckinStoreReportPO;
    }


    /**
     * 查询门店的套餐详情，用于生成探店打卡报告
     *
     * @param storeId
     * @param storeType
     * @return
     */
    @Override
    public List<StorePackageGoodsDescVO> queryStorePackageGoodsInfoByStoreId(Integer storeId, Integer storeType) {
        List<StorePackageGoodsDescVO> storePackageGoodsDescVOList = new ArrayList<>();
        try {
            String configContent = heCustomerClientConfigService.queryStoreDetailByStoreId(storeId);
            if (ObjectUtil.isNotNull(configContent)) {
                if (storeType.equals(0)) {
                    buildSbl(configContent, storePackageGoodsDescVOList);
                } else if (storeType.equals(100)) {
                    buildIsla(configContent, storePackageGoodsDescVOList);
                } else if (storeType.equals(1)) {
                    buildBabybelly(configContent, storePackageGoodsDescVOList);
                }
            }
        }catch (Exception e){
            log.error("查询门店的套餐详情异常",e);
        }
        return storePackageGoodsDescVOList;
    }

    private static void buildBabybelly(String configContent, List<StorePackageGoodsDescVO> storePackageGoodsDescVOList) {
        // ISLA门店 详情json
        BabyBellaStoreDetailVO bellaStoreDetailVO = JSONUtil.toBean(configContent, BabyBellaStoreDetailVO.class);
        if(ObjectUtil.isNotNull(bellaStoreDetailVO)){
            Integer orderBy = 1;
            for (BabyBellaStoreDetailVO.SwiperList swiperList : bellaStoreDetailVO.getSwiperList()) {
                StorePackageGoodsDescVO storePackageGoodsDescVO = new StorePackageGoodsDescVO();
                storePackageGoodsDescVO.setPackageName(swiperList.getGoodTitle() + "套餐");
                storePackageGoodsDescVO.setPackageImage(swiperList.getImgUrl());
                //增加排序，方便前端跳转至对应套餐
                storePackageGoodsDescVO.setOrderBy(orderBy);
                storePackageGoodsDescVO.setVrUrl(swiperList.getVrUrl());
                storePackageGoodsDescVO.setVrCoverUrl(swiperList.getVrCoverUrl());
                orderBy++;
                storePackageGoodsDescVOList.add(storePackageGoodsDescVO);
            }
        }
    }

    private static void buildIsla(String configContent, List<StorePackageGoodsDescVO> storePackageGoodsDescVOList) {
        // ISLA门店 详情json
        IslaStoreDetailVO islaStoreDetailVO = JSONUtil.toBean(configContent, IslaStoreDetailVO.class);
        if(ObjectUtil.isNotNull(islaStoreDetailVO)) {
            Integer orderBy = 1;
            for (IslaStoreDetailVO.PackageInfo packageInfo : islaStoreDetailVO.getPackageInfo()) {
                StorePackageGoodsDescVO storePackageGoodsDescVO = new StorePackageGoodsDescVO();
                storePackageGoodsDescVO.setPackageName(packageInfo.getTitle() + "套餐");
                //增加排序，方便前端跳转至对应套餐
                storePackageGoodsDescVO.setOrderBy(orderBy);
                storePackageGoodsDescVO.setGoodsId(packageInfo.getGoodsId());
                if(Objects.nonNull(packageInfo.getOptions())){
                    storePackageGoodsDescVO.setPackageImage(packageInfo.getOptions().getMasterImage());
                    storePackageGoodsDescVO.setVrUrl(packageInfo.getOptions().getVrUrl());
                    storePackageGoodsDescVO.setVrCoverUrl(packageInfo.getOptions().getVrCoverUrl());
                    storePackageGoodsDescVO.setRoomTypeName(packageInfo.getOptions().getRoomTypeName());
                }
                orderBy++;
                storePackageGoodsDescVOList.add(storePackageGoodsDescVO);
            }
        }
    }

    private static void buildSbl(String configContent, List<StorePackageGoodsDescVO> storePackageGoodsDescVOList) {
        // 圣贝拉门店
        StbellaStorePackageGoodsConfigDTO stbellaStorePackageGoodsConfigDTO = JSONUtil.toBean(
                configContent, StbellaStorePackageGoodsConfigDTO.class);

        Integer orderBy = 1;
        for (PackageDetailDTO packageDetailDTO : stbellaStorePackageGoodsConfigDTO.getMealShow()) {
            StorePackageGoodsDescVO storePackageGoodsDescVO = new StorePackageGoodsDescVO();
            storePackageGoodsDescVO.setPackageName(packageDetailDTO.getName() + "套餐");
            storePackageGoodsDescVO.setPackageImage(packageDetailDTO.getVrCoverUrl());
            //增加排序，方便前端跳转至对应套餐
            storePackageGoodsDescVO.setOrderBy(orderBy);
            storePackageGoodsDescVO.setVrUrl(packageDetailDTO.getVrUrl());
            storePackageGoodsDescVO.setVrCoverUrl(packageDetailDTO.getVrCoverUrl());
            orderBy++;

            List<DetailImageVO> detailImages = new ArrayList<>();

            // 取房间图和描述
            DetailImageVO detailImageVO1 = new DetailImageVO();
            detailImageVO1.setBanner(packageDetailDTO.getMatchImgs().get(0));
            detailImageVO1.setDetail(packageDetailDTO.getMatchItemImgs().get(0));

            detailImages.add(detailImageVO1);

            // 取客厅图和描述
            if (packageDetailDTO.getMatchImgs().size() >= 2) {
                DetailImageVO detailImageVO2 = new DetailImageVO();
                detailImageVO2.setBanner(packageDetailDTO.getMatchImgs().get(1));
                detailImageVO2.setDetail(packageDetailDTO.getMatchItemImgs().get(1));

                detailImages.add(detailImageVO2);
            }

            storePackageGoodsDescVO.setDetailImages(detailImages);
            storePackageGoodsDescVOList.add(storePackageGoodsDescVO);
        }
    }

    /**
     * 删除一个探店报告
     *
     * @param id
     * @return
     */
    @Override
    public Boolean deleteReportById(Long id) {
        return removeById(id);
    }


    /**
     * 通知scrm同步用户姓名和性别
     *
     * @param phone      phone
     * @param clientName client name
     * @param gender     gender
     * <AUTHOR>
     * @date 2023/10/10 11:28:23
     * @since 1.0.0
     */
    private void notifyScrmCustomerNameAndGender(String phone, String clientName, Integer gender) {
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoByPhone(phone);
        if (ObjectUtil.isNotNull(userBasicPO)) {
            ScrmCustomerPO scrmCustomerPO = scrmCustomerService.queryCustomerByPhone(
                userBasicPO.getPhone());
            if (ObjectUtil.isNotNull(scrmCustomerPO)) {
                xsyScrmService.updateCustomerUserNameAndGender(scrmCustomerPO.getScrmCustomerId(),
                    clientName, gender);
            }
        }
    }

    /**
     * 同步用户名称和性别至 ecp-tabClient saas customer
     *
     * @param phone      phone
     * @param clientName client name
     * @param gender     gender
     * <AUTHOR>
     * @date 2023/10/10 11:53:26
     * @since 1.0.0
     */
    private void notifyCustomerNameAndGender(String phone, String clientName, Integer gender) {

        UpdateWrapper<TabClientPO> tabClientPOUpdateWrapper = new UpdateWrapper<>();
        tabClientPOUpdateWrapper.set("name", clientName);
        tabClientPOUpdateWrapper.set("sex", gender);
        tabClientPOUpdateWrapper.eq("phone", phone);
        tabClientPOUpdateWrapper.eq("active", 1);

        boolean isUpdate = tabClientService.update(tabClientPOUpdateWrapper);
        if (!isUpdate) {
            log.info("同步用户名称和性别到tabClient失败");
        }

        UpdateWrapper<HeUserBasicPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("name", clientName);
        updateWrapper.set("gender", gender);
        updateWrapper.eq("phone", phone);
        updateWrapper.eq("is_delete", 0);

        isUpdate = heUserBasicService.update(updateWrapper);
        if (!isUpdate) {
            log.info("同步用户名称和性别到heUserBasic失败");
        }

        UpdateWrapper<ScrmCustomerPO> scrmCustomerPOUpdateWrapper = new UpdateWrapper<>();
        scrmCustomerPOUpdateWrapper.set("name", clientName);
        scrmCustomerPOUpdateWrapper.set("sex", gender);
        scrmCustomerPOUpdateWrapper.eq("phone", phone);
        scrmCustomerPOUpdateWrapper.eq("deleted", 0);

        isUpdate = scrmCustomerService.update(scrmCustomerPOUpdateWrapper);
        if (!isUpdate) {
            log.info("同步用户名称和性别到scrmCustomer失败");
        }
    }

    /**
     * 同步scrm的时候需要增加操作记录 增加操作记录
     *
     * <AUTHOR>
     * @date 2023/10/10 02:36:44
     * @since 1.0.0
     */
    private void addSystemLog(Integer saleId, String clintName, Integer gender) {

        Map<String, Object> operateDataMap = new HashMap<>();
        operateDataMap.put("saleId", saleId);
        operateDataMap.put("clintName", clintName);
        operateDataMap.put("gender", gender);

        SystemOperateLogPO systemOperateLogPO = new SystemOperateLogPO();
        systemOperateLogPO.setOperateType(
            SystemOperateTypeEnum.PI_UPDATE_USER_NAME_AND_GENDER_TO_SCRM.getCode());
        if (ObjectUtil.equals(gender, 1)) {
            systemOperateLogPO.setRemark("用户名称变更为：" + clintName + "，用户性别变更为：男");
        } else {
            systemOperateLogPO.setRemark("用户名称变更为：" + clintName + "，用户性别变更为：女");
        }
        systemOperateLogPO.setOperateData(JSONUtil.toJsonStr(operateDataMap));
        systemOperateLogPO.setOperatorType(SystemOperatorTypeEnum.SALE.getCode());
        systemOperateLogPO.setOperator(saleId);

        systemOperateLogService.save(systemOperateLogPO);
    }


    /**
     * 查看探店报告发送积分
     *
     * @param id       id
     * @param basicUid basic uid
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/27 11:00:59
     * @since 1.0.0
     */
    @Override
    public Boolean checkinStoreReportSendIntegral(Long id, Long basicUid) {
        CustomerCheckinStoreReportPO report = getById(id);
        if (Objects.isNull(report)) {
            log.info("未查到对应探店报告，id:" + id);
            return false;
        }
        HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoById(basicUid);
        if (Objects.isNull(heUserBasicPO)) {
            log.info("未查到用户basic信息，basicUid：" + basicUid);
            return false;
        }
        TabClientPO client = tabClientService.getOne(new LambdaQueryWrapper<TabClientPO>()
                .eq(TabClientPO::getBasicUid, basicUid)
                .eq(TabClientPO::getActive, 1)
                .last("limit 1"));
        if (Objects.isNull(client)) {
            log.info("未查到对应用户client信息，basicUid:" + basicUid);
            return false;
        }
        //不是本人，不加积分
        if (Objects.equals(report.getClientPhone(), client.getPhone())) {
            // 发放到店积分
            customerInStoreAddIntegral(0, report, client);
        }
        return true;
    }
    /**
     * 到店参观添加积分（资产中心版）
     *
     * <AUTHOR>
     * @date 2023/11/27 10:56:58
     * @since 1.0.0
     */
    private void customerInStoreAddIntegral(Integer inviteAddIntegralType, CustomerCheckinStoreReportPO report, TabClientPO client) {
        log.info("到店参观加积分，用户id:" + client.getBasicUid());
        Integer clientType = ClientTypeConstant.CLIENT_YZ_BUYER;//月子客户
        CfgStore cfgStore = cfgStoreService.queryByStoreId(report.getStoreId());
        if (Objects.isNull(cfgStore)) {
            log.info("未查到门店信息，storeId:" + report.getStoreId());
        }
        CustomerIntegralReq req = new CustomerIntegralReq();
        req.setBasicId(client.getBasicUid().longValue());
        //根据品牌和场景计算积分
        CustomerIntegralVO integralVO = new CustomerIntegralVO();
        //圣贝拉
        if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_SBL.getCode())) {
            req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_SBL_NEW.getCode());
            req.setBrandType(CustomerAssetsTypeEnum.GROW_SBL.getCode());
            integralVO = customerGrowthService.genCustomerIntegral(req);
        } else if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_XBL.getCode())) {
            //小贝拉
            req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_XBL_NEW.getCode());
            integralVO = customerGrowthService.genCustomerIntegralXbl(req);
        } else if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_ISLA.getCode())) {
            //艾屿
            req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_ISLA_NEW.getCode());
            req.setBrandType(CustomerAssetsTypeEnum.GROW_ISLA.getCode());
            integralVO = customerGrowthService.genCustomerIntegral(req);
        }

        //有场景，并且积分不为空
        if (Objects.nonNull(integralVO) && integralVO.getIntegral() > 0L) {
            Long integral = integralVO.getIntegral();
            //调用资产方法增加积分，增加积分记录
            UserTradeReq tradeReq = new UserTradeReq();
            tradeReq.setUserId(client.getBasicUid().toString());
            tradeReq.setTradeType(TradeType.AVAILABLE_STORE_SIGN.getCode());
            tradeReq.setAmount(integral);
            // 以类型加用户id组成唯一键，保证该类型的积分只能加一次
            String uniqueId = TradeType.AVAILABLE_STORE_SIGN.getCode() + "_" + client.getBasicUid();
            tradeReq.setUniqueId(uniqueId);
            tradeReq.setTitle("到店有礼-门店打卡奖励");
            tradeReq.setRemark("查看探店报告发送积分");
            Result<List<Long>> result = tradeCmdService.execUserTrade(tradeReq);
            log.info("调用远程接口增加,提交查看探店报告积分,用户id:{},storeId:{},integralVO:{},tradeReq:{},result{}",
                    client.getBasicUid(),report.getStoreId(),JSON.toJSON(integralVO),
                    JSON.toJSONString(tradeReq),
                    result);
            if (result.getSuccess()) {
                // 添加积分弹框和发短信
                if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_SBL.getCode())
                    || Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_XBL.getCode())) {
                    // TODO 艾屿暂时不发短信
                    Map<String, Object> integralQueryMap = new HashMap<>();
                    integralQueryMap.put("myVisitIntegral", client.getBasicUid());
                    integralQueryMap.put("basicUid", integral);
                    addIntegral(integralQueryMap);
                    log.info("到店参观加积分-给自己弹框");

                    if (StringUtils.isNotEmpty(client.getPhone()) && StringUtils.isNotBlank(client.getPhone())) {
                        Map<String, String> parameter = new HashMap<>();
                        parameter.put("phone", "+86" + client.getPhone());
                        parameter.put("storeType", cfgStore.getType().toString());
                        parameter.put("clientName", "客户");
                        parameter.put("goodsName", "您的到店参观礼已送达");
                        parameter.put("integral", integral.toString());
                        sendSmsMessage(parameter);
                    }
                }

                // 上级加积分
                if (Objects.nonNull(inviteAddIntegralType)) {
                    parentAddIntegral(cfgStore, client);
                }
            }
        }
    }


    /**
     * 给上级加积分
     *
     * <AUTHOR>
     * @date 2023/11/27 02:54:53
     * @since 1.0.0
     */
    private void parentAddIntegral(CfgStore cfgStore, TabClientPO client) {
        // 需要给上级加积分
        HeInviteRelationPO relation = heInviteRelationService.getOne(new LambdaQueryWrapper<HeInviteRelationPO>()
                .eq(HeInviteRelationPO::getBasicId, client.getBasicUid())
                .eq(HeInviteRelationPO::getDeleted, 0)
                .last("limit 1"));
        if (Objects.nonNull(relation)) {

            HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoById(relation.getParentBasicId());

            if (Objects.isNull(heUserBasicPO)) {
                log.info("未查到上级信息，上级id：" + relation.getParentBasicId());
                return;
            }

            CustomerIntegralReq req = new CustomerIntegralReq();
            req.setBasicId(relation.getParentBasicId());
            //根据品牌和场景计算积分
            CustomerIntegralVO integralVO = new CustomerIntegralVO();
            //圣贝拉
            if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_SBL.getCode())) {
                req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_SBL_WORD_MOUTH.getCode());
                req.setBrandType(CustomerAssetsTypeEnum.GROW_SBL.getCode());
                integralVO = customerGrowthService.genCustomerIntegral(req);
            } else if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_XBL.getCode())) {
                //小贝拉
                req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_XBL_WORD_MOUTH.getCode());
                integralVO = customerGrowthService.genCustomerIntegralXbl(req);
            } else if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_ISLA.getCode())) {
                // 艾屿
                req.setScene(CustomerIntegralSceneEnum.SCENE_TYPE_ISLA_WORD_MOUTH.getCode());
                req.setBrandType(CustomerAssetsTypeEnum.GROW_ISLA.getCode());
                integralVO = customerGrowthService.genCustomerIntegral(req);
            }

            //有场景，并且积分不为空
            if (Objects.nonNull(integralVO) && integralVO.getIntegral() > 0L) {
                Long integral = integralVO.getIntegral();

                //调用资产方法增加积分，增加积分记录
                UserTradeReq tradeReq = new UserTradeReq();
                tradeReq.setUserId(relation.getParentBasicId().toString());
                tradeReq.setTradeType(TradeType.AVAILABLE_RECOMMEND_SIGN.getCode());
                tradeReq.setAmount(integral);

                // 以类型加上级用户id加用户id组成唯一键，保证该类型的积分只能加一次
                String uniqueId = TradeType.AVAILABLE_RECOMMEND_SIGN.getCode() + "_" + relation.getParentBasicId()
                        + "_" + relation.getBasicId();

                tradeReq.setUniqueId(uniqueId);
                tradeReq.setTitle("口碑有礼-好友打卡奖励");
                tradeReq.setRemark("查看探店报告给上级发送积分");
                Result<List<Long>> result = tradeCmdService.execUserTrade(tradeReq);
                log.info("调用远程接口增加,提交查看探店报告积分：result{}", result);
                if (result.getSuccess()) {
                    // 更新邀请关系表的integral（我给parent带来的积分总和）
                    relation.setIntegral(relation.getIntegral() + integral);
                    heInviteRelationService.updateById(relation);

                    if (Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_SBL.getCode())
                        || Objects.equals(cfgStore.getType(), CustomerAssetsBrandEnum.BRAND_XBL.getCode())) {
                        // TODO 艾屿暂时不发短信
                        // 添加积分弹框和发短信
                        Map<String, Object> integralQueryMap = new HashMap<>();
                        integralQueryMap.put("myFriendVisitIntegral", integral);
                        integralQueryMap.put("basicUid", relation.getParentBasicId());
                        addIntegral(integralQueryMap);
                        log.info("到店参观加积分-给上级弹框");

                        // 发送短信
                        if (StringUtils.isNotEmpty(heUserBasicPO.getPhone()) && StringUtils.isNotBlank(heUserBasicPO.getPhone())) {
                            Map<String, String> parameter = new HashMap<>();
                            parameter.put("phone", "+86" + heUserBasicPO.getPhone());
                            parameter.put("storeType", cfgStore.getType().toString());
                            parameter.put("clientName", heUserBasicPO.getName());
                            parameter.put("inviteClientName", client.getName());
                            parameter.put("goodsName", "到店参观");
                            parameter.put("integral", integral.toString());
                            sendSmsMessage(parameter);
                        }
                    }
                }
            }
        }
    }

    /**
     * 添加积分缓存
     *
     * @param integralQueryMap integral query map
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/27 01:42:30
     * @since 1.0.0
     */
    public void addIntegral(Map<String, Object> integralQueryMap) {
        String str = "-";
        //定义缓存的key
        String myOrderCacheKey = RedisConstant.MY_ORDER_INTEGRAL_COUNT + str + integralQueryMap.get("basicUid");
        String myVisitCacheKey = RedisConstant.MY_VISIT_INTEGRAL_COUNT + str + integralQueryMap.get("basicUid");
        String myFriendOrderCacheKey = RedisConstant.MY_FRIEND_ORDER_INTEGRAL_COUNT + str + integralQueryMap.get("basicUid");
        String myFriendVisitCacheKey = RedisConstant.MY_FRIEND_VISIT_INTEGRAL_COUNT + str + integralQueryMap.get("basicUid");

        // 定义新年赠送积分缓存的key
        String newYearIntegralGiftCacheKey = RedisConstant.NEW_YEAR_INTEGRAL_GIFT_COUNT + str + integralQueryMap.get("basicUid");

        //我的订单
        if (integralQueryMap.containsKey("myOrderIntegral")) {
            addBulletFrame(myOrderCacheKey, Long.valueOf(integralQueryMap.get("myOrderIntegral").toString()));
        }
        //我的到店
        if (integralQueryMap.containsKey("myVisitIntegral")) {
            addBulletFrame(myVisitCacheKey, Long.valueOf(integralQueryMap.get("myVisitIntegral").toString()));
        }
        //我的好友订单
        if (integralQueryMap.containsKey("myFriendOrderIntegral")) {
            addBulletFrame(myFriendOrderCacheKey, Long.valueOf(integralQueryMap.get("myFriendOrderIntegral").toString()));
        }
        //我的好友到店
        if (integralQueryMap.containsKey("myFriendVisitIntegral")) {
            addBulletFrame(myFriendVisitCacheKey, Long.valueOf(integralQueryMap.get("myFriendVisitIntegral").toString()));
        }

        // 虎年新年赠送积分
        if (integralQueryMap.containsKey("newTigerYearIntegral")) {
            addBulletFrame(newYearIntegralGiftCacheKey, Long.valueOf(integralQueryMap.get("newTigerYearIntegral").toString()));
        }

    }

    /**
     * 设置两次弹窗时间之间的积分
     *
     * @param key
     * @param integral
     */
    private void addBulletFrame(String key, Long integral) {
        boolean flag = redisService.hasKey(key);
        if (flag) {
            redisService.increment(key, integral);
        } else {
            redisService.setCacheObject(key, integral);
        }
    }


    /**
     * 发送增加积分的短信
     *
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/27 02:15:27
     * @since 1.0.0
     */
    private Boolean sendSmsMessage(Map<String, String> parameter) {
        Sms sms = Sms.start(parameter.get("phone")).setMessageApp().babyBella();
        //圣贝拉
        if (Objects.equals(parameter.get("storeType"), "0")) {
            if (parameter.containsKey("inviteClientName")) {
                //上级通知
                sms.setMessageSign().salntBella().setMessageTemplate().saintParentAddIntegralMsg(parameter.get("clientName"), parameter.get("inviteClientName"), parameter.get("goodsName"), parameter.get("integral"));
                log.info("订单加积分-给上级短信，请求参数:" + JSONUtil.toJsonStr(parameter));
            } else {
                //本人通知
                sms.setMessageSign().salntBella().setMessageTemplate().saintSelfAddIntegralMsg(parameter.get("clientName"), parameter.get("goodsName"), parameter.get("integral"));
                log.info("订单加积分-给自己短信，请求参数:" + JSONUtil.toJsonStr(parameter));
            }
        } else {
            if (parameter.containsKey("inviteClientName")) {
                sms.setMessageSign().babyBella().setMessageTemplate().babyBellaParentAddIntegralMsg(parameter.get("clientName"), parameter.get("inviteClientName"), parameter.get("goodsName"), parameter.get("integral"));
                log.info("订单加积分-给上级短信，请求参数:" + JSONUtil.toJsonStr(parameter));
            } else {
                //小贝拉
                sms.setMessageSign().babyBella().setMessageTemplate().babyBellaSelfAddIntegralMsg(parameter.get("clientName"), parameter.get("goodsName"), parameter.get("integral"));
                log.info("订单加积分-给自己短信，请求参数:" + JSONUtil.toJsonStr(parameter));
            }
        }
        return smsService.sendMessage(sms);
    }

}
