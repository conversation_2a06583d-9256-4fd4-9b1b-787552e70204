package com.stbella.customer.server.manager;

import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.core.result.Result;
import com.stbella.notice.server.NoticeService;
import com.stbella.scrm.service.ScrmMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BaseManager {

    @DubboReference(timeout = 5000)
    private ScrmMessageService scrmMessageService;

    @DubboReference(timeout = 5000)
    private NoticeService noticeService;

    /**
     * 获取企微中员工的二维码
     *
     * @param phone
     * @return
     */
    public String getQwQrcodeByStaffPhone(String phone) {
        log.info("去base服务中查询员工的企微二维码, phone:{}", phone);
        String qrcode = scrmMessageService.getStaffQwQrcode(phone);
        log.info("去base服务中查询员工的企微二维码， qrcode:{}", qrcode);

        return qrcode;
    }

    public Boolean notice(WechatNailNailRobotDeclarationRequest request) {
        log.info("发送企微消息, request:{}", JSONUtil.toJsonStr(request));
        Result<Boolean> result = noticeService.notice(request);
        log.info("发送企微消息， result:{}", result);
        return result.getData();
    }
}
