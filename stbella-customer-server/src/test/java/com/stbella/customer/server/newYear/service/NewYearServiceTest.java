package com.stbella.customer.server.newYear.service;

import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.newYear.component.NewYearSnowballEngine;
import com.stbella.customer.server.newYear.req.DrawCheckinPrizeRequest;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@TestPropertySource(properties = {"spring.profiles.active=dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
public class NewYearServiceTest {

    @Resource
    private NewYearSnowballEngine newYearSnowballEngine;

    @Test
    public void prizeDraw() {
        DrawCheckinPrizeRequest request = new DrawCheckinPrizeRequest();
        request.setBasicUid(10819);
        request.setBrandType(0);
        request.setCheckinDate(new Date());
        request.setCheckinDay(17);
        request.setCheckinId(137L);

        newYearSnowballEngine.drawCheckinPrize(request);
    }
}
