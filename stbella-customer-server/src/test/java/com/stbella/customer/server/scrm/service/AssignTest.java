package com.stbella.customer.server.scrm.service;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.scrm.component.SnowballEngine;
import com.stbella.customer.server.scrm.dto.OpportunityFact;
import com.stbella.customer.server.scrm.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 分配测试
 * @CreateDate 2023-12-13 14:09
 * @Version 1.0
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AssignTest {

    @Resource
    SnowballEngine engine;

    @Test
    public void assignByCitySuccess() {
        OpportunityFact fact = new OpportunityFact();
        fact.setProvince("330000");
        fact.setCity("330100");
        fact.setScrmOpportunityId(11L);
        Result<OpportunityFact> opportunityFactResult = engine.assignOpportunity(fact);
        log.info("assignByCitySuccess {}", JSONUtil.toJsonStr(opportunityFactResult));
    }

    @Test
    public void assignByCityFail() {
        OpportunityFact fact = new OpportunityFact();
        fact.setProvince("330000");
        fact.setCity("330101");
        fact.setScrmOpportunityId(11L);
        Result<OpportunityFact> opportunityFactResult = engine.assignOpportunity(fact);
        log.info("assignByCitySuccess {}", JSONUtil.toJsonStr(opportunityFactResult));
    }
}