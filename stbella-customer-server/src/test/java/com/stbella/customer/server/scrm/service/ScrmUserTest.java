package com.stbella.customer.server.scrm.service;

import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.scrm.component.processor.OpportunityAssignAlertProcessor;
import com.stbella.customer.server.scrm.dto.OpportunityAssignAlertDTO;
import com.stbella.customer.server.scrm.dto.OpportunityFact;
import com.stbella.customer.server.scrm.dto.ScrmOppotrunityProcessorDTO;
import com.stbella.customer.server.scrm.enums.AssignFailTypeEnum;
import com.stbella.customer.server.scrm.enums.BusinessTypeEnum;

import org.apache.logging.log4j.util.Strings;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @ProjectName stbella-customer
 * @PackageName com.stbella.customer.server.scrm.service
 * @ClassName ScrmUserTest
 * @Description
 * <AUTHOR>
 * @CreateDate 2023-12-13 14:09
 * @Version 1.0
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ScrmUserTest {

    @Resource
    private OpportunityAssignAlertProcessor opportunityAssignAlertProcessor;
    @Test
    public void scrmUser() {
        log.info("scrmUser");
//        List<String> names = EnumUtil.getNames(BusinessTypeEnum.class);
//        log.info("BusinessTypeEnum => {}", JSONUtil.toJsonStr(names));
        Map<String, Object> code = EnumUtil.getNameFieldMap(BusinessTypeEnum.class, "code");
        log.info("code值{}", JSONUtil.toJsonStr(code));
    }

    @Test
    public void name() {
        Integer errorCode = 1;
        String reason = AssignFailTypeEnum.getValueByCode(errorCode);
        if (Strings.isBlank(reason)) {
            reason = "未知错误";
        }
        OpportunityFact fact = new OpportunityFact();
        fact.setOrderType(0);
        fact.setProvince("");
        fact.setCity("");
        fact.setDistrict("");
        fact.setStoreId(0);
        fact.setScrmOpportunityId(0L);
        fact.setTeamId(0L);
        fact.setScrmMemberId(0L);
        fact.setProvinceName("");
        fact.setCityName("");
        fact.setDistrictName("");
        fact.setClientName("");
        fact.setClientPhone("");
        fact.setCustomerBudget("");

        OpportunityAssignAlertDTO build = OpportunityAssignAlertDTO.builder()
                .scrmOpportunityId(fact.getScrmOpportunityId())
                .reasonType(errorCode)
                .reason(reason)
                .build();
        /**
         * 商机error处理
         */
        ScrmOppotrunityProcessorDTO scrmOppotrunityProcessorDTO = ScrmOppotrunityProcessorDTO.builder()
                .opportunityAssignAlertDTO(build)
                .opportunityFact(fact).build();

        opportunityAssignAlertProcessor.run(scrmOppotrunityProcessorDTO);
    }
}