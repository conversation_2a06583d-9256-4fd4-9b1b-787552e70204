package com.stbella.customer.server.customer.service.impl;

import cn.hutool.json.JSONUtil;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.model.ddzh.poiqrcode.poiqrcodeQuerydzcoupon.PoiqrcodeQuerydzcouponRequest;
import com.meituan.sdk.model.ddzh.poiqrcode.poiqrcodeQuerydzcoupon.PoiqrcodeQuerydzcouponResponse;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.request.meituan.MessageBaseRequest;
import com.stbella.customer.server.customer.service.MeituanService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
@SpringBootTest(classes = {CustomerLauncher.class})
@TestPropertySource(properties = {"spring.profiles.active=dev"})
public class MeituanServiceImplTest {

    private static final String APPAUTHTOKEN = "V2-9e0a2d96cd723ff6f1ee5cc76e8353229ee90f0e91cb94378781d3d64a5f5f58e301175186da26a97bdf49b1195c5c0f469c1e358306258dd0a05229915ea2f69e4dec38f444afe11c10f8d947784d3e";

    @Resource
    private MeituanService meituanService;

    @Test
    public void testNewleads() {
        String requestBody = "{\"msgType\":5810053,\"timestamp\":1729492246,\"sign\":\"123456\",\"developerId\":113283,\"businessId\":58,\"opBizCode\":\"af161587ed4dc3abcef35bc349ad834a\",\"ePoiId\":\"af161587ed4dc3abcef35bc349ad834a\",\"message\":\"{\\\"consumerId\\\":466292245,\\\"consumerName\\\":\\\"\\\",\\\"phoneShowType\\\":0,\\\"phone\\\":\\\"***********\\\",\\\"platform\\\":2,\\\"shopName\\\":\\\"baby bella\\u5c0f\\u8d1d\\u62c9\\u6bcd\\u5a74\\u62a4\\u7406\\u4e2d\\u5fc3(\\u957f\\u6c99\\u96c5\\u8bd7\\u9601\\u5e97)\\\",\\\"lastLeadsFirstSource\\\":24,\\\"lastLeadsSecondSource\\\":\\\"\\u56e2\\u8d2d\\u865a\\u62df\\u53f7\\\",\\\"lastLeadsThirdSource\\\":\\\"\\u9884\\u7ea6\\u53c2\\u89c2|\\u7ebf\\u4e0b\\u966a\\u62a4\\u53c2\\u89c2+\\u5b55\\u671f\\u6307\\u5bfc|\\u51711\\u5f20\\u5238\\uff0c\\u672a\\u4f7f\\u75281\\u5f20\\uff0c\\u8fc7\\u671f\\u65f6\\u95f4\\u4e3a2025-01-08\\\",\\\"lastLeadsAddTime\\\":1728532823000,\\\"status\\\":1}\"}";
        MessageBaseRequest messageBaseRequest = JSONUtil.toBean(requestBody, MessageBaseRequest.class);
        boolean result = meituanService.newleads(messageBaseRequest);

        log.info("大众点评客资预约消息通知结果:{}", result);
    }

    @Test
    public void testCoupon() {

        DefaultMeituanClient meituanClient = DefaultMeituanClient.builder(113283L, "ph2urx0shyk0o85t").build();
        PoiqrcodeQuerydzcouponRequest request = new PoiqrcodeQuerydzcouponRequest();

        try {
            MeituanResponse<PoiqrcodeQuerydzcouponResponse> response = meituanClient.invokeApi(request, APPAUTHTOKEN);
            log.info("获取优惠券成功:{}", response);
        } catch (MtSdkException e) {
            log.info("获取优惠券失败:{}", e.getMessage());
        }
    }
}
