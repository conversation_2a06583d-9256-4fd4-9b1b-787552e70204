package com.stbella.customer.server.scrm.service;

import cn.hutool.core.util.PhoneUtil;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-01-04  15:46
 * @Description: test
 */
public class Test {
    public static void main(String[] args) {

        String p1 = "";
        String p2 = "111";
        String p3 = "1111";
        String p4 = "11111";
        String p5 = "111111";
        String p6 = "1111111";
        String p7 = "11111111";
        String p8 = "111111111";
        String p9 = "1111111111";
        String p10 = "11111111111";
        String p11 = "111111111111";

        //验证手机号隐藏中间4位
        CharSequence charSequence = PhoneUtil.hideBetween(p1);
        System.out.println(p1+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p2);
        System.out.println(p2+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p3);
        System.out.println(p3+"  "+ charSequence);

        //写出测试p4到p11的 验证手机号隐藏中间4位的代码

        charSequence = PhoneUtil.hideBetween(p4);
        System.out.println(p4+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p5);
        System.out.println(p5+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p6);
        System.out.println(p6+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p7);
        System.out.println(p7+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p8);
        System.out.println(p8+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p9);
        System.out.println(p9+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p10);
        System.out.println(p10+"  "+ charSequence);

        charSequence = PhoneUtil.hideBetween(p11);
        System.out.println(p11+"  "+ charSequence);




    }
}
