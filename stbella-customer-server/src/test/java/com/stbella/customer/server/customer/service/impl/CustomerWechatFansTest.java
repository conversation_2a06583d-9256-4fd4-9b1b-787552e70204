package com.stbella.customer.server.customer.service.impl;

import cn.hutool.json.JSONUtil;
import com.stbella.care.server.care.entity.RoomStateCheckInRoomInfoPO;
import com.stbella.cts.server.enums.YesOrNoEnum;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.entity.CustomerWechatFansPO;
import com.stbella.customer.server.customer.service.CustomerWechatFansService;

import java.time.LocalDate;
import java.util.*;
import javax.annotation.Resource;

import com.stbella.customer.server.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest(classes = {CustomerLauncher.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("dev2")
@Slf4j
public class CustomerWechatFansTest {
    @Resource
    private CustomerWechatFansService customerWechatFansService;

    @Test
    public void saveUserInfo() {
        CustomerWechatFansPO wechatFansPO = new CustomerWechatFansPO();
        wechatFansPO.setSubscribeNum(1);
        wechatFansPO.setAppType(2);
        wechatFansPO.setSubscribe(YesOrNoEnum.YES.getCode());
        wechatFansPO.setSubscribeScene("ADD_SCENE_SEARCH");
        wechatFansPO.setSubscribeTime(new Date());
        wechatFansPO.setOpenId("olqNq658cMd4f8bJo9b41KGNoXZg");
        wechatFansPO.setLanguage("zh_CN");
        wechatFansPO.setRemark("");
        wechatFansPO.setUnionId("o8dwUxLEDvyM44i_y0FWkctFGzj0");
        wechatFansPO.setGroupId(JSONUtil.toJsonStr(new Object()));
        wechatFansPO.setQrSceneStr("");
        wechatFansPO.setSource(100);

        Boolean subscribeResult = customerWechatFansService.saveSubscribeUserInfo(wechatFansPO);
        log.info("subscribeResult:{}", subscribeResult);
    }



    public static void main(String[] args) {
        List<RoomStateCheckInRoomInfoPO> roomInfoList = new ArrayList<>();
        RoomStateCheckInRoomInfoPO po = new RoomStateCheckInRoomInfoPO();
        po.setCheckInDate(LocalDate.of(2021, 10, 1));
        po.setCheckOutDate(LocalDate.of(2021, 10, 6));
        po.setOvernightDays(6);

        RoomStateCheckInRoomInfoPO po1 = new RoomStateCheckInRoomInfoPO();
        po1.setCheckInDate(LocalDate.of(2021, 10, 8));
        po1.setCheckOutDate(LocalDate.of(2021, 10, 9));
        po1.setOvernightDays(2);
        roomInfoList.add(po);roomInfoList.add(po1);

        Date checkInDateAfter = DateUtils.getCheckInDateAfter(roomInfoList, 7);

        Date checkOutDateBefore = DateUtils.getCheckOutDateBefore(roomInfoList, 3);

        System.out.println(checkInDateAfter+""+checkOutDateBefore);


    }


}
