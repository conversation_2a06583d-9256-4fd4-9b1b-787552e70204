package com.stbella.customer.server.scrm.service;

import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.service.CustomerGrowthService;
import com.stbella.customer.server.scrm.request.BatchAddTeamMemberReq;
import com.stbella.customer.server.scrm.request.DeleteTeamMemberReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@ActiveProfiles("dev")
@TestPropertySource(properties = {"spring.profiles.active=dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TeamMemberTest {

    @Resource
    TeamMemberService teamMemberService;

    @Resource
    private CustomerGrowthService customerGrowthService;

    @Test
    public void testAddTeamMember() {
        BatchAddTeamMemberReq request = new BatchAddTeamMemberReq();

        request.setTeamId(1734498671945158658L);

        BatchAddTeamMemberReq.TeamMember teamMember = new BatchAddTeamMemberReq.TeamMember();
        teamMember.setTeamId(1734498671945158658L);
        teamMember.setScrmId(2917851716060200L);
        teamMember.setSalesName("测试名字");
        teamMember.setEmployeeId(1461209899810713602L);
        request.getTeamMembers().add(teamMember);

        log.info("testAddSalesTeam result {}", teamMemberService.addTeamMember(request));

    }

    @Test
    public void testDeleteTeamMember() {
        DeleteTeamMemberReq request = new DeleteTeamMemberReq();

        request.setId(1736581133281693699L);
        log.info("testAddSalesTeam result {}",  teamMemberService.deleteTeamMember(request));

    }

    @Test
    public void testMemberWillDownLevelNotify() {
        customerGrowthService.memberWillDownLevelNotify(7, 0);
    }

}
