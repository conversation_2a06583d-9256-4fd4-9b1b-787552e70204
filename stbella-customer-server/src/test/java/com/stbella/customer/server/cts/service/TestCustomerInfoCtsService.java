package com.stbella.customer.server.cts.service;

import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.dto.CustomerWechatDTO;
import com.stbella.customer.server.customer.service.CustomerWechatFansService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 这里写类的注释
 *
 * <AUTHOR>
 * @date 2022-03-09 14:07
 * @sine 1.0.0
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestCustomerInfoCtsService {

    @Resource
    private CustomerInfoCtsService customerInfoCtsService;
    @Resource
    private CustomerWechatFansService customerWechatFansService;

    @Test
    public void testSitterSave() {
    }

    @Test
    public void getCustomerWechatInfoByClientIdTest() {
        List<CustomerWechatDTO> customerWechatInfoByClientId = customerWechatFansService.getCustomerWechatInfoByClientId(Arrays.asList(7815L));

        System.out.println(customerWechatInfoByClientId);
    }
}
