package com.stbella.customer.server.customer.service.impl;

import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.request.YouzanVoucherInfoRequest;
import com.stbella.customer.server.customer.service.CustomerBaseVoucherService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@TestPropertySource(properties = {"spring.profiles.active=dev2"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
public class CustomerBaseVoucherTest {

    @Resource
    private CustomerBaseVoucherService customerBaseVoucherService;


    @Test
    public void youzanVoucherBatchSync() {
       String requestMsg = "[{\"basicId\":723272,\"voucherIdentity\":{\"couponId\":\"31766697474\",\"couponType\":0},\"sendSource\":\"scrm_detail\",\"verifyCode\":\"ZAN4879113024192\",\"validStartTime\":1734019200000,\"validEndTime\":1765555199000,\"sendAt\":1734068459000,\"verifiedAt\":null,\"title\":\"嘉人卡-S-bra\",\"description\":\"嘉人卡-S-bra\\n活动时间：领券当日起365天内可用\\n优惠内容：部分商品可用，满任意金额可用\",\"thresholdType\":0,\"preferentialMode\":3,\"thresholdAmount\":\"0\",\"value\":\"0\",\"status\":1,\"activityId\":\"23226543\"},{\"basicId\":723272,\"voucherIdentity\":{\"couponId\":\"31766697473\",\"couponType\":0},\"sendSource\":\"scrm_detail\",\"verifyCode\":\"ZAN4604236165825\",\"validStartTime\":1734019200000,\"validEndTime\":1765555199000,\"sendAt\":1734068459000,\"verifiedAt\":null,\"title\":\"嘉人卡-双人下午茶\",\"description\":\"嘉人卡-双人下午茶\\n活动时间：领券当日起365天内可用\\n优惠内容：部分商品可用，满任意金额可用\",\"thresholdType\":0,\"preferentialMode\":3,\"thresholdAmount\":\"0\",\"value\":\"0\",\"status\":1,\"activityId\":\"23226518\"},{\"basicId\":723272,\"voucherIdentity\":{\"couponId\":\"20944839796\",\"couponType\":0},\"sendSource\":\"scrm_detail\",\"verifyCode\":\"ZAN9655675532366\",\"validStartTime\":1733741685000,\"validEndTime\":1734105599000,\"sendAt\":1734068463000,\"verifiedAt\":null,\"title\":\"88折\",\"description\":\"优惠内容：部分商品可用，满1000元可用，8.8折优惠券\",\"thresholdType\":1,\"preferentialMode\":2,\"thresholdAmount\":\"100000\",\"value\":\"88\",\"status\":4,\"activityId\":\"32418259\"}]";

        List<YouzanVoucherInfoRequest> request = JSONUtil.toList(requestMsg, YouzanVoucherInfoRequest.class);
        customerBaseVoucherService.youzanVoucherBatchSync(request);
    }
}
