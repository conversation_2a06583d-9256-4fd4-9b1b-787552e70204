package com.stbella.customer.server.scrm.service;

import cn.hutool.json.JSONUtil;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.utils.sso.EmployeeContextHolder;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.customer.vo.SalesTeamVO;
import com.stbella.customer.server.scrm.entity.SalesTeamPO;
import com.stbella.customer.server.scrm.request.AddSalesTeamReq;
import com.stbella.customer.server.scrm.request.DeleteSalesTeamReq;
import com.stbella.customer.server.scrm.request.QuerySalesTeamReq;
import com.stbella.customer.server.scrm.request.UpdateSalesTeamReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class SalesTeamTest {

    @Resource
    private SalesTeamService salesTeamService;

    // 测试查询团队方法
    @Test
    public void testQuerySalesTeamByCondition() {
        // 创建一个模拟的请求对象
        QuerySalesTeamReq request = new QuerySalesTeamReq();
        request.setId(1734498785413664769L);
        // 调用服务方法
        List<SalesTeamVO> result = salesTeamService.querySalesTeamByCondition(request).getData().getList();
        log.info("testQuerySalesTeamByCondition result {}", JSONUtil.toJsonStr(result));
    }

    @Test
    public void testAddSalesTeam() {
        AddSalesTeamReq request = new AddSalesTeamReq();
        // 设置请求参数
        request.setTeamName("测试3122");
        request.setRemark("remark");

        EmployeeContextHolder.setCurrentUser(new UserTokenInfoDTO());

        log.info("testAddSalesTeam result {}", salesTeamService.addSalesTeam(request));
    }

    // 测试更新团队方法
    @Test
    public void testUpdateSalesTeam() {
        UpdateSalesTeamReq request = new UpdateSalesTeamReq();
        // 设置请求参数
        request.setId(1734498785413664769L);
        request.setRemark("mark2");

        log.info("testAddSalesTeam result {}", salesTeamService.updateSalesTeam(request));
    }

    @Test
    public void testDeleteSalesTeam() {
        DeleteSalesTeamReq request = new DeleteSalesTeamReq();
        // 设置请求参数
        request.setId(1734498785413664769L);

        log.info("testAddSalesTeam result {}", salesTeamService.deleteSalesTeam(request));
    }

    @Test
    public void testQuerySalesTeamByIds() {
        List<Long> ids = new ArrayList<>();
        ids.add(1734498785413664769L);

        // 调用服务方法
        List<SalesTeamPO> result = salesTeamService.querySalesTeamByIds(ids);

        log.info("testAddSalesTeam result {}", JSONUtil.toJsonStr(result));
    }


}
