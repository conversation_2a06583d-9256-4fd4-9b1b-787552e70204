package com.stbella.customer.server.customer.service.impl;

import com.stbella.customer.server.cts.enums.CustomerAssetsTypeEnum;
import com.stbella.customer.server.customer.request.growth.CustomerAssetsFlowReq;
import com.stbella.customer.server.customer.request.growth.CustomerProductionGiftRequest;
import com.stbella.customer.server.scrm.manager.ScrmApiConfig;
import com.stbella.customer.server.scrm.manager.ScrmManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CustomerGrowthServiceImplTest {

    @Mock
    private ScrmManager scrmManager;

    @InjectMocks
    private CustomerGrowthServiceOldImpl customerGrowthService;

    private Method postMemberProductGiveMethod;

    @BeforeEach
    void setUp() throws NoSuchMethodException {
        // 使用反射获取私有方法
        postMemberProductGiveMethod = CustomerGrowthServiceOldImpl.class
            .getDeclaredMethod("postMemberProductGive", CustomerAssetsFlowReq.class, Long.class);
        postMemberProductGiveMethod.setAccessible(true);
    }

    @Test
    void testPostMemberProductGive() throws Exception {
        // 创建参数
        CustomerAssetsFlowReq req = new CustomerAssetsFlowReq();
        req.setBasicId(123L);
        req.setBizType(CustomerAssetsTypeEnum.GROW_ISLA.getCode()); // 此处应设置为CustomerAssetsTypeEnum.GROW_ISLA.getCode()
        Long id = 1L;
        // 调用私有方法
        // 验证ScrmManager的handleScrmCommonPost方法是否被正确调用
        verify(scrmManager).handleScrmCommonPost(any(CustomerProductionGiftRequest.class), eq(ScrmApiConfig.SCRM_ISLA_MEMBER_PRODUCT_GIVE));
    }
}
