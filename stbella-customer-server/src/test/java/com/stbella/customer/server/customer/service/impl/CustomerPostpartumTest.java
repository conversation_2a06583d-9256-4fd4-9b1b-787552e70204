package com.stbella.customer.server.customer.service.impl;

import cn.hutool.core.io.FileTypeUtil;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.config.oss.OSSFactory;
import com.stbella.customer.server.config.oss.OssUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.Base64Utils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.UUID;

/**
 * <AUTHOR> liuChang
 * @date : 2025/2/19 5:58 PM
 */
@SpringBootTest(classes = {CustomerLauncher.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("dev2")
@TestPropertySource(properties = {"spring.profiles.active=dev2"})
@Slf4j
public class CustomerPostpartumTest {

	@Test
	public void testUrl() {

		String img_breast_front = "";
		byte[] bytes = Base64Utils.decodeFromString(img_breast_front);
		// 将字节数组转换为输入流
		InputStream inputStream = new ByteArrayInputStream(bytes);
		// 使用 Hutool 的 FileTypeUtil 检测文件类型
		String fileType = FileTypeUtil.getType(inputStream);

		String path = "stbella/customer/" + "callBackModifyTest/" + UUID.randomUUID() + "." + fileType;
		OssUploadResponse imageResult = OSSFactory.build().upload(bytes, path);
		String url = imageResult.getUrl();


		System.out.println(url);
	}
}
