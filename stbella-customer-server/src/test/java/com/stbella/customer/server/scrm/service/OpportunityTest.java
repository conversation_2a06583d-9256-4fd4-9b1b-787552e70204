package com.stbella.customer.server.scrm.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.scrm.convert.SCRMConvert;
import com.stbella.customer.server.scrm.entity.ScrmCustomerPO;
import com.stbella.customer.server.scrm.request.AccountInfoRequest;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@TestPropertySource(properties = {"spring.profiles.active=dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerLauncher.class, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
public class OpportunityTest {

    @Resource
    private ScrmBusinessOpportunityService scrmBusinessOpportunityService;

    @Resource
    private XsyScrmService xsyScrmService;

    @Resource
    private ScrmCustomerService scrmCustomerService;

    @Resource
    private SCRMConvert scrmConvert;

    @Test
    public void customerAddOpportunity() {
        String message = "{\"id\":3082464569673796,\"entityType\":11010000300001,\"ownerId\":2520687701264453,\"opportunityName\":\"客资分配1920标准月子商机\",\"customerId\":3082422074591259,\"priceId\":2668573236381732,\"opportunityType\":1,\"money\":100000,\"saleStageId\":2520686580287529,\"winRate\":10,\"closeDate\":1703088000000,\"commitmentFlg\":2,\"createdAt\":1702903809072,\"createdBy\":2520687701264453,\"updatedAt\":1702903809103,\"updatedBy\":2520687701264453,\"dimDepart\":2520685979699293,\"lockStatus\":1,\"approvalStatus\":0,\"status\":1,\"opportunityScore\":42,\"territoryHighSeaStatus\":1,\"territoryHighSeaId\":2721291030186086,\"fcastMoney\":10000,\"duplicateFlg\":0,\"customItem173__c\":1702903722692,\"intendedProvince__c\":1,\"intendedCity__c\":4,\"intendedArea__c\":2,\"intendedStore__c\":1,\"intendedBrand__c\":1,\"customerBudget__c\":4}";

        SCRMOpportunityRequest request = JSONUtil.toBean(message, SCRMOpportunityRequest.class);
        scrmBusinessOpportunityService.customerAddOpportunity(request);
    }

    @Test
    public void convertScrmCustomerInfo() {
        long start = System.currentTimeMillis();
        System.out.println("开始时间:" + start);
        ScrmCustomerPO scrmCustomerPO = scrmCustomerService.getByScrmCustomerId(3370841460443178L);
        long getUser = System.currentTimeMillis();
        System.out.println("获取用户时间:" + getUser + ", 耗时:" + (getUser - start));
        if (scrmCustomerPO != null) {
            System.out.println("");
            AccountInfoRequest accountInfoRequest = scrmConvert.scrmCustomerPO2AccountInfoRequest(scrmCustomerPO);
            long convert1 = System.currentTimeMillis();
            System.out.println("PO转为request时间:" + convert1 + ", 耗时:" + (convert1 - getUser));
            accountInfoRequest.setId(ObjectUtil.isNotEmpty(accountInfoRequest.getId()) && accountInfoRequest.getId() == 0L ? null : accountInfoRequest.getId());
            AccountInfoRequest request = xsyScrmService.converAccountPicpRequestToScrmRequest(accountInfoRequest);
            long convert2 = System.currentTimeMillis();
            System.out.println("request转为scrm时间:" + convert2 + ", 耗时:" + (convert2 - convert1));
            System.out.println("总耗时:" + (convert2 - start));
        }
    }
}
