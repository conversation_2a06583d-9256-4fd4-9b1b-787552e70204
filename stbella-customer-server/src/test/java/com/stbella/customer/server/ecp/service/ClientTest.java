package com.stbella.customer.server.ecp.service;

import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.ecp.entity.TabClientBailorPO;
import com.stbella.customer.server.ecp.request.ClientSearchByIdRequest;
import com.stbella.customer.server.ecp.request.ClientSearchByKeywordRequest;
import com.stbella.customer.server.ecp.request.SaveTabClientBailorRequest;
import com.stbella.customer.server.ecp.request.SaveTabClientForOmniRequest;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.customer.server.ecp.vo.ClientWithStoreInfoListVO;
import com.stbella.customer.server.scrm.vo.ScrmCustomUserStoreVO;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@SpringBootTest(classes = {CustomerLauncher.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("dev")
@TestPropertySource(properties = {"spring.profiles.active=dev"})
@Slf4j
public class ClientTest {

    @Resource
    private TabClientService tabClientService;

    @Resource
    private TabClientBailorService tabClientBailorService;

    @Resource
    private HeInviteRelationService heInviteRelationService;


    @Test
    public void searchClientInfoByKeyword() {
        Operator operator = new Operator();
        operator.setOperatorGuid(Integer.toString(720));
        operator.setOperatorName("周鹏程");
        operator.setOperatorPhone("18321771613");

        ClientSearchByKeywordRequest request = new ClientSearchByKeywordRequest();
        request.setRequestId(Long.toString(new DateTime().getTime()));
        request.setOperator(operator);
        request.setKeyword("18321771613");
        request.setType(0);
        request.setPageNum(1);
        request.setPageSize(10);

        Result<PageVO<ClientWithStoreInfoListVO>> pageVOResult = tabClientService
                .searchClientInfoByKeyword(request);
        log.info("成功:{}", pageVOResult);
    }

    @Test
    public void queryClientInfoById() {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(7796);

        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoById(request);
        log.info("成功:{}", clientInfoVOResult);
    }

    /**
     * 新增委托人
     */
    @Test
    public void createClientBailor() {
        SaveTabClientBailorRequest request = new SaveTabClientBailorRequest();
        request.setClientUid(7796);
        request.setName("王大帅");
        request.setPhoneType(0);
        request.setPhone("18321771612");
        request.setCertType(0);
        request.setIdCard("******************");
        request.setIdCardFront("https://cos.primecare.top/static/helper/72f69354db01ef36a95d58bb70fc0fb2.png");
        request.setIdCardBack("https://cos.primecare.top/static/helper/72f69354db01ef36a95d58bb70fc0fb2.png");

        Result<Integer> response = tabClientBailorService.createBailor(request);
        log.info("成功:{}", JSONUtil.toJsonStr(response));
    }

    /**
     * 查询委托人
     */
    @Test
    public void queryClientBailor() {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(7796);

        Result<TabClientBailorPO> response = tabClientBailorService
                .queryBailorInfoByClientId(request);
        log.info("成功:{}", JSONUtil.toJsonStr(response));
    }

    @Test
    public void removeAllEmojis() {
        String str = "给哦抑郁症地哦哟上午地哦哟上午😅😅😂💇🏻‍♀️理发";
        String s = removeAllEmojis(str);
        System.out.println("s = " + s);
    }

    public static String removeAllEmojis(String input) {
        if (input == null) {
            return null;
        }
        // 所有Emoji字符范围
        String regex = "[\\u20a0-\\u32ff\\ud83c\\udc00-\\ud83d\\udfff\\udbb8\\udc00-\\udbbf\\udfff]";
        return input.replaceAll(regex, "");
    }

    @Test
    public void name() {
        List<ScrmCustomUserStoreVO> scrmCustomUserStoreVOS = null;
        if (scrmCustomUserStoreVOS.stream().findFirst().isPresent()) {
            System.out.println("111");
        }
    }

    @Test
    public void queryAllUserInviteBasicId() {
        List<Long> longs = heInviteRelationService.queryAllUserInviteBasicId();
        log.info(longs.toString());
    }

    @Test
    public void test1321() {
        List<Long> longs = tabClientService.queryTabClientBasicIdByFromTypeOrSellerId(null, 19);
        log.info(longs.toString());
        List<Long> longs1 = tabClientService.queryTabClientBasicIdByFromTypeOrSellerId("周鹏程",
            null);
        log.info(longs1.toString());
        List<Long> longs2 = tabClientService.queryTabClientBasicIdByFromTypeOrSellerId("周鹏程",
            19);
        log.info(longs2.toString());
    }

    @Test
    public void updateClientInfoForOmni() {
        String requestStr = "{\"basicUid\":721639,\"phoneType\":0,\"certType\":0,\"address\":\"测试数据\",\"clientUid\":26825,\"city\":110100,\"idCard\":\"320682199601214075\",\"isCardVerify\":1,\"idCardFront\":\"https://cos.primecare.top/static/helper/53465d4ea5c9b5e3923540f0ace61228.png\",\"relationWithClient\":1,\"province\":110000,\"phone\":\"18362112378\",\"name\":\"邹君\",\"urgentName\":\"邹君\",\"authType\":0,\"region\":110102,\"urgentPhone\":\"18362112378\",\"isPhoneVerify\":1,\"idCardBack\":\"https://cos.primecare.top/static/helper/5f1d073c8859ab383d175fcc4cedf5f8.png\",\"email\":\"\"}";
        SaveTabClientForOmniRequest request = JSONUtil.toBean(requestStr, SaveTabClientForOmniRequest.class);

        Result<Boolean> result = tabClientService.updateClientInfoForOmni(request);
        log.info("更新结果:{}", result);
    }

}
