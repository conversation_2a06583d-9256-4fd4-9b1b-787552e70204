package com.stbella.customer.server.nursingboard.service;

import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.nursingboard.request.BabyDetailDataRequest;
import com.stbella.customer.server.nursingboard.request.DailySummaryDetailDataRequest;
import com.stbella.customer.server.nursingboard.request.MomCheckRoomDetailRequest;
import com.stbella.customer.server.nursingboard.vo.BabyCheckRoomDataVO;
import com.stbella.customer.server.nursingboard.vo.DailySummaryDataVO;
import com.stbella.customer.server.nursingboard.vo.MomCheckRoomDataVO;
import com.stbella.customer.server.nursingboard.vo.NurseBoardBabyDataVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@SpringBootTest(classes = {CustomerLauncher.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class NursingBoardTest {


    @Resource
    NursingBoardService nursingBoardService;


    /**
     * 测试-查询医生建议
     */
    @Test
    public void queryDoctorAdvice() {
        DailySummaryDetailDataRequest request = new DailySummaryDetailDataRequest();
        request.setDay(new Date(Long.parseLong("1685980800000")));
        request.setOrderNo("HEM742648674536416");
       // String orderNo = "HEM742648674536416";

        DailySummaryDataVO dailySummaryDataVO = nursingBoardService.queryDoctorAdvice(request);
        log.info("成功{}", dailySummaryDataVO);
    }

    /**
     * 测试-宝宝房态信息 整合到妈妈房态中
     */
/*    @Test
    public void queryBabyCheckRoomInfo() {
        DailySummaryDetailDataRequest request = new DailySummaryDetailDataRequest();
        request.setDay(new Date(Long.parseLong("1685980800000")));
        request.setOrderNo("HEM742648674536416");
        //String orderNo = "HEM742648674536416";
        List<BabyCheckRoomDataVO> babyCheckRoomDataVOList = nursingBoardService.queryBabyCheckRoomInfo(request);
        log.info("成功{}", babyCheckRoomDataVOList);
    }*/

    /**
     * 测试-查询妈妈房态信息
     */
    @Test
    public void queryMomCheckRoomInfo() {
        MomCheckRoomDetailRequest request = new MomCheckRoomDetailRequest();
        request.setStoreType(1);
        request.setOrderNo("HEM742648674536416");
        // String orderNo = "HEM742648674536416";
        MomCheckRoomDataVO momCheckRoomDataVO = nursingBoardService.queryMomCheckRoomInfo(request);
        log.info("成功{}", momCheckRoomDataVO);
    }

    /**
     * 测试-查询宝宝护理信息
     */
    @Test
    public void queryNurseBoardBabyInfo() {
        BabyDetailDataRequest request = new BabyDetailDataRequest();
        request.setDay(new Date(Long.parseLong("1685980800000")));
        request.setOrderNo("HEM742648674536416");
        request.setBabyId(Long.parseLong("6576"));
        request.setCustomerId(Long.parseLong("18182"));
        NurseBoardBabyDataVO nurseBoardBabyDataVO = nursingBoardService.queryNurseBoardBabyInfo(request);
        log.info("成功{}", nurseBoardBabyDataVO);
    }


}
