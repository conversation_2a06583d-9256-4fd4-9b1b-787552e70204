package com.stbella.customer.server.tracking.service;

import com.alibaba.excel.EasyExcel;
import com.stbella.core.base.PageVO;
import com.stbella.customer.server.CustomerLauncher;
import com.stbella.customer.server.tracking.request.*;
import com.stbella.customer.server.tracking.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = {CustomerLauncher.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class TrackingClientTest {

    @Resource
    private CustomerDataTrackingEventService customerDataTrackingEventService;

    @Resource
    private CustomerDataTrackingVisitService customerDataTrackingVisitService;

    @Resource
    private CustomerDataTrackingSessionService customerDataTrackingSessionService;

    @Resource
    private CustomerDataTrackingApiService customerDataTrackingApiService;

    @Resource
    private CustomerDataTrackingSourceConfigService customerDataTrackingSourceConfigService;




    @Test
    public void userStatVoList(){
        DataStatDateRequest request = new DataStatDateRequest();
        //https://jpicp-test.primecare.top/customer/tracking/userStatVOList?dateStart=1688140800000&dateEnd=1688610819038&pageNum=1&pageSize=10
        Date dateStart = new Date(Long.parseLong("1687017600000"));
        Date dateEnd = new Date(Long.parseLong("1689695999999"));
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);

        request.setPageSize(10);
        request.setPageNum(1);
        PageVO<UserStatVO> userStatVOPageVO = customerDataTrackingSessionService.userStatVO(
                request);
        log.info("成功:{}", userStatVOPageVO);
    }

    @Test
    public void userStatDetailVoList(){
        UserStatDetailRequest request  = new UserStatDetailRequest();
        //https://jpicp-test.primecare.top/customer/tracking/userStatVOList?dateStart=1688140800000&dateEnd=1688610819038&pageNum=1&pageSize=10
        Date dateStart = new Date(Long.parseLong("1688951246000"));
        Date dateEnd = new Date(Long.parseLong("1689217126000"));
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);
        request.setNameOrPhone("陈大帅");
       // request.setType(2);
        request.setPageSize(10);
        request.setPageNum(1);
        PageVO<UserStatDetailVO> userStatVOPageVO = customerDataTrackingSessionService.userStatDetailVO(
                request);
        log.info("成功:{}", userStatVOPageVO);
    }

    @Test
    public void sessionStatList(){
        SessionStatRequest request = new SessionStatRequest();
        Date dateStart = new Date(Long.parseLong("1686672000000"));
        Date dateEnd = new Date(Long.parseLong("1689350399999"));
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);
        request.setNameOrPhone("孟晓敏");
        request.setPageSize(10);
        request.setPageNum(1);

        PageVO<SessionStatVO> sessionStatVO = customerDataTrackingSessionService.sessionStatVO(
                request);
        log.info("成功:{}", sessionStatVO);
    }

    @Test
    public void sessionStatDetailList(){
        SessionDetailRequest request = new SessionDetailRequest();

        request.setSessionId("oq9TN4sg9VqdVB1TS5TmhH9Z3tDo_1689326540427");
        request.setPageSize(10);
        request.setPageNum(1);

        PageVO<SessionDetailVO> sessionStatVO = customerDataTrackingSessionService.sessionDetailVO(
                request);
        log.info("成功:{}", sessionStatVO);
    }


    @Test
    public void visitTrendList(){
        VisitTrendRequest request = new VisitTrendRequest();

        Date dateStart = new Date(Long.parseLong("1683734400000"));
        Date dateEnd = new Date(Long.parseLong("1683907200000"));
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);
        request.setPageName("首页");
        request.setPagePath("pages/home/<USER>");


        VisitTrendVO sessionStatVO = customerDataTrackingVisitService.visitTrend(
                request);


        log.info("成功:{}", sessionStatVO);



    }

    @Test
    public void visitStatList(){
        VisitStatRequest request = new VisitStatRequest();

        Date dateStart = new Date(Long.parseLong("1683734400000"));
        Date dateEnd = new Date(Long.parseLong("1683907200000"));
       // request.setDateStart(dateStart);
       // request.setDateEnd(dateEnd);
        request.setPageName("首页");
        request.setPageNum(1);
        request.setPageSize(10);


        PageVO<VisitStatVO> visitStatList = customerDataTrackingVisitService.visitStatList(
                request);


        log.info("成功:{}", visitStatList);



    }


    @Test
    public void activeUserStat(){
        DataStatOverviewRequest request =new DataStatOverviewRequest();
        request.setDate("2023-5");
        request.setType(1);
        BaseCalculateNumVO baseCalculateNumVO = customerDataTrackingSessionService.activeUserStat(request);
        log.info("成功:{}", baseCalculateNumVO);
    }

    @Test
    public void newUserStat(){
        DataStatOverviewRequest request =new DataStatOverviewRequest();
        request.setDate("2023");
        request.setType(0);
        BaseCalculateNumVO baseCalculateNumVO = customerDataTrackingSessionService.newUserStat(request);
        log.info("成功:{}", baseCalculateNumVO);
    }

    @Test
    public void newUserPStat(){
        DataStatOverviewRequest request =new DataStatOverviewRequest();
        request.setDate("2023");
        request.setType(0);
        BaseCalculateDoubleNumVO baseCalculateNumVO = customerDataTrackingSessionService.newUserProportionStat(request);
        log.info("成功:{}", baseCalculateNumVO);
    }

    @Test
    public void visitTimeStat(){
        DataStatOverviewRequest request =new DataStatOverviewRequest();
        request.setDate("2023");
        request.setType(0);
        BaseCalculateDoubleNumVO baseCalculateNumVO = customerDataTrackingSessionService.userVisitTimeStat(request);
        log.info("成功:{}", baseCalculateNumVO);
    }



    @Test
    public void queryPlatformStatUserOverview(){
        //平台统计 - 查用户数据概况
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));

        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);


        PlatformStatUserOverviewVO platformStatUserOverviewVO = customerDataTrackingSessionService.queryPlatformStatUserOverview(baseDateRequest);

        log.info("成功:{}", platformStatUserOverviewVO);

    }

    @Test
    public void queryActiveUserByDate(){
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));

        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        BaseCalculateNumVO baseCalculateNumVO = customerDataTrackingSessionService.queryActiveUserByDate(baseDateRequest);

        log.info("成功:{}", baseCalculateNumVO);

    }

    /**
     * 平台统计-30日 平均单日使用时长
     */
    @Test
    public void queryArgDurationTimeByDate(){
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));

        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        BaseCalculateDoubleNumVO baseCalculateNumVO = customerDataTrackingSessionService.queryArgDurationTimeByDate(baseDateRequest);

        log.info("成功:{}", baseCalculateNumVO);

    }


    @Test
    public void queryPageViewByDate(){
        //平台统计-30日 启动次数趋势
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));

        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        BaseCalculateNumVO baseCalculateNumVO = customerDataTrackingSessionService.queryPageViewByDate(baseDateRequest);

        log.info("成功:{}", baseCalculateNumVO);

    }


    @Test
    public void queryOneSessionArgDurationTimeByDate(){
        //平台统计-30日 平均单次使用时长
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));
        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        BaseCalculateNumVO baseCalculateNumVO = customerDataTrackingSessionService.queryOneSessionArgDurationTimeByDate(baseDateRequest);

        log.info("成功:{}", baseCalculateNumVO);
    }


    @Test
    public void queryNewUserProportionByDate(){
        //平台统计-30日 新用户占比趋势
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));
        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        BaseCalculateDoubleNumVO baseCalculateDoubleNumVO = customerDataTrackingSessionService.queryNewUserProportionByDate(baseDateRequest);

        log.info("成功:{}", baseCalculateDoubleNumVO);
    }


    @Test
    public void queryActiveOldOrNewUserByDate(){
        //平台统计-30日 新老活跃用户趋势
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1688720400000"));
        BaseDateRequest baseDateRequest = new BaseDateRequest();
        baseDateRequest.setDateStart(dateStart);
        baseDateRequest.setDateEnd(dateEnd);

        List<PlatformStatActiveOldOrNewUserVo> platformStatActiveOldOrNewUserVoList = customerDataTrackingSessionService.queryActiveOldOrNewUserByDate(baseDateRequest);

        log.info("成功:{}", platformStatActiveOldOrNewUserVoList.toString());

    }

    @Test
    public void visitSourceStat(){
        //11
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1689004800000"));

        DataStatOverviewRequest request  = new  DataStatOverviewRequest();
        request.setType(1);
        request.setDate("2023-07");
        Map<String, Object> sourceStatUserMap = customerDataTrackingSessionService.visitSourceStat(
                request);

        log.info("成功:{}", sourceStatUserMap);
    }

    /**
     * 事件详情
     */
    @Test
    public void eventDetailList(){

        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1689004800000"));

        EventDetailRequest request  = new EventDetailRequest();
        request.setEventId("store_detail_change_tab");
        request.setPageNum(1);
        request.setPageSize(10);
        request.setDateEnd(dateEnd);
        request.setDateStart(dateStart);
        PageVO<EventDetailVO> eventDetailVOPageVO = customerDataTrackingEventService.eventDetailList(
                request);

        log.info("成功:{}", eventDetailVOPageVO);
    }


    /**
     * 渠道概览-访问人数  11
     */
    @Test
    public void totalUserStat(){

        Date dateStart = new Date(Long.parseLong("1681005926000"));
        Date dateEnd = new Date(Long.parseLong("1689226250000"));

        DataStatDateRequest request  = new   DataStatDateRequest();
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);
        Map<String,Object> sourceStatUserMap = customerDataTrackingSourceConfigService.totalUserStat(
                request);

        log.info("成功:{}", sourceStatUserMap);
    }

    /**
     * 渠道概览-新增用户
     */
    @Test
    public void newUserSourceStat(){

        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1689148064000"));

        DataStatDateRequest request  = new   DataStatDateRequest();
        request.setDateStart(dateStart);
        request.setDateEnd(dateEnd);
        Map<String,Object> sourceStatUserMap = customerDataTrackingSourceConfigService.newUserStat(
                request);
        log.info("成功:{}", sourceStatUserMap);
    }





    @Test
    public void sourceStatList(){
        Date dateStart = new Date(Long.parseLong("1688140800000"));
        Date dateEnd = new Date(Long.parseLong("1689004800000"));
        SourceStatDetailRequest request = new SourceStatDetailRequest();
       // request.setDateStart(dateStart);
     //   request.setDateEnd(dateEnd);
        request.setPageNum(1);
        request.setPageSize(10);
        PageVO<SourceStatDetailVO> sourceStatDetailVOPageVO = customerDataTrackingSourceConfigService.sourceStatList(
                request);
        log.info("成功:{}", sourceStatDetailVOPageVO);
    }

    @Test
    public void report(){
        CustomerDataTrackingSessionRequest request = new CustomerDataTrackingSessionRequest();
        request.setSessionid("oq9TN4irDHm1IXYw6SI8kPO2mpN8_1686554488826");


        Boolean result =  customerDataTrackingSessionService.report(request);
        log.info("成功:{}", result);

    }




    @Test
    public void export() throws IOException {

        VisitStatRequest visitStatRequest  = new VisitStatRequest();
      //  visitStatRequest.setPageName();
        visitStatRequest.setPageSize(10);
        visitStatRequest.setPageNum(1);
        visitStatRequest.setDateStart(new Date(Long.parseLong("1686585600000")));
        visitStatRequest.setDateEnd(new Date(Long.parseLong("1689263999999")));

        String sheetName = "22222.xlsx";
    //    HttpServletResponse response = new HttpServletResponseWrapper();


        VisitStatVO v1 = new VisitStatVO();
        v1.setPageName("1111");
        v1.setPagePath("111122222");
        v1.setUserView(0L);
        v1.setPageView(0L);
        v1.setDurationTime(BigDecimal.valueOf(0.0));
        v1.setAvgDurationTime(BigDecimal.valueOf(0.0));

        VisitStatVO v2 = new VisitStatVO();
        v2.setPageName("2222");
        v2.setPagePath("22211111");
        v2.setUserView(0L);
        v2.setPageView(0L);
        v2.setDurationTime(BigDecimal.valueOf(0.0));
        v2.setAvgDurationTime(BigDecimal.valueOf(0.0));

        List<VisitStatVO> statVOList = new ArrayList<>();
        statVOList.add(v1);
        statVOList.add(v2);

        EasyExcel.write(sheetName, VisitStatVO.class).sheet("模板").doWrite(statVOList);

    //    this.customerDataTrackingApiService.excelExport(sheetName,response,statVOList);

    }









   /* @Test
    public void searchClientInfoByKeyword() {
        Operator operator = new Operator();
        operator.setOperatorGuid(Integer.toString(720));
        operator.setOperatorName("周鹏程");
        operator.setOperatorPhone("18321771613");

        ClientSearchByKeywordRequest request = new ClientSearchByKeywordRequest();
        request.setRequestId(Long.toString(new DateTime().getTime()));
        request.setOperator(operator);
        request.setKeyword("18321771613");
        request.setType(0);
        request.setPageNum(1);
        request.setPageSize(10);

        Result<PageVO<ClientWithStoreInfoListVO>> pageVOResult = tabClientService
                .searchClientInfoByKeyword(request);
        log.info("成功:{}", pageVOResult);
    }

    @Test
    public void queryClientInfoById() {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(7796);

        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoById(request);
        log.info("成功:{}", clientInfoVOResult);
    }

    *//**
     * 新增委托人
     *//*
    @Test
    public void createClientBailor() {
        SaveTabClientBailorRequest request = new SaveTabClientBailorRequest();
        request.setClientUid(7796);
        request.setName("王大帅");
        request.setPhoneType(0);
        request.setPhone("18321771612");
        request.setCertType(0);
        request.setIdCard("******************");
        request.setIdCardFront("https://cos.primecare.top/static/helper/72f69354db01ef36a95d58bb70fc0fb2.png");
        request.setIdCardBack("https://cos.primecare.top/static/helper/72f69354db01ef36a95d58bb70fc0fb2.png");

        Result<Integer> response = tabClientBailorService.createBailor(request);
        log.info("成功:{}", JSONUtil.toJsonStr(response));
    }

    *//**
     * 查询委托人
     *//*
    @Test
    public void queryClientBailor() {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(7796);

        Result<TabClientBailorPO> response = tabClientBailorService
                .queryBailorInfoByClientId(request);
        log.info("成功:{}", JSONUtil.toJsonStr(response));
    }

    @Test
    public void removeAllEmojis() {
        String str = "给哦抑郁症地哦哟上午地哦哟上午😅😅😂💇🏻‍♀️理发";
        String s = removeAllEmojis(str);
        System.out.println("s = " + s);
    }

    public static String removeAllEmojis(String input) {
        if (input == null) {
            return null;
        }
        // 所有Emoji字符范围
        String regex = "[\\u20a0-\\u32ff\\ud83c\\udc00-\\ud83d\\udfff\\udbb8\\udc00-\\udbbf\\udfff]";
        return input.replaceAll(regex, "");
    }

    @Test
    public void name() {
        List<ScrmCustomUserStoreVO> scrmCustomUserStoreVOS =null;
        if (scrmCustomUserStoreVOS.stream().findFirst().isPresent()) {
            System.out.println("111");
        }
    }*/
}
