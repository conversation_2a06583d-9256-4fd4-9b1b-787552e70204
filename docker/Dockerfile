FROM reg.primecare.top/base/alpine-java:1.3
COPY stbella-customer-server-1.0-SNAPSHOT.jar /home/<USER>/stbella-customer-server-1.0-SNAPSHOT.jar
CMD ["java","-Xms1g", "-Xmx1g", "-Xmn500m", "-Xss1m", "-XX:SurvivorRatio=8", "-XX:MaxTenuringThreshold=10", "-XX:+UseConcMarkSweepGC", "-XX:CMSInitiatingOccupancyFraction=70", "-XX:+UseCMSInitiatingOccupancyOnly", "-XX:+AlwaysPreTouch", "-XX:+HeapDumpOnOutOfMemoryError", "-verbose:gc", "-XX:+PrintGCDetails", "-XX:+PrintGCDateStamps", "-XX:+PrintGCTimeStamps", "-Xloggc:/home/<USER>/logs/gc.log","-javaagent:/home/<USER>/pinpoint/pinpoint-bootstrap-2.4.2.jar","-Dpinpoint.applicationName=stbella-customer-prod","-Dpinpoint.agentId=stbella-customer-prod","-Djava.security.egd=file:/dev/./urandom","-jar","/home/<USER>/stbella-customer-server-1.0-SNAPSHOT.jar"]
