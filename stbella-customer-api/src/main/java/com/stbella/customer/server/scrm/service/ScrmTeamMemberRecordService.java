package com.stbella.customer.server.scrm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.customer.server.customer.vo.ClientSearchMemberVO;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.request.ScrmTeamMemberRecordRequest;

import java.util.List;

/**
 * <p>
 * scrm各实体的团队成员记录 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-09-05
 */
public interface ScrmTeamMemberRecordService extends IService<ScrmTeamMemberRecordPO> {

    /**
     * 创建或更新scrm团队成员关系
     *
     * @param request
     */
    ScrmTeamMemberRecordPO edit(ScrmTeamMemberRecordRequest request);

    /**
     * 删除scrm团队成员关系
     *
     * @param request
     */
    void delete(ScrmTeamMemberRecordRequest request);

    Page<ClientSearchMemberVO> searchPageByKeyword(Page page, Long scrmId, String keyword);

    List<ClientSearchMemberVO> searchByKeyword(Long scrmId, String keyword);

    /**
     * 将销售拉到客户的团队成员中
     *
     * @param customerId
     * @param saleId
     * @return
     */
    ScrmTeamMemberRecordPO addCustomerTeamMember(Long customerId, Long saleId);

    List<ScrmTeamMemberRecordPO> queryTeamMemberRecordList(Long recordId, Integer type);
}
