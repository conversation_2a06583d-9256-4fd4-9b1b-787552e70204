package com.stbella.customer.server.scrm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.core.result.Result;
import com.stbella.customer.server.cts.request.BatchOpportunityRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunitiesWinOrderRequest;
import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.customer.request.store.ClientCreateRequest;
import com.stbella.customer.server.customer.vo.ClientSearchVO;
import com.stbella.customer.server.scrm.dto.OpportunityDistributionMessage;
import com.stbella.customer.server.scrm.dto.ScrmCheckinUserInfoDTO;
import com.stbella.customer.server.scrm.dto.ScrmDistributionCustomerDTO;
import com.stbella.customer.server.scrm.entity.ScrmBusinessOpportunityPO;
import com.stbella.customer.server.scrm.request.OpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmCrateOpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmCrateWinOpportunityRequest;
import com.stbella.customer.server.scrm.request.ScrmOpportunityActivityRecordRequest;
import com.stbella.customer.server.scrm.request.ServiceOpportunityQuery;
import com.stbella.customer.server.scrm.request.ServiceOpportunityWinRequest;
import com.stbella.customer.server.scrm.vo.StoreUserNumVO;
import java.util.Date;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 商机表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-03-13
 */
public interface ScrmBusinessOpportunityService extends IService<ScrmBusinessOpportunityPO> {

    @Transactional(rollbackFor = Exception.class)
    void updateOpportunity(SCRMOpportunityRequest scrmOpportunityRequest);

    /**
     * 分配销售自动创建商机
     * @param  scrmAutoOpportunityRequestList
     */
    Result distributeSaleScreateOpportunities(List<ScrmDistributionCustomerDTO> scrmAutoOpportunityRequestList);

    /**
     * 商机自动赢单（订单支付金额>=50%）
     * @param scrmOpportunitiesWinOrderRequest
     * @return
     */
    Long opportunitiesAutoWinOrders(SCRMOpportunitiesWinOrderRequest scrmOpportunitiesWinOrderRequest);

    @Transactional(rollbackFor = Exception.class)
    Result updateFollowUpRecord(ScrmOpportunityActivityRecordRequest scrmOpportunityActivityRecordRequest);

    /**
     * 查询这个客户目前有哪些正在进行中的商机
     * @param scrmId
     * @return
     */
    List<ScrmBusinessOpportunityPO> listByCustomerId(Long scrmId);

    /**
     * 根据客户id和销售id列表查询商机
     * @param customerId
     * @param saleIdList
     */
    List<ScrmBusinessOpportunityPO> listByCustomerIdAndSaleIds(Long customerId, List<Long> saleIdList);

    List<ScrmBusinessOpportunityPO> listByCustomerIdAndStoreConfigIds(Long customerId, List<Long> storeConfigId);

    void add(SCRMOpportunityRequest scrmOpportunityRequest);

    void createOpportunity(ScrmCrateOpportunityRequest scrmCrateOpportunityRequest,ScrmCrateWinOpportunityRequest scrmCrateWinOpportunityRequest);

    void createWinOpportunity(ScrmCrateWinOpportunityRequest scrmCrateWinOpportunityRequest);

    /**
     * 删除商机
     * @param scrmOpportunityRequest
     */
    void del(SCRMOpportunityRequest scrmOpportunityRequest);

    /**
     * 批量插入商机
     * @param scrmOpportunityRequests
     */
    @Transactional(rollbackFor = Exception.class)
    void batchInsertOpportunity(List<BatchOpportunityRequest> scrmOpportunityRequests);

    void saveOpportunityStatus(
            Long scrmOpportunityId,
            String scrmOpportunityName,
            Long saleStageId,
            Long contractedStore,
            Long ownerId,
            Long dimDepart,
            Long entityType,
            Integer lockStatus,
            Date realFinishTime
    );

    /**
     * 判断目前所有的商机阶段是否完整以及新增字段是否完成
     */
    void updateAllOpportunityIntermediateTable();

    /**
     * 根据scrm商机id新增或更新商机
     * @param scrmId
     */
    void updateOpportunityByScrmId(Long scrmId);

    /**
     * 通知所有门店到店数
     */
    void reminderCheckinStoreUsers();

    /**
     * 400角色新增商机
     *
     * @param scrmOpportunityRequest
     */
    void customerAddOpportunity(SCRMOpportunityRequest scrmOpportunityRequest);

    /**
     * 根据时间戳查询scrm中门店分配的客户数量
     *
     * @param dateStartTime
     * @param dateEndTime
     * @return
     */
    List<StoreUserNumVO> queryScrmUserNum(Long dateStartTime, Long dateEndTime);

    /**
     * 查询用户指定订单类型的商机列表
     * 如果订单类型为空，则默认获取该用户所有的商机
     *
     * @param customerId
     * @param orderType 订单类型，参考(com.stbella.order.common.enums.core.OmniOrderTypeEnum)
     * @return
     */
    List<ScrmBusinessOpportunityPO> queryCustomerOpportunityList(Long customerId, Integer orderType);

    /**
     * 订单类型转为商机业务类型
     *
     * @param orderType
     * @return
     */
    Long orderType2OpportunityEntityType(Integer orderType);

    /**
     * 获取scrm商机类型对应进行中的状态id列表
     *
     * @param entityType scrm商机类型
     * @return
     */
    List<Long> getOpportunityInProcessStatus(Long entityType);

    /**
     * 通过订单类型获取scrm商机类型对应进行中的状态id列表
     *
     * @param orderType 订单类型，参考(com.stbella.order.common.enums.core.OmniOrderTypeEnum)
     * @return
     */
    List<Long> getOpportunityInProcessStatusByOrderType(Integer orderType);

    /**
     * 根据商机业务类型获取商机状态对应的商机阶段
     *
     * @param entityType
     * @param opportunityStatus
     * @return
     */
    Long opportunityStatus2StageIdByEntityType(Long entityType, Integer opportunityStatus);

    /**
     * 根据订单类型获取商机状态对应的商机阶段
     *
     * @param orderType
     * @param opportunityStatus
     * @return
     */
    Long opportunityStatus2StageIdByOrderType(Integer orderType, Integer opportunityStatus);

    /**
     * 在scrm中新增商机
     *
     * @param request
     * @param distributedUserId 分配人id
     * @return
     */
    ScrmBusinessOpportunityPO createScrmOpportunity(OpportunityRequest request, Long distributedUserId);


    /**
     * 处理更新商机的操作,并同步到scrm
     *
     * @param opportunityPO 商机po
     * @param stageTime 阶段到达时间
     * @return
     */
    Boolean updateOpportunity(ScrmBusinessOpportunityPO opportunityPO, Date stageTime);

    /**
     * 转移商机
     *
     * @param opportunityPO
     * @param newOwnerId
     * @return
     */
    ScrmBusinessOpportunityPO transformScrmOpportunity(ScrmBusinessOpportunityPO opportunityPO, Long newOwnerId);

    /**
     * 获取scrm中指定时间段内的打卡人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    List<ScrmCheckinUserInfoDTO> queryCheckinUsersByDate(Date dateStart, Date dateEnd);

    /**
     * 获取scrm中指定时间段内的分配人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    List<ScrmCheckinUserInfoDTO> queryDistributeUsersByDate(Date dateStart, Date dateEnd);

    /**
     * 获取scrm中指定时间段内的首签人数
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    List<ScrmCheckinUserInfoDTO> queryFirstSignUsersByDate(Date dateStart, Date dateEnd);

    /**
     * 判断该门店是否有商机
     *
     * @param storeId
     * @return
     */
    Boolean existOpportunityByStoreId(Long storeId);

    /**
     * 获取绑定订单的对应商机
     *
     * @param scrmOrderId
     * @return
     */
    ScrmBusinessOpportunityPO queryOpportunityByScrmOrderId(Long scrmOrderId);

    ClientSearchVO createScrmUser(ClientCreateRequest request);

    boolean checkOpportunityStatusForUser(Long basicUid);

    /**
     * scrm分配团队成员成功后发送企微消息通知
     *
     * @param message
     */
    void scrmDistributionCustomerSuccessMessageNotify(OpportunityDistributionMessage message);

    void opportunityFor400Update(SCRMOpportunityRequest request);

    ScrmBusinessOpportunityPO queryOpportunityByScrmId(Long scrmId);

    /**
     * 商机团队成员处理
     *
     * @param dtoList
     */
    void opportunityTeamMemberHandle(List<ScrmDistributionCustomerDTO> dtoList);

    /**
     * 400商机查询
     *
     * @param request
     * @return
     */
    List<ScrmBusinessOpportunityPO> queryServiceOpportunityList(ServiceOpportunityQuery request);

    /**
     * 400商机赢单处理
     *
     * @param request
     */
    void serviceOpportunityWinHandle(ServiceOpportunityWinRequest request);
}
