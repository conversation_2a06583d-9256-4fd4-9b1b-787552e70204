package com.stbella.customer.server.ecp.service.dubbo;

import com.stbella.core.result.Result;

/**
 * 首签首访判断服务
 * 判断用户是否满足首签首访的顾客条件
 *
 * <AUTHOR>
 */
public interface FirstCheckInContractService {

    /**
     * 根据basicUid查询该用户是否满足首签首访的顾客条件
     * <p>
     * 前置条件：用户没有下过任何订单（有订单直接返回false）
     * <p>
     * 满足以下任一条件即可：
     * 情况1：首次在圣贝拉品牌门店打卡
     * - 用户在圣贝拉品牌门店首次打卡（即之前在圣贝拉品牌门店没有任何打卡记录）
     * - 用户没有下过任何订单
     * <p>
     * 情况2：用户在兄弟品牌门店有打卡记录但从未在圣贝拉打卡
     * - 用户在小贝拉/艾屿品牌门店有打卡记录
     * - 用户在圣贝拉品牌门店从未打卡
     * - 用户没有下过任何订单
     *
     * @param basicUid 全局用户id，不能为null
     * @return true: 满足首签首访条件; false: 不满足条件或发生异常
     */
    Result<Boolean> checkFirstCheckInContract(Integer basicUid);
}