package com.stbella.customer.server.scrm.service;

import com.stbella.customer.server.cts.request.SCRMOpportunityRequest;
import com.stbella.customer.server.scrm.dto.OrderGoodsDTO;
import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.customer.server.scrm.dto.ScrmCreateClockInStoreDTO;
import com.stbella.customer.server.scrm.entity.ScrmTeamMemberRecordPO;
import com.stbella.customer.server.scrm.request.*;
import com.stbella.customer.server.scrm.vo.OpportunityTransferVO;
import com.stbella.customer.server.scrm.vo.OrderSyncResultVO;
import com.stbella.customer.server.scrm.vo.PicpUserInitVO;
import com.stbella.customer.server.scrm.vo.ScrmCustomUserStoreVO;
import com.stbella.customer.server.scrm.vo.ScrmDescriptionResultVO.FieldData.SelectItemData;
import com.stbella.customer.server.scrm.vo.ScrmOpportunityDetailVO;
import com.stbella.customer.server.scrm.vo.ScrmTeamMemberObjectVO;
import com.stbella.customer.server.scrm.vo.ScrmTeamMemberVO;
import com.stbella.customer.server.scrm.vo.ScrmUserInfoVO;

import com.stbella.customer.server.scrm.vo.ScrmUserResponsibilityVO.Records;

import com.stbella.customer.server.scrm.vo.ScrmUserResponsibilyQueryVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售易scrm接口定义
 */
public interface XsyScrmService {

    /**
     * 获取可用的scrm access_token
     *
     * @param flush 是否强制刷新token
     * @return
     */
    String getAccessToken(Boolean flush);

    /**
     * 获取scrm中的员工信息
     *
     * @param id
     * @return
     */
    ScrmUserInfoVO getUserInfo(Long id);

    /**
     * 添加客户到scrm中
     *
     * @param req
     * @return
     */
    Long accountEdit(AccountInfoRequest req);

    /**
     * 新增、编辑销售机会信息
     *
     * @param req
     * @return 返回销售机会在scrm中的id
     */
    Long opportunityEdit(OpportunityRequest req);

    /**
     * 通过销售机会id查询销售机会详情
     *
     * @param id
     * @return
     */
    ScrmOpportunityDetailVO queryOpportunityDetail(Long id);

    /**
     * 新增、编辑订单到scrm
     *
     * @param req
     * @return 返回订单在scrm中的id
     */
    Long orderEdit(OrderInfoRequest req);

    /**
     * 新增、编辑到店打卡记录到scrm
     *
     * @param req
     * @return
     */
    Long checkinStoreEdit(CheckinStoreRequest req);

    /**
     * 新增、编辑宝宝信息到scrm
     *
     * @param req
     * @return
     */
    Long babyInfoEdit(ContactInfoRequest req);

    /**
     * 删除scrm中的宝宝信息
     *
     * @param babyScrmId
     * @return
     */
    Boolean babyDelete(Long babyScrmId);

    /**
     * 查询scrm某个对象中的字段所有的选项列表
     *
     * @param objectKey 对象名
     * @return 选项列表
     */
    Map<String, List<SelectItemData>> getScrmObjectOptionList(String objectKey);

    /**
     * scrm中选项id值转换为picp的枚举值
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName 在scrm中的字段枚举值
     * @return 返回picp中的枚举值
     */
    String convertScrmOptionIdToPicpId(String objectKey, String fieldName, Long keyName);

    /**
     * picp的枚举值转换为scrm中选项id值
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName 在picp中的字段枚举值
     * @return 返回scrm中的枚举值
     */
    Long convertPicpIdToScrmOptionId(String objectKey, String fieldName, String keyName);

    /**
     * scrm中选项id值转换为对应的label
     *
     * @param objectKey scrm中对象值
     * @param fieldName 字段名称
     * @param keyName 在scrm中的字段枚举值
     * @return 返回scrm该选项的label名
     */
    String convertScrmOptionIdToLabel(String objectKey, String fieldName, Long keyName);

    /**
     * 客户模块scrm请求中枚举值转为picp的枚举值
     *
     * @param request
     * @return
     */
    AccountInfoRequest converAccountScrmRequestToPicpRequest(AccountInfoRequest request);

    /**
     * 客户模块picp请求中枚举值转为scrm的枚举值
     *
     * @param request
     * @return
     */
    AccountInfoRequest converAccountPicpRequestToScrmRequest(AccountInfoRequest request);

    /**
     * 商机模块scrm请求中枚举值转为picp的枚举值(SCRMOpportunityRequest)
     *
     * @param request
     * @return
     */
    SCRMOpportunityRequest convertOpportunityScrmRequestToPicpRequest(SCRMOpportunityRequest request);

    /**
     * 商机模块picp请求中枚举值转为scrm的枚举值(SCRMOpportunityRequest)
     *
     * @param request
     * @return
     */
    SCRMOpportunityRequest convertOpportunityPicpRequestToScrmRequest(SCRMOpportunityRequest request);

    /**
     * 商机模块scrm请求中枚举值转为picp的枚举值(OpportunityRequest)
     *
     * @param request
     * @return
     */
    OpportunityRequest convertOpportunityScrmRequestToPicpRequest(OpportunityRequest request);

    /**
     * 商机模块picp请求中枚举值转为scrm的枚举值(OpportunityRequest)
     *
     * @param request
     * @return
     */
    OpportunityRequest convertOpportunityPicpRequestToScrmRequest(OpportunityRequest request);

    /**
     * 删除团队成员中的销售
     *
     * @param id 团队成员id
     * @return 返回订单在scrm中的id
     */
    void delTeamMember(Long id);

    /**
     * 新增团队成员
     * @param request
     */
    void addTeamMember(ScrmAddTeamMemberRequest request);

    /**
     * 获取这个客户下所有的销售
     *
     * @param customerId
     * @return
     */
    List<ScrmTeamMemberVO> getAllTeamMember(Long customerId);


    /**
     * 发送企微消息
     *
     * @param qwMsgRequest
     * @return
     */
    Long batchSendMsg(QwMsgRequest qwMsgRequest);

    /**
     * 自建应用发送企微消息
     *
     * @return
     */
    Boolean localBatchSendMsg(LocalQwMsgRequest localQwMsgRequest);

    /**
     * 获取员工在scrm中的职能列表
     *
     * @param id
     * @return
     */
    List<Records> getUserResponsibilities(Long id);

    /**
     * 通过sql方式查询scrm中的客户信息
     *
     * @param query
     * @return
     */
    AccountInfoRequest queryScrmAccount(ScrmAccountQuery query);

    /**
     * 通过SQL方式查询scrm中的商机列表
     *
     * @param query
     * @return
     */
    List<OpportunityRequest> queryScrmOpportunityList(ScrmOpportunityQuery query);

    /**
     * 扫码信息绑定或解绑商机
     * @param signInInformation 扫码id
     * @param bindOrUnBind      0-解绑；1-绑定
     */
    void signInInformationBindOrUnBindOpportunity(Long signInInformation,Integer bindOrUnBind);

    ScrmCreateClockInStoreDTO getSignInInformation(Long signInInformation);


    /**
     * 删除某个客户的团队成员
     * @param customerId
     * @param ownerId
     */
    void delTeamMember(Long customerId,Long ownerId);


    /**
     * '通过SQL方式查询scrm中的销售与门店关系列表'
     *
     * @param query
     * @return
     */
    List<ScrmCustomUserStoreVO> queryScrmCustomUserStoreList(ScrmCustomUserStoreQuery query);

    /**
     * @param orderInfoRequestList
     * @return {@link List}<{@link OrderSyncResultVO}>
     */
    OrderSyncResultVO  syncBatchOrder(List<OrderInfoRequest> orderInfoRequestList);

    /**
     * 通过自定义接口同步宝宝信息
     *
     * @param req
     * @return
     */
    Long babySync(ContactInfoRequest req);

    /**
     * 商机中间表编辑更新
     *
     * @param req
     * @return
     */
    Long opportunityTempEdit(OpportunityTempRequest req);

    /**
     * 删除商机中间表的数据
     * @param scrmId
     * @return
     */
    Long opportunityTempDelete(Long scrmId);

    /**
     * 批量修改活动记录的ownerId
     * @param opportunityId
     * @param ownerId
     */
    void batchUpdateActivityOwnerId(Long opportunityId, Long ownerId);

    /**
     * 回收商机
     * @param opportunityId
     */
    void reclaimBusinessOpportunity(Long opportunityId);

    /**
     * 400分配客资中间表编辑更新
     *
     * @param req
     * @return
     */
    Long userAssignAccountTempEdit(ScrmAssignAccountTempRequest req);

    /**
     * 查询scrm中团队成员的对象信息
     *
     * @param type 对象id，比如客户、联系人等，在ScrmConfigBizTypeEnum中可以找到
     * @param userId 用户id
     * @param recordId 记录id，可以是客户id、联系人id、商机id等
     * @return
     */
    ScrmTeamMemberObjectVO queryTeamMemberInfo(Long type, Long userId, Long recordId);

    /**
     * 通过销售与门店关系中间表的id查询门店品牌记录id
     *
     * @param saleStoreRecordId
     * @return
     */
    Long queryStoreBrandRecordIdBySaleStoreId(Long saleStoreRecordId);

    /**
     * 保存退单记录到scrm中
     *
     * @param req
     * @return
     */
    Long orderRefundEdit(ScrmOrderRefundRequest req);

    /**
     * 通过员工在scrm中的id列表，查询这一批次的用户角色和部门
     *
     * @param ids
     * @return
     */
    List<ScrmUserResponsibilyQueryVO> queryUserSaleRoleByIds(List<Long> ids);

    /**
     * 初始化客户的商机及订单
     *
     * @param req
     * @return
     */
    PicpUserInitVO picpInitOpportunity(PicpUserInitRequest req);

    /**
     * 满意度调查编辑
     *
     * @param req
     * @return
     */
    Long satisfactionInvestigateEdit(ScrmSatisfactionInvestigateRequest req);

    /**
     * 通过id查询满意度调查信息
     *
     * @param id
     * @return
     */
    ScrmSatisfactionInvestigateRequest querySatisfactionInvestigateById(Long id);

    /**
     * 修改scrm客户的会员等级
     *
     * @param scrmId
     * @param growthLevelId
     * @param brandType
     */
    void updateCustomerGrowthLevel(Long scrmId, Long growthLevelId, Integer brandType);


    /**
     * 修改scrm客户的用户名和性别
     *
     * @param scrmId   scrm id
     * @param userName user name
     * @param gender   gender
     * <AUTHOR>
     * @date 2023/10/10 10:41:33
     * @since 1.0.0
     */
    void updateCustomerUserNameAndGender(Long scrmId, String userName, Integer gender);

    /**
     * 通过职能查询该职能所有用户信息
     *
     * @param responsibilityKey
     * @return
     */
    List<ScrmUserInfoVO> queryUserInfoListByResponsibilityKey(String responsibilityKey);

    /**
     * 在scrm中创建销售与门店关系记录
     *
     * @param request
     * @return
     */
    Long createScrmUserStoreConfig(ScrmUserStoreConfigOperateRequest request);

    /**
     * 转移商机给新的所有人
     *
     * @param opportunityId
     * @param newOwnerId
     * @return
     */
    OpportunityTransferVO opportunityTransfer(Long opportunityId, Long newOwnerId);

    /**
     * 查询手机号对应的scrm打卡列表
     *
     * @param phone
     * @return
     */
    List<ScrmCheckinStoreRequest> queryScrmCheckinStoreListByPhone(String phone);

    /**
     * 在scrm中新增到店记录回访表
     *
     * @param request
     * @return
     */
    Long addCheckinStoreReturnVisit(CheckinStoreReturnVisitRequest request);

    /**
     * 订单中的商品列表同步到scrm中
     *
     * @param orderGoodsDTOList
     */
    void orderGoodsSync(List<OrderGoodsDTO> orderGoodsDTOList);

    /**
     * 订单减免记录同步到scrm中
     *
     * @param recordDTO
     */
    void orderReductionRecordSync(OrderReductionRecordDTO recordDTO);

    /**
     * 移除团队成员
     *
     * @param request
     */
    void removeTeamMember(ScrmTeamMemberRecordRequest request);
}
