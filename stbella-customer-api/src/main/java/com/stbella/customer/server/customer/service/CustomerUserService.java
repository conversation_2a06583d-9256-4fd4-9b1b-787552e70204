package com.stbella.customer.server.customer.service;

import com.stbella.core.base.PageVO;
import com.stbella.customer.server.customer.request.QrCodeRequest;
import com.stbella.customer.server.customer.request.UserQrCodeRequest;
import com.stbella.customer.server.customer.request.UserTestRoleCheckRequest;
import com.stbella.customer.server.customer.request.*;
import com.stbella.customer.server.customer.request.store.ClientSearchAddUserRequest;
import com.stbella.customer.server.customer.vo.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员
 *
 * <AUTHOR>
 * @date 2023/09/20 11:30:34
 */
public interface CustomerUserService {

    /**
     * 查询Banner列表  默认顶部和腰部图（0，1）
     *
     * @return boolean
     * <AUTHOR>
     * @date 2023/09/20 11:38:30
     * @since 1.0.0
     */
    CustomerBannerInfoVO queryBannerList(Integer brandType, Integer page);


    /**
     * 查询是否拥有演示权限
     *
     * @return boolean
     * <AUTHOR>
     * @date 2023/09/20 11:38:53
     * @since 1.0.0
     */
    Integer queryUserTestRoleCheck(UserTestRoleCheckRequest request);


    /**
     * 统计模块信息抽取
     *
     * @param basicId
     * @return
     */
    CustomerUserCenterVO.CountData getCountData(Long basicId);

    CountData  getCountDataByBrandType(UserCenterRequest request);

    /**
     * 获取我的二维码
     *
     * @param request request
     * @return user qr code vo
     * <AUTHOR>
     * @date 2023/10/31 05:13:09
     * @since 1.0.0
     */
    UserQrCodeVO getUserQrCode(UserQrCodeRequest request);



    /**
     * 用户中心信息
     *
     * @param
     * <AUTHOR>
     * @date 2023/10/25 02:23:11
     * @since 1.0.0
     */
    CustomerUserCenterVO center(UserCenterRequest request);


    /**
     * 查询 user info
     *
     * @param phone phone
     * @param brandType 品牌 0-圣贝拉 1-小贝拉
     * <AUTHOR>
     * @date 2023/11/06 06:46:49
     * @since 1.0.0
     */
    CustomerWechatUserInfoVO getUserInfo(String phone, Integer brandType);

    /**
     * 通过二维码获取用户头像和昵称
     *
     * @param request request
     * @return map<string, object>
     * <AUTHOR>
     * @date 2023/11/01 11:08:35
     * @since 1.0.0
     */
    Map<String, Object> getUserNickNameByQrCode(QrCodeRequest request);
    /**
     * update user info
     *
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/07 10:11:03
     * @since 1.0.0
     */
    Boolean updateUserInfo(CustomerUserEditInfoRequest request);


    /**
     * 获取新的邀请积分规则
     *
     * @param brandType brand type
     * @return invite rule vo
     * <AUTHOR>
     * @date 2023/11/01 11:46:32
     * @since 1.0.0
     */
    List<InviteRuleVO> getInviteRule(Integer brandType);
    //设置我的预产期
    Boolean setUserPregnancy(CustomerUserEditPregnancyRequest request);


    /**
     * 孕期专题
     *
     * @param type       type
     * @param clientType client type
     * <AUTHOR>
     * @date 2023/11/14 03:34:47
     * @since 1.0.0
     */
    Map<String, Object> getTopicsDuringPregnancy(Integer type, Integer clientType);

    /**
     * 朋友圈广告用户上报
     *
     * @param request
     * @return
     */
    Boolean wechatMomentPhoneReport(MomentClientPhoneReportRequest request);

    /**
     * 获取朋友圈广告点击人数
     *
     * @param brandType
     * @return
     */
    Long queryWechatMomentClickCount(Integer brandType);

    /**
     * 保存品牌客户第一次登录小程序时的调研问题
     *
     * @param request
     * @return
     */
    Boolean saveBrandResearchQuestion(BrandResearchQuestionRequest request);

    PageVO<ClientSearchVO> clientSearch(ClientSearchRequest request);

    ClientSearchVO createScrmUser(ClientSearchAddUserRequest request);

    /**
     * 获取对应VVIP客户的数据报表
     *
     * @param reportId
     * @param dateStart
     * @param dateEnd
     * @return
     */
    ReportDataVO getVvipCustomerData(Long reportId, Date dateStart, Date dateEnd);

    /**
     * 获取客户产康金基础信息
     *
     * @param basicId
     * @return
     */
    CustomerProductionAmountBaseInfoVO getCustomerProductionAmountBaseInfo(Integer basicId);

    /**
     * 查询客户产康金流水列表
     *
     * @param request
     * @return
     */
    PageVO<CustomerProductionAmountStreamVO> queryCustomerProductionAmountStream(CustomerProductionAmountStreamRequest request);
}
