package com.stbella.customer.server.customer.service;

import com.stbella.core.result.Result;
import com.stbella.customer.server.customer.entity.CustomerPostpartumInfoPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.customer.server.customer.request.CustomerPostpartumSaveRequest;
import com.stbella.customer.server.customer.request.HistoryCustomerRequest;
import com.stbella.customer.server.customer.request.postpartum.TaskNodeRequest;
import com.stbella.customer.server.customer.vo.PostpartumCalendarVO;
import com.stbella.customer.server.customer.vo.PostpartumCustomerVO;
import com.stbella.customer.server.customer.vo.PostpartumDetailVO;
import com.stbella.customer.server.customer.vo.postpartum.TaskNodeVO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 客户产后修复数据 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-09
 */
public interface CustomerPostpartumInfoService extends IService<CustomerPostpartumInfoPO> {

    Result<Void> savePostpartum(CustomerPostpartumSaveRequest request);

    List<CustomerPostpartumInfoPO> listByCustomerIdAndDate(List<Date> recordDateList, Long customerId);

    Result<PostpartumDetailVO> detail(Date recordDate, Long customerId);

    Result<PostpartumCalendarVO> calendar(Long customerId);

    Result<List<PostpartumCustomerVO>> historyCustomer(HistoryCustomerRequest request);

    List<TaskNodeVO> taskNodes(TaskNodeRequest request);

    List<CustomerPostpartumInfoPO> listByOrderNo(List<String> orderNoList, List<Integer> taskNodeList);


    /**
     * 入住第七天提醒
     */
    void remindSeventhDayAdmission();

    /**
     * 离店前3天
     */
    void remindThreeDayBeforeDeparture();

    /**
     * 宫体入盆提醒
     */
    void remindUterusInBasin();


    void savePostpartumBatch(List<CustomerPostpartumSaveRequest> request);

    List<CustomerPostpartumInfoPO> listByCustomerIdList(List<Long> customerIdList);

}
